[Unit]
Description=Agent Service
After=network.target
Documentation=https://github.com/yourusername/agent

[Service]
Type=forking
PIDFile=/var/run/agent.pid
ExecStart=/usr/local/bin/agent -config /etc/agent/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=3
User=agent
Group=agent
RuntimeDirectory=agent
RuntimeDirectoryMode=0755

# Security settings
NoNewPrivileges=true
ProtectSystem=full
ProtectHome=true
PrivateTmp=true
CapabilityBoundingSet=CAP_NET_BIND_SERVICE

# Resource limits
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target 