client:
  customer-id: 0                 # Customer ID
  client-id: 0                   # Client ID

comms:
  addrs:                         # List of server addresses
    - "0.0.0.0:0"
  tls:                           # TLS encryption configuration
    enabled: false               # Whether to enable TLS encryption (default: false, for backward compatibility)
    cert-dir: "./certs"          # Certificate file directory (default: ./certs)
    cert-file: "server.crt"      # Certificate file name (default: server.crt)
    key-file: "server.key"       # Private key file name (default: server.key)
    skip-verify: true            # Skip server certificate verification (default: true, for self-signed certificates)
    auto-generate: true          # Automatically generate self-signed certificates (default: true)

logging:
  level: "DEBUG"                                        # Logging level (e.g., DEBUG, INFO, WARN, ERROR)
  format: "json"                                        # Log format (e.g., json, text)
  outputs:                                              # Log output configuration
    - type: "file"                                      # File output type
      file: "/var/log/unisase_agent/agent.log"          # Log file path
      maxSize: 128                                      # Maximum size of a single log file in MB
      maxAge: 30                                        # Maximum number of days to retain old log files
      maxBackups: 10                                    # Maximum number of old log files to retain
      compress: true                                    # Whether to compress rotated log files
    - type: "console"                                   # Console output type
      stderr: false                                     # Whether to output to stderr instead of stdout
