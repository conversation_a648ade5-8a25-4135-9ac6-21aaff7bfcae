# Go 文件注释整改进度

此列表追踪根据 `docs/coding_guideline.md` 中定义的注释规范对项目中的 Go 文件进行注释整改的进度。

## 文件列表

- [x] `D:\go\src\agent\cmd\agent-debug-client\main.go`
- [x] `D:\go\src\agent\cmd\agent\main.go`
- [x] `D:\go\src\agent\internal\client\client.go`
- [x] `D:\go\src\agent\internal\client\instance.go`
- [x] `D:\go\src\agent\internal\client\interceptor.go`
- [x] `D:\go\src\agent\internal\client\proxy_status_monitor.go`
- [x] `D:\go\src\agent\internal\client\resolver.go`
- [x] `D:\go\src\agent\internal\client\task\common_floweye_utils.go`
- [x] `D:\go\src\agent\internal\client\task\common_protobuf_utils.go`
- [x] `D:\go\src\agent\internal\client\task\dhcp_config.go`
- [x] `D:\go\src\agent\internal\client\task\dhcp_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\dns_policy_config.go`
- [ ] `D:\go\src\agent\internal\client\task\dns_policy_helper.go`
- [ ] `D:\go\src\agent\internal\client\task\dns_policy_ordering.go`
- [ ] `D:\go\src\agent\internal\client\task\dns_policy_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\dns_tracking_policy_config.go`
- [ ] `D:\go\src\agent\internal\client\task\dns_tracking_policy_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\domain_group_config.go`
- [ ] `D:\go\src\agent\internal\client\task\domain_group_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\effective_time_config.go`
- [ ] `D:\go\src\agent\internal\client\task\effective_time_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\flow_control_config.go`
- [ ] `D:\go\src\agent\internal\client\task\flow_control_policy_group.go`
- [ ] `D:\go\src\agent\internal\client\task\flow_control_policy.go`
- [ ] `D:\go\src\agent\internal\client\task\flow_control_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\interface_config.go`
- [ ] `D:\go\src\agent\internal\client\task\interface_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\ip_group_config.go`
- [ ] `D:\go\src\agent\internal\client\task\ip_group_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\iwan_mapping_config.go`
- [ ] `D:\go\src\agent\internal\client\task\iwan_mapping_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\iwan_proxy_config.go`
- [ ] `D:\go\src\agent\internal\client\task\iwan_proxy_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\iwan_service_config.go`
- [ ] `D:\go\src\agent\internal\client\task\iwan_service_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\lan_config.go`
- [ ] `D:\go\src\agent\internal\client\task\lan_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\manager.go`
- [ ] `D:\go\src\agent\internal\client\task\route_policy_config.go`
- [ ] `D:\go\src\agent\internal\client\task\route_policy_lmp_ordering.go`
- [ ] `D:\go\src\agent\internal\client\task\route_policy_ordering.go`
- [ ] `D:\go\src\agent\internal\client\task\route_policy_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\sr_path_monitor_example.go`
- [ ] `D:\go\src\agent\internal\client\task\sr_path_monitor.go`
- [ ] `D:\go\src\agent\internal\client\task\sr_proxy_config.go`
- [ ] `D:\go\src\agent\internal\client\task\sr_proxy_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\task_logger.go`
- [ ] `D:\go\src\agent\internal\client\task\task.go`
- [ ] `D:\go\src\agent\internal\client\task\traffic_channel_config.go`
- [ ] `D:\go\src\agent\internal\client\task\traffic_channel_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\traffic_stat_config.go`
- [ ] `D:\go\src\agent\internal\client\task\traffic_stat_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\user_config.go`
- [ ] `D:\go\src\agent\internal\client\task\user_group_config.go`
- [ ] `D:\go\src\agent\internal\client\task\user_group_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\user_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\wan_config.go`
- [ ] `D:\go\src\agent\internal\client\task\wan_processor.go`
- [ ] `D:\go\src\agent\internal\client\task\wangroup_config.go`
- [ ] `D:\go\src\agent\internal\client\task\wangroup_processor.go`
- [ ] `D:\go\src\agent\internal\client\tls.go`
- [ ] `D:\go\src\agent\internal\config\config.go`
- [ ] `D:\go\src\agent\internal\daemon\daemon.go`
- [ ] `D:\go\src\agent\internal\debug\handler.go`
- [ ] `D:\go\src\agent\internal\debug\server.go`
- [ ] `D:\go\src\agent\internal\ipc\debug.go`
- [ ] `D:\go\src\agent\internal\ipc\ipc_client.go`
- [ ] `D:\go\src\agent\internal\ipc\ipc_server.go`
- [ ] `D:\go\src\agent\internal\ipc\ipc_types.go`
- [ ] `D:\go\src\agent\internal\ipc\unix_client.go`
- [ ] `D:\go\src\agent\internal\ipc\unix_server.go`
- [ ] `D:\go\src\agent\internal\logger\logger.go`
- [ ] `D:\go\src\agent\internal\metrics\metrics.go`
- [ ] `D:\go\src\agent\internal\utils\floweye_json.go`
- [ ] `D:\go\src\agent\internal\utils\ip.go`
- [ ] `D:\go\src\agent\internal\utils\shell.go`
- [ ] `D:\go\src\agent\internal\utils\test_helpers.go`
- [ ] `D:\go\src\agent\tool\floweye_tool\main.go`
