package main

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// 转换字符串从 UTF-8 到 GB2312
func utf8ToGB2312(input string) ([]byte, error) {
	// 创建一个 UTF-8 到 GB2312 的转换器
	encoder := simplifiedchinese.GBK.NewEncoder()
	return encoder.Bytes([]byte(input))
}

// 转换字符串从 GB2312 到 UTF-8
func gb2312ToUTF8(input []byte) (string, error) {
	// 创建一个 GB2312 到 UTF-8 的转换器
	decoder := simplifiedchinese.GBK.NewDecoder()
	decoded, _, err := transform.Bytes(decoder, input)
	if err != nil {
		return "", err
	}
	return string(decoded), nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: floweye_wrapper <参数...>")
		return
	}

	// 传入参数转换为 GB2312 编码
	utf8Args := os.Args[1:]
	gb2312Args := []string{}
	for _, arg := range utf8Args {
		gb2312Arg, err := utf8ToGB2312(arg)
		if err != nil {
			fmt.Fprintf(os.Stderr, "UTF-8 转 GB2312 失败: %v\n", err)
			os.Exit(1)
		}
		gb2312Args = append(gb2312Args, string(gb2312Arg))
	}

	// 构造原始命令：floweye 参数...
	cmd := exec.Command("floweye", gb2312Args...)
	var outBuf bytes.Buffer
	cmd.Stdout = &outBuf
	cmd.Stderr = os.Stderr

	// 执行 floweye 命令
	if err := cmd.Run(); err != nil {
		fmt.Fprintf(os.Stderr, "执行 floweye 失败: %v\n", err)
		os.Exit(1)
	}

	// 转码 GB2312 输出为 UTF-8
	decodedOutput, err := gb2312ToUTF8(outBuf.Bytes())
	if err != nil {
		fmt.Fprintf(os.Stderr, "转码 GB2312 到 UTF-8 失败: %v\n", err)
		os.Exit(1)
	}

	// 输出转换后的结果
	fmt.Print(decodedOutput)
}
