# UniSASE Agent

## 项目简介 (Project Introduction)

UniSASE Agent 是一个基于Go语言开发的系统管理和监控守护进程。它提供了与UniSASE orchestrater的双向通信能力，支持配置同步、任务执行、心跳检测等核心功能，将Orch下发的配置使用`floweye`命令配置进本地PA系统。该代理程序采用模块化设计，具有良好的可扩展性和可维护性。

(UniSASE Agent is a system management and monitoring daemon developed in Go. It provides bidirectional communication with the UniSASE orchestrator, supporting core functions like configuration synchronization, task execution, and heartbeat monitoring. It applies configurations received from the orchestrator to the local PA system using `floweye` commands. The agent features a modular design for extensibility and maintainability.)

## 主要特性 (Key Features)

*   **gRPC配置管理 (gRPC Configuration Management)**: Supports incremental and full configuration synchronization, validation, and consistency checks. (See `docs/CONTRIBUTING.md` Section 4.6 for details)
*   **任务处理 (Task Processing)**: Asynchronously executes various configuration tasks (Interface, WAN, LAN, DHCP, Policy, WAN Group, User Group, User, iWAN Proxy, iWAN Service, DNS Policy) with status tracking and feedback. (See `docs/CONTRIBUTING.md` and specific `docs/*_implementation.md` / `docs/floweye_*.md`)
*   **心跳检测 (Heartbeat)**: Monitors connection health with the orchestrator and enables auto-reconnection.
*   **日志管理 (Logging)**: Provides configurable, multi-level logging with rotation using `zap`.
*   **进程管理 (Process Management)**: Supports running as a daemon with graceful shutdown and status monitoring.
*   **调试支持 (Debugging Support)**: Includes an IPC-based debug server/client for status checks.

## 技术栈 (Technology Stack)

*   **开发语言 (Language)**: Go 1.24+
*   **通信协议 (Communication)**: gRPC (Agent <-> Orchestrator)
*   **进程间通信 (IPC)**: Unix Domain Socket (for local interaction, e.g., debug client)
*   **日志系统 (Logging)**: zap
*   **配置管理 (Configuration)**: YAML
*   **构建工具 (Build Tool)**: Make

## 系统架构概览 (High-Level Architecture)

```
+---------------------+      +-----------------------+      +---------------------+
|                     |      |                       |      |                     |
|  UniSASE Orchestrator |<-gRPC->|    UniSASE Agent      |<----->|  Local PA System  |
|       (Orch)        |      | (Go Daemon Process)   |      | (via floweye cmd) |
|                     |      |                       |      |                     |
+---------------------+      +----------+------------+      +----------+----------+\
```

The agent receives commands via gRPC and interacts with the local system, primarily by executing `floweye` commands. An IPC interface allows local tools to interact with the agent.

For detailed architecture, component descriptions, application flows, and the **development workflow**, please see the **[Contribution Guide](docs/CONTRIBUTING.md)**.

## 环境要求 (Requirements)

*   Go 1.24+
*   Unix-like Operating System
*   Access to UniSASE Orchestrator (for gRPC communication)
*   `floweye` command available locally (or equivalent PA system interface)

## 快速开始 (Quick Start)

### 1. 安装 (Installation)

```bash
# Clone the repository
git clone https://github.com/your-org/agent.git # Replace with actual URL
cd agent

# Install dependencies
go mod download

# Build binaries
make build

# Install the service (adjust based on system requirements)
# sudo make install
```

### 2. 配置 (Configuration)

Modify the `config.yaml` file in the project root or installation directory. Key settings include:

```yaml
client:
  customer-id: 12345    # Customer ID
  client-id: 223        # Agent/Client ID for this instance

comms:
  addrs:                # List of Orchestrator addresses
    - "orchestrator.example.com:50051"
    # - "127.0.0.1:50051" # Example local address

logging:
  level: "INFO"         # Log level (DEBUG, INFO, WARN, ERROR)
  format: "json"        # Log format (json or console)
  outputs:              # Log output configuration
    - type: "file"
      file: "/var/log/agent.log" # Log file path
      maxSize: 128              # Max size in MB before rotation
      maxAge: 30                # Max days to keep old log files
      maxBackups: 10            # Max number of old log files to keep
      compress: true            # Compress rotated logs
    # - type: "stdout" # Uncomment to also log to console
```

### 3. 运行 (Running)

```bash
# Start the agent (e.g., directly or via systemd/service manager)
./agent # Or use the installed service command
```

Check the configured log file (e.g., `/var/log/agent.log`) for status and errors.

## 文档 (Documentation)

*   **[Contribution Guide](docs/CONTRIBUTING.md)**: **Primary resource for developers.** Contains detailed project structure, component descriptions, application flows, configuration sync mechanism, development workflow, and coding guidelines summary.
*   **[Implementation Plan](docs/implementation_plan.md)**: Template for future development tasks.
*   **Specific Design/Implementation Docs**: Located in the `docs/` directory (e.g., `floweye_*.md`, `*_implementation.md`). These provide deep dives into specific areas referenced in the Contribution Guide.
*   **[AI Rules](.airules)**: Definitive guidelines for AI-assisted development and coding standards.

## 贡献 (Contributing)

Please refer to the **[Contribution Guide](docs/CONTRIBUTING.md)** for comprehensive details on how to contribute to this project.
