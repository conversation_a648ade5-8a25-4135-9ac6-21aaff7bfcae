# Makefile for agent

# Git Information
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_COMMIT := $(shell git rev-parse --short HEAD)
BUILD_TIME := $(shell date -u '+%Y-%m-%d %H:%M:%S')

# --- Versioning ---
# The version can be specified via the UNISASE_AGENT_VERSION environment variable.
# e.g., UNISASE_AGENT_VERSION=v2.1.0 make build-package
# If not set, a default version is generated using the latest git tag, branch name, commit hash, and build time.
# Fallback to v0.0.0 if no git tag is found.
LATEST_TAG := $(shell git describe --tags --always 2>/dev/null || echo "v0.0.0")
BUILD_TIMESTAMP_FOR_VER := $(shell date -u '+%Y%m%d_%H%M%S')
DEFAULT_VERSION := $(LATEST_TAG)_$(GIT_BRANCH)_$(BUILD_TIMESTAMP_FOR_VER)
VERSION := $(or $(UNISASE_AGENT_VERSION),$(DEFAULT_VERSION))

# Architecture Variables
ARCH_AMD64 := amd64
ARCH_ARM64 := arm64
# Supported Operating Systems
OS_LINUX := linux
# Host System
HOST_OS := $(shell uname -s | tr '[:upper:]' '[:lower:]')

# Build Flags
LDFLAGS := -ldflags "-X 'main.Version=$(VERSION)' -X 'main.BuildTime=$(BUILD_TIME)' -X 'main.GitBranch=$(GIT_BRANCH)' -X 'main.GitCommit=$(GIT_COMMIT)'"
GO_BUILD_ENV := CGO_ENABLED=0

# Default Target
.PHONY: all
all: build

# Build All Architectures
.PHONY: build
build: build-linux

# Build Linux Versions
.PHONY: build-linux
build-linux: build-linux-amd64 build-linux-arm64

# Build for Linux AMD64
.PHONY: build-linux-amd64
build-linux-amd64: agent-linux-amd64 debug-client-linux-amd64

# Build for Linux ARM64
.PHONY: build-linux-arm64
build-linux-arm64: agent-linux-arm64 debug-client-linux-arm64

# Backward Compatibility Targets
.PHONY: build-amd64
build-amd64: build-linux-amd64

.PHONY: build-arm64
build-arm64: build-linux-arm64

# Build agent for Linux AMD64
.PHONY: agent-linux-amd64
agent-linux-amd64:
	@echo "Building agent for Linux AMD64..."
	@mkdir -p build/$(OS_LINUX)_$(ARCH_AMD64)
	$(GO_BUILD_ENV) GOOS=$(OS_LINUX) GOARCH=$(ARCH_AMD64) go build -buildvcs=false $(LDFLAGS) -o build/$(OS_LINUX)_$(ARCH_AMD64)/agent ./cmd/agent

# Build agent for Linux ARM64
.PHONY: agent-linux-arm64
agent-linux-arm64:
	@echo "Building agent for Linux ARM64..."
	@mkdir -p build/$(OS_LINUX)_$(ARCH_ARM64)
	$(GO_BUILD_ENV) GOOS=$(OS_LINUX) GOARCH=$(ARCH_ARM64) go build -buildvcs=false $(LDFLAGS) -o build/$(OS_LINUX)_$(ARCH_ARM64)/agent ./cmd/agent

# Build debug client for Linux AMD64
.PHONY: debug-client-linux-amd64
debug-client-linux-amd64:
	@echo "Building debug client for Linux AMD64..."
	@mkdir -p build/$(OS_LINUX)_$(ARCH_AMD64)
	$(GO_BUILD_ENV) GOOS=$(OS_LINUX) GOARCH=$(ARCH_AMD64) go build -buildvcs=false -o build/$(OS_LINUX)_$(ARCH_AMD64)/agent-debug-client ./cmd/agent-debug-client

# Build debug client for Linux ARM64
.PHONY: debug-client-linux-arm64
debug-client-linux-arm64:
	@echo "Building debug client for Linux ARM64..."
	@mkdir -p build/$(OS_LINUX)_$(ARCH_ARM64)
	$(GO_BUILD_ENV) GOOS=$(OS_LINUX) GOARCH=$(ARCH_ARM64) go build -buildvcs=false -o build/$(OS_LINUX)_$(ARCH_ARM64)/agent-debug-client ./cmd/agent-debug-client

# Backward Compatibility Targets
.PHONY: agent-amd64
agent-amd64: agent-linux-amd64

.PHONY: agent-arm64
agent-arm64: agent-linux-arm64

.PHONY: debug-client-amd64
debug-client-amd64: debug-client-linux-amd64

.PHONY: debug-client-arm64
debug-client-arm64: debug-client-linux-arm64

# Build agent for current platform (Linux only)
.PHONY: agent
agent:
	@if [ "$(HOST_OS)" != "linux" ]; then \
		echo "Error: This target is intended for Linux environments only. Your OS is $(HOST_OS)."; \
		exit 1; \
	fi
	@echo "Building agent for current platform (Linux)..."
	@mkdir -p build
	$(GO_BUILD_ENV) GOOS=$(HOST_OS) go build -buildvcs=false $(LDFLAGS) -o build/agent ./cmd/agent

# Build debug client for current platform (Linux only)
.PHONY: debug-client
debug-client:
	@if [ "$(HOST_OS)" != "linux" ]; then \
		echo "Error: This target is intended for Linux environments only. Your OS is $(HOST_OS)."; \
		exit 1; \
	fi
	@echo "Building debug client for current platform (Linux)..."
	@mkdir -p build
	$(GO_BUILD_ENV) GOOS=$(HOST_OS) go build -buildvcs=false -o build/agent-debug-client ./cmd/agent-debug-client

# Clean
.PHONY: clean
clean:
	@echo "Cleaning..."
	@rm -rf build
	@go clean

# Run agent on current platform (Linux only)
.PHONY: run
run: agent
	@echo "Running agent..."
	./build/agent

# Test Variables
PA_HOST ?= *************
PA_USER ?= root
PA_DEPLOY_DIR ?= /root/agent

# Unit Tests
.PHONY: test
test:
	@echo "Running unit tests..."
	$(GO_BUILD_ENV) go test -v ./...

# Automated Test System - Common Function
.PHONY: test-deploy-and-run
test-deploy-and-run:
	@if [ -z "$(MODULE)" ]; then \
		echo "Error: MODULE parameter is required"; \
		echo "Usage: make test-deploy-and-run MODULE=interface [TEST_SCRIPT=interface_quick_test.sh]"; \
		exit 1; \
	fi
	@echo "Starting automated test deployment and execution..."
	@echo "Module: $(MODULE)"
	@echo "Test Script: $(if $(TEST_SCRIPT),$(TEST_SCRIPT),$(MODULE)_quick_test.sh)"
	@echo "Target System: $(PA_USER)@$(PA_HOST)"
	@echo "Verbose Mode: $(if $(VERBOSE),true,false)"
	@if [ "$(VERBOSE)" = "true" ]; then \
		BASH_DEBUG=true ./scripts/deploy_and_test.sh $(MODULE) $(TEST_SCRIPT); \
	else \
		./scripts/deploy_and_test.sh $(MODULE) $(TEST_SCRIPT); \
	fi

# Interface Module Tests
.PHONY: test-interface
test-interface: build-linux-arm64
	@echo "Running Interface module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=interface TEST_SCRIPT=interface_comprehensive_test.sh

.PHONY: test-interface-quick
test-interface-quick: build-linux-arm64
	@echo "Running Interface module quick tests..."
	@$(MAKE) test-deploy-and-run MODULE=interface TEST_SCRIPT=interface_quick_test.sh

.PHONY: test-interface-comprehensive
test-interface-comprehensive: build-linux-arm64
	@echo "Running Interface module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=interface TEST_SCRIPT=interface_comprehensive_test.sh

# WAN Module Tests
.PHONY: test-wan
test-wan: build-linux-arm64
	@echo "Running WAN module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=wan TEST_SCRIPT=wan_comprehensive_test.sh

.PHONY: test-wan-comprehensive
test-wan-comprehensive: test-wan

# LAN Module Tests
.PHONY: test-lan
test-lan: build-linux-arm64
	@echo "Running LAN module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=lan TEST_SCRIPT=lan_comprehensive_test.sh

.PHONY: test-lan-comprehensive
test-lan-comprehensive: test-lan

# DHCP Module Tests
.PHONY: test-dhcp
test-dhcp: build-linux-arm64
	@echo "Running DHCP module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=dhcp TEST_SCRIPT=dhcp_comprehensive_test.sh

.PHONY: test-dhcp-comprehensive
test-dhcp-comprehensive: test-dhcp

# WAN Group Module Tests
.PHONY: test-wangroup
test-wangroup: build-linux-arm64
	@echo "Running WAN Group module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=wangroup TEST_SCRIPT=wangroup_comprehensive_test.sh

.PHONY: test-wangroup-comprehensive
test-wangroup-comprehensive: test-wangroup

# User Group Module Tests
.PHONY: test-usergroup
test-usergroup: build-linux-arm64
	@echo "Running User Group module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=usergroup TEST_SCRIPT=user_group_comprehensive_test.sh

.PHONY: test-usergroup-comprehensive
test-usergroup-comprehensive: test-usergroup

# User Module Tests
.PHONY: test-user
test-user: build-linux-arm64
	@echo "Running User module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=user TEST_SCRIPT=user_comprehensive_test.sh

.PHONY: test-user-comprehensive
test-user-comprehensive: test-user

# iWAN Proxy Module Tests
.PHONY: test-iwan-proxy
test-iwan-proxy: build-linux-arm64
	@echo "Running iWAN Proxy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=iwan-proxy TEST_SCRIPT=iwan_proxy_comprehensive_test.sh

.PHONY: test-iwan-proxy-comprehensive
test-iwan-proxy-comprehensive: test-iwan-proxy

# iWAN Service Module Tests
.PHONY: test-iwan-service
test-iwan-service: build-linux-arm64
	@echo "Running iWAN Service module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=iwan-service TEST_SCRIPT=iwan_service_comprehensive_test.sh

.PHONY: test-iwan-service-comprehensive
test-iwan-service-comprehensive: test-iwan-service

# iWAN Mapping Module Tests
.PHONY: test-iwan-mapping
test-iwan-mapping: build-linux-arm64
	@echo "Running iWAN Mapping module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=iwan-mapping TEST_SCRIPT=iwan_mapping_comprehensive_test.sh

.PHONY: test-iwan-mapping-comprehensive
test-iwan-mapping-comprehensive: test-iwan-mapping

# SR Proxy Module Tests
.PHONY: test-sr-proxy
test-sr-proxy: build-linux-arm64
	@echo "Running SR Proxy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=sr-proxy TEST_SCRIPT=sr_proxy_comprehensive_test.sh

.PHONY: test-sr-proxy-comprehensive
test-sr-proxy-comprehensive: test-sr-proxy

# IP Group Module Tests
.PHONY: test-ip-group
test-ip-group: build-linux-arm64
	@echo "Running IP Group module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=ip-group TEST_SCRIPT=ip_group_comprehensive_test.sh

.PHONY: test-ip-group-comprehensive
test-ip-group-comprehensive: test-ip-group

# Domain Group Module Tests
.PHONY: test-domain-group
test-domain-group: build-linux-arm64
	@echo "Running Domain Group module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=domain-group TEST_SCRIPT=domain_group_comprehensive_test.sh

.PHONY: test-domain-group-comprehensive
test-domain-group-comprehensive: test-domain-group

# Traffic Channel Module Tests
.PHONY: test-traffic-channel
test-traffic-channel: build-linux-arm64
	@echo "Running Traffic Channel module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=traffic-channel TEST_SCRIPT=traffic_channel_comprehensive_test.sh

.PHONY: test-traffic-channel-comprehensive
test-traffic-channel-comprehensive: test-traffic-channel

# Traffic Stat Module Tests
.PHONY: test-traffic-stat
test-traffic-stat: build-linux-arm64
	@echo "Running Traffic Stat module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=traffic-stat TEST_SCRIPT=traffic_stat_comprehensive_test.sh

.PHONY: test-traffic-stat-comprehensive
test-traffic-stat-comprehensive: test-traffic-stat

# Effective Time Module Tests
.PHONY: test-effective-time
test-effective-time: build-linux-arm64
	@echo "Running Effective Time module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=effective-time TEST_SCRIPT=effective_time_comprehensive_test.sh

.PHONY: test-effective-time-comprehensive
test-effective-time-comprehensive: test-effective-time

# Flow Control Policy Module Tests
.PHONY: test-flow-control
test-flow-control: build-linux-arm64
	@echo "Running Flow Control Policy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=flow-control TEST_SCRIPT=flow_control_comprehensive_test.sh

.PHONY: test-flow-control-comprehensive
test-flow-control-comprehensive: test-flow-control

# DNS Policy Module Tests
.PHONY: test-dns-policy
test-dns-policy: build-linux-arm64
	@echo "Running DNS Policy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=dns-policy TEST_SCRIPT=dns_policy_comprehensive_test.sh

.PHONY: test-dns-policy-comprehensive
test-dns-policy-comprehensive: test-dns-policy

# DNS Tracking Policy Module Tests
.PHONY: test-dns-tracking-policy
test-dns-tracking-policy: build-linux-arm64
	@echo "Running DNS Tracking Policy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=dns-tracking-policy TEST_SCRIPT=dns_tracking_policy_comprehensive_test.sh

.PHONY: test-dns-tracking-policy-comprehensive
test-dns-tracking-policy-comprehensive: test-dns-tracking-policy

# Flow Control Group Policy Module Tests
.PHONY: test-flow-control-group-policy
test-flow-control-group-policy: build-linux-arm64
	@echo "Running Flow Control Group Policy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=flow-control-group-policy TEST_SCRIPT=flow_control_group_policy_comprehensive_test.sh

.PHONY: test-flow-control-group-policy-comprehensive
test-flow-control-group-policy-comprehensive: test-flow-control-group-policy

# Route Policy Module Tests
.PHONY: test-route-policy
test-route-policy: build-linux-arm64
	@echo "Running Route Policy module comprehensive tests..."
	@$(MAKE) test-deploy-and-run MODULE=route-policy TEST_SCRIPT=route_policy_comprehensive_test.sh

.PHONY: test-route-policy-comprehensive
test-route-policy-comprehensive: test-route-policy

# All Module Tests (Reserved)
.PHONY: test-all
test-all: build-linux-arm64
	@echo "Running all module tests..."
	@echo "Note: This will run comprehensive tests for all modules sequentially"
	@$(MAKE) test-interface
	@$(MAKE) test-wan
	@$(MAKE) test-lan
	@$(MAKE) test-dhcp
	@$(MAKE) test-wangroup
	@$(MAKE) test-usergroup
	@$(MAKE) test-user
	@$(MAKE) test-iwan-proxy
	@$(MAKE) test-iwan-service
	@$(MAKE) test-iwan-mapping
	@$(MAKE) test-sr-proxy
	@$(MAKE) test-ip-group
	@$(MAKE) test-domain-group
	@$(MAKE) test-traffic-channel
	@$(MAKE) test-effective-time
	@$(MAKE) test-flow-control
	@$(MAKE) test-dns-policy
	@$(MAKE) test-dns-tracking-policy
	@$(MAKE) test-flow-control-group-policy
	@$(MAKE) test-route-policy
	@echo "All module tests completed"

# Test Environment Check
.PHONY: test-check-env
test-check-env:
	@echo "Checking test environment..."
	@echo "PA System: $(PA_USER)@$(PA_HOST)"
	@echo "Deployment Directory: $(PA_DEPLOY_DIR)"
	@echo "Checking SSH connection..."
	@ssh -o ConnectTimeout=10 -o BatchMode=yes $(PA_USER)@$(PA_HOST) 'echo "SSH connection successful"' || \
		(echo "SSH connection failed. Please check network and key configuration"; exit 1)
	@echo "Checking for floweye command..."
	@ssh $(PA_USER)@$(PA_HOST) 'command -v floweye >/dev/null && echo "floweye command is available" || echo "Warning: floweye command is not available"'
	@echo "Test environment check complete"

# Collect Test Results
.PHONY: test-collect-results
test-collect-results:
	@echo "Collecting test results from PA system..."
	@./scripts/collect_test_results.sh

# Clean Up Remote Test Environment
.PHONY: test-cleanup-remote
test-cleanup-remote:
	@echo "Cleaning up remote test environment..."
	@ssh $(PA_USER)@$(PA_HOST) 'rm -rf $(PA_DEPLOY_DIR) $(PA_DEPLOY_DIR)_logs /tmp/agent_test* /tmp/test_package*' || true
	@echo "Remote environment cleanup complete"

# Test Tool Targets
.PHONY: test-tools
test-tools:
	@echo "Test Tools and Scripts:"
	@echo "  scripts/deploy_and_test.sh      - Automated deployment and testing script"
	@echo "  scripts/collect_test_results.sh - Test result collection script"
	@echo "  scripts/test_config_template.yaml - PA environment test configuration template"
	@echo ""
	@echo "Usage:"
	@echo "  make test-interface             - Run interface module tests"
	@echo "  make test-collect-results       - Collect test results"
	@echo "  make test-cleanup-remote        - Clean up remote environment"

# Format
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint
.PHONY: lint
lint:
	@echo "Linting code..."
	golangci-lint run

# Install (default for current platform)
.PHONY: install
install: agent
	@echo "Installing agent for current platform..."
	@sudo mkdir -p /usr/local/bin
	@sudo cp build/agent /usr/local/bin/
	@sudo mkdir -p /etc/agent
	@sudo cp configs/config.yaml.template /etc/agent/config.yaml
	@echo "Agent installed to /usr/local/bin/agent"
	@echo "Configuration file installed to /etc/agent/config.yaml"

# Install for Linux AMD64
.PHONY: install-linux-amd64
install-linux-amd64: agent-linux-amd64
	@echo "Installing agent for Linux AMD64..."
	@sudo mkdir -p /usr/local/bin
	@sudo cp build/$(OS_LINUX)_$(ARCH_AMD64)/agent /usr/local/bin/agent
	@sudo mkdir -p /etc/agent
	@sudo cp configs/config.yaml.template /etc/agent/config.yaml
	@echo "Agent (Linux AMD64) installed to /usr/local/bin/agent"
	@echo "Configuration file installed to /etc/agent/config.yaml"

# Install for Linux ARM64
.PHONY: install-linux-arm64
install-linux-arm64: agent-linux-arm64
	@echo "Installing agent for Linux ARM64..."
	@sudo mkdir -p /usr/local/bin
	@sudo cp build/$(OS_LINUX)_$(ARCH_ARM64)/agent /usr/local/bin/agent
	@sudo mkdir -p /etc/agent
	@sudo cp configs/config.yaml.template /etc/agent/config.yaml
	@echo "Agent (Linux ARM64) installed to /usr/local/bin/agent"
	@echo "Configuration file installed to /etc/agent/config.yaml"

# Backward Compatibility Targets
.PHONY: install-amd64
install-amd64: install-linux-amd64

.PHONY: install-arm64
install-arm64: install-linux-arm64

# --- Package Targets ---
PACKAGE_DIR := package
PACKAGE_BUILD_DIR := $(PACKAGE_DIR)/build
PACKAGE_NAME := unisase_agent
PACKAGE_VERSION := $(VERSION)

# Package for all architectures (no build)
.PHONY: package
package: package-linux-amd64 package-linux-arm64

# Package for specific architectures (no build)
.PHONY: package-linux-amd64
package-linux-amd64:
	@$(MAKE) _package_arch ARCH=$(ARCH_AMD64)

.PHONY: package-linux-arm64
package-linux-arm64:
	@$(MAKE) _package_arch ARCH=$(ARCH_ARM64)

# Internal parameterized target for packaging a specific architecture
# This is not meant to be called directly by the user.
.PHONY: _package_arch
_package_arch:
	@echo "--> Packaging for $(OS_LINUX)_$(ARCH)..."
	@echo "--> Checking for binaries..."
	@if [ ! -f "build/$(OS_LINUX)_$(ARCH)/agent" ] || [ ! -f "build/$(OS_LINUX)_$(ARCH)/agent-debug-client" ]; then \
		echo "Error: Build artifacts for $(OS_LINUX)_$(ARCH) not found. Please run 'make build-linux-$(ARCH)' first."; \
		exit 1; \
	fi
	@echo "--> Preparing temporary files..."
	@rm -rf $(PACKAGE_DIR)/build
	@mkdir -p $(PACKAGE_DIR)/build
	@mkdir -p $(PACKAGE_DIR)/bin
	@cp build/$(OS_LINUX)_$(ARCH)/agent $(PACKAGE_DIR)/bin/
	@cp build/$(OS_LINUX)_$(ARCH)/agent-debug-client $(PACKAGE_DIR)/bin/
	@mkdir -p $(PACKAGE_DIR)/config
	@cp configs/config.yaml.default $(PACKAGE_DIR)/config/config.yaml
	@echo "--> Running staging script..."
	@$(SHELL) $(PACKAGE_DIR)/stage_build_files.sh $(VERSION)
	@echo "--> Creating archive..."
	@mkdir -p build
	@cd $(PACKAGE_BUILD_DIR) && tar -czf ../../build/$(PACKAGE_NAME)-$(PACKAGE_VERSION)-$(OS_LINUX)-$(ARCH).tar.gz .
	@echo "--> Cleaning up temporary files..."
	@rm -rf $(PACKAGE_DIR)/bin $(PACKAGE_DIR)/config $(PACKAGE_BUILD_DIR)
	@echo "Packaging complete for $(OS_LINUX)_$(ARCH)"

# Build and package for all architectures
.PHONY: build-package
build-package: build-package-linux-amd64 build-package-linux-arm64

# Build and package for specific architectures
.PHONY: build-package-linux-amd64
build-package-linux-amd64: build-linux-amd64 package-linux-amd64

.PHONY: build-package-linux-arm64
build-package-linux-arm64: build-linux-arm64 package-linux-arm64

# Clean Package Files
.PHONY: package-clean
package-clean:
	@echo "Cleaning package files..."
	@rm -rf $(PACKAGE_BUILD_DIR)
	@rm -f build/$(PACKAGE_NAME)-*.tar.gz

# Verify Packages for all architectures
.PHONY: package-verify
package-verify: package-verify-linux-amd64 package-verify-linux-arm64

.PHONY: package-verify-linux-amd64
package-verify-linux-amd64:
	@$(MAKE) _package_verify_arch ARCH=$(ARCH_AMD64)

.PHONY: package-verify-linux-arm64
package-verify-linux-arm64:
	@$(MAKE) _package_verify_arch ARCH=$(ARCH_ARM64)

# Internal parameterized target for verifying a package
.PHONY: _package_verify_arch
_package_verify_arch:
	@echo "--> Verifying package for $(OS_LINUX)-$(ARCH)..."
	@if [ ! -f "build/$(PACKAGE_NAME)-$(PACKAGE_VERSION)-$(OS_LINUX)-$(ARCH).tar.gz" ]; then \
		echo "Error: Package file not found: build/$(PACKAGE_NAME)-$(PACKAGE_VERSION)-$(OS_LINUX)-$(ARCH).tar.gz"; \
		exit 1; \
	fi
	@echo "--> Checking archive contents..."
	@tar -tzf build/$(PACKAGE_NAME)-$(PACKAGE_VERSION)-$(OS_LINUX)-$(ARCH).tar.gz | head -20
	@echo "..."
	@echo "--> Archive verification complete for $(OS_LINUX)-$(ARCH)"

# Help
.PHONY: help
help:
	@echo "Available targets:"
	@echo ""
	@echo "Build targets:"
	@echo "  build               - Build binaries for all supported architectures and OS (Currently Linux only)"
	@echo "  build-linux         - Build binaries for Linux (AMD64, ARM64)"
	@echo "  build-linux-amd64   - Build binaries for Linux AMD64"
	@echo "  build-linux-arm64   - Build binaries for Linux ARM64"
	@echo "  agent               - Build the agent binary for current platform (Linux only)"
	@echo "  debug-client        - Build the debug client binary for current platform (Linux only)"
	@echo ""
	@echo "Package targets:"
	@echo "  package-linux-amd64 - Package agent for linux-amd64 (no build)"
	@echo "  package-linux-arm64 - Package agent for linux-arm64 (no build)"
	@echo "  package             - Package agent for all architectures (no build)"
	@echo "  build-package-linux-amd64 - Build and package for linux-amd64"
	@echo "  build-package-linux-arm64 - Build and package for linux-arm64"
	@echo "  build-package       - Build and package for all architectures"
	@echo "  package-clean       - Clean all package files"
	@echo "  package-verify      - Verify the amd64 package"
	@echo ""
	@echo "Development targets:"
	@echo "  clean               - Remove build artifacts"
	@echo "  run                 - Build and run the agent (current platform, Linux only)"
	@echo "  fmt                 - Format code"
	@echo "  lint                - Run linter"
	@echo ""
	@echo "Test targets:"
	@echo "  test                - Run unit tests"
	@echo "  test-check-env      - Check PA test environment connectivity"
	@echo "  test-interface      - Run Interface module comprehensive test on PA system"
	@echo "  test-interface-quick - Run Interface module quick test on PA system"
	@echo "  test-interface-comprehensive - Run Interface module comprehensive test"
	@echo "  test-wan            - Run WAN module comprehensive test on PA system"
	@echo "  test-wan-comprehensive - Alias for test-wan"
	@echo "  test-lan            - Run LAN module comprehensive test on PA system"
	@echo "  test-lan-comprehensive - Alias for test-lan"
	@echo "  test-dhcp           - Run DHCP module comprehensive test on PA system"
	@echo "  test-dhcp-comprehensive - Alias for test-dhcp"
	@echo "  test-wangroup       - Run WAN Group module comprehensive test on PA system"
	@echo "  test-wangroup-comprehensive - Alias for test-wangroup"
	@echo "  test-usergroup      - Run User Group module comprehensive test on PA system"
	@echo "  test-usergroup-comprehensive - Alias for test-usergroup"
	@echo "  test-user           - Run User module comprehensive test on PA system"
	@echo "  test-user-comprehensive - Alias for test-user"
	@echo "  test-iwan-proxy     - Run iWAN Proxy module comprehensive test on PA system"
	@echo "  test-iwan-proxy-comprehensive - Alias for test-iwan-proxy"
	@echo "  test-iwan-service   - Run iWAN Service module comprehensive test on PA system"
	@echo "  test-iwan-service-comprehensive - Alias for test-iwan-service"
	@echo "  test-iwan-mapping   - Run iWAN Mapping module comprehensive test on PA system"
	@echo "  test-iwan-mapping-comprehensive - Alias for test-iwan-mapping"
	@echo "  test-sr-proxy       - Run SR Proxy module comprehensive test on PA system"
	@echo "  test-sr-proxy-comprehensive - Alias for test-sr-proxy"
	@echo "  test-ip-group       - Run IP Group module comprehensive test on PA system"
	@echo "  test-ip-group-comprehensive - Alias for test-ip-group"
	@echo "  test-domain-group   - Run Domain Group module comprehensive test on PA system"
	@echo "  test-domain-group-comprehensive - Alias for test-domain-group"
	@echo "  test-traffic-channel - Run Traffic Channel module comprehensive test on PA system"
	@echo "  test-traffic-channel-comprehensive - Alias for test-traffic-channel"
	@echo "  test-traffic-stat   - Run Traffic Stat module comprehensive test on PA system"
	@echo "  test-traffic-stat-comprehensive - Alias for test-traffic-stat"
	@echo "  test-effective-time - Run Effective Time module comprehensive test on PA system"
	@echo "  test-effective-time-comprehensive - Alias for test-effective-time"
	@echo "  test-flow-control   - Run Flow Control Policy module comprehensive test on PA system"
	@echo "  test-flow-control-comprehensive - Alias for test-flow-control"
	@echo "  test-dns-policy     - Run DNS Policy module comprehensive test on PA system"
	@echo "  test-dns-policy-comprehensive - Alias for test-dns-policy"
	@echo "  test-dns-tracking-policy - Run DNS Tracking Policy module comprehensive test on PA system"
	@echo "  test-dns-tracking-policy-comprehensive - Alias for test-dns-tracking-policy"
	@echo "  test-route-policy   - Run Route Policy module comprehensive test on PA system"
	@echo "  test-route-policy-comprehensive - Alias for test-route-policy"
	@echo "  test-all            - Run all module tests on PA system"
	@echo "  test-collect-results - Collect test results from PA system"
	@echo "  test-cleanup-remote - Clean up remote test environment"
	@echo "  test-tools          - Show available test tools and usage"
	@echo ""
	@echo "Install targets:"
	@echo "  install             - Install agent to system (current platform, Linux only)"
	@echo "  install-linux-amd64 - Install agent to system (Linux AMD64)"
	@echo "  install-linux-arm64 - Install agent to system (Linux ARM64)"
	@echo ""
	@echo "Environment variables for testing:"
	@echo "  PA_HOST             - PA system IP address (default: *************)"
	@echo "  PA_USER             - SSH username (default: root)"
	@echo "  PA_DEPLOY_DIR       - Deployment directory (default: /root/agent)"
	@echo ""
	@echo "Examples:"
	@echo "  make build-package                   # Build and create installation package for all architectures"
	@echo "  make build-package-linux-amd64       # Build and create installation package for linux-amd64"
	@echo "  make package                         # Create installation package from existing binaries for all architectures"
	@echo "  make package-linux-arm64             # Create installation package from existing binaries for linux-arm64"
	@echo "  make package-verify                  # Verify created package (amd64)"
	@echo "  make test-interface                  # Comprehensive interface test"
	@echo "  VERBOSE=true make test-interface    # Test with detailed execution output"
	@echo "  PA_HOST=************* make test-wan # Test on different PA system"}
