// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : resolver.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package client

import (
	"net"
	"strings"
	"sync"

	"google.golang.org/grpc/resolver"
)

// StaticResolver implements a custom resolver for static address resolution.
// It handles DNS resolution and provides a list of resolved addresses to the gRPC client.
type StaticResolver struct {
	target resolver.Target    // The resolver target containing the endpoint information.
	cc     resolver.ClientConn  // The client connection that will use this resolver.
	addrs  []string           // A list of addresses to be resolved.
	mu     sync.Mutex         // A mutex for thread-safe operations.
	done   chan struct{}      // A channel for signaling resolver shutdown.
}

// StaticResolverBuilder implements the resolver.Builder interface.
// It creates new instances of StaticResolver for the gRPC client.
type StaticResolverBuilder struct{}

// Build creates a new resolver instance for the given target.
// It parses the address list from the endpoint and initializes the resolver.
//
// Parameters:
//   - target: The resolver target containing the endpoint information.
//   - cc: The client connection that will use this resolver.
//   - opts: Build options for the resolver.
func (b *StaticResolverBuilder) Build(target resolver.Target, cc resolver.ClientConn, opts resolver.BuildOptions) (resolver.Resolver, error) {
	// Parse address list from endpoint.
	endpoint := target.Endpoint()
	addrs := strings.Split(strings.TrimPrefix(endpoint, "static:///"), ",")

	r := &StaticResolver{
		target: target,
		cc:     cc,
		addrs:  addrs,
		done:   make(chan struct{}),
	}

	// Update addresses immediately.
	r.updateAddresses()

	return r, nil
}

// Scheme returns the resolver scheme identifier.
// This is used by gRPC to identify which resolver to use for a given target.
func (b *StaticResolverBuilder) Scheme() string {
	return "static"
}

// updateAddresses updates the resolved address list.
// It performs DNS resolution for domain names and updates the client connection state.
// This method is thread-safe and can be called from multiple goroutines.
func (r *StaticResolver) updateAddresses() {
	r.mu.Lock()
	defer r.mu.Unlock()

	var addresses []resolver.Address
	for _, addr := range r.addrs {
		// Split host and port.
		host, port, err := net.SplitHostPort(addr)
		if err != nil {
			// Use default port if not specified.
			host = addr
			port = "443"
		}

		// Check if host is an IP address.
		if net.ParseIP(host) != nil {
			// Use IP address directly.
			addresses = append(addresses, resolver.Address{
				Addr: net.JoinHostPort(host, port),
			})
			continue
		}

		// Resolve domain name if not an IP.
		ips, err := net.LookupIP(host)
		if err != nil {
			// Use original address if resolution fails.
			addresses = append(addresses, resolver.Address{
				Addr: addr,
			})
			continue
		}

		// Add all resolved IP addresses.
		for _, ip := range ips {
			addresses = append(addresses, resolver.Address{
				Addr: net.JoinHostPort(ip.String(), port),
			})
		}
	}

	// Update connection state with resolved addresses.
	r.cc.UpdateState(resolver.State{
		Addresses: addresses,
	})
}

// ResolveNow triggers immediate address resolution.
// This method is called by gRPC when it needs to refresh the address list.
//
// Parameters:
//   - o: ResolveNow options.
func (r *StaticResolver) ResolveNow(o resolver.ResolveNowOptions) {
	r.updateAddresses()
}

// Close closes the resolver and releases its resources.
// This method is called by gRPC when the client connection is closed.
func (r *StaticResolver) Close() {
	close(r.done)
}

func init() {
	// Register static resolver with gRPC.
	resolver.Register(&StaticResolverBuilder{})
}