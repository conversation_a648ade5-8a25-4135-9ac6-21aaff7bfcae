// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : proxy_status_monitor.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package client

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
)

// ProxyStatusInfo is an internal data structure for proxy status information.
type ProxyStatusInfo struct {
	Name  string `json:"name"`  // Proxy name.
	Type  string `json:"type"`  // Proxy type (iwan/srpxy).
	State int    `json:"state"` // Proxy state (0=down, 1=up).
}

// ProxyStatusMonitor is responsible for collecting, detecting changes in, and reporting proxy status.
type ProxyStatusMonitor struct {
	logger         *logger.Logger               // Logger instance.
	client         *Client                      // gRPC client reference.
	previousStatus map[string]*ProxyStatusInfo  // Previous status cache for change detection and periodic reporting.
	reportClient   pb.DeviceReportServiceClient // Device report service client.
}

// NewProxyStatusMonitor creates a new instance of the proxy status monitor.
//
// Parameters:
//   - log: The logger instance.
//   - client: A reference to the gRPC client.
//   - conn: The gRPC connection for creating the report client.
func NewProxyStatusMonitor(log *logger.Logger, client *Client, conn *grpc.ClientConn) *ProxyStatusMonitor {
	return &ProxyStatusMonitor{
		logger:         log.WithModule("proxy-status-monitor"),
		client:         client,
		previousStatus: make(map[string]*ProxyStatusInfo),
		reportClient:   pb.NewDeviceReportServiceClient(conn),
	}
}

// collectProxyStatus collects the current proxy status information.
// It executes the `floweye nat listproxy json=1` command and parses the results,
// returning a map of the current proxy statuses or an error if collection fails.
func (m *ProxyStatusMonitor) collectProxyStatus() (map[string]*ProxyStatusInfo, error) {
	// Execute floweye command to get proxy status.
	output, err := utils.ExecuteCommand(m.logger, 10, "floweye", "nat", "listproxy", "json=1")
	if err != nil {
		return nil, fmt.Errorf("failed to execute floweye command: %w", err)
	}

	// Parse JSON output using the unified floweye JSON parser.
	rawProxies, err := utils.ParseFloweyeJSON(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse floweye JSON output: %w", err)
	}

	// Extract iwan and srpxy proxy status.
	currentStatus := make(map[string]*ProxyStatusInfo)
	for _, proxy := range rawProxies {
		// Get proxy type.
		proxyType, ok := proxy["type"].(string)
		if !ok {
			continue
		}

		// Only process iwan and srpxy types.
		if proxyType != "iwan" && proxyType != "srpxy" {
			continue
		}

		// Get proxy name.
		name, ok := proxy["name"].(string)
		if !ok || name == "" {
			continue
		}

		// Get proxy state.
		state, ok := proxy["state"].(float64)
		if !ok {
			continue
		}

		currentStatus[name] = &ProxyStatusInfo{
			Name:  name,
			Type:  proxyType,
			State: int(state),
		}
	}

	m.logger.Debug("collected proxy status",
		zap.Int("total_count", len(currentStatus)),
		zap.Any("status", currentStatus))

	return currentStatus, nil
}

// detectChanges detects status changes, including new proxies and state changes,
// and returns a list of proxies whose status has changed.
//
// Parameters:
//   - currentStatus: The current status mapping.
func (m *ProxyStatusMonitor) detectChanges(currentStatus map[string]*ProxyStatusInfo) []*ProxyStatusInfo {
	var changes []*ProxyStatusInfo

	// Check for new proxies and state changes.
	for name, current := range currentStatus {
		previous, exists := m.previousStatus[name]

		if !exists {
			// New proxy detected.
			changes = append(changes, current)
			m.logger.Info("detected new proxy",
				zap.String("name", name),
				zap.String("type", current.Type),
				zap.Int("state", current.State))
		} else if previous.State != current.State {
			// State change detected.
			changes = append(changes, current)
			m.logger.Info("detected proxy state change",
				zap.String("name", name),
				zap.String("type", current.Type),
				zap.Int("previous_state", previous.State),
				zap.Int("current_state", current.State))
		}
	}

	return changes
}

// sendReport sends a device report request.
//
// Parameters:
//   - ctx: The context for the request.
//   - triggerType: The trigger type for the report (event/periodic).
//   - statusList: The list of proxy statuses to report.
func (m *ProxyStatusMonitor) sendReport(ctx context.Context, triggerType pb.ReportTriggerType, statusList []*ProxyStatusInfo) error {
	if m.reportClient == nil {
		return fmt.Errorf("report client not initialized")
	}

	if len(statusList) == 0 {
		m.logger.Debug("no status to report, skipping")
		return nil
	}

	// Convert to protobuf format.
	var pbStatusList []*pb.ProxyStatusInfo
	for _, status := range statusList {
		pbType := pb.ProxyType_PROXY_TYPE_NONE
		if status.Type == "iwan" {
			pbType = pb.ProxyType_PROXY_TYPE_IWAN
		} else if status.Type == "srpxy" {
			pbType = pb.ProxyType_PROXY_TYPE_SRPXY
		}

		pbState := pb.ProxyState_PROXY_STATE_DOWN
		if status.State == 1 {
			pbState = pb.ProxyState_PROXY_STATE_UP
		}

		pbStatusList = append(pbStatusList, &pb.ProxyStatusInfo{
			Name:  status.Name,
			Type:  pbType,
			State: pbState,
		})
	}

	// Build report request.
	proxyStatusReport := &pb.ProxyStatusReport{
		TriggerType: triggerType,
		ProxyStatus: pbStatusList,
	}

	deviceReport := &pb.DeviceReport{
		ReportType: pb.ReportType_REPORT_TYPE_PROXY_STATUS,
		Payload: &pb.DeviceReport_ProxyStatusReport{
			ProxyStatusReport: proxyStatusReport,
		},
	}

	request := &pb.DeviceReportRequest{
		CustomerId:      int32(m.client.cfg.Client.CustomerID),
		ClientId:        int32(m.client.cfg.Client.ClientID),
		Uuid:            fmt.Sprintf("proxy-status-%d", time.Now().UnixNano()),
		DeviceReports:   []*pb.DeviceReport{deviceReport},
		ReportTimestamp: time.Now().Unix(),
	}

	// Send report request.
	response, err := m.reportClient.SendDeviceReport(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to send device report: %w", err)
	}

	m.logger.Info("proxy status report sent successfully",
		zap.String("trigger_type", triggerType.String()),
		zap.Int("status_count", len(statusList)),
		zap.String("uuid", request.Uuid),
		zap.Int32("err_code", response.ErrCode))

	if response.ErrCode != 0 {
		return fmt.Errorf("server returned error code: %d", response.ErrCode)
	}

	return nil
}

// checkAndReportChanges checks for status changes and performs an event-triggered report.
// This is the status check logic executed every 5 seconds.
//
// Parameters:
//   - ctx: The context for the request.
func (m *ProxyStatusMonitor) checkAndReportChanges(ctx context.Context) error {
	// Collect current status.
	currentStatus, err := m.collectProxyStatus()
	if err != nil {
		m.logger.Error("failed to collect proxy status", zap.Error(err))
		return err
	}

	// Detect changes.
	changes := m.detectChanges(currentStatus)

	// If there are changes, perform an event-triggered report.
	if len(changes) > 0 {
		if err := m.sendReport(ctx, pb.ReportTriggerType_REPORT_TRIGGER_EVENT, changes); err != nil {
			m.logger.Error("failed to send event report", zap.Error(err))
			// Don't return error, continue to update cache.
		}
	}

	// Update status cache.
	m.previousStatus = currentStatus

	return nil
}

// sendPeriodicReport sends a periodic full report.
// This is the full status reporting logic executed every 60 seconds.
//
// Parameters:
//   - ctx: The context for the request.
func (m *ProxyStatusMonitor) sendPeriodicReport(ctx context.Context) error {
	// Reuse previous check status (previousStatus is actually the latest current status).
	if len(m.previousStatus) == 0 {
		m.logger.Warn("no previous status available for periodic report, skipping")
		return nil
	}

	// Convert to list format.
	var statusList []*ProxyStatusInfo
	for _, status := range m.previousStatus {
		statusList = append(statusList, status)
	}

	// Send periodic report.
	if err := m.sendReport(ctx, pb.ReportTriggerType_REPORT_TRIGGER_PERIODIC, statusList); err != nil {
		m.logger.Error("failed to send periodic report", zap.Error(err))
		return err
	}

	m.logger.Info("periodic proxy status report completed",
		zap.Int("total_proxies", len(statusList)))

	return nil
}