// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : instance.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package client

import (
	"agent/internal/client/task"
	"sync"
)

var (
	instance     *Client
	instanceLock sync.Mutex
)

// SetInstance sets the global client instance.
// This function is used to make the client instance available to other components.
//
// Parameters:
//   - client: The client instance to set as the global instance.
func SetInstance(client *Client) {
	instanceLock.Lock()
	defer instanceLock.Unlock()
	instance = client
}

// GetInstance gets the global client instance.
// This function is used by other components to access the client instance.
// It returns the global client instance, or nil if it has not been set.
func GetInstance() *Client {
	instanceLock.Lock()
	defer instanceLock.Unlock()
	return instance
}

// GetTaskManager gets the task manager from the client instance.
// This method provides access to the task manager for task processing.
func (c *Client) GetTaskManager() *task.TaskManager {
	return c.taskManager
}