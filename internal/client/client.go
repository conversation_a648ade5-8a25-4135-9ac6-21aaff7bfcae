// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : client.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package client

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"agent/internal/client/task"
	"agent/internal/config"
	"agent/internal/logger"
	pb "agent/internal/pb"

	"github.com/google/uuid"
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/logging"
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/retry"
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/timeout"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
)

// Client represents a gRPC client with logging and heartbeat functionality.
// It manages the connection, heartbeat, and configuration synchronization.
type Client struct {
	cfg                    *config.Config             // Application configuration.
	logger                 *logger.Logger             // Logger instance for client operations.
	conn                   *grpc.ClientConn           // gRPC client connection.
	heartbeat              pb.HeartbeatServiceClient  // Heartbeat service client.
	configSync             pb.ConfigSyncServiceClient // Configuration synchronization client.
	stopChan               chan struct{}              // Channel for stopping background operations.
	interceptorLogger      *zap.Logger                // Logger for interceptors.
	taskMu                 sync.Mutex                 // Mutex for task management.
	hasTaskInProgress      bool                       // Flag indicating if a task is in progress.
	taskManager            *task.TaskManager          // Task manager for processing tasks.
	fullSyncInProgress     bool                       // Flag indicating if a full sync is in progress.
	fullSyncMu             sync.Mutex                 // Mutex for full sync management.
	hasTriggeredFullSync   bool                       // Flag indicating if a full sync has been successfully completed.
	hasTriggeredFullSyncMu sync.Mutex                 // Mutex for hasTriggeredFullSync flag.
	lastConnState          connectivity.State         // Last known connection state.
	proxyStatusMonitor     *ProxyStatusMonitor        // Proxy status monitor for reporting.
}

// contextKey is a custom type for context keys to avoid collisions.
type contextKey string

const (
	clientIDKey   contextKey = "client_id"
	customerIDKey contextKey = "customer_id"
)

// New creates and initializes a new gRPC client instance.
// It establishes the connection, starts the heartbeat routine, and returns the initialized client or an error if initialization fails.
//
// Parameters:
//   - cfg: The application configuration.
//   - log: The logger instance for client operations.
func New(cfg *config.Config, log *logger.Logger) (*Client, error) {
	// Create a logger for the gRPC client.
	clientLogger := log.WithModule("grpc-client")

	// Create a dedicated logger for gRPC interceptors.
	// Use WithModule method to create a new logger instance that inherits all output configurations from the main logger.
	interceptorLogger := log.WithModule("grpc-interceptor").Logger

	taskManager := task.InitializeTaskManager(log)
	if taskManager == nil {
		return nil, fmt.Errorf("failed to create task manager")
	}

	// Create client instance.
	client := &Client{
		cfg:               cfg,
		logger:            clientLogger,
		stopChan:          make(chan struct{}),
		interceptorLogger: interceptorLogger,
		taskManager:       taskManager,
		lastConnState:     connectivity.Idle, // Initial state
	}

	// Initialize connection.
	if err := client.connect(); err != nil {
		return nil, fmt.Errorf("failed to connect: %w", err)
	}

	// Start background tasks.
	go client.startBackgroundTasks()

	return client, nil
}

// createTransportCredentials creates transport credentials based on the TLS configuration.
// It returns insecure credentials if TLS is disabled, otherwise it loads TLS credentials from files.
func (c *Client) createTransportCredentials() (credentials.TransportCredentials, error) {
	if !c.cfg.Comms.TLS.Enabled {
		c.logger.Info("TLS disabled, using insecure connection")
		return insecure.NewCredentials(), nil
	}

	c.logger.Info("TLS enabled, loading TLS credentials",
		zap.String("cert_dir", c.cfg.Comms.TLS.CertDir),
		zap.String("cert_file", c.cfg.Comms.TLS.CertFile),
		zap.String("key_file", c.cfg.Comms.TLS.KeyFile),
		zap.Bool("skip_verify", c.cfg.Comms.TLS.SkipVerify),
		zap.Bool("auto_generate", c.cfg.Comms.TLS.AutoGenerate))

	creds, err := loadTLSCredentials(&c.cfg.Comms.TLS)
	if err != nil {
		return nil, fmt.Errorf("failed to load TLS credentials: %w", err)
	}

	c.logger.Info("TLS credentials loaded successfully")
	return creds, nil
}

// connect establishes a connection to the gRPC server.
// It sets up logging and retry interceptors and attempts to connect to the configured servers.
func (c *Client) connect() error {
	// Create logging interceptor options.
	logOpts := []logging.Option{
		logging.WithLogOnEvents(
			logging.StartCall,
			logging.FinishCall,
			logging.PayloadReceived,
			logging.PayloadSent,
		),
		logging.WithLevels(
			logging.DefaultClientCodeToLevel,
		),
	}

	// Create interceptors.
	zapLogger := NewZapLoggerAdapter(c.interceptorLogger)
	logInterceptor := logging.UnaryClientInterceptor(zapLogger, logOpts...)
	streamLogInterceptor := logging.StreamClientInterceptor(zapLogger, logOpts...)

	// Create unified message printing interceptors.
	messagePrinter := NewGrpcMessagePrinter(c.logger)
	unifiedMessageInterceptor := UnifiedMessageLoggingInterceptor(messagePrinter)
	unifiedStreamInterceptor := UnifiedMessageLoggingStreamInterceptor(messagePrinter)

	// Create retry interceptor options.
	retryOpts := []retry.CallOption{
		retry.WithMax(3),
		retry.WithBackoff(retry.BackoffExponential(100 * time.Millisecond)),
		retry.WithCodes(codes.Unavailable, codes.DeadlineExceeded),
	}
	retryInterceptor := retry.UnaryClientInterceptor(retryOpts...)

	// Create timeout interceptor.
	timeoutInterceptor := timeout.UnaryClientInterceptor(3 * time.Second)

	// Build server address list.
	var addrs []string
	for _, addr := range c.cfg.Comms.Addrs {
		// Ensure address format is correct.
		if !strings.Contains(addr, ":") {
			addr = addr + ":443" // Use default port.
		}
		addrs = append(addrs, addr)
	}

	// Build target address using static resolver.
	target := "static:///" + strings.Join(addrs, ",")

	// Create service configuration.
	serviceConfig := `{
		"loadBalancingPolicy": "round_robin",
		"healthCheckConfig": {
			"serviceName": "heartbeat.HeartbeatService"
		},
		"methodConfig": [{
			"name": [{"service": "heartbeat.HeartbeatService"}],
			"waitForReady": true,
			"retryPolicy": {
				"maxAttempts": 3,
				"initialBackoff": "0.1s",
				"maxBackoff": "1s",
				"backoffMultiplier": 2.0,
				"retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]
			}
		}]
	}`

	// Create transport credentials.
	creds, err := c.createTransportCredentials()
	if err != nil {
		c.logger.Error("failed to create transport credentials",
			zap.String("target", target),
			zap.Error(err))
		return fmt.Errorf("failed to create transport credentials: %w", err)
	}

	// Create dial options.
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(creds),
		grpc.WithChainUnaryInterceptor(
			logInterceptor,
			unifiedMessageInterceptor, // Unified message printing interceptor.
			retryInterceptor,
			timeoutInterceptor,
		),
		grpc.WithChainStreamInterceptor(
			streamLogInterceptor,
			unifiedStreamInterceptor, // Unified stream message printing interceptor.
		),
		grpc.WithDefaultServiceConfig(serviceConfig),
	}

	// Create a new client connection.
	conn, err := grpc.NewClient(target, opts...)
	if err != nil {
		c.logger.Error("failed to create client",
			zap.String("target", target),
			zap.Error(err))
		return fmt.Errorf("failed to create client: %w", err)
	}

	// Set up clients.
	c.conn = conn
	c.heartbeat = pb.NewHeartbeatServiceClient(conn)
	c.configSync = pb.NewConfigSyncServiceClient(conn)
	// Initialize proxy status monitor after connection is established.
	c.proxyStatusMonitor = NewProxyStatusMonitor(c.logger, c, conn)

	c.logger.Info("successfully created client",
		zap.String("resolver", "static"),
		zap.String("target", target),
		zap.Strings("all_targets", addrs),
		zap.Bool("tls_enabled", c.cfg.Comms.TLS.Enabled))

	return nil
}

// startBackgroundTasks starts background tasks including connection state monitoring and heartbeat.
// This function combines the functionality of monitorConnectionState and startHeartbeat.
func (c *Client) startBackgroundTasks() {
	// Initial state.
	c.lastConnState = c.conn.GetState()

	// Create heartbeat timer, send heartbeat every 10 seconds.
	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer heartbeatTicker.Stop()

	// Create proxy status monitoring timer.
	// Status check: every 5 seconds (each tick).
	// Periodic report: every 60 seconds (cnt % 12 == 0).
	proxyStatusTicker := time.NewTicker(5 * time.Second)
	defer proxyStatusTicker.Stop()

	var proxyStatusCount int

	// Send initial heartbeat, but don't check configuration.
	c.sendHeartbeatWithConfig(false)

	// Create context for controlling state monitoring.
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle heartbeat and proxy status monitoring in a separate goroutine.
	go func() {
		for {
			select {
			case <-c.stopChan:
				cancel() // Stop state monitoring.
				c.logger.Info("Background tasks goroutine stopped")
				return
			case <-heartbeatTicker.C:
				// Get the latest hasTriggeredFullSync value from struct field.
				c.hasTriggeredFullSyncMu.Lock()
				hasTriggeredFullSync := c.hasTriggeredFullSync
				c.hasTriggeredFullSyncMu.Unlock()
				c.sendHeartbeatWithConfig(hasTriggeredFullSync)
			case <-proxyStatusTicker.C:
				proxyStatusCount++

				// Status check: execute every 5 seconds (each ticker trigger).
				if err := c.proxyStatusMonitor.checkAndReportChanges(ctx); err != nil {
					c.logger.Error("proxy status check failed", zap.Error(err))
				}

				// Periodic full report: execute every 60 seconds (every 12 ticker triggers).
				if proxyStatusCount%12 == 0 {
					if err := c.proxyStatusMonitor.sendPeriodicReport(ctx); err != nil {
						c.logger.Error("periodic proxy status report failed", zap.Error(err))
					}
				}
			}
		}
	}()

	// Main loop uses WaitForStateChange to monitor connection state.
	for {
		// Wait for state change.
		if !c.conn.WaitForStateChange(ctx, c.lastConnState) {
			c.logger.Info("Background tasks stopped due to context cancellation")
			return
		}

		// Handle state change.
		currentState := c.conn.GetState()
		if currentState == c.lastConnState {
			continue // State unchanged (theoretically shouldn't happen, but defensive check).
		}

		// Log state change.
		c.logger.Info("connection state changed",
			zap.String("previous_state", c.lastConnState.String()),
			zap.String("current_state", currentState.String()))

		// If state changes from non-Ready to Ready, and not in sync, trigger full sync.
		if currentState == connectivity.Ready &&
			c.lastConnState != connectivity.Ready &&
			!c.fullSyncInProgress {
			c.logger.Info("connection is ready, triggering full sync")
			// Note: hasTriggeredFullSync will be set to true only after successful completion.
			go c.startFullSync()
		}

		// Update last connection state.
		c.lastConnState = currentState
	}
}

// startFullSync starts a full configuration sync process.
// It retries every 25 seconds if no response is received.
// It initializes all processors for full sync at the beginning and cleans up at the end.
func (c *Client) startFullSync() {
	// Set full sync flag.
	c.fullSyncMu.Lock()
	if c.fullSyncInProgress {
		c.fullSyncMu.Unlock()
		return
	}
	c.fullSyncInProgress = true
	c.fullSyncMu.Unlock()

	// Initialize full sync for all processors.
	c.logger.Info("initializing full sync for all processors")
	if err := c.taskManager.StartFullSync(); err != nil {
		c.logger.Error("failed to start full sync for processors", zap.Error(err))
		// Clean up full sync flag.
		c.fullSyncMu.Lock()
		c.fullSyncInProgress = false
		c.fullSyncMu.Unlock()
		return
	}

	// Create a channel to stop retry attempts and track success.
	stopRetryChan := make(chan struct{})
	successChan := make(chan bool, 1)

	// Send the first sync request immediately.
	c.sendFullSyncRequest(stopRetryChan, successChan)

	// Start retry timer.
	ticker := time.NewTicker(25 * time.Second)
	defer func() {
		ticker.Stop()
		// Ensure full sync flag is cleaned up when function returns.
		c.fullSyncMu.Lock()
		c.fullSyncInProgress = false
		c.fullSyncMu.Unlock()
	}()

	for {
		select {
		case <-c.stopChan:
			return
		case success := <-successChan:
			if success {
				// Only call EndFullSync and set hasTriggeredFullSync on success.
				c.logger.Info("ending full sync for all processors")
				c.taskManager.EndFullSync()

				c.hasTriggeredFullSyncMu.Lock()
				c.hasTriggeredFullSync = true
				c.hasTriggeredFullSyncMu.Unlock()
				c.logger.Info("full sync completed successfully")
			}
			return
		case <-stopRetryChan:
			return
		case <-ticker.C:
			// Send full sync request.
			c.sendFullSyncRequest(stopRetryChan, successChan)
		}
	}
}

// sendFullSyncRequest sends a full configuration sync request to the server.
// It stops retrying if a response is received and processes tasks synchronously.
//
// Parameters:
//   - stopRetryChan: Channel to signal stopping retry attempts.
//   - successChan: Channel to signal successful completion of sync and task processing.
func (c *Client) sendFullSyncRequest(stopRetryChan chan struct{}, successChan chan bool) {
	// Create context with client information.
	ctx := context.WithValue(context.Background(), clientIDKey, c.cfg.Client.ClientID)
	ctx = context.WithValue(ctx, customerIDKey, c.cfg.Client.CustomerID)

	// Create full sync request.
	req := &pb.ClientSyncRequest{
		CustomerId: int32(c.cfg.Client.CustomerID),
		ClientId:   int32(c.cfg.Client.ClientID),
		Uuid:       uuid.New().String(),
		SyncType:   pb.SyncType_FULL_SYNC,
	}

	c.logger.Info("sending full config sync request",
		zap.Int32("customer_id", req.CustomerId),
		zap.Int32("client_id", req.ClientId),
		zap.String("uuid", req.Uuid),
		zap.String("sync_type", req.SyncType.String()),
		zap.Any("request", req),
	)

	// Send sync request.
	resp, err := c.configSync.HandleConfigSyncRequest(ctx, req)
	if err != nil {
		c.logger.Error("failed to sync full config",
			zap.Error(err),
			zap.Any("request", req),
		)
		return
	}

	// Consider sync successful if any response is received.
	c.logger.Info("full config sync response received",
		zap.Int32("customer_id", req.CustomerId),
		zap.Int32("client_id", req.ClientId),
		zap.String("uuid", req.Uuid),
		zap.Int("task_count", len(resp.TaskTxs)),
		zap.Any("request", req),
		zap.Any("response", resp),
	)

	// Signal success immediately to stop retry loop.
	successChan <- true

	// Process tasks after signaling success to avoid blocking retry loop.
	if len(resp.TaskTxs) > 0 {
		c.processTasks(resp.TaskTxs, req.Uuid)
	}
}

// sendHeartbeatWithConfig sends a heartbeat request to the server.
//
// Parameters:
//   - checkConfig: Whether to check for new configurations.
func (c *Client) sendHeartbeatWithConfig(checkConfig bool) {
	// Get and log gRPC connection state.
	state := c.conn.GetState()
	c.logger.Info("current connection status",
		zap.String("state", state.String()))

	// Create context with client information.
	ctx := context.WithValue(context.Background(), clientIDKey, c.cfg.Client.ClientID)
	ctx = context.WithValue(ctx, customerIDKey, c.cfg.Client.CustomerID)

	// Check if there's a task in progress or full sync in progress.
	c.taskMu.Lock()
	hasTaskInProgress := c.hasTaskInProgress
	c.taskMu.Unlock()

	c.fullSyncMu.Lock()
	fullSyncInProgress := c.fullSyncInProgress
	c.fullSyncMu.Unlock()

	// Create heartbeat request.
	req := &pb.HeartbeatRequest{
		CustomerId:     int32(c.cfg.Client.CustomerID),
		ClientId:       int32(c.cfg.Client.ClientID),
		CheckNewConfig: checkConfig && !hasTaskInProgress && !fullSyncInProgress, // Only check new config when specified and no task in progress and no full sync in progress.
	}

	c.logger.Info("sending heartbeat request",
		zap.Int32("customer_id", req.CustomerId),
		zap.Int32("client_id", req.ClientId),
		zap.Bool("check_new_config", req.CheckNewConfig),
		zap.Any("request", req),
	)

	// Send heartbeat request.
	resp, err := c.heartbeat.SendHeartbeat(ctx, req)
	if err != nil {
		c.logger.Error("failed to send heartbeat",
			zap.Error(err),
			zap.Any("request", req),
		)
		return
	}

	c.logger.Info("heartbeat response received",
		zap.Int32("customer_id", req.CustomerId),
		zap.Int32("client_id", req.ClientId),
		zap.Bool("has_new_config", resp.HasNewConfig),
		zap.Any("request", req),
		zap.Any("response", resp),
	)

	// If there's new config and no task in progress and no full sync in progress, start a goroutine to sync.
	if resp.HasNewConfig && !hasTaskInProgress && !fullSyncInProgress {
		go c.syncConfig()
	}
}

// syncConfig sends a config sync request to the server.
// It creates a new UUID for the sync request and processes any received tasks.
func (c *Client) syncConfig() {
	// Create context with client information.
	ctx := context.WithValue(context.Background(), clientIDKey, c.cfg.Client.ClientID)
	ctx = context.WithValue(ctx, customerIDKey, c.cfg.Client.CustomerID)

	// Set task execution flag to prevent concurrent task processing.
	c.taskMu.Lock()
	c.hasTaskInProgress = true
	c.taskMu.Unlock()

	// Create sync request.
	req := &pb.ClientSyncRequest{
		CustomerId: int32(c.cfg.Client.CustomerID),
		ClientId:   int32(c.cfg.Client.ClientID),
		Uuid:       uuid.New().String(),
		SyncType:   pb.SyncType_INCREMENTAL_SYNC,
	}

	c.logger.Info("sending config sync request",
		zap.Int32("customer_id", req.CustomerId),
		zap.Int32("client_id", req.ClientId),
		zap.String("uuid", req.Uuid),
		zap.String("sync_type", req.SyncType.String()),
		zap.Any("request", req),
	)

	// Send sync request.
	resp, err := c.configSync.HandleConfigSyncRequest(ctx, req)
	if err != nil {
		c.logger.Error("failed to sync config",
			zap.Error(err),
			zap.Any("request", req),
		)
		return
	}

	c.logger.Info("config sync response received",
		zap.Int32("customer_id", req.CustomerId),
		zap.Int32("client_id", req.ClientId),
		zap.String("uuid", req.Uuid),
		zap.Int("task_count", len(resp.TaskTxs)),
		zap.Any("request", req),
		zap.Any("response", resp),
	)

	// Process tasks if any.
	if len(resp.TaskTxs) > 0 {
		c.processTasks(resp.TaskTxs, req.Uuid)
	}

	// Clear task execution flag.
	c.taskMu.Lock()
	c.hasTaskInProgress = false
	c.taskMu.Unlock()
}

// processTasks processes all received tasks and sends a single acknowledgment
// containing the status of all tasks. It handles the task execution state
// and ensures proper synchronization with the server.
//
// Parameters:
//   - tasks: A list of task transactions to process.
//   - uuid: The UUID of the sync request.
func (c *Client) processTasks(tasks []*pb.TaskTx, uuid string) {
	// Create acknowledgment message.
	ack := &pb.ClientSyncAck{
		CustomerId:   int32(c.cfg.Client.CustomerID),
		ClientId:     int32(c.cfg.Client.ClientID),
		Uuid:         uuid,
		TaskTxStatus: make([]*pb.TaskTxStatus, 0, len(tasks)),
	}

	// Use task manager to process all tasks.
	ack.TaskTxStatus = c.taskManager.ProcessTasks(context.Background(), tasks)

	// Send acknowledgment message.
	c.sendSyncAck(ack)
}

// sendSyncAck sends a single acknowledgment containing the status of all processed tasks
// to the server. It handles the communication and error reporting for the acknowledgment.
//
// Parameters:
//   - ack: The acknowledgment message containing task statuses.
func (c *Client) sendSyncAck(ack *pb.ClientSyncAck) {
	// Create context with client identification.
	ctx := context.WithValue(context.Background(), clientIDKey, c.cfg.Client.ClientID)
	ctx = context.WithValue(ctx, customerIDKey, c.cfg.Client.CustomerID)

	c.logger.Info("sending sync acknowledgment",
		zap.String("uuid", ack.Uuid),
		zap.Int("task_count", len(ack.TaskTxStatus)),
		zap.Any("ack", ack),
	)

	// Send the acknowledgment to the server.
	resp, err := c.configSync.HandleClientSyncAck(ctx, ack)
	if err != nil {
		c.logger.Error("failed to send sync acknowledgment",
			zap.Error(err),
			zap.String("uuid", ack.Uuid),
		)
		return
	}

	if resp.Code != 0 {
		c.logger.Error("server returned error code",
			zap.Int32("error_code", resp.Code),
			zap.String("uuid", ack.Uuid),
		)
		return
	}

	c.logger.Info("sync acknowledgment sent successfully",
		zap.String("uuid", ack.Uuid),
		zap.Int("task_count", len(ack.TaskTxStatus)),
	)
}

// Close gracefully shuts down the client.
// It stops the heartbeat routine and closes the gRPC connection, returning any error from the connection closing.
func (c *Client) Close() error {
	close(c.stopChan)
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}