/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      tls.go
 *
 * DESCRIPTION :    TLS certificate management for gRPC client connections.
 *                  Handles automatic certificate generation and TLS credential creation.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        21/06/2025  create
 ******************************************************************************/

package client

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"math/big"
	"net"
	"os"
	"path/filepath"
	"time"

	"agent/internal/config"

	"google.golang.org/grpc/credentials"
)

/*****************************************************************************
 * NAME: ensureCertDir
 *
 * DESCRIPTION:
 *     Ensures the certificate directory exists with appropriate permissions.
 *     Creates the directory if it doesn't exist.
 *
 * PARAMETERS:
 *     certDir - Path to the certificate directory
 *
 * RETURNS:
 *     error - Error if directory creation fails
 *****************************************************************************/
func ensureCertDir(certDir string) error {
	if _, err := os.Stat(certDir); os.IsNotExist(err) {
		if err := os.MkdirAll(certDir, 0755); err != nil {
			return fmt.Errorf("failed to create certificate directory: %w", err)
		}
	}
	return nil
}

/*****************************************************************************
 * NAME: generateSelfSignedCert
 *
 * DESCRIPTION:
 *     Generates a self-signed certificate and private key for TLS communication.
 *     The certificate is valid for 1000 years and includes localhost and loopback addresses.
 *
 * PARAMETERS:
 *     certPath - Path where the certificate file will be saved
 *     keyPath  - Path where the private key file will be saved
 *
 * RETURNS:
 *     error - Error if certificate generation fails
 *****************************************************************************/
func generateSelfSignedCert(certPath, keyPath string) error {
	// 生成 RSA 私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return fmt.Errorf("failed to generate private key: %w", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"UNISASE Corporation"},
			Country:       []string{"CN"},
			Province:      []string{""},
			Locality:      []string{""},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
		},
		NotBefore:    time.Now(),
		NotAfter:     time.Now().AddDate(1000, 0, 0), // 1000 年有效期
		KeyUsage:     x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:  []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		IPAddresses:  []net.IP{net.IPv4(127, 0, 0, 1), net.IPv6loopback},
		DNSNames:     []string{"localhost"},
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return fmt.Errorf("failed to create certificate: %w", err)
	}

	// 保存证书文件
	certOut, err := os.Create(certPath)
	if err != nil {
		return fmt.Errorf("failed to create certificate file: %w", err)
	}
	defer certOut.Close()

	if err := pem.Encode(certOut, &pem.Block{Type: "CERTIFICATE", Bytes: certDER}); err != nil {
		return fmt.Errorf("failed to write certificate: %w", err)
	}

	// 设置证书文件权限
	if err := os.Chmod(certPath, 0644); err != nil {
		return fmt.Errorf("failed to set certificate file permissions: %w", err)
	}

	// 保存私钥文件
	keyOut, err := os.Create(keyPath)
	if err != nil {
		return fmt.Errorf("failed to create private key file: %w", err)
	}
	defer keyOut.Close()

	privateKeyDER, err := x509.MarshalPKCS8PrivateKey(privateKey)
	if err != nil {
		return fmt.Errorf("failed to marshal private key: %w", err)
	}

	if err := pem.Encode(keyOut, &pem.Block{Type: "PRIVATE KEY", Bytes: privateKeyDER}); err != nil {
		return fmt.Errorf("failed to write private key: %w", err)
	}

	// 设置私钥文件权限（仅所有者可读写）
	if err := os.Chmod(keyPath, 0600); err != nil {
		return fmt.Errorf("failed to set private key file permissions: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: loadTLSCredentials
 *
 * DESCRIPTION:
 *     Loads TLS credentials from certificate files or generates them if needed.
 *     Supports automatic certificate generation and insecure skip verify option.
 *
 * PARAMETERS:
 *     tlsConfig - TLS configuration containing certificate paths and options
 *
 * RETURNS:
 *     credentials.TransportCredentials - gRPC transport credentials for TLS
 *     error                           - Error if credential loading fails
 *****************************************************************************/
func loadTLSCredentials(tlsConfig *config.TLSConfig) (credentials.TransportCredentials, error) {
	// 确保证书目录存在
	if err := ensureCertDir(tlsConfig.CertDir); err != nil {
		return nil, err
	}

	certPath := filepath.Join(tlsConfig.CertDir, tlsConfig.CertFile)
	keyPath := filepath.Join(tlsConfig.CertDir, tlsConfig.KeyFile)

	// 检查证书文件是否存在
	if _, err := os.Stat(certPath); os.IsNotExist(err) {
		if tlsConfig.AutoGenerate {
			if err := generateSelfSignedCert(certPath, keyPath); err != nil {
				return nil, fmt.Errorf("failed to generate self-signed certificate: %w", err)
			}
		} else {
			return nil, fmt.Errorf("certificate file not found: %s", certPath)
		}
	}

	// 检查私钥文件是否存在
	if _, err := os.Stat(keyPath); os.IsNotExist(err) {
		if tlsConfig.AutoGenerate {
			if err := generateSelfSignedCert(certPath, keyPath); err != nil {
				return nil, fmt.Errorf("failed to generate self-signed certificate: %w", err)
			}
		} else {
			return nil, fmt.Errorf("private key file not found: %s", keyPath)
		}
	}

	// 加载证书
	cert, err := tls.LoadX509KeyPair(certPath, keyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load certificate: %w", err)
	}

	// 创建 TLS 配置
	tlsConf := &tls.Config{
		Certificates:       []tls.Certificate{cert},
		InsecureSkipVerify: tlsConfig.SkipVerify,
	}

	return credentials.NewTLS(tlsConf), nil
}
