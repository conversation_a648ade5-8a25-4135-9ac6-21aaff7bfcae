/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      proxy_status_monitor_test.go
 *
 * DESCRIPTION :    线路状态监控模块的单元测试
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025/01/XX  create
 ******************************************************************************/

package client

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"encoding/json"
	"testing"
)

// MockExecutor 模拟命令执行器，用于测试
type MockExecutor struct {
	output string
	err    error
}

func (m *MockExecutor) ExecuteCommand(logger *logger.Logger, timeout int, command string, args ...string) (string, error) {
	return m.output, m.err
}

// TestProxyStatusMonitor_parseFloweye tests floweye JSON parsing
func TestProxyStatusMonitor_parseFloweye(t *testing.T) {
	// Test the JSON parsing logic with actual floweye output format
	floweye_output := `{"id":1,"name":"wan","type":"proxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth0","ip":"*************","gw":"***********","mask":"*************","vlan":"0/0"},{"id":2,"name":"wan1","type":"proxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth1","ip":"*************","gw":"***********","mask":"*************","vlan":"0/0"},{"id":4,"name":"iwan1","type":"iwan","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":0,"if":"wan","ip":"0.0.0.0","gw":"0.0.0.0","mask":"*************","vlan":"0/0"},{"id":5,"name":"sr1","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"}`

	// Test parsing logic
	jsonArray := "[" + floweye_output + "]"
	var rawProxies []map[string]interface{}
	err := json.Unmarshal([]byte(jsonArray), &rawProxies)
	if err != nil {
		t.Fatalf("Failed to parse floweye JSON output: %v", err)
	}

	// Verify parsing results
	if len(rawProxies) != 4 {
		t.Errorf("Expected 4 proxy entries, got %d", len(rawProxies))
	}

	// Count iwan and srpxy types
	iwanCount := 0
	srpxyCount := 0
	for _, proxy := range rawProxies {
		if proxyType, ok := proxy["type"].(string); ok {
			if proxyType == "iwan" {
				iwanCount++
			} else if proxyType == "srpxy" {
				srpxyCount++
			}
		}
	}

	if iwanCount != 1 {
		t.Errorf("Expected 1 iwan proxy, got %d", iwanCount)
	}
	if srpxyCount != 1 {
		t.Errorf("Expected 1 srpxy proxy, got %d", srpxyCount)
	}
}

// TestProxyStatusMonitor_detectChanges tests change detection functionality
func TestProxyStatusMonitor_detectChanges(t *testing.T) {
	// Create test logger
	log, err := logger.NewLogger(logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
	})
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Create monitor
	monitor := &ProxyStatusMonitor{
		logger:         log.WithModule("proxy-status-monitor"),
		client:         nil,
		previousStatus: make(map[string]*ProxyStatusInfo),
		reportClient:   nil,
	}

	// 设置初始状态
	monitor.previousStatus = map[string]*ProxyStatusInfo{
		"iwan1": {Name: "iwan1", Type: "iwan", State: 0},
		"sr1":   {Name: "sr1", Type: "srpxy", State: 1},
	}

	// 创建当前状态（包含新增和状态变更）
	currentStatus := map[string]*ProxyStatusInfo{
		"iwan1": {Name: "iwan1", Type: "iwan", State: 1}, // 状态变更：0 -> 1
		"sr1":   {Name: "sr1", Type: "srpxy", State: 1},  // 无变更
		"iwan2": {Name: "iwan2", Type: "iwan", State: 0}, // 新增线路
	}

	// 检测变更
	changes := monitor.detectChanges(currentStatus)

	// 验证变更数量
	if len(changes) != 2 {
		t.Errorf("Expected 2 changes, got %d", len(changes))
	}

	// 验证变更内容
	changeMap := make(map[string]*ProxyStatusInfo)
	for _, change := range changes {
		changeMap[change.Name] = change
	}

	// 验证 iwan1 状态变更
	if iwan1, exists := changeMap["iwan1"]; exists {
		if iwan1.State != 1 {
			t.Errorf("Expected iwan1 new state to be 1, got %d", iwan1.State)
		}
	} else {
		t.Error("Expected iwan1 state change to be detected")
	}

	// 验证 iwan2 新增
	if iwan2, exists := changeMap["iwan2"]; exists {
		if iwan2.State != 0 {
			t.Errorf("Expected iwan2 state to be 0, got %d", iwan2.State)
		}
	} else {
		t.Error("Expected iwan2 new proxy to be detected")
	}

	// 验证 sr1 无变更
	if _, exists := changeMap["sr1"]; exists {
		t.Error("Expected sr1 to have no changes")
	}
}

// TestProxyStatusMonitor_convertToProtobuf tests protobuf conversion
func TestProxyStatusMonitor_convertToProtobuf(t *testing.T) {
	// This test doesn't need a monitor instance, just tests the conversion logic

	// 创建测试状态列表
	statusList := []*ProxyStatusInfo{
		{Name: "iwan1", Type: "iwan", State: 1},
		{Name: "sr1", Type: "srpxy", State: 0},
	}

	// 转换为 protobuf 格式（模拟 sendReport 中的转换逻辑）
	var pbStatusList []*pb.ProxyStatusInfo
	for _, status := range statusList {
		pbType := pb.ProxyType_PROXY_TYPE_NONE
		if status.Type == "iwan" {
			pbType = pb.ProxyType_PROXY_TYPE_IWAN
		} else if status.Type == "srpxy" {
			pbType = pb.ProxyType_PROXY_TYPE_SRPXY
		}

		pbState := pb.ProxyState_PROXY_STATE_DOWN
		if status.State == 1 {
			pbState = pb.ProxyState_PROXY_STATE_UP
		}

		pbStatusList = append(pbStatusList, &pb.ProxyStatusInfo{
			Name:  status.Name,
			Type:  pbType,
			State: pbState,
		})
	}

	// 验证转换结果
	if len(pbStatusList) != 2 {
		t.Errorf("Expected 2 protobuf status entries, got %d", len(pbStatusList))
	}

	// 验证 iwan1 转换
	iwan1 := pbStatusList[0]
	if iwan1.Name != "iwan1" {
		t.Errorf("Expected name 'iwan1', got '%s'", iwan1.Name)
	}
	if iwan1.Type != pb.ProxyType_PROXY_TYPE_IWAN {
		t.Errorf("Expected type PROXY_TYPE_IWAN, got %v", iwan1.Type)
	}
	if iwan1.State != pb.ProxyState_PROXY_STATE_UP {
		t.Errorf("Expected state PROXY_STATE_UP, got %v", iwan1.State)
	}

	// 验证 sr1 转换
	sr1 := pbStatusList[1]
	if sr1.Name != "sr1" {
		t.Errorf("Expected name 'sr1', got '%s'", sr1.Name)
	}
	if sr1.Type != pb.ProxyType_PROXY_TYPE_SRPXY {
		t.Errorf("Expected type PROXY_TYPE_SRPXY, got %v", sr1.Type)
	}
	if sr1.State != pb.ProxyState_PROXY_STATE_DOWN {
		t.Errorf("Expected state PROXY_STATE_DOWN, got %v", sr1.State)
	}
}

// 全局变量用于测试时替换 utils.ExecuteCommand
var executeCommandFunc = func(logger *logger.Logger, timeout int, command string, args ...string) (string, error) {
	// 这里应该调用真实的 utils.ExecuteCommand，但为了测试我们提供一个可替换的版本
	return "", nil
}
