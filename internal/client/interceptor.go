// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : interceptor.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package client

import (
	"agent/internal/logger"
	"context"
	"fmt"

	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/logging"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// zapLoggerAdapter adapts a *zap.Logger to the logging.Logger interface.
type zapLoggerAdapter struct {
	logger *zap.Logger // The underlying zap.Logger instance.
}

// NewZapLoggerAdapter creates a new zap logger adapter.
//
// Parameters:
//   - logger: The *zap.Logger instance to adapt.
func NewZapLoggerAdapter(logger *zap.Logger) logging.Logger {
	return &zapLoggerAdapter{logger: logger}
}

// Log implements the logging.Logger interface by converting logging.Level
// to zapcore.Level and forwarding the log call to the underlying zap logger.
//
// Parameters:
//   - ctx: The context for the log operation.
//   - level: The log level.
//   - msg: The log message.
//   - args: Additional key-value arguments.
func (l *zapLoggerAdapter) Log(ctx context.Context, level logging.Level, msg string, args ...any) {
	zapLevel := zapcore.InfoLevel
	switch level {
	case logging.LevelDebug:
		zapLevel = zapcore.DebugLevel
	case logging.LevelInfo:
		zapLevel = zapcore.InfoLevel
	case logging.LevelWarn:
		zapLevel = zapcore.WarnLevel
	case logging.LevelError:
		zapLevel = zapcore.ErrorLevel
	}

	// Convert args to zap fields.
	fields := make([]zap.Field, 0, len(args)/2)
	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			key, ok := args[i].(string)
			if !ok {
				continue
			}
			fields = append(fields, zap.Any(key, args[i+1]))
		}
	}

	l.logger.Log(zapLevel, msg, fields...)
}

// GrpcMessagePrinter provides a unified way to serialize and print protobuf messages.
type GrpcMessagePrinter struct {
	logger  *logger.Logger           // The logger instance.
	options protojson.MarshalOptions // The protojson marshaling options.
}

// NewGrpcMessagePrinter creates a new instance of GrpcMessagePrinter.
//
// Parameters:
//   - logger: The logger instance.
func NewGrpcMessagePrinter(logger *logger.Logger) *GrpcMessagePrinter {
	return &GrpcMessagePrinter{
		logger: logger,
		options: protojson.MarshalOptions{
			Multiline:       true,  // Use multi-line format for readability.
			Indent:          "  ",  // Indent with 2 spaces.
			AllowPartial:    true,  // Allow partial messages.
			UseProtoNames:   true,  // Use proto field names instead of JSON field names.
			UseEnumNumbers:  false, // Use enum names instead of numbers.
			EmitUnpopulated: true,  // Output unpopulated fields (show default values).
		},
	}
}

// PrintMessage prints the full structured content of a protobuf message.
//
// Parameters:
//   - msg: The protobuf message.
//   - msgType: A description of the message type (e.g., "request", "response").
//   - methodName: The name of the gRPC method.
func (p *GrpcMessagePrinter) PrintMessage(msg proto.Message, msgType, methodName string) {
	if msg == nil {
		p.logger.Debug(fmt.Sprintf("gRPC %s message is nil", msgType),
			zap.String("method", methodName),
			zap.String("message_type", msgType))
		return
	}

	// Marshal to JSON format.
	jsonBytes, err := p.options.Marshal(msg)
	if err != nil {
		p.logger.Error("failed to marshal protobuf message to JSON",
			zap.String("method", methodName),
			zap.String("message_type", msgType),
			zap.String("proto_type", string(msg.ProtoReflect().Descriptor().FullName())),
			zap.Error(err))
		return
	}

	// Log the full message content at Debug level.
	p.logger.Debug(fmt.Sprintf("gRPC %s message", msgType),
		zap.String("method", methodName),
		zap.String("message_type", msgType),
		zap.String("proto_type", string(msg.ProtoReflect().Descriptor().FullName())),
		zap.String("message_json", string(jsonBytes)),
		zap.Int("message_size_bytes", len(jsonBytes)))
}

// UnifiedMessageLoggingInterceptor returns a gRPC unary client interceptor that logs the full content of request and response messages.
//
// Parameters:
//   - printer: The message printer instance.
func UnifiedMessageLoggingInterceptor(printer *GrpcMessagePrinter) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		// Log the request message.
		if reqMsg, ok := req.(proto.Message); ok {
			printer.PrintMessage(reqMsg, "request", method)
		}

		// Call the actual RPC method.
		err := invoker(ctx, method, req, reply, cc, opts...)

		// Log the response message.
		if replyMsg, ok := reply.(proto.Message); ok {
			printer.PrintMessage(replyMsg, "response", method)
		}

		return err
	}
}

// UnifiedMessageLoggingStreamInterceptor returns a gRPC stream client interceptor that logs the content of stream messages.
//
// Parameters:
//   - printer: The message printer instance.
func UnifiedMessageLoggingStreamInterceptor(printer *GrpcMessagePrinter) grpc.StreamClientInterceptor {
	return func(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
		// Create the stream.
		stream, err := streamer(ctx, desc, cc, method, opts...)
		if err != nil {
			return nil, err
		}

		// Wrap the stream to log messages.
		return &loggingClientStream{
			ClientStream: stream,
			printer:      printer,
			method:       method,
		}, nil
	}
}

// loggingClientStream wraps a grpc.ClientStream to log stream messages.
type loggingClientStream struct {
	grpc.ClientStream
	printer *GrpcMessagePrinter // The message printer instance.
	method  string              // The name of the gRPC method.
}

// SendMsg logs the message content before sending it.
func (s *loggingClientStream) SendMsg(m interface{}) error {
	if msg, ok := m.(proto.Message); ok {
		s.printer.PrintMessage(msg, "stream_request", s.method)
	}
	return s.ClientStream.SendMsg(m)
}

// RecvMsg logs the message content after receiving it.
func (s *loggingClientStream) RecvMsg(m interface{}) error {
	err := s.ClientStream.RecvMsg(m)
	if msg, ok := m.(proto.Message); ok {
		s.printer.PrintMessage(msg, "stream_response", s.method)
	}
	return err
}