/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      wangroup_processor.go
 *
 * DESCRIPTION :    WAN Group processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
)

/*****************************************************************************
 * NAME: WanGroupProcessor
 *
 * DESCRIPTION:
 *     Processor for WAN group configuration tasks.
 *     Handles creation, modification, and deletion of WAN groups.
 *     Supports full synchronization and incremental updates.
 *
 * FIELDS:
 *     logger             - Logger instance for logging
 *     localConfigs       - Cache of local configurations for full sync redundant deletion, keyed by WAN group ID
 *     workingConfigs     - Cache of working configurations for all operations, keyed by WAN group ID
 *     fullSyncInProgress - Flag indicating whether full sync is in progress
 *****************************************************************************/
type WanGroupProcessor struct {
	logger *logger.Logger

	// 全量同步专用配置（仅用于冗余删除）
	localConfigs map[int32]*WanGroupConfig // 本地配置缓存，key 为 WAN 群组 ID

	// 操作专用工作配置（用于所有操作）
	workingConfigs map[int32]*WanGroupConfig // 工作配置缓存，key 为 WAN 群组 ID

	fullSyncInProgress bool // 是否正在进行全量同步
}

/*****************************************************************************
 * NAME: NewWanGroupProcessor
 *
 * DESCRIPTION:
 *     Creates a new WAN group processor.
 *     Initializes the processor with a logger and empty configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for logging
 *
 * RETURNS:
 *     *WanGroupProcessor - Pointer to the new WAN group processor
 *****************************************************************************/
func NewWanGroupProcessor(log *logger.Logger) *WanGroupProcessor {
	return &WanGroupProcessor{
		logger:         log.WithModule("wangroup-processor"),
		localConfigs:   make(map[int32]*WanGroupConfig),
		workingConfigs: make(map[int32]*WanGroupConfig),
	}
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type that this processor can handle.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - The task type (TASK_WAN_GROUP)
 *****************************************************************************/
func (p *WanGroupProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_WAN_GROUP
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a WAN group configuration task.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - The task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *WanGroupProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get WAN group task data
	wanGroupTask := task.GetWanGroupTask()
	if wanGroupTask == nil {
		return "WAN group task data is empty", fmt.Errorf("wan group task is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "wan_group", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Int32("id", wanGroupTask.GetId()),
		zap.String("type", wanGroupTask.GetType().String()),
		zap.Int("members_count", len(wanGroupTask.GetMembers())),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		// 对于新增和编辑操作，使用同一个处理函数
		result, err = p.handleConfigChange(ctx, wanGroupTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, wanGroupTask)
	default:
		err = fmt.Errorf("unknown task action: %v", task.TaskAction)
		result = ""
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Gets all local WAN group configurations and caches them.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if synchronization fails
 *****************************************************************************/
func (p *WanGroupProcessor) StartFullSync() error {
	p.logger.Info("starting full sync for WAN group processor")

	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	p.logger.Info("full sync started for WAN group processor")
	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Deletes any local configurations that were not in the server config.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *WanGroupProcessor) EndFullSync() {
	p.logger.Info("Ending full sync for WAN groups")

	// Create a copy of remaining WAN group configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations while keeping fullSyncInProgress = true
	remainingGroups := make(map[int32]*WanGroupConfig)
	for id, config := range p.localConfigs {
		remainingGroups[id] = config
	}

	// 删除本地存在但服务器未下发的配置
	// Keep fullSyncInProgress = true during cleanup so handleDeleteConfig can properly
	// remove items from localConfigs map
	for id, config := range remainingGroups {
		p.logger.Info("Deleting WAN group not in server config",
			zap.Int32("id", id),
			zap.String("name", config.Name))

		// 创建删除任务
		deleteTask := &pb.WanGroupTask{
			Id: id,
		}

		// 删除 WAN 群组
		_, err := p.handleDeleteConfig(context.Background(), deleteTask)
		if err != nil {
			p.logger.Error("Failed to delete WAN group",
				zap.Int32("id", id),
				zap.String("name", config.Name),
				zap.Error(err))
			// 继续处理其他配置，不返回错误
		}
	}

	// Verify that all remaining WAN groups have been cleaned up
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some WAN groups were not cleaned up during full sync",
			zap.Int("remaining_count", len(p.localConfigs)))

		// Log the remaining groups for debugging
		for id, config := range p.localConfigs {
			p.logger.Warn("remaining WAN group after cleanup",
				zap.Int32("id", id),
				zap.String("name", config.Name))
		}
	} else {
		p.logger.Info("all remaining WAN groups cleaned up successfully")
	}

	// Reset state
	p.fullSyncInProgress = false
	p.localConfigs = make(map[int32]*WanGroupConfig)
	p.workingConfigs = make(map[int32]*WanGroupConfig)
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles both creation and editing of WAN group configurations.
 *     Compares with local configurations to determine the actual operation needed.
 *
 * PARAMETERS:
 *     ctx          - Context for the operation
 *     wanGroupTask - The WAN group task containing the configuration
 *     taskAction   - The original task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *WanGroupProcessor) handleConfigChange(ctx context.Context, wanGroupTask *pb.WanGroupTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified WanGroupConfig structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	expectedConfig, err := ConvertWanGroupTaskToConfig(wanGroupTask)
	if err != nil {
		p.logger.Error("failed to convert WAN group task to config",
			zap.String("name", wanGroupTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert WAN group configuration: %v", err), err
	}

	// 验证必填字段
	if expectedConfig.Name == "" {
		return "WAN group name is required", fmt.Errorf("wan group name is required")
	}
	if expectedConfig.ID < 1 || expectedConfig.ID > 128 {
		return "WAN group ID must be between 1 and 128", fmt.Errorf("wan group id must be between 1 and 128")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		id := expectedConfig.ID // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, id)
		}()
	}

	// 获取最新配置用于后续操作
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// 检查 ID 是否已存在
	localConfig, exists := p.workingConfigs[expectedConfig.ID]

	// 根据是否存在决定操作类型
	cmdName := "floweye"
	var cmdArgs []string
	var operationType string

	if !exists {
		// 对象不在全量配置中：执行创建流程
		operationType = "creation"
		cmdArgs = []string{
			"wangroup", "add",
			"id=" + fmt.Sprintf("%d", expectedConfig.ID),
			"name=" + expectedConfig.Name,
			"type=" + expectedConfig.Type,
		}
	} else {
		/*
			// 对象已在全量配置中：检查配置是否一致
			if CompareWanGroupConfig(p.logger, expectedConfig, localConfig) {
				// 配置一致：忽略，无需修改
				p.logger.Info("WAN group configuration is identical, no changes needed",
					zap.Int32("id", expectedConfig.ID),
					zap.String("name", expectedConfig.Name))

				return "WAN group configuration is identical, no changes needed", nil
			}
		*/
		// 配置不一致：更新名称和类型
		operationType = "update"
		cmdArgs = []string{
			"wangroup", "set",
			"id=" + fmt.Sprintf("%d", expectedConfig.ID),
			"name=" + expectedConfig.Name,
			"type=" + expectedConfig.Type,
		}
	}

	// 执行 floweye 命令
	p.logger.Info(fmt.Sprintf("Executing floweye command for WAN group %s", operationType),
		zap.Int32("id", expectedConfig.ID),
		zap.String("name", expectedConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 30, cmdName, cmdArgs...)
	if err != nil {
		p.logger.Error(fmt.Sprintf("Failed to execute floweye command for WAN group %s", operationType),
			zap.Int32("id", expectedConfig.ID),
			zap.String("name", expectedConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to %s WAN group: %v", operationType, err), err
	}

	// 如果输出包含 "EXIST"，表示名称冲突
	if strings.Contains(output, "EXIST") {
		p.logger.Error("WAN group name already exists",
			zap.String("name", expectedConfig.Name),
			zap.String("output", output))
		return fmt.Sprintf("WAN group name '%s' already exists", expectedConfig.Name),
			fmt.Errorf("wan group name already exists")
	}

	// 处理成员变更
	currentMembers := make(map[string]bool)
	if exists {
		// 如果是编辑现有配置，获取当前成员列表
		currentMembers = localConfig.Members
	}

	// 使用转换后的内部结构中的成员列表
	newMembers := expectedConfig.Members

	// 添加新成员
	for proxyName := range newMembers {
		if !currentMembers[proxyName] {
			// 添加成员
			memberCmdArgs := []string{
				"wangroup", "set",
				"id=" + fmt.Sprintf("%d", expectedConfig.ID),
				"proxy=" + proxyName,
				"weight=1", // 默认权重为 1
			}

			memberOutput, memberErr := utils.ExecuteCommand(p.logger, 30, cmdName, memberCmdArgs...)
			if memberErr != nil {
				p.logger.Error("Failed to add member to WAN group",
					zap.Int32("id", expectedConfig.ID),
					zap.String("proxy", proxyName),
					zap.Error(memberErr),
					zap.String("output", memberOutput))
				// 继续处理其他成员，不返回错误
			}
		}
	}

	// 删除不再存在的成员（只在编辑现有配置时执行）
	if exists {
		for proxyName := range currentMembers {
			if !newMembers[proxyName] {
				// 删除成员
				memberCmdArgs := []string{
					"wangroup", "set",
					"id=" + fmt.Sprintf("%d", expectedConfig.ID),
					"proxy=" + proxyName,
					"weight=-1", // 负权重表示删除
				}

				memberOutput, memberErr := utils.ExecuteCommand(p.logger, 30, cmdName, memberCmdArgs...)
				if memberErr != nil {
					p.logger.Error("Failed to remove member from WAN group",
						zap.Int32("id", expectedConfig.ID),
						zap.String("proxy", proxyName),
						zap.Error(memberErr),
						zap.String("output", memberOutput))
					// 继续处理其他成员，不返回错误
				}
			}
		}
	}

	// 再次获取配置以包含新创建/更新的对象
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// 不返回错误，因为主要操作已成功
	}

	// 验证配置是否成功应用
	success, verifyErr := VerifyWanGroupConfig(p.logger, expectedConfig)
	if verifyErr != nil {
		p.logger.Error("Failed to verify WAN group configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify WAN group configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("WAN group configuration verification failed")
		return "WAN group configuration verification failed", fmt.Errorf("verification failed")
	}

	// 根据操作类型返回不同的成功消息
	if exists {
		p.logger.Info("WAN group updated successfully",
			zap.Int32("id", expectedConfig.ID),
			zap.String("name", expectedConfig.Name))
		return "WAN group updated successfully", nil
	} else {
		p.logger.Info("WAN group created successfully",
			zap.Int32("id", expectedConfig.ID),
			zap.String("name", expectedConfig.Name))
		return "WAN group created successfully", nil
	}
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles deletion of a WAN group configuration.
 *     Removes the WAN group if it exists.
 *
 * PARAMETERS:
 *     ctx          - Context for the operation
 *     wanGroupTask - The WAN group task containing the ID to delete
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if deletion fails
 *****************************************************************************/
func (p *WanGroupProcessor) handleDeleteConfig(ctx context.Context, wanGroupTask *pb.WanGroupTask) (string, error) {
	// 验证必填字段
	if wanGroupTask.GetId() < 1 || wanGroupTask.GetId() > 128 {
		return "WAN group ID must be between 1 and 128", fmt.Errorf("wan group id must be between 1 and 128")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		id := wanGroupTask.GetId() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, id)
		}()
	}

	// 获取最新配置用于后续操作
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// 检查 ID 是否存在
	_, exists := p.workingConfigs[wanGroupTask.GetId()]
	if !exists {
		// ID 不存在，无需删除
		p.logger.Info("WAN group ID does not exist, no need to delete",
			zap.Int32("id", wanGroupTask.GetId()))
		return "WAN group does not exist, no need to delete", nil
	}

	// 构建 floweye 命令
	cmdName := "floweye"
	cmdArgs := []string{
		"wangroup", "remove",
		"id=" + fmt.Sprintf("%d", wanGroupTask.GetId()),
	}

	// 执行 floweye 命令
	p.logger.Info("Executing floweye command for WAN group deletion",
		zap.Int32("id", wanGroupTask.GetId()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 30, cmdName, cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("WAN group already does not exist, treating as successful delete",
				zap.Int32("id", wanGroupTask.GetId()))
		} else {
			p.logger.Error("Failed to execute floweye command for WAN group deletion",
				zap.Int32("id", wanGroupTask.GetId()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete WAN group: %v", err), err
		}
	}

	// Skip post-delete verification for improved performance and reliability

	p.logger.Info("WAN group deleted successfully",
		zap.Int32("id", wanGroupTask.GetId()))
	return "WAN group deleted successfully", nil
}

/*****************************************************************************
 * NAME: fetchWanGroupConfigs
 *
 * DESCRIPTION:
 *     Fetches WAN group configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[int32]*WanGroupConfig - WAN group configurations by ID
 *     error                     - Error if fetch fails
 *****************************************************************************/
func (p *WanGroupProcessor) fetchWanGroupConfigs() (map[int32]*WanGroupConfig, error) {
	configs, err := GetAllWanGroups(p.logger)
	if err != nil {
		p.logger.Error("failed to fetch WAN group configurations", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch WAN group configurations: %w", err)
	}
	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local WAN group configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *WanGroupProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local WAN group configurations")

	configs, err := p.fetchWanGroupConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs

	p.logger.Debug("refreshed local WAN group configurations",
		zap.Int("wan_groups", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working WAN group configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *WanGroupProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working WAN group configurations")

	configs, err := p.fetchWanGroupConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs

	p.logger.Debug("refreshed working WAN group configurations",
		zap.Int("wan_groups", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like creation, modification, deletion.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *WanGroupProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}
