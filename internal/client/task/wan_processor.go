/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      wan_processor.go
 *
 * DESCRIPTION :    Processor for CPE_WAN tasks
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: WanProcessor
 *
 * DESCRIPTION:
 *     Processes CPE_WAN type tasks.
 *     Handles WAN configuration operations.
 *
 * FIELDS:
 *     logger             - Logger for WAN processor operations
 *     localConfigs       - Cache of local WAN configurations (used for full sync redundant deletion)
 *     localNameToID      - Map of WAN names to IDs (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     workingNameToID    - Working map of WAN names to IDs
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type WanProcessor struct {
	logger             *logger.Logger        // Logger for WAN processor operations
	localConfigs       map[string]*WanConfig // Cache of local WAN configurations (used for full sync redundant deletion)
	localNameToID      map[string]int        // Map of WAN names to IDs (used for full sync redundant deletion)
	workingConfigs     map[string]*WanConfig // Working cache for operations (can be refreshed during full sync)
	workingNameToID    map[string]int        // Working map of WAN names to IDs
	fullSyncInProgress bool                  // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewWanProcessor
 *
 * DESCRIPTION:
 *     Creates a new WAN processor instance.
 *     Initializes the local configuration cache and working configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *WanProcessor - Initialized WAN processor
 *****************************************************************************/
func NewWanProcessor(log *logger.Logger) *WanProcessor {
	processor := &WanProcessor{
		logger:             log.WithModule("wan-processor"),
		localConfigs:       make(map[string]*WanConfig),
		localNameToID:      make(map[string]int),
		workingConfigs:     make(map[string]*WanConfig),
		workingNameToID:    make(map[string]int),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: fetchWanConfigs
 *
 * DESCRIPTION:
 *     Fetches WAN configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*WanConfig - WAN configurations by name
 *     map[string]int        - WAN name to ID mapping
 *     error                 - Error if fetch fails
 *****************************************************************************/
func (p *WanProcessor) fetchWanConfigs() (map[string]*WanConfig, map[string]int, error) {
	p.logger.Debug("fetching WAN configurations from floweye")

	// Get all WAN configurations using existing logic
	configs, err := GetLocalWanConfigs(p.logger)
	if err != nil {
		p.logger.Error("failed to fetch WAN configurations", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to fetch WAN configurations: %w", err)
	}

	// Build name to ID mapping
	nameToID := make(map[string]int)
	for name, config := range configs {
		nameToID[name] = config.Id
	}

	p.logger.Debug("fetched WAN configurations",
		zap.Int("count", len(configs)),
		zap.Int("name_to_id_count", len(nameToID)))

	return configs, nameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes the local WAN configuration cache.
 *     This is used only for full sync redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *WanProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local WAN configurations")

	configs, nameToID, err := p.fetchWanConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for local cache: %w", err)
	}

	p.localConfigs = configs
	p.localNameToID = nameToID

	p.logger.Debug("refreshed local WAN configurations",
		zap.Int("configs_count", len(p.localConfigs)),
		zap.Int("name_to_id_count", len(p.localNameToID)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working WAN configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *WanProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working WAN configurations")

	configs, nameToID, err := p.fetchWanConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	p.workingConfigs = configs
	p.workingNameToID = nameToID

	p.logger.Debug("refreshed working WAN configurations",
		zap.Int("configs_count", len(p.workingConfigs)),
		zap.Int("name_to_id_count", len(p.workingNameToID)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like creation, update, deletion, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *WanProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (p *WanProcessor) StartFullSync() error {
	p.logger.Info("starting full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	err := p.refreshLocalConfigs()
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up any remaining resources.
 *****************************************************************************/
func (p *WanProcessor) EndFullSync() {
	p.logger.Info("ending full synchronization")

	// Create a copy of remaining WAN configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations while keeping fullSyncInProgress = true
	remainingWans := make(map[string]*WanConfig)
	for wanName, config := range p.localConfigs {
		remainingWans[wanName] = config
	}

	// Process remaining WANs in local configuration
	// These are WANs that were not included in the full sync and should be deleted
	// Keep fullSyncInProgress = true during cleanup so handleDeleteConfig can properly
	// remove items from localConfigs map
	if len(remainingWans) > 0 {
		p.logger.Info("cleaning up remaining WANs",
			zap.Int("count", len(remainingWans)))

		for wanName := range remainingWans {
			// Create a delete task for this WAN
			deleteTask := &pb.WanTask{
				Name: wanName,
			}

			// Delete the WAN
			p.logger.Info("deleting WAN",
				zap.String("name", wanName))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("failed to delete WAN",
					zap.String("name", wanName),
					zap.Error(err))
			}
		}
	}

	// Verify that all remaining WANs have been cleaned up
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some WANs were not cleaned up during full sync",
			zap.Int("remaining_count", len(p.localConfigs)))

		// Log the remaining WANs for debugging
		for wanName := range p.localConfigs {
			p.logger.Warn("remaining WAN after cleanup",
				zap.String("name", wanName))
		}
	} else {
		p.logger.Info("all remaining WANs cleaned up successfully")
	}

	// Now set fullSyncInProgress to false after cleanup is complete
	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*WanConfig)
	p.localNameToID = make(map[string]int)
	p.workingConfigs = make(map[string]*WanConfig)
	p.workingNameToID = make(map[string]int)
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the type of task this processor can handle.
 *
 * RETURNS:
 *     pb.TaskType - Type of task (TASK_WAN)
 *****************************************************************************/
func (p *WanProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_WAN
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a WAN task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *WanProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get WAN task data
	wanTask := task.GetWanTask()
	if wanTask == nil {
		return "WAN task data is empty", fmt.Errorf("wan task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "wan", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.String("ifname", wanTask.Ifname),
		zap.Int32("mtu", wanTask.Mtu),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, wanTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, wanTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles both new and edit WAN configuration tasks.
 *     Configures or updates a WAN interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     wanTask    - WAN task containing configuration details
 *     taskAction - The original task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *WanProcessor) handleConfigChange(ctx context.Context, wanTask *pb.WanTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified WanConfig structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	expectedConfig, err := ConvertWanTaskToConfig(wanTask)
	if err != nil {
		p.logger.Error("failed to convert WAN task to config",
			zap.String("name", wanTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert WAN configuration: %v", err), err
	}

	p.logger.Info("Processing WAN configuration",
		zap.String("name", expectedConfig.Name),
		zap.String("ifname", expectedConfig.Ifname),
		zap.Int("mtu", expectedConfig.Mtu),
		zap.String("type", expectedConfig.Type),
		zap.String("static_ip", expectedConfig.Addr),
		zap.String("gateway", expectedConfig.Gateway),
		zap.String("dns", expectedConfig.Dns),
		zap.String("pppoe_user", expectedConfig.Username),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Validate required fields using converted data
	if expectedConfig.Name == "" {
		return "WAN name is required", fmt.Errorf("wan name is required")
	}

	if expectedConfig.Ifname == "" {
		return "Interface name is required", fmt.Errorf("interface name is required")
	}

	if expectedConfig.Mtu <= 0 {
		return "MTU must be greater than 0", fmt.Errorf("mtu must be greater than 0")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := expectedConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), fmt.Errorf("failed to get configurations: %w", err)
	}

	// Check if WAN exists in working configuration
	workingConfig, exists := p.workingConfigs[expectedConfig.Name]

	// Check if WAN type is changing (only relevant if WAN exists)
	var wanTypeChanging bool = false
	var oldType string

	if exists {
		oldType = workingConfig.Type
		wanTypeChanging = (oldType != expectedConfig.Type) && (oldType != "") && (expectedConfig.Type != "")

		// If WAN type is changing, we need to delete and recreate
		if wanTypeChanging {
			p.logger.Info("WAN type is changing, deleting existing configuration first",
				zap.String("name", expectedConfig.Name),
				zap.String("old_type", oldType),
				zap.String("new_type", expectedConfig.Type))

			// Create a delete task
			deleteTask := &pb.WanTask{
				Name: expectedConfig.Name,
			}

			// Delete the existing WAN
			_, err := p.handleDeleteConfig(ctx, deleteTask)
			if err != nil {
				p.logger.Error("failed to delete existing WAN before type change",
					zap.String("name", expectedConfig.Name),
					zap.Error(err))
				return fmt.Sprintf("Failed to delete existing WAN before type change: %v", err), fmt.Errorf("failed to delete existing WAN before type change: %w", err)
			}

			// Set exists to false since we've deleted the WAN
			exists = false
		}
	}

	/*
		// If WAN exists and configurations match, no need to modify
		if exists && !wanTypeChanging && CompareWanConfig(p.logger, expectedConfig, localConfig) {
			p.logger.Info("WAN configuration already matches, no changes needed",
				zap.String("name", expectedConfig.Name),
				zap.String("type", expectedConfig.Type))

			// Remove from local configs if in full sync mode
			if p.fullSyncInProgress {
				delete(p.localConfigs, expectedConfig.Name)
			}

			return "WAN configuration already matches, no changes needed", nil
		}
	*/

	// Build floweye command arguments using converted data structure
	var cmdArgs []string

	switch expectedConfig.Type {
	case "proxy":
		// Handle static IP configuration
		cmdArgs, err = p.handleStaticIpConfig(expectedConfig, exists)
		if err != nil {
			return fmt.Sprintf("Failed to build static IP configuration: %v", err), err
		}

	case "dhcpwan":
		// Handle DHCP configuration
		cmdArgs, err = p.handleDhcpConfig(expectedConfig, exists)
		if err != nil {
			return fmt.Sprintf("Failed to build DHCP configuration: %v", err), err
		}

	case "pppoe":
		// Handle PPPoE configuration
		cmdArgs, err = p.handlePppoeConfig(expectedConfig, exists)
		if err != nil {
			return fmt.Sprintf("Failed to build PPPoE configuration: %v", err), err
		}

	default:
		return "WAN type is required", fmt.Errorf("wan type is required")
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command for WAN configuration",
		zap.String("name", expectedConfig.Name),
		zap.String("type", expectedConfig.Type),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 30, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for WAN configuration",
			zap.String("name", expectedConfig.Name),
			zap.String("type", expectedConfig.Type),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure WAN: %v", err), fmt.Errorf("failed to configure WAN: %w", err)
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", expectedConfig.Name),
		zap.String("output", output))

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyWanConfig(p.logger, expectedConfig)
	if verifyErr != nil {
		p.logger.Error("failed to verify WAN configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify WAN configuration: %v", verifyErr), fmt.Errorf("failed to verify WAN configuration: %w", verifyErr)
	}

	if !success {
		p.logger.Error("WAN configuration verification failed")
		return "WAN configuration verification failed", fmt.Errorf("verification failed")
	}

	// Refresh working configs after successful configuration to include the new/updated WAN
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error since the main operation was successful
	}

	// Return appropriate success message based on whether WAN existed
	if exists {
		p.logger.Info("WAN configured successfully",
			zap.String("name", expectedConfig.Name),
			zap.String("ifname", expectedConfig.Ifname),
			zap.Int("mtu", expectedConfig.Mtu),
			zap.String("type", expectedConfig.Type))
		return "WAN configuration modified successfully", nil
	} else {
		p.logger.Info("WAN configured successfully",
			zap.String("name", expectedConfig.Name),
			zap.String("ifname", expectedConfig.Ifname),
			zap.Int("mtu", expectedConfig.Mtu),
			zap.String("type", expectedConfig.Type))
		return "WAN configuration created successfully", nil
	}
}

/*****************************************************************************
 * NAME: handleNewConfig
 *
 * DESCRIPTION:
 *     Handles new WAN configuration tasks.
 *     Configures a WAN interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx     - Context for the operation
 *     wanTask - WAN task containing configuration details
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *WanProcessor) handleNewConfig(ctx context.Context, wanTask *pb.WanTask) (string, error) {
	// This method is kept for backward compatibility
	// It now delegates to the unified handleConfigChange method
	// The unified method is used because the Orchestrator doesn't know the current PA system state
	// The agent needs to determine whether to add, modify, keep unchanged, or delete-then-add based on local state
	return p.handleConfigChange(ctx, wanTask, pb.TaskAction_NEW_CONFIG)
}

/*****************************************************************************
 * NAME: handleEditConfig
 *
 * DESCRIPTION:
 *     Handles WAN configuration modification tasks.
 *     Updates a WAN interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx     - Context for the operation
 *     wanTask - WAN task containing configuration changes
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *WanProcessor) handleEditConfig(ctx context.Context, wanTask *pb.WanTask) (string, error) {
	// This method is kept for backward compatibility
	// It now delegates to the unified handleConfigChange method
	// The unified method is used because the Orchestrator doesn't know the current PA system state
	// The agent needs to determine whether to add, modify, keep unchanged, or delete-then-add based on local state
	return p.handleConfigChange(ctx, wanTask, pb.TaskAction_EDIT_CONFIG)
}

/*****************************************************************************
 * NAME: addCommonWanParameters
 *
 * DESCRIPTION:
 *     Adds common WAN parameters to the command arguments.
 *     This helper function reduces code duplication by centralizing the
 *     common parameter handling logic used by different WAN types.
 *     Uses unified WanConfig structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     cmdArgs - Existing command arguments to append to
 *     config  - WAN configuration data
 *
 * RETURNS:
 *     []string - Updated command arguments with common parameters added
 *****************************************************************************/
func (p *WanProcessor) addCommonWanParameters(cmdArgs []string, config *WanConfig) []string {
	// Add VLAN parameters
	cmdArgs = append(cmdArgs, "vlan=0", "vlan1=0")

	// Add clone MAC
	cloneMac := "00-00-00-00-00-00" // Default clone MAC
	if config.CloneMac != "" {
		cloneMac = config.CloneMac
	}
	cmdArgs = append(cmdArgs, "clonemac="+cloneMac)

	// Add ping disable flag
	cmdArgs = append(cmdArgs, "ping_disable="+fmt.Sprintf("%d", config.PingDisable))

	// Add heartbeat parameters
	if config.PingIp != "" {
		cmdArgs = append(cmdArgs, "pingip="+config.PingIp)
	} else {
		cmdArgs = append(cmdArgs, "pingip=0.0.0.0")
	}

	if config.PingIp2 != "" {
		cmdArgs = append(cmdArgs, "pingip2="+config.PingIp2)
	} else {
		cmdArgs = append(cmdArgs, "pingip2=0.0.0.0")
	}

	if config.MaxDelay != 0 {
		cmdArgs = append(cmdArgs, "maxdelay="+fmt.Sprintf("%d", config.MaxDelay))
	} else {
		cmdArgs = append(cmdArgs, "maxdelay=0")
	}

	// Add DNS proxy flag
	cmdArgs = append(cmdArgs, "dnspxy="+fmt.Sprintf("%d", config.DnsPxy))

	return cmdArgs
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles WAN configuration deletion tasks.
 *     Deletes a WAN interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx     - Context for the operation
 *     wanTask - WAN task containing configuration to delete
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *WanProcessor) handleDeleteConfig(ctx context.Context, wanTask *pb.WanTask) (string, error) {

	// Validate required fields
	if wanTask.Name == "" {
		return "WAN name is required", fmt.Errorf("wan name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := wanTask.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), fmt.Errorf("failed to get configurations: %w", err)
	}

	// Check if WAN exists in working configuration
	_, exists := p.workingConfigs[wanTask.Name]

	// If WAN doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("WAN not found in local configuration, nothing to delete",
			zap.String("name", wanTask.Name))
		return "WAN not found, nothing to delete", nil
	}

	var cmdArgs []string
	var cmdName string

	cmdName = "floweye"
	cmdArgs = []string{"nat", "rmvproxy", wanTask.Name}

	// Execute floweye command
	p.logger.Info("executing floweye command", zap.Strings("args", cmdArgs))
	output, err := utils.ExecuteCommand(p.logger, 10, cmdName, cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("WAN already does not exist, treating as successful delete",
				zap.String("name", wanTask.Name))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete WAN: %v", err), fmt.Errorf("failed to delete WAN: %w", err)
		}
	}

	// Skip post-delete verification for improved performance and reliability

	p.logger.Info("WAN deleted successfully", zap.String("output", output))
	return "WAN configuration deleted successfully", nil
}

/*****************************************************************************
 * NAME: handleStaticIpConfig
 *
 * DESCRIPTION:
 *     处理静态IP配置的命令构建。
 *     使用统一的WanConfig结构，消除重复的protobuf解析。
 *
 * PARAMETERS:
 *     config - WAN配置数据
 *     exists - WAN是否已存在
 *
 * RETURNS:
 *     []string - 构建的命令参数
 *     error    - 构建失败时的错误
 *****************************************************************************/
func (p *WanProcessor) handleStaticIpConfig(config *WanConfig, exists bool) ([]string, error) {
	var cmdArgs []string

	// 构建基础命令参数
	if !exists {
		cmdArgs = []string{"nat", "addproxy", "name=" + config.Name, "ifname=" + config.Ifname, "mtu=" + fmt.Sprintf("%d", config.Mtu)}
	} else {
		cmdArgs = []string{"nat", "setproxy", "name=" + config.Name, "newname=" + config.Name, "ifname=" + config.Ifname, "mtu=" + fmt.Sprintf("%d", config.Mtu)}
	}

	// 添加网关类型
	cmdArgs = append(cmdArgs, "gwpxy="+fmt.Sprintf("%d", config.GwPxy))

	// 添加IP地址
	if config.Addr == "" {
		return nil, fmt.Errorf("ip address is required")
	}
	cmdArgs = append(cmdArgs, "addr="+config.Addr)

	// 添加网关
	if config.Gateway == "" {
		return nil, fmt.Errorf("gateway is required")
	}
	cmdArgs = append(cmdArgs, "gateway="+config.Gateway)

	// 添加DNS（如果指定）
	dns := ""
	if config.Dns != "" {
		dns = config.Dns
	}
	cmdArgs = append(cmdArgs, "dns="+dns)

	// 添加NAT IP
	natIp := "0.0.0.0"
	if config.NatIp != "" {
		natIp = config.NatIp
	}
	cmdArgs = append(cmdArgs, "natip="+natIp)

	// 添加通用WAN参数
	cmdArgs = p.addCommonWanParameters(cmdArgs, config)

	return cmdArgs, nil
}

/*****************************************************************************
 * NAME: handleDhcpConfig
 *
 * DESCRIPTION:
 *     处理DHCP配置的命令构建。
 *     使用统一的WanConfig结构，消除重复的protobuf解析。
 *
 * PARAMETERS:
 *     config - WAN配置数据
 *     exists - WAN是否已存在
 *
 * RETURNS:
 *     []string - 构建的命令参数
 *     error    - 构建失败时的错误
 *****************************************************************************/
func (p *WanProcessor) handleDhcpConfig(config *WanConfig, exists bool) ([]string, error) {
	var cmdArgs []string

	// 构建基础命令参数
	if !exists {
		cmdArgs = []string{"nat", "adddhcpwan", "name=" + config.Name, "ifname=" + config.Ifname, "mtu=" + fmt.Sprintf("%d", config.Mtu)}
	} else {
		cmdArgs = []string{"nat", "setdhcpwan", "name=" + config.Name, "newname=" + config.Name, "ifname=" + config.Ifname, "mtu=" + fmt.Sprintf("%d", config.Mtu)}
	}

	// 添加通用WAN参数
	cmdArgs = p.addCommonWanParameters(cmdArgs, config)

	// 添加DHCP选项（如果指定）
	// 添加选项12（主机名）
	if option12, exists := config.DhcpOptions["12"]; exists {
		cmdArgs = append(cmdArgs, "dhcp_option=12,"+option12)
	}

	// 添加选项61（供应商类别ID）
	if option61, exists := config.DhcpOptions["61"]; exists {
		cmdArgs = append(cmdArgs, "dhcp_option=61,"+option61)
	}

	// 添加选项60（客户端ID）
	if option60, exists := config.DhcpOptions["60"]; exists {
		cmdArgs = append(cmdArgs, "dhcp_option=60,"+option60)
	}

	return cmdArgs, nil
}

/*****************************************************************************
 * NAME: handlePppoeConfig
 *
 * DESCRIPTION:
 *     处理PPPoE配置的命令构建。
 *     使用统一的WanConfig结构，消除重复的protobuf解析。
 *
 * PARAMETERS:
 *     config - WAN配置数据
 *     exists - WAN是否已存在
 *
 * RETURNS:
 *     []string - 构建的命令参数
 *     error    - 构建失败时的错误
 *****************************************************************************/
func (p *WanProcessor) handlePppoeConfig(config *WanConfig, exists bool) ([]string, error) {
	var cmdArgs []string

	// 构建基础命令参数
	if !exists {
		cmdArgs = []string{"nat", "addpppoe", "name=" + config.Name, "ifname=" + config.Ifname, "mtu=" + fmt.Sprintf("%d", config.Mtu)}
	} else {
		cmdArgs = []string{"nat", "setpppoe", "name=" + config.Name, "newname=" + config.Name, "ifname=" + config.Ifname, "mtu=" + fmt.Sprintf("%d", config.Mtu)}
	}

	// 添加用户名和密码
	if config.Username == "" {
		return nil, fmt.Errorf("username is required")
	}
	cmdArgs = append(cmdArgs, "username="+config.Username)

	if config.Password == "" {
		return nil, fmt.Errorf("password is required")
	}
	cmdArgs = append(cmdArgs, "password="+config.Password)

	// 添加等待时间
	cmdArgs = append(cmdArgs, "waitime="+fmt.Sprintf("%d", config.WaitTime))

	// 添加IPv6标志（protobuf中未定义，使用默认值）
	cmdArgs = append(cmdArgs, "ipv6=0") // 默认：禁用IPv6

	// 添加AC名称（如果指定）
	if config.AcName != "" {
		cmdArgs = append(cmdArgs, "acname="+config.AcName)
	} else {
		cmdArgs = append(cmdArgs, "acname=NULL")
	}

	// 添加服务名称（如果指定）
	if config.SvcName != "" {
		cmdArgs = append(cmdArgs, "svcname="+config.SvcName)
	} else {
		cmdArgs = append(cmdArgs, "svcname=NULL")
	}

	// 添加通用WAN参数
	cmdArgs = p.addCommonWanParameters(cmdArgs, config)

	return cmdArgs, nil
}
