/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      interface_config.go
 *
 * DESCRIPTION :    Interface configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: InterfaceConfig
 *
 * DESCRIPTION:
 *     Represents the configuration of a network interface.
 *     Stores all relevant interface parameters retrieved from the system.
 *
 * FIELDS:
 *     Name       - Interface name (e.g., eth0)
 *     Mode       - Interface mode (0-6, 0=monitor, 1-6=bridge)
 *     Zone       - Interface zone (inside/outside)
 *     MixMode    - Mix mode (0/1)
 *     Lagroup    - Link aggregation group (0-6, 0=no aggregation)
 *     Peer       - Bridge peer interface name (for bridge mode)
 *     LacpConfig - LACP configuration (when lagroup > 0)
 *****************************************************************************/
type InterfaceConfig struct {
	Name       string      // Interface name (e.g., eth0)
	Mode       int         // Interface mode (0-6, 0=monitor, 1-6=bridge)
	Zone       string      // Interface zone (inside/outside)
	MixMode    int         // Mix mode (0/1)
	Lagroup    int         // Link aggregation group (0-6, 0=no aggregation)
	Peer       string      // Bridge peer interface name (for bridge mode)
	LacpConfig *LacpConfig // LACP configuration (when lagroup > 0)
}

/*****************************************************************************
 * NAME: LacpConfig
 *
 * DESCRIPTION:
 *     Represents the LACP configuration for link aggregation.
 *     Stores LACP parameters retrieved from the system.
 *
 * FIELDS:
 *     Lag     - Link aggregation group ID (1-6)
 *     Enable  - LACP protocol enable (0=static, 1=LACP)
 *     Timeout - LACP timeout mode (0=slow, 1=fast)
 *     Passive - LACP passive mode (0=active, 1=passive)
 *****************************************************************************/
type LacpConfig struct {
	Lag     int // Link aggregation group ID (1-6)
	Enable  int // LACP protocol enable (0=static, 1=LACP)
	Timeout int // LACP timeout mode (0=slow, 1=fast)
	Passive int // LACP passive mode (0=active, 1=passive)
}

/*****************************************************************************
 * NAME: GetLocalInterfaceConfigs
 *
 * DESCRIPTION:
 *     Retrieves all local interface configurations from the system.
 *     Executes the floweye if list command and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*InterfaceConfig - Map of interface names to their configurations
 *     error                       - Error if retrieval fails
 *****************************************************************************/
func GetLocalInterfaceConfigs(logger *logger.Logger) (map[string]*InterfaceConfig, error) {
	logger.Info("Starting to retrieve all local interface configurations")

	// Execute floweye if list command
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "if", "list")
	if err != nil {
		logger.Error("failed to execute floweye if list command",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get interface list: %v", err)
	}

	// Parse output to get interface names
	interfaces := make(map[string]*InterfaceConfig)
	lines := strings.Split(output, "\n")

	// Regular expression to match interface line
	// Format: eth0 0 outside up PANAOS 00-e0-4c-00-00-45 PAENIC ...
	re := regexp.MustCompile(`^(\S+)\s+(\d+)\s+(\S+)`)

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		matches := re.FindStringSubmatch(line)
		if len(matches) >= 4 {
			ifName := matches[1]

			// Get detailed configuration for this interface
			ifConfig, err := GetInterfaceConfig(logger, ifName)
			if err != nil {
				logger.Warn("failed to get detailed config for interface",
					zap.String("interface", ifName),
					zap.Error(err))
				continue
			}

			interfaces[ifName] = ifConfig
		}
	}

	// Log summary of all interfaces
	interfaceNames := make([]string, 0, len(interfaces))
	for name := range interfaces {
		interfaceNames = append(interfaceNames, name)
	}

	logger.Info("Retrieved all local interface configurations",
		zap.Int("count", len(interfaces)),
		zap.Strings("interfaces", interfaceNames))

	// Log detailed configuration for each interface
	for name, config := range interfaces {
		logger.Info("Interface configuration details",
			zap.String("name", name),
			zap.Int("mode", config.Mode),
			zap.String("zone", config.Zone),
			zap.Int("mixmode", config.MixMode),
			zap.Int("lagroup", config.Lagroup),
			zap.String("peer", config.Peer))
	}
	return interfaces, nil
}

/*****************************************************************************
 * NAME: GetInterfaceConfig
 *
 * DESCRIPTION:
 *     Retrieves the configuration of a specific network interface.
 *     Executes the floweye if get command and parses the output into a structured format.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     ifName - Name of the interface to retrieve configuration for
 *
 * RETURNS:
 *     *InterfaceConfig - Interface configuration structure
 *     error           - Error if retrieval fails
 *****************************************************************************/
func GetInterfaceConfig(logger *logger.Logger, ifName string) (*InterfaceConfig, error) {
	logger.Debug("Retrieving configuration for single interface", zap.String("interface", ifName))

	// Execute floweye if get command
	output, err := utils.ExecuteCommand(logger, 5, "floweye", "if", "get", ifName)
	if err != nil {
		logger.Error("failed to execute floweye if get command",
			zap.Error(err),
			zap.String("interface", ifName),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get interface config: %v", err)
	}

	// Create configuration object with default values
	config := &InterfaceConfig{
		Name:       ifName,
		Mode:       0,        // Default mode (monitor)
		Zone:       "inside", // Default zone
		MixMode:    0,        // Default mixmode
		Lagroup:    0,        // Default lagroup (no aggregation)
		Peer:       "none",   // Default peer (no bridge)
		LacpConfig: nil,      // Default no LACP config
	}

	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Extract values from the map using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "mode":
			if val, err := strconv.Atoi(value); err == nil {
				config.Mode = val
			}
		case "zone":
			config.Zone = value
		case "mixmode":
			if val, err := strconv.Atoi(value); err == nil {
				config.MixMode = val
			}
		case "lagroup":
			if val, err := strconv.Atoi(value); err == nil {
				config.Lagroup = val
			}
		case "peer":
			config.Peer = value
		}
	}

	// Get LACP configuration if link aggregation is enabled
	if config.Lagroup > 0 {
		lacpConfig, err := GetLacpConfig(logger, config.Lagroup)
		if err != nil {
			logger.Warn("failed to get LACP configuration",
				zap.String("interface", ifName),
				zap.Int("lagroup", config.Lagroup),
				zap.Error(err))
			// Continue without LACP config rather than failing completely
		} else {
			config.LacpConfig = lacpConfig
		}
	}

	logger.Debug("Retrieved interface configuration",
		zap.String("interface", ifName),
		zap.Int("mode", config.Mode),
		zap.String("zone", config.Zone),
		zap.Int("mixmode", config.MixMode),
		zap.Int("lagroup", config.Lagroup),
		zap.String("peer", config.Peer),
		zap.Bool("has_lacp", config.LacpConfig != nil))
	return config, nil
}

/*****************************************************************************
 * NAME: ConvertInterfaceTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf InterfaceTask message to unified internal InterfaceConfig.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields.
 *
 * PARAMETERS:
 *     interfaceTask - Protobuf interface task message to convert
 *
 * RETURNS:
 *     *InterfaceConfig - Converted internal data structure
 *     error            - Error if conversion fails
 *****************************************************************************/
func ConvertInterfaceTaskToConfig(interfaceTask *pb.InterfaceTask) (*InterfaceConfig, error) {
	if interfaceTask == nil {
		return nil, fmt.Errorf("interfaceTask is nil")
	}

	// Initialize with basic fields and default values
	config := &InterfaceConfig{
		Name:    interfaceTask.GetName(),
		Zone:    "inside", // Default zone
		MixMode: 0,        // Default mixmode
		Lagroup: 0,        // Default lagroup
		Peer:    "none",   // Default peer
		LacpConfig: &LacpConfig{
			Lag:     0,
			Enable:  0,
			Timeout: 0,
			Passive: 0,
		},
	}

	// Convert interface mode enum to int
	switch interfaceTask.GetMode() {
	case pb.InterfaceMode_INTERFACE_MODE_MONITOR:
		config.Mode = 0
	case pb.InterfaceMode_INTERFACE_MODE_BRIDGE1:
		config.Mode = 1
	case pb.InterfaceMode_INTERFACE_MODE_BRIDGE2:
		config.Mode = 2
	case pb.InterfaceMode_INTERFACE_MODE_BRIDGE3:
		config.Mode = 3
	case pb.InterfaceMode_INTERFACE_MODE_BRIDGE4:
		config.Mode = 4
	case pb.InterfaceMode_INTERFACE_MODE_BRIDGE5:
		config.Mode = 5
	case pb.InterfaceMode_INTERFACE_MODE_BRIDGE6:
		config.Mode = 6
	default:
		config.Mode = 0 // Default to monitor mode
	}

	// Convert zone enum to string
	if interfaceTask.GetZone() == pb.InterfaceZone_INTERFACE_ZONE_OUTSIDE {
		config.Zone = "outside"
	}

	// Convert mixmode optional field
	if interfaceTask.MixMode != nil && *interfaceTask.MixMode {
		config.MixMode = 1
	}

	// Convert lagroup optional field
	if interfaceTask.LaGroup != nil {
		config.Lagroup = int(*interfaceTask.LaGroup)
	}

	// Convert peer optional field
	if interfaceTask.Peer != nil {
		config.Peer = *interfaceTask.Peer
	}

	// Convert LACP configuration if present
	if interfaceTask.GetLacpConfig() != nil {
		lacpConfig := interfaceTask.GetLacpConfig()

		// Convert protocol enum to enable flag
		if lacpConfig.GetProtocol() == pb.LacpProtocol_LACP_PROTOCOL_LACP {
			config.LacpConfig.Enable = 1
		}

		// Convert timeout enum if specified
		if lacpConfig.Timeout != nil {
			if *lacpConfig.Timeout == pb.LacpTimeout_LACP_TIMEOUT_FAST {
				config.LacpConfig.Timeout = 1
			}
		}

		// Convert passive mode if specified
		if lacpConfig.Passive != nil && *lacpConfig.Passive {
			config.LacpConfig.Passive = 1
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: VerifyInterfaceConfig
 *
 * DESCRIPTION:
 *     Verifies that the interface configuration was applied correctly.
 *     Compares the requested configuration with the actual configuration on the system.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger        - Logger instance for logging operations
 *     expectedConfig - Expected interface configuration
 *
 * RETURNS:
 *     bool  - True if configuration matches, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyInterfaceConfig(logger *logger.Logger, expectedConfig *InterfaceConfig) (bool, error) {
	logger.Info("Verifying interface configuration",
		zap.String("name", expectedConfig.Name),
		zap.Int("mode", expectedConfig.Mode),
		zap.String("zone", expectedConfig.Zone))

	// Get current interface configuration
	actualConfig, err := GetInterfaceConfig(logger, expectedConfig.Name)
	if err != nil {
		return false, err
	}

	// Verify zone (the main parameter we care about)
	if actualConfig.Zone != expectedConfig.Zone {
		logger.Error("interface zone mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.String("expected", expectedConfig.Zone),
			zap.String("actual", actualConfig.Zone))
		return false, nil
	}

	// Verify mode
	if actualConfig.Mode != expectedConfig.Mode {
		logger.Error("interface mode mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.Int("expected", expectedConfig.Mode),
			zap.Int("actual", actualConfig.Mode))
		return false, nil
	}

	// Verify mixmode
	if actualConfig.MixMode != expectedConfig.MixMode {
		logger.Error("interface mixmode mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.Int("expected", expectedConfig.MixMode),
			zap.Int("actual", actualConfig.MixMode))
		return false, nil
	}

	// Verify lagroup
	if actualConfig.Lagroup != expectedConfig.Lagroup {
		logger.Error("interface lagroup mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.Int("expected", expectedConfig.Lagroup),
			zap.Int("actual", actualConfig.Lagroup))
		return false, nil
	}

	// Verify peer (for bridge mode)
	if actualConfig.Peer != expectedConfig.Peer {
		logger.Error("interface peer mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.String("expected", expectedConfig.Peer),
			zap.String("actual", actualConfig.Peer))
		return false, nil
	}

	// Verify LACP configuration if link aggregation is enabled
	if expectedConfig.Lagroup > 0 && expectedConfig.LacpConfig != nil {
		if !CompareLacpConfig(expectedConfig.LacpConfig, actualConfig.LacpConfig) {
			logger.Error("LACP configuration mismatch",
				zap.String("interface", expectedConfig.Name))
			return false, nil
		}
	}

	logger.Info("Interface configuration verified successfully",
		zap.String("name", expectedConfig.Name),
		zap.String("zone", expectedConfig.Zone),
		zap.Int("mixmode", expectedConfig.MixMode),
		zap.Int("lagroup", expectedConfig.Lagroup),
		zap.String("peer", expectedConfig.Peer))
	return true, nil
}

/*****************************************************************************
 * NAME: CompareInterfaceConfig
 *
 * DESCRIPTION:
 *     Compares the requested configuration with the local configuration.
 *     Determines if the requested configuration matches the existing configuration.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected interface configuration
 *     localConfig    - Local interface configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareInterfaceConfig(logger *logger.Logger, expectedConfig *InterfaceConfig, localConfig *InterfaceConfig) bool {
	if localConfig == nil {
		return false
	}

	// Compare zone (the main parameter we care about)
	if localConfig.Zone != expectedConfig.Zone {
		logger.Debug("interface zone mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.String("expected", expectedConfig.Zone),
			zap.String("actual", localConfig.Zone))
		return false
	}

	// Compare mode
	if localConfig.Mode != expectedConfig.Mode {
		logger.Debug("interface mode mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.Int("expected", expectedConfig.Mode),
			zap.Int("actual", localConfig.Mode))
		return false
	}

	// Compare mixmode
	if localConfig.MixMode != expectedConfig.MixMode {
		logger.Debug("interface mixmode mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.Int("expected", expectedConfig.MixMode),
			zap.Int("actual", localConfig.MixMode))
		return false
	}

	// Compare lagroup
	if localConfig.Lagroup != expectedConfig.Lagroup {
		logger.Debug("interface lagroup mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.Int("expected", expectedConfig.Lagroup),
			zap.Int("actual", localConfig.Lagroup))
		return false
	}

	// Compare peer (for bridge mode)
	if localConfig.Peer != expectedConfig.Peer {
		logger.Debug("interface peer mismatch",
			zap.String("interface", expectedConfig.Name),
			zap.String("expected", expectedConfig.Peer),
			zap.String("actual", localConfig.Peer))
		return false
	}

	// Compare LACP configuration if link aggregation is enabled
	if expectedConfig.Lagroup > 0 && expectedConfig.LacpConfig != nil {
		if !CompareLacpConfig(expectedConfig.LacpConfig, localConfig.LacpConfig) {
			logger.Debug("interface LACP configuration mismatch",
				zap.String("interface", expectedConfig.Name))
			return false
		}
	}

	return true
}

/*****************************************************************************
 * NAME: GetLacpConfig
 *
 * DESCRIPTION:
 *     Retrieves the LACP configuration for a specific link aggregation group.
 *     Executes the floweye lacp get command and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     lag    - Link aggregation group ID (1-6)
 *
 * RETURNS:
 *     *LacpConfig - LACP configuration structure
 *     error       - Error if retrieval fails
 *****************************************************************************/
func GetLacpConfig(logger *logger.Logger, lag int) (*LacpConfig, error) {
	logger.Debug("Retrieving LACP configuration", zap.Int("lag", lag))

	// Execute floweye lacp get command
	output, err := utils.ExecuteCommand(logger, 5, "floweye", "lacp", "get", fmt.Sprintf("lag=%d", lag))
	if err != nil {
		logger.Error("failed to execute floweye lacp get command",
			zap.Error(err),
			zap.Int("lag", lag),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get LACP config: %v", err)
	}

	// Create configuration object with default values
	config := &LacpConfig{
		Lag:     lag,
		Enable:  0, // Default to static
		Timeout: 0, // Default to slow
		Passive: 0, // Default to active
	}

	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Extract values from the map using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "enable":
			if val, err := strconv.Atoi(value); err == nil {
				config.Enable = val
			}
		case "timeout":
			if val, err := strconv.Atoi(value); err == nil {
				config.Timeout = val
			}
		case "passive":
			if val, err := strconv.Atoi(value); err == nil {
				config.Passive = val
			}
		}
	}

	logger.Debug("Retrieved LACP configuration",
		zap.Int("lag", lag),
		zap.Int("enable", config.Enable),
		zap.Int("timeout", config.Timeout),
		zap.Int("passive", config.Passive))
	return config, nil
}

/*****************************************************************************
 * NAME: CompareLacpConfig
 *
 * DESCRIPTION:
 *     Compares the requested LACP configuration with the local configuration.
 *     Determines if the requested LACP configuration matches the existing configuration.
 *
 * PARAMETERS:
 *     expectedLacp - Expected LACP configuration
 *     localLacp    - Local LACP configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareLacpConfig(expectedLacp *LacpConfig, localLacp *LacpConfig) bool {
	if expectedLacp == nil && localLacp == nil {
		return true // Both are nil, match
	}

	if expectedLacp == nil || localLacp == nil {
		return false // One is nil, the other is not
	}

	// Compare protocol/enable
	if localLacp.Enable != expectedLacp.Enable {
		return false
	}

	// Compare timeout
	if localLacp.Timeout != expectedLacp.Timeout {
		return false
	}

	// Compare passive mode
	if localLacp.Passive != expectedLacp.Passive {
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: BuildInterfaceCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for interface configuration.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     config - Interface configuration data
 *
 * RETURNS:
 *     []string - Command arguments for floweye if set
 *     error    - Error if command building fails
 *****************************************************************************/
func BuildInterfaceCommand(config *InterfaceConfig) ([]string, error) {
	// Validate required fields
	if config.Name == "" {
		return nil, fmt.Errorf("interface name is required")
	}

	// Build basic command arguments
	cmdArgs := []string{
		"if", "set",
		"name=" + config.Name,
		"mode=" + strconv.Itoa(config.Mode),
		"zone=" + config.Zone,
		"lagroup=" + strconv.Itoa(config.Lagroup),
		"mixmode=" + strconv.Itoa(config.MixMode),
	}

	// Add peer parameter for bridge modes (mode 1-6)
	if config.Mode >= 1 && config.Mode <= 6 {
		cmdArgs = append(cmdArgs, "peer="+config.Peer)
	}

	return cmdArgs, nil
}

/*****************************************************************************
 * NAME: BuildLacpCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for LACP configuration.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     config - Interface configuration containing LACP settings
 *
 * RETURNS:
 *     []string - Command arguments for floweye lacp set
 *     error    - Error if command building fails
 *****************************************************************************/
func BuildLacpCommand(config *InterfaceConfig) ([]string, error) {
	// Validate that LACP is needed
	if config.Lagroup == 0 || config.LacpConfig == nil {
		return nil, fmt.Errorf("LACP configuration not needed for lagroup=0 or missing LACP config")
	}

	// Build LACP command arguments
	cmdArgs := []string{
		"lacp", "set",
		"lag=" + strconv.Itoa(config.Lagroup),
		"enable=" + strconv.Itoa(config.LacpConfig.Enable),
		"timeout=" + strconv.Itoa(config.LacpConfig.Timeout),
		"passive=" + strconv.Itoa(config.LacpConfig.Passive),
	}

	return cmdArgs, nil
}
