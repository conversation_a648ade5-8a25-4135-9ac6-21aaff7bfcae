/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_tracking_policy_processor.go
 *
 * DESCRIPTION :    DNS tracking policy processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        17/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

// Override utils.ExecuteCommand for testing
var executeCommandDnsTracking = func(logger *logger.Logger, timeout int, command string, args ...string) (string, error) {
	return utils.ExecuteCommand(logger, timeout, command, args...)
}

/*****************************************************************************
 * NAME: DnsTrackingPolicyProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_DNS_TRACKING_POLICY type tasks.
 *     Handles DNS tracking policy configuration operations including creation,
 *     modification, deletion, and ordering management.
 *
 * FIELDS:
 *     logger               - Logger for DNS tracking policy processor operations
 *     localConfigs         - Cache of local DNS tracking policy configurations (used for full sync redundant deletion)
 *     fullSyncInProgress   - Flag indicating if full sync is in progress
 *     cookieToID           - Map of policy cookies to local IDs (used for full sync redundant deletion)
 *     workingConfigs       - Working cache for operations (can be refreshed during full sync)
 *     workingCookieToID    - Working map of policy cookies to IDs
 *****************************************************************************/
type DnsTrackingPolicyProcessor struct {
	logger             *logger.Logger
	localConfigs       map[uint32]*DnsTrackingPolicyConfig // Cache of local DNS tracking policy configurations (used for full sync redundant deletion)
	fullSyncInProgress bool
	cookieToID         map[uint32]int                      // Map of policy cookies to local IDs (used for full sync redundant deletion)
	workingConfigs     map[uint32]*DnsTrackingPolicyConfig // Working cache for operations (can be refreshed during full sync)
	workingCookieToID  map[uint32]int                      // Working map of policy cookies to IDs
}

/*****************************************************************************
 * NAME: NewDnsTrackingPolicyProcessor
 *
 * DESCRIPTION:
 *     Creates a new DNS tracking policy processor instance.
 *     Initializes the processor with logger and empty configuration cache.
 *
 * PARAMETERS:
 *     logger - Logger instance for processor operations
 *
 * RETURNS:
 *     *DnsTrackingPolicyProcessor - New DNS tracking policy processor instance
 *****************************************************************************/
func NewDnsTrackingPolicyProcessor(logger *logger.Logger) *DnsTrackingPolicyProcessor {
	return &DnsTrackingPolicyProcessor{
		logger:             logger,
		localConfigs:       make(map[uint32]*DnsTrackingPolicyConfig),
		fullSyncInProgress: false,
		cookieToID:         make(map[uint32]int),
		workingConfigs:     make(map[uint32]*DnsTrackingPolicyConfig),
		workingCookieToID:  make(map[uint32]int),
	}
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the type of task this processor can handle.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - Type of task (TASK_DNS_TRACKING_POLICY)
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_DNS_TRACKING_POLICY
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a DNS tracking policy task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Extract DNS tracking policy task
	dnsTrackingTask := task.GetDnsTrackingPolicyTask()
	if dnsTrackingTask == nil {
		return "DNS tracking policy task payload is nil", fmt.Errorf("DNS tracking policy task payload is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "dns_tracking_policy", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different processing based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, dnsTrackingTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, dnsTrackingTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("Unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes local configuration cache and sets sync flag.
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) StartFullSync() error {
	p.logger.Info("starting full sync for DNS tracking policy processor")

	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	p.logger.Info("full sync started for DNS tracking policy processor",
		zap.Int("local_configs_count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up any remaining local configurations that were not processed.
 *     Implements safe cleanup pattern to avoid concurrent map modification.
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) EndFullSync() {
	p.logger.Info("ending full sync for DNS tracking policy processor",
		zap.Int("remaining_configs", len(p.localConfigs)))

	// Create a copy of remaining items to avoid modifying map during iteration
	remainingItems := make(map[uint32]*DnsTrackingPolicyConfig)
	for cookie, config := range p.localConfigs {
		remainingItems[cookie] = config
	}

	// Process remaining items (keep fullSyncInProgress = true during cleanup)
	for cookie := range remainingItems {
		deleteTask := &pb.DnsTrackingPolicyTask{Cookie: cookie}
		_, err := p.handleDeleteConfig(context.Background(), deleteTask)
		if err != nil {
			p.logger.Error("failed to delete remaining DNS tracking policy during cleanup",
				zap.Uint32("cookie", cookie),
				zap.Error(err))
		}
	}

	// Verify cleanup and set flag to false
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some DNS tracking policies not cleaned up",
			zap.Int("count", len(p.localConfigs)))
	}

	p.fullSyncInProgress = false
	p.localConfigs = make(map[uint32]*DnsTrackingPolicyConfig)
	p.cookieToID = make(map[uint32]int)
	p.workingConfigs = make(map[uint32]*DnsTrackingPolicyConfig)
	p.workingCookieToID = make(map[uint32]int)

	p.logger.Info("full sync ended for DNS tracking policy processor")
}

/*****************************************************************************
 * NAME: fetchDnsTrackingPolicyConfigs
 *
 * DESCRIPTION:
 *     Fetches DNS tracking policy configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[uint32]*DnsTrackingPolicyConfig - DNS tracking policies by cookie
 *     map[uint32]int                      - Cookie to ID mapping
 *     error                               - Error if fetch fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) fetchDnsTrackingPolicyConfigs() (map[uint32]*DnsTrackingPolicyConfig, map[uint32]int, error) {
	// Get all local configurations
	configs, err := GetLocalDnsTrackingPolicyConfigs(p.logger)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get local DNS tracking policy configurations: %w", err)
	}

	// Build cookie to ID mapping
	cookieToID := make(map[uint32]int)
	for cookie, config := range configs {
		cookieToID[cookie] = config.ID
	}

	return configs, cookieToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes the local configuration cache by fetching current configurations
 *     from the PA system using floweye commands.
 *     This is used only for full sync redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local DNS tracking policy configurations")

	configs, cookieToID, err := p.fetchDnsTrackingPolicyConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for local cache: %w", err)
	}

	// Update local cache (used for full sync redundant deletion)
	p.localConfigs = configs
	p.cookieToID = cookieToID

	p.logger.Debug("refreshed local DNS tracking policy configurations",
		zap.Int("count", len(configs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working DNS tracking policy configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working DNS tracking policy configurations")

	configs, cookieToID, err := p.fetchDnsTrackingPolicyConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs
	p.workingCookieToID = cookieToID

	p.logger.Debug("refreshed working DNS tracking policy configurations",
		zap.Int("count", len(configs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like ordering, enable/disable, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: resolveDomainGroupNameToID
 *
 * DESCRIPTION:
 *     Resolves domain group name to ID using the domain group module.
 *     Uses the GetDomainGroupIdByName function from domain_group_config.go.
 *
 * PARAMETERS:
 *     domainGroupName - Name of the domain group to resolve
 *
 * RETURNS:
 *     int   - Domain group ID, -1 if not found
 *     error - Error if resolution fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) resolveDomainGroupNameToID(domainGroupName string) (int, error) {
	p.logger.Debug("resolving domain group name to ID",
		zap.String("domain_group_name", domainGroupName))

	// Use the domain group module's function to resolve name to ID
	id, err := GetDomainGroupIdByName(p.logger, domainGroupName)
	if err != nil {
		p.logger.Error("failed to resolve domain group name to ID",
			zap.String("domain_group_name", domainGroupName),
			zap.Error(err))
		return -1, fmt.Errorf("failed to resolve domain group name '%s' to ID: %w", domainGroupName, err)
	}

	if id == -1 {
		p.logger.Warn("domain group not found",
			zap.String("domain_group_name", domainGroupName))
		return -1, fmt.Errorf("domain group '%s' not found", domainGroupName)
	}

	p.logger.Debug("resolved domain group name to ID",
		zap.String("domain_group_name", domainGroupName),
		zap.Int("id", id))

	return id, nil
}

/*****************************************************************************
 * NAME: checkDomainGroupReferenceConflict
 *
 * DESCRIPTION:
 *     Checks if a domain group is already referenced by another DNS tracking policy.
 *     Returns the conflicting policy cookie if found.
 *
 * PARAMETERS:
 *     domainGroupID - ID of the domain group to check
 *     currentCookie - Cookie of the current policy (to exclude from conflict check)
 *
 * RETURNS:
 *     uint32 - Cookie of the conflicting policy (0 if no conflict)
 *     bool   - True if conflict exists
 *     error  - Error if check fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) checkDomainGroupReferenceConflict(domainGroupID int, currentCookie uint32) (uint32, bool, error) {
	p.logger.Debug("checking domain group reference conflict",
		zap.Int("domain_group_id", domainGroupID),
		zap.Uint32("current_cookie", currentCookie))

	// Get all current DNS tracking policies
	configs, err := GetLocalDnsTrackingPolicyConfigs(p.logger)
	if err != nil {
		return 0, false, fmt.Errorf("failed to get DNS tracking policies for conflict check: %w", err)
	}

	// Check each policy for domain group reference
	for cookie, config := range configs {
		// Skip the current policy
		if cookie == currentCookie {
			continue
		}

		// Get detailed configuration to check domain group ID
		detailedConfig, err := GetDnsTrackingPolicyConfig(p.logger, config.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed config for conflict check",
				zap.Uint32("cookie", cookie),
				zap.Int("policy_id", config.ID),
				zap.Error(err))
			continue
		}

		// Check if this policy uses the same domain group
		if detailedConfig.DnsID == domainGroupID {
			p.logger.Info("found domain group reference conflict",
				zap.Int("domain_group_id", domainGroupID),
				zap.Uint32("current_cookie", currentCookie),
				zap.Uint32("conflicting_cookie", cookie),
				zap.Int("conflicting_policy_id", config.ID))
			return cookie, true, nil
		}
	}

	p.logger.Debug("no domain group reference conflict found",
		zap.Int("domain_group_id", domainGroupID),
		zap.Uint32("current_cookie", currentCookie))

	return 0, false, nil
}

/*****************************************************************************
 * NAME: buildDnsTrackingPolicyCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for DNS tracking policy operations.
 *     Uses converted internal data structure instead of protobuf access.
 *     Always includes all parameters to ensure proper default value handling.
 *
 * PARAMETERS:
 *     configData    - Converted DNS tracking policy configuration data
 *     policyID      - Policy ID
 *     domainGroupID - Domain group ID
 *     isUpdate      - Whether this is an update operation
 *
 * RETURNS:
 *     []string - Command arguments array
 *     error    - Error if command building fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) buildDnsTrackingPolicyCommand(configData *DnsTrackingPolicyConfig, policyID int, domainGroupID int, isUpdate bool) ([]string, error) {
	// Build base command
	operation := "add"
	if isUpdate {
		operation = "set"
	}
	cmdArgs := []string{"dnrt", operation}

	// Add all parameters - always include to ensure proper default value handling
	cmdArgs = append(cmdArgs, fmt.Sprintf("id=%d", policyID))
	cmdArgs = append(cmdArgs, fmt.Sprintf("dns=%d", domainGroupID))

	cmdArgs = append(cmdArgs, fmt.Sprintf("pxy=%s", configData.Pxy))
	cmdArgs = append(cmdArgs, fmt.Sprintf("bkuppxy=%s", configData.BkupPxy))
	cmdArgs = append(cmdArgs, fmt.Sprintf("cookie=%d", configData.Cookie))

	// Add optional parameters with defaults - always include to ensure proper default value handling
	cmdArgs = append(cmdArgs, fmt.Sprintf("dnsaddr=%s", configData.DnsAddr))

	trackHostValue := 0
	if configData.TrackHost {
		trackHostValue = 1
	}
	cmdArgs = append(cmdArgs, fmt.Sprintf("trackhost=%d", trackHostValue))

	cmdArgs = append(cmdArgs, fmt.Sprintf("cachettl=%d", configData.CacheTTL))
	cmdArgs = append(cmdArgs, fmt.Sprintf("desc=%s", configData.Desc))

	p.logger.Debug("built DNS tracking policy command",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("policy_id", policyID),
		zap.Int("domain_group_id", domainGroupID),
		zap.Bool("is_update", isUpdate),
		zap.Strings("args", cmdArgs))

	return cmdArgs, nil
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles DNS tracking policy configuration changes (NEW_CONFIG and EDIT_CONFIG).
 *     Implements unified logic for both create and update operations.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - DNS tracking policy task to process
 *     taskAction - Task action type (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) handleConfigChange(ctx context.Context, task *pb.DnsTrackingPolicyTask, taskAction pb.TaskAction) (string, error) {
	p.logger.Info("handling DNS tracking policy config change",
		zap.Uint32("cookie", task.GetCookie()),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertDnsTrackingPolicyTaskToConfig(task)
	if err != nil {
		p.logger.Error("failed to convert DNS tracking policy task to config data",
			zap.Uint32("cookie", task.GetCookie()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert DNS tracking policy configuration: %v", err), err
	}

	// Validate required fields using converted data
	if configData.Cookie == 0 {
		return "DNS tracking policy cookie is required", fmt.Errorf("DNS tracking policy cookie is required")
	}

	if configData.DnsName == "" {
		return "DNS tracking policy domain group is required", fmt.Errorf("DNS tracking policy domain group is required")
	}

	if configData.Pxy == "" {
		return "DNS tracking policy primary proxy is required", fmt.Errorf("DNS tracking policy primary proxy is required")
	}

	if configData.BkupPxy == "" {
		return "DNS tracking policy backup proxy is required", fmt.Errorf("DNS tracking policy backup proxy is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		cookie := configData.Cookie // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, cookie)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if policy exists in working configs
	existingConfig, exists := p.workingConfigs[configData.Cookie]
	var policyID int

	if exists {
		/*
			// Policy exists, check if update is needed using converted data
			if CompareDnsTrackingPolicyConfig(p.logger, configData, existingConfig) {
				p.logger.Info("DNS tracking policy configuration unchanged, skipping update",
					zap.Uint32("cookie", configData.Cookie))

				return "DNS tracking policy configuration unchanged", nil
			}
		*/

		policyID = existingConfig.ID
		p.logger.Info("updating existing DNS tracking policy",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("id", policyID))
	} else {
		// Policy doesn't exist, need to create new one
		// Allocate new ID (max existing ID + 1)
		policyID = p.allocateNewPolicyID()
		p.logger.Info("creating new DNS tracking policy",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("id", policyID))
	}

	// Resolve domain group name to ID using converted data
	domainGroupID, err := p.resolveDomainGroupNameToID(configData.DnsName)
	if err != nil {
		return fmt.Sprintf("Failed to resolve domain group: %v", err), err
	}

	// First, always check for domain group reference conflicts
	// This must be done before determining if the policy exists to ensure clean state
	conflictingCookie, hasConflict, err := p.checkDomainGroupReferenceConflict(domainGroupID, configData.Cookie)
	if err != nil {
		p.logger.Warn("failed to check domain group reference conflict, continuing",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("domain_group_id", domainGroupID),
			zap.Error(err))
	} else if hasConflict {
		p.logger.Info("domain group reference conflict detected, removing conflicting policy",
			zap.Uint32("current_cookie", configData.Cookie),
			zap.Uint32("conflicting_cookie", conflictingCookie),
			zap.Int("domain_group_id", domainGroupID),
			zap.String("domain_group_name", configData.DnsName))

		// Delete the conflicting policy
		deleteTask := &pb.DnsTrackingPolicyTask{Cookie: conflictingCookie}
		deleteResult, deleteErr := p.handleDeleteConfig(context.Background(), deleteTask)
		if deleteErr != nil {
			p.logger.Error("failed to delete conflicting DNS tracking policy",
				zap.Uint32("conflicting_cookie", conflictingCookie),
				zap.Error(deleteErr))
			return fmt.Sprintf("Failed to resolve domain group conflict: %v", deleteErr), deleteErr
		}

		p.logger.Info("successfully deleted conflicting DNS tracking policy",
			zap.Uint32("conflicting_cookie", conflictingCookie),
			zap.String("delete_result", deleteResult))

		// Refresh working configs after deletion to get updated state
		if refreshErr := p.getConfigsForOperation(); refreshErr != nil {
			p.logger.Warn("failed to refresh configs after conflict resolution",
				zap.Error(refreshErr))
		}
	}

	// After conflict resolution, re-check if the current policy exists
	existingConfig, exists = p.workingConfigs[configData.Cookie]
	if exists {
		policyID = existingConfig.ID
		p.logger.Debug("policy exists after conflict resolution",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("policy_id", policyID))
	} else {
		// Allocate new policy ID for new policy
		policyID = p.allocateNewPolicyID()
		p.logger.Debug("allocated new policy ID after conflict resolution",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("new_policy_id", policyID))
	}

	// Build floweye command arguments using converted data
	cmdArgs, err := p.buildDnsTrackingPolicyCommand(configData, policyID, domainGroupID, exists)
	if err != nil {
		return fmt.Sprintf("Failed to build DNS tracking policy command: %v", err), err
	}

	// Execute floweye command
	p.logger.Info("executing floweye command for DNS tracking policy",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("id", policyID),
		zap.Strings("args", cmdArgs))

	output, err := executeCommandDnsTracking(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye command for DNS tracking policy",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("id", policyID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to %s DNS tracking policy: %v",
			map[bool]string{true: "update", false: "create"}[exists], err), err
	}

	p.logger.Debug("floweye command executed successfully",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("id", policyID),
		zap.String("output", output))

	// Refresh working configs after creation/update to get latest state
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error as the main operation succeeded
	}

	// Handle enable/disable operation before verification to ensure correct state
	// Debug: log the enable/disable values
	p.logger.Info("DEBUG: DNS tracking policy enable/disable values",
		zap.Uint32("cookie", configData.Cookie),
		zap.Bool("config_enable", configData.Enable),
		zap.Bool("disable_param", !configData.Enable))

	if err := p.handlePolicyEnableDisable(configData.Cookie, !configData.Enable, policyID); err != nil {
		p.logger.Error("failed to handle DNS tracking policy enable/disable",
			zap.Uint32("cookie", configData.Cookie),
			zap.Bool("disable", !configData.Enable),
			zap.Error(err))
		return fmt.Sprintf("Failed to handle policy enable/disable: %v", err), err
	}

	// Handle ordering logic if previous field is specified using converted data
	if configData.Previous != nil {
		if err := p.handlePolicyOrdering(configData, policyID); err != nil {
			p.logger.Error("failed to handle DNS tracking policy ordering",
				zap.Uint32("cookie", configData.Cookie),
				zap.Uint32("previous", *configData.Previous),
				zap.Error(err))
			return fmt.Sprintf("Failed to handle policy ordering: %v", err), err
		}

		// After ordering, refresh working configs to get the updated policy ID
		if refreshErr := p.getConfigsForOperation(); refreshErr != nil {
			p.logger.Warn("failed to refresh configs after ordering",
				zap.Uint32("cookie", configData.Cookie),
				zap.Error(refreshErr))
		} else {
			// Update policyID with the current ID after ordering
			if updatedConfig, exists := p.workingConfigs[configData.Cookie]; exists {
				if updatedConfig.ID != policyID {
					p.logger.Info("policy ID changed after ordering, updating for verification",
						zap.Uint32("cookie", configData.Cookie),
						zap.Int("old_id", policyID),
						zap.Int("new_id", updatedConfig.ID))
					policyID = updatedConfig.ID
				}
			} else {
				p.logger.Error("policy not found in working configs after ordering",
					zap.Uint32("cookie", configData.Cookie),
					zap.Int("expected_id", policyID))
				return ("Policy not found after ordering"),
					fmt.Errorf("policy with cookie %d not found after ordering", configData.Cookie)
			}
		}
	}

	// Verify configuration using converted data and single-object retrieval for better performance
	success, verifyErr := VerifyDnsTrackingPolicyConfig(p.logger, configData, policyID)
	if verifyErr != nil {
		p.logger.Error("failed to verify DNS tracking policy configuration",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("id", policyID),
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify DNS tracking policy configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("DNS tracking policy configuration verification failed",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("id", policyID))
		return "DNS tracking policy configuration verification failed",
			fmt.Errorf("configuration verification failed")
	}

	// Update working cookie to ID mapping
	p.workingCookieToID[configData.Cookie] = policyID

	actionStr := map[bool]string{true: "updated", false: "created"}[exists]
	p.logger.Info("DNS tracking policy "+actionStr+" successfully",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("id", policyID))

	return fmt.Sprintf("DNS tracking policy %s successfully", actionStr), nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles DNS tracking policy deletion (DELETE_CONFIG).
 *     Implements standardized delete behavior with NEXIST error handling.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - DNS tracking policy task to delete
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) handleDeleteConfig(ctx context.Context, task *pb.DnsTrackingPolicyTask) (string, error) {
	p.logger.Info("handling DNS tracking policy deletion",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Validate required fields
	if task.GetCookie() == 0 {
		return "DNS tracking policy cookie is required", fmt.Errorf("DNS tracking policy cookie is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		cookie := task.GetCookie() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, cookie)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if policy exists in working configs
	existingConfig, exists := p.workingConfigs[task.GetCookie()]
	if !exists {
		p.logger.Info("DNS tracking policy does not exist, no need to delete",
			zap.Uint32("cookie", task.GetCookie()))

		return "DNS tracking policy does not exist, no need to delete", nil
	}

	// Get current policy ID from floweye (don't rely on cached localConfig ID which may be outdated)
	var currentPolicyID int
	if p.fullSyncInProgress {
		// During full sync, get real-time ID from floweye to avoid stale cache issues
		var err error
		currentPolicyID, err = p.getCurrentPolicyIDByCookie(task.GetCookie())
		if err != nil {
			p.logger.Info("DNS tracking policy does not exist in floweye, treating as successful delete",
				zap.Uint32("cookie", task.GetCookie()),
				zap.Error(err))
			return "DNS tracking policy does not exist, deletion successful", nil
		}
	} else {
		// For incremental updates, use cached ID (should be up-to-date after refresh)
		currentPolicyID = existingConfig.ID
	}

	// Store deletion information for ordering logic
	policyID := currentPolicyID

	// Build floweye command arguments
	cmdArgs := []string{"dnrt", "remove", "id=" + strconv.Itoa(policyID)}

	// Execute floweye command
	p.logger.Info("executing floweye command to delete DNS tracking policy",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Int("id", policyID),
		zap.Strings("args", cmdArgs))

	output, err := executeCommandDnsTracking(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("DNS tracking policy already does not exist, treating as successful delete",
				zap.Uint32("cookie", task.GetCookie()),
				zap.Int("id", policyID))
		} else {
			p.logger.Error("failed to execute floweye command to delete DNS tracking policy",
				zap.Uint32("cookie", task.GetCookie()),
				zap.Int("id", policyID),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete DNS tracking policy: %v", err), err
		}
	}

	p.logger.Debug("floweye command executed successfully",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Int("id", policyID),
		zap.String("output", output))

	// Skip post-delete verification for improved performance and reliability

	// Handle ID decrementation for policies with ID > deleted ID
	if err := p.handlePolicyIDDecrement(policyID); err != nil {
		p.logger.Error("failed to handle policy ID decrementation after deletion",
			zap.Uint32("cookie", task.GetCookie()),
			zap.Int("deleted_id", policyID),
			zap.Error(err))
		// Don't return error as the main delete operation succeeded
	}

	// Remove from working cookie to ID mapping
	delete(p.workingCookieToID, task.GetCookie())

	p.logger.Info("DNS tracking policy deleted successfully",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Int("id", policyID))

	return "DNS tracking policy deleted successfully", nil
}

/*****************************************************************************
 * NAME: allocateNewPolicyID
 *
 * DESCRIPTION:
 *     Allocates a new policy ID by finding the maximum existing ID and adding 1.
 *     Used when creating new DNS tracking policies.
 *
 * RETURNS:
 *     int - New policy ID
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) allocateNewPolicyID() int {
	// Ensure we have the latest configuration data
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh working configs during ID allocation, using cached data",
			zap.Error(err))
	}

	maxID := 0
	for _, config := range p.workingConfigs {
		// Skip temporary IDs (like 65535) used during ordering
		if config.ID < 65535 && config.ID > maxID {
			maxID = config.ID
		}
	}

	// Start from 1 if no policies exist
	newID := maxID + 1
	if newID == 1 && len(p.workingConfigs) == 0 {
		newID = 1
	}

	p.logger.Debug("allocated new DNS tracking policy ID",
		zap.Int("new_id", newID),
		zap.Int("max_existing_id", maxID),
		zap.Int("total_policies", len(p.workingConfigs)))

	return newID
}

/*****************************************************************************
 * NAME: handlePolicyOrdering
 *
 * DESCRIPTION:
 *     Handles DNS tracking policy ordering based on the previous field.
 *     Uses converted internal data structure instead of protobuf access.
 *     Implements policy insertion and reordering logic.
 *
 * PARAMETERS:
 *     configData - Converted DNS tracking policy configuration data
 *     currentID  - Current policy ID
 *
 * RETURNS:
 *     error - Error if ordering fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) handlePolicyOrdering(configData *DnsTrackingPolicyConfig, currentID int) error {
	if configData.Previous == nil {
		return nil // No ordering needed if previous is not specified
	}

	p.logger.Debug("handling DNS tracking policy ordering",
		zap.Uint32("cookie", configData.Cookie),
		zap.Uint32("previous", *configData.Previous),
		zap.Int("current_id", currentID))

	previous := *configData.Previous

	// Determine target position based on previous cookie
	var targetPosition int
	if previous == 0 {
		// Insert at the beginning (position 1)
		targetPosition = 1
	} else if previous == uint32(0xFFFFFFFF) { // -1 as uint32
		// Append to the end - find max ID
		maxID := 0
		for _, config := range p.workingConfigs {
			if config.ID > maxID {
				maxID = config.ID
			}
		}
		targetPosition = maxID + 1
	} else {
		// Find the policy with the specified previous cookie
		previousPolicyID := -1
		for _, config := range p.workingConfigs {
			if config.Cookie == previous {
				previousPolicyID = config.ID
				break
			}
		}

		if previousPolicyID == -1 {
			p.logger.Warn("Previous policy cookie not found, appending to end",
				zap.Uint32("previous_cookie", previous))
			// If previous policy not found, append to end
			maxID := 0
			for _, config := range p.workingConfigs {
				if config.ID > maxID {
					maxID = config.ID
				}
			}
			targetPosition = maxID + 1
		} else {
			targetPosition = previousPolicyID + 1
		}
	}

	// If current position equals target position, no reordering needed
	if currentID == targetPosition {
		p.logger.Debug("DNS tracking policy already in correct position",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("current_id", currentID),
			zap.Int("target_position", targetPosition))
		return nil
	}

	p.logger.Info("Reordering DNS tracking policy",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("current_id", currentID),
		zap.Int("target_position", targetPosition))

	// Implement the actual reordering logic
	return p.movePolicyToPosition(currentID, targetPosition, configData.Cookie)
}

/*****************************************************************************
 * NAME: movePolicyToPosition
 *
 * DESCRIPTION:
 *     Moves a DNS tracking policy to the specified position by adjusting IDs.
 *     Implements both forward insertion and backward movement logic.
 *
 * PARAMETERS:
 *     currentID      - Current policy ID
 *     targetPosition - Target position for the policy
 *     cookie         - Policy cookie for logging
 *
 * RETURNS:
 *     error - Error if movement fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) movePolicyToPosition(currentID, targetPosition int, cookie uint32) error {
	p.logger.Debug("Moving DNS tracking policy to position",
		zap.Int("current_id", currentID),
		zap.Int("target_position", targetPosition),
		zap.Uint32("cookie", cookie))

	// Get current policies for batch processing
	currentPolicies, err := p.getCurrentPolicyOrderInfo()
	if err != nil {
		return fmt.Errorf("failed to get current policies for ordering: %w", err)
	}

	// Step 1: Set current policy to temporary ID to avoid conflicts
	const tempID = 65535
	if err := p.setPolicyID(currentID, tempID, cookie); err != nil {
		return fmt.Errorf("failed to set temporary ID: %w", err)
	}

	// Step 2: Adjust IDs of other policies based on movement direction
	var adjustErr error
	if currentID > targetPosition {
		// Forward insertion: current ID > target ID
		// Increment IDs in range [target, current-1] by +1 (from large to small)
		adjustErr = p.adjustPolicyIDsForwardInsertion(targetPosition, currentID-1, currentPolicies)
	} else if currentID < targetPosition {
		// Backward movement: current ID < target ID
		// Shift policies at target position and beyond by +1 (to make room for insertion)
		adjustErr = p.adjustPolicyIDsBackwardMovement(targetPosition, currentPolicies)
	}

	if adjustErr != nil {
		// Try to restore original ID if adjustment fails
		p.setPolicyID(tempID, currentID, cookie)
		return fmt.Errorf("failed to adjust policy IDs: %w", adjustErr)
	}

	// Step 3: Set policy to final target position
	if err := p.setPolicyID(tempID, targetPosition, cookie); err != nil {
		return fmt.Errorf("failed to set final position: %w", err)
	}

	p.logger.Info("DNS tracking policy moved successfully",
		zap.Int("from", currentID),
		zap.Int("to", targetPosition),
		zap.Uint32("cookie", cookie))

	return nil
}

/*****************************************************************************
 * NAME: getCurrentPolicyOrderInfo
 *
 * DESCRIPTION:
 *     Gets current DNS tracking policy order information for batch processing.
 *     Returns a list of policies with their IDs and cookies.
 *
 * RETURNS:
 *     []DnsPolicyOrderInfo - List of policy order information
 *     error                - Error if operation fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) getCurrentPolicyOrderInfo() ([]DnsPolicyOrderInfo, error) {
	var orderInfo []DnsPolicyOrderInfo

	// Get all current policies from working configs
	for _, config := range p.workingConfigs {
		orderInfo = append(orderInfo, DnsPolicyOrderInfo{
			ID:     config.ID,
			Cookie: config.Cookie,
		})
	}

	return orderInfo, nil
}

/*****************************************************************************
 * NAME: adjustPolicyIDsForwardInsertion
 *
 * DESCRIPTION:
 *     Adjusts policy IDs for forward insertion (current ID > target ID).
 *     Increments IDs in range [targetID, currentIDMinus1] by +1 (from large to small).
 *     Based on flow control policy implementation for robustness.
 *
 * PARAMETERS:
 *     targetID        - Target position ID
 *     currentIDMinus1 - Current ID minus 1 (end of range)
 *     currentPolicies - Current list of policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) adjustPolicyIDsForwardInsertion(targetID, currentIDMinus1 int, currentPolicies []DnsPolicyOrderInfo) error {
	p.logger.Debug("adjusting policy IDs for forward insertion",
		zap.Int("target_id", targetID),
		zap.Int("current_id_minus_1", currentIDMinus1))

	// Find policies in range [targetID, currentID-1] that need to be shifted
	var policiesToShift []DnsPolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID && policy.ID <= currentIDMinus1 {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID < policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by +1
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setPolicyID(policy.ID, newID, policy.Cookie); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID for forward insertion",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: adjustPolicyIDsBackwardMovement
 *
 * DESCRIPTION:
 *     Adjusts policy IDs for backward movement (current ID < target ID).
 *     Shifts policies at target position and beyond by +1 (to make room for insertion).
 *     Based on flow control policy implementation for robustness.
 *
 * PARAMETERS:
 *     targetID        - Target position ID
 *     currentPolicies - Current list of policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) adjustPolicyIDsBackwardMovement(targetID int, currentPolicies []DnsPolicyOrderInfo) error {
	p.logger.Debug("adjusting policy IDs for backward movement",
		zap.Int("target_id", targetID))

	// Find policies at target position and beyond that need to be shifted to make room
	var policiesToShift []DnsPolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID < policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by +1 to make room for insertion
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setPolicyID(policy.ID, newID, policy.Cookie); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID for backward movement",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: setPolicyID
 *
 * DESCRIPTION:
 *     Sets a DNS tracking policy to a specific ID using floweye dnrt set command.
 *     Uses simple ID change approach suitable for DNS tracking policies.
 *
 * PARAMETERS:
 *     oldID  - Current policy ID
 *     newID  - New policy ID
 *     cookie - Policy cookie for identification
 *
 * RETURNS:
 *     error - Error if ID setting fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) setPolicyID(oldID, newID int, cookie uint32) error {
	p.logger.Debug("setting DNS tracking policy ID",
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID),
		zap.Uint32("cookie", cookie))

	cmdArgs := []string{"dnrt", "set",
		"id=" + strconv.Itoa(oldID),
		"newid=" + strconv.Itoa(newID)}

	output, err := executeCommandDnsTracking(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to set DNS tracking policy ID",
			zap.Int("old_id", oldID),
			zap.Int("new_id", newID),
			zap.Uint32("cookie", cookie),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to set policy ID from %d to %d: %w", oldID, newID, err)
	}

	p.logger.Debug("DNS tracking policy ID set successfully",
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID),
		zap.Uint32("cookie", cookie))

	return nil
}

/*****************************************************************************
 * NAME: buildDnsPolicyCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for DNS tracking policy using configuration data.
 *     Adds essential configuration parameters to ensure consistency during ID changes.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - DNS tracking policy configuration data
 *
 * RETURNS:
 *     error - Error if building command fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) buildDnsPolicyCommand(cmdArgs *[]string, configData *DnsTrackingPolicyConfig) error {
	// Add basic policy configuration
	*cmdArgs = append(*cmdArgs, "cookie="+strconv.Itoa(int(configData.Cookie)))

	if configData.Desc != "" {
		*cmdArgs = append(*cmdArgs, "desc="+configData.Desc)
	}

	// Add domain group configuration
	if configData.DnsName != "" {
		*cmdArgs = append(*cmdArgs, "dnsname="+configData.DnsName)
	}

	// Add proxy configuration
	if configData.Pxy != "" {
		*cmdArgs = append(*cmdArgs, "pxy="+configData.Pxy)
	}

	if configData.BkupPxy != "" {
		*cmdArgs = append(*cmdArgs, "bkuppxy="+configData.BkupPxy)
	}

	// Add optional configuration parameters
	if configData.TrackHost {
		*cmdArgs = append(*cmdArgs, "trackhost=1")
	} else {
		*cmdArgs = append(*cmdArgs, "trackhost=0")
	}

	if configData.CacheTTL > 0 {
		*cmdArgs = append(*cmdArgs, "cachettl="+strconv.Itoa(configData.CacheTTL))
	} else {
		*cmdArgs = append(*cmdArgs, "cachettl=0")
	}

	if configData.DnsAddr != "" {
		*cmdArgs = append(*cmdArgs, "dnsaddr="+configData.DnsAddr)
	}

	// Add enable/disable status
	if configData.Enable {
		*cmdArgs = append(*cmdArgs, "disable=0")
	} else {
		*cmdArgs = append(*cmdArgs, "disable=1")
	}

	return nil
}

/*****************************************************************************
 * NAME: getCurrentPolicyIDByCookie
 *
 * DESCRIPTION:
 *     Gets the current policy ID for a given cookie by querying floweye directly.
 *     This ensures we get the real-time ID, not potentially stale cached data.
 *
 * PARAMETERS:
 *     cookie - Policy cookie to search for
 *
 * RETURNS:
 *     int   - Current policy ID
 *     error - Error if policy not found or query fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) getCurrentPolicyIDByCookie(cookie uint32) (int, error) {
	p.logger.Debug("getting current DNS tracking policy ID by cookie",
		zap.Uint32("cookie", cookie))

	// Get all current policies from floweye
	configs, err := GetLocalDnsTrackingPolicyConfigs(p.logger)
	if err != nil {
		return 0, fmt.Errorf("failed to get DNS tracking policies: %w", err)
	}

	// Find the policy with matching cookie
	for _, config := range configs {
		if config.Cookie == cookie {
			p.logger.Debug("found current DNS tracking policy ID by cookie",
				zap.Uint32("cookie", cookie),
				zap.Int("current_id", config.ID))
			return config.ID, nil
		}
	}

	return 0, fmt.Errorf("DNS tracking policy with cookie %d not found", cookie)
}

/*****************************************************************************
 * NAME: incrementPolicyID
 *
 * DESCRIPTION:
 *     Increments a DNS tracking policy ID by 1.
 *
 * PARAMETERS:
 *     currentID - Current policy ID
 *     cookie    - Policy cookie for logging context
 *
 * RETURNS:
 *     error - Error if increment fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) incrementPolicyID(currentID int, cookie uint32) error {
	return p.setPolicyID(currentID, currentID+1, cookie)
}

/*****************************************************************************
 * NAME: decrementPolicyID
 *
 * DESCRIPTION:
 *     Decrements a DNS tracking policy ID by 1.
 *
 * PARAMETERS:
 *     currentID - Current policy ID
 *     cookie    - Policy cookie for logging context
 *
 * RETURNS:
 *     error - Error if decrement fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) decrementPolicyID(currentID int, cookie uint32) error {
	return p.setPolicyID(currentID, currentID-1, cookie)
}

/*****************************************************************************
 * NAME: handlePolicyIDDecrement
 *
 * DESCRIPTION:
 *     Handles policy ID decrementation after deletion to maintain continuous ordering.
 *     All policies with ID > deleted ID will have their ID decremented by 1.
 *     Uses batch processing approach similar to flow control policy.
 *
 * PARAMETERS:
 *     deletedID - ID of the deleted policy
 *
 * RETURNS:
 *     error - Error if ID decrementation fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) handlePolicyIDDecrement(deletedID int) error {
	p.logger.Debug("handling policy ID decrementation after deletion",
		zap.Int("deleted_id", deletedID))

	// Get current policies for batch processing
	currentPolicies, err := p.getCurrentPolicyOrderInfo()
	if err != nil {
		return fmt.Errorf("failed to get current policies for ordering: %w", err)
	}

	// Find policies with ID > deleted ID that need to be decremented
	var policiesToShift []DnsPolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID > deletedID {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in ascending order (adjust from small to large to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID > policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by -1 to maintain continuous ordering
	for _, policy := range policiesToShift {
		newID := policy.ID - 1
		if err := p.setPolicyID(policy.ID, newID, policy.Cookie); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d after deletion: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID after deletion",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	p.logger.Debug("policy deletion ordering completed",
		zap.Int("deleted_id", deletedID),
		zap.Int("policies_shifted", len(policiesToShift)))

	return nil
}

/*****************************************************************************
 * NAME: handlePolicyEnableDisable
 *
 * DESCRIPTION:
 *     Handles DNS tracking policy enable/disable operation separately from policy creation/modification.
 *     Uses dedicated floweye dnrt set command with only disable parameter.
 *
 * PARAMETERS:
 *     cookie   - Policy cookie
 *     disable  - Whether to disable the policy (true=disable, false=enable)
 *     policyID - Policy ID
 *
 * RETURNS:
 *     error - Error if enable/disable operation fails
 *****************************************************************************/
func (p *DnsTrackingPolicyProcessor) handlePolicyEnableDisable(cookie uint32, disable bool, policyID int) error {
	p.logger.Debug("handling DNS tracking policy enable/disable",
		zap.Uint32("cookie", cookie),
		zap.Bool("disable", disable),
		zap.Int("policy_id", policyID))

	// Build floweye command for enable/disable operation
	disableValue := "0" // 0 = enable
	if disable {
		disableValue = "1" // 1 = disable
	}

	cmdArgs := []string{"dnrt", "set", "id=" + strconv.Itoa(policyID), "disable=" + disableValue}

	// Execute floweye command
	p.logger.Info("executing floweye enable/disable command for DNS tracking policy",
		zap.Uint32("cookie", cookie),
		zap.Int("id", policyID),
		zap.String("disable", disableValue),
		zap.Strings("args", cmdArgs))

	output, err := executeCommandDnsTracking(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye enable/disable command for DNS tracking policy",
			zap.Uint32("cookie", cookie),
			zap.Int("id", policyID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to %s DNS tracking policy: %w",
			map[bool]string{true: "disable", false: "enable"}[disable], err)
	}

	p.logger.Debug("floweye enable/disable command executed successfully",
		zap.Uint32("cookie", cookie),
		zap.Int("id", policyID),
		zap.String("operation", map[bool]string{true: "disabled", false: "enabled"}[disable]),
		zap.String("output", output))

	return nil
}
