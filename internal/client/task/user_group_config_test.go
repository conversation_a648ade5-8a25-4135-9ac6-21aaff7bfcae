/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_group_config_test.go
 *
 * DESCRIPTION :    Unit tests for user group configuration functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock for utils.ExecuteCommand
type UserGroupMockExecuteCommand struct {
	mock.Mock
}

func (m *UserGroupMockExecuteCommand) Execute(timeout int, command string, args ...string) (string, error) {
	var mockArgs mock.Arguments
	if len(args) > 0 {
		// 将参数一个一个传递，而不是作为一个切片
		mockArgs = m.Called(append([]interface{}{timeout, command}, utils.StringSliceToInterfaceSlice(args)...)...)
	} else {
		mockArgs = m.Called(timeout, command)
	}
	return mockArgs.String(0), mockArgs.Error(1)
}

// 使用user_config_test.go中定义的int32Ptr和stringPtr函数

// Setup test logger
func setupUserGroupTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level: "DEBUG",
		Outputs: []logger.Output{
			{
				Type: logger.TypeConsole,
			},
		},
	}
	log, _ := logger.NewLogger(logConfig)
	return log
}

// 使用一个不同的方法来处理mock
var currentMock *UserGroupMockExecuteCommand

// Setup mock for ExecuteCommand
func setupUserGroupMockExecuteCommand() (*UserGroupMockExecuteCommand, func()) {
	mockExecuteCommand := new(UserGroupMockExecuteCommand)

	// 设置当前的mock
	currentMock = mockExecuteCommand

	cleanup := func() {
		// 测试结束后清除当前的mock
		currentMock = nil
	}

	return mockExecuteCommand, cleanup
}

// Test GetLocalUserGroupConfigs
func TestGetLocalUserGroupConfigs(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Mock the list command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0\n"+
		"100 group1 1 default ************* ************* 101 101 1024 2048 0 0 101 ************* 0 reject eth0 0 ::/0",
		nil,
	)

	// Mock the get command for each user group
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
		"name=default\n"+
		"pid=0\n"+
		"start=0.0.0.0\n"+
		"end=0.0.0.0\n"+
		"ratein=0\n"+
		"rateout=0\n"+
		"ratein6=0\n"+
		"rateout6=0\n"+
		"dns=0.0.0.0\n"+
		"maxonlinetime=0\n"+
		"clntepa=reject\n"+
		"prefix=::\n"+
		"pfxlen=0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
		"name=group1\n"+
		"pid=1\n"+
		"start=*************\n"+
		"end=*************\n"+
		"ratein=1024\n"+
		"rateout=2048\n"+
		"ratein6=0\n"+
		"rateout6=0\n"+
		"dns=*******\n"+
		"maxonlinetime=24\n"+
		"clntepa=reject\n"+
		"prefix=::\n"+
		"pfxlen=0",
		nil,
	)

	// Call the function
	configs, err := GetLocalUserGroupConfigs(log)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, configs)
	assert.Equal(t, 2, len(configs))

	// Verify default group
	defaultGroup, exists := configs[1]
	assert.True(t, exists)
	assert.Equal(t, int32(1), defaultGroup.ID)
	assert.Equal(t, "default", defaultGroup.Name)
	assert.Equal(t, int32(0), defaultGroup.PID)
	assert.Equal(t, "0.0.0.0", defaultGroup.StartIP)
	assert.Equal(t, "0.0.0.0", defaultGroup.EndIP)
	assert.Equal(t, int32(0), defaultGroup.RateIn)
	assert.Equal(t, int32(0), defaultGroup.RateOut)
	assert.Equal(t, "reject", defaultGroup.ClntEpa)

	// Verify group1
	group1, exists := configs[100]
	assert.True(t, exists)
	assert.Equal(t, int32(100), group1.ID)
	assert.Equal(t, "group1", group1.Name)
	assert.Equal(t, int32(1), group1.PID)
	assert.Equal(t, "*************", group1.StartIP)
	assert.Equal(t, "*************", group1.EndIP)
	assert.Equal(t, int32(1024), group1.RateIn)
	assert.Equal(t, int32(2048), group1.RateOut)
	assert.Equal(t, "reject", group1.ClntEpa)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test GetUserGroupConfig
func TestGetUserGroupConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Mock the get command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
		"name=group1\n"+
		"pid=1\n"+
		"start=*************\n"+
		"end=*************\n"+
		"ratein=1024\n"+
		"rateout=2048\n"+
		"ratein6=0\n"+
		"rateout6=0\n"+
		"dns=*******\n"+
		"maxonlinetime=24\n"+
		"clntepa=reject\n"+
		"prefix=::\n"+
		"pfxlen=0",
		nil,
	)

	// Call the function
	config, err := GetUserGroupConfig(log, 100)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, int32(100), config.ID)
	assert.Equal(t, "group1", config.Name)
	assert.Equal(t, int32(1), config.PID)
	assert.Equal(t, "*************", config.StartIP)
	assert.Equal(t, "*************", config.EndIP)
	assert.Equal(t, int32(1024), config.RateIn)
	assert.Equal(t, int32(2048), config.RateOut)
	assert.Equal(t, "reject", config.ClntEpa)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test VerifyUserGroupConfig
func TestVerifyUserGroupConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Mock the get command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
		"name=group1\n"+
		"pid=1\n"+
		"start=*************\n"+
		"end=*************\n"+
		"ratein=1024\n"+
		"rateout=2048\n"+
		"ratein6=0\n"+
		"rateout6=0\n"+
		"dns=*******\n"+
		"maxonlinetime=24\n"+
		"clntepa=reject\n"+
		"prefix=::\n"+
		"pfxlen=0",
		nil,
	)

	// Create a user group task
	userGroupTask := &pb.UserGroupTask{
		Id:   100,
		Name: "group1",
		Pid:  1,
	}

	// Call the function
	success, err := VerifyUserGroupConfig(log, userGroupTask)

	// Verify results
	assert.NoError(t, err)
	assert.True(t, success)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test CompareUserGroupConfig
func TestCompareUserGroupConfig(t *testing.T) {
	log := setupUserGroupTestLogger()

	// Create a user group task
	userGroupTask := &pb.UserGroupTask{
		Id:   100,
		Name: "group1",
		Pid:  1,
	}

	// Create a matching local config
	localConfig := &UserGroupConfig{
		ID:      100,
		Name:    "group1",
		PID:     1,
		StartIP: "*************",
		EndIP:   "*************",
		RateIn:  1024,
		RateOut: 2048,
		ClntEpa: "reject",
	}

	// Test matching configs
	result := CompareUserGroupConfig(log, userGroupTask, localConfig)
	assert.True(t, result)

	// Test non-matching configs
	localConfig.Name = "different_name"
	result = CompareUserGroupConfig(log, userGroupTask, localConfig)
	assert.False(t, result)
}

// Test CompareUserGroupConfig with all fields
func TestCompareUserGroupConfig_AllFields(t *testing.T) {
	log := setupUserGroupTestLogger()

	// Create IP addresses for testing
	startIp := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{IpString: "*************"},
	}
	endIp := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{IpString: "*************"},
	}
	v6Ip := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{IpString: "2001:db8::1"},
	}

	// Create rate limits
	rateIn := int32(1024)
	rateOut := int32(2048)
	rateIn6 := int32(3072)
	rateOut6 := int32(4096)

	// Create max online time
	maxOnlineTime := int32(24)

	// Create expired account policy
	clntEpa := pb.UserExpiredPolicy_USER_EXPIRED_POLICY_REJECT

	// Create a user group task with all fields
	userGroupTask := &pb.UserGroupTask{
		Id:   100,
		Name: "group1",
		Pid:  1,
		V4Range: &pb.IpRange{
			StartIp: startIp,
			EndIp:   endIp,
		},
		V6Range: v6Ip,
		V4Rate: &pb.UserBwLimit{
			RateIn:  int32Ptr(rateIn),
			RateOut: int32Ptr(rateOut),
		},
		V6Rate: &pb.UserBwLimit{
			RateIn:  int32Ptr(rateIn6),
			RateOut: int32Ptr(rateOut6),
		},
		MaxOnlineTime: &maxOnlineTime,
		ClntEpa:       &clntEpa,
	}

	// Create a matching local config
	localConfig := &UserGroupConfig{
		ID:            100,
		Name:          "group1",
		PID:           1,
		StartIP:       "*************",
		EndIP:         "*************",
		Prefix:        "2001:db8::1",
		PrefixLen:     "64",
		RateIn:        1024,
		RateOut:       2048,
		RateIn6:       3072,
		RateOut6:      4096,
		MaxOnlineTime: 24,
		ClntEpa:       "reject",
	}

	// Test matching configs
	result := CompareUserGroupConfig(log, userGroupTask, localConfig)
	assert.True(t, result)

	// Test non-matching configs - change each field one by one
	tests := []struct {
		name     string
		modifyFn func()
	}{
		{
			name: "Different ID",
			modifyFn: func() {
				localConfig.ID = 101
			},
		},
		{
			name: "Different Name",
			modifyFn: func() {
				localConfig.Name = "different_name"
			},
		},
		{
			name: "Different PID",
			modifyFn: func() {
				localConfig.PID = 2
			},
		},
		{
			name: "Different StartIP",
			modifyFn: func() {
				localConfig.StartIP = "*************"
			},
		},
		{
			name: "Different EndIP",
			modifyFn: func() {
				localConfig.EndIP = "*************"
			},
		},
		{
			name: "Different Prefix",
			modifyFn: func() {
				localConfig.Prefix = "2001:db8::2"
			},
		},
		{
			name: "Different RateIn",
			modifyFn: func() {
				localConfig.RateIn = 1025
			},
		},
		{
			name: "Different RateOut",
			modifyFn: func() {
				localConfig.RateOut = 2049
			},
		},
		{
			name: "Different RateIn6",
			modifyFn: func() {
				localConfig.RateIn6 = 3073
			},
		},
		{
			name: "Different RateOut6",
			modifyFn: func() {
				localConfig.RateOut6 = 4097
			},
		},
		{
			name: "Different MaxOnlineTime",
			modifyFn: func() {
				localConfig.MaxOnlineTime = 25
			},
		},
		{
			name: "Different ClntEpa",
			modifyFn: func() {
				localConfig.ClntEpa = "login"
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Reset config
			localConfig = &UserGroupConfig{
				ID:            100,
				Name:          "group1",
				PID:           1,
				StartIP:       "*************",
				EndIP:         "*************",
				Prefix:        "2001:db8::1",
				PrefixLen:     "64",
				RateIn:        1024,
				RateOut:       2048,
				RateIn6:       3072,
				RateOut6:      4096,
				MaxOnlineTime: 24,
				ClntEpa:       "reject",
			}

			// Modify one field
			test.modifyFn()

			// Test non-matching configs
			result := CompareUserGroupConfig(log, userGroupTask, localConfig)
			assert.False(t, result)
		})
	}
}
