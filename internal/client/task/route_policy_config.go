/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      route_policy_config.go
 *
 * DESCRIPTION :    Route policy configuration data structures and parsing functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        05/06/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// Zone优先级范围常量定义
const (
	// CTRL_TIER_T1 - 平台连接策略层
	CTRL_TIER_T1_MIN_PRIORITY = 1
	CTRL_TIER_T1_MAX_PRIORITY = 5000

	// CUST_TIER_T2 - 自定义策略层
	CUST_TIER_T2_MIN_PRIORITY = 5001
	CUST_TIER_T2_MAX_PRIORITY = 50000

	// LPM_TIER_T3 - 最长前缀策略层
	LPM_TIER_T3_MIN_PRIORITY = 50001
	LPM_TIER_T3_MAX_PRIORITY = 60000

	// DEF_WAN_TIER_T4 - 默认路由策略层
	DEF_WAN_TIER_T4_MIN_PRIORITY = 60001
	DEF_WAN_TIER_T4_MAX_PRIORITY = 65535
)

/*****************************************************************************
 * NAME: RoutePolicyConfig
 *
 * DESCRIPTION:
 *     Represents a route policy configuration.
 *     Contains policy details including matching criteria and actions.
 *
 * FIELDS:
 *     ID          - Policy ID (local identifier)
 *     Cookie      - Policy cookie (unique identifier)
 *     Desc        - Policy description
 *     Previous    - Previous policy cookie for ordering
 *     Disable     - Whether the policy is disabled
 *     SchTime     - Policy time segment ID
 *     Src         - Source address specifications
 *     SrcPort     - Source port specifications
 *     UsrType     - User type (any/ippxy/nonippxy)
 *     Pool        - User group ID
 *     Dst         - Destination address specifications
 *     DstPort     - Destination port specifications
 *     Proto       - Protocol type
 *     App         - Application protocol specification
 *     InIf        - Source interface
 *     WanBw       - Uplink bandwidth limit
 *     WanBwOut    - Downlink bandwidth limit
 *     VLAN        - VLAN ID range
 *     TTL         - TTL value range
 *     DSCP        - DSCP value range
 *     Action      - Policy action (route/nat/dnat/proxy)
 *     Proxy       - Line name
 *     NextHop     - Next hop address
 *     NewDstIP    - DNAT target address
 *     NatIP       - SNAT address pool
 *     FullConeNat - Full cone NAT flag
 *     NoSnat      - No SNAT flag
 *****************************************************************************/
type RoutePolicyConfig struct {
	ID          int                // Policy ID (local identifier, 0 for new policies)
	Cookie      uint32             // Policy cookie (unique identifier)
	Desc        string             // Policy description
	Previous    uint32             // Previous policy cookie for ordering
	PreviousSet bool               // Whether Previous field was explicitly set
	Disable     bool               // Whether the policy is disabled
	SchTime     int32              // Policy time segment ID (int32 for protobuf compatibility)
	Zone        pb.RoutePolicyZone // Policy zone for layered sorting

	// Matching criteria - source
	Src     string // Source address specifications
	SrcPort string // Source port specifications
	UsrType string // User type (any/ippxy/nonippxy)
	Pool    int32  // User group ID (int32 for protobuf compatibility)

	// Matching criteria - destination
	Dst     string // Destination address specifications
	DstPort string // Destination port specifications
	Proto   string // Protocol type
	App     string // Application protocol specification

	// Interface and QoS
	InIf     string // Source interface
	WanBw    int32  // Uplink bandwidth limit (int32 for protobuf compatibility)
	WanBwOut int32  // Downlink bandwidth limit (int32 for protobuf compatibility)
	VLAN     string // VLAN ID range
	TTL      string // TTL value range
	DSCP     string // DSCP value range

	// Action configuration
	Action      string // Policy action (route/nat/dnat/proxy)
	Proxy       string // Line name
	NextHop     string // Next hop address
	NewDstIP    string // DNAT target address
	NatIP       string // SNAT address pool
	FullConeNat bool   // Full cone NAT flag
	NoSnat      bool   // No SNAT flag
}

// Note: RouteConfigData structure removed - now using unified RoutePolicyConfig structure
// This eliminates duplication and maintains consistency across the module

/*****************************************************************************
 * NAME: ParseRoutePolicyFromList
 *
 * DESCRIPTION:
 *     Parses route policy configurations from floweye route list json=1 output.
 *     Expected JSON format with policy details.
 *
 * PARAMETERS:
 *     output - Output from floweye route list json=1 command
 *
 * RETURNS:
 *     []*RoutePolicyConfig - List of parsed route policy configurations
 *     error                - Error if parsing fails
 *****************************************************************************/
func ParseRoutePolicyFromList(output string) ([]*RoutePolicyConfig, error) {
	var configs []*RoutePolicyConfig

	// Parse JSON output using unified floweye JSON parser
	jsonObjects, err := utils.ParseFloweyeJSON(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse floweye JSON output: %w", err)
	}

	for i, obj := range jsonObjects {
		// Marshal back to JSON then parse with existing function
		jsonBytes, err := json.Marshal(obj)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal object at index %d: %w", i, err)
		}

		config, err := parseRoutePolicyFromJSON(string(jsonBytes))
		if err != nil {
			return nil, fmt.Errorf("failed to parse route policy JSON at index %d: %w", i, err)
		}
		configs = append(configs, config)
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: parseRoutePolicyFromJSON
 *
 * DESCRIPTION:
 *     Parses a single route policy configuration from JSON string.
 *     Handles the JSON format returned by floweye route list command.
 *
 * PARAMETERS:
 *     jsonStr - JSON string for a single route policy configuration
 *
 * RETURNS:
 *     *RoutePolicyConfig - Parsed route policy configuration
 *     error              - Error if parsing fails
 *****************************************************************************/
func parseRoutePolicyFromJSON(jsonStr string) (*RoutePolicyConfig, error) {
	var policyData map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &policyData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	config := &RoutePolicyConfig{}

	// Parse basic fields
	if polno, ok := policyData["polno"].(float64); ok {
		config.ID = int(polno)
	}
	if desc, ok := policyData["desc"].(string); ok {
		config.Desc = desc
	}
	if disabled, ok := policyData["disabled"].(float64); ok {
		config.Disable = int(disabled) == 1
	}

	// Parse source criteria
	if srcip, ok := policyData["srcip"].(string); ok {
		config.Src = srcip
	}
	if srcport, ok := policyData["srcport"].(string); ok {
		config.SrcPort = srcport
	}
	if usrtype, ok := policyData["usrtype"].(string); ok {
		config.UsrType = usrtype
	}
	if pid, ok := policyData["pid"].(float64); ok {
		config.Pool = int32(pid)
	}

	// Parse destination criteria
	if dstip, ok := policyData["dstip"].(string); ok {
		config.Dst = dstip
	}
	if dstport, ok := policyData["dstport"].(string); ok {
		config.DstPort = dstport
	}
	if proto, ok := policyData["proto"].(string); ok {
		config.Proto = proto
	}
	if appname, ok := policyData["appname"].(string); ok {
		config.App = appname
	}

	// Parse interface and QoS
	if inif, ok := policyData["inif"].(string); ok {
		config.InIf = inif
	}
	if vlan, ok := policyData["vlan"].(string); ok {
		config.VLAN = vlan
	}
	if ttl, ok := policyData["ttl"].(string); ok {
		config.TTL = ttl
	}
	if dscp, ok := policyData["dscp"].(float64); ok {
		config.DSCP = fmt.Sprintf("%.0f", dscp)
	}

	// Parse action configuration
	if action, ok := policyData["action"].(string); ok {
		config.Action = action
	}
	// Fix: Parse proxy from correct field based on action type
	if nexthop, ok := policyData["nexthop"].(string); ok {
		config.Proxy = nexthop
	}
	// Parse gateway as next hop address
	if gateway, ok := policyData["gateway"].(string); ok {
		config.NextHop = gateway
	}
	if newdstip, ok := policyData["newdstip"].(string); ok && newdstip != "0.0.0.0" {
		config.NewDstIP = newdstip
	}
	if natip, ok := policyData["natip"].(string); ok && natip != " 0.0.0.0" {
		config.NatIP = strings.TrimSpace(natip)
	}
	if fullconenat, ok := policyData["fullconenat"].(float64); ok {
		config.FullConeNat = int(fullconenat) == 1
	}

	// Set zone based on policy ID (same as ParseRoutePolicyFromGet)
	if config.ID != 0 {
		config.Zone = GetZoneFromPriority(config.ID)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ParseRoutePolicyFromGet
 *
 * DESCRIPTION:
 *     Parses route policy configuration from floweye rtpolicy get output.
 *     Expected format: key=value pairs, one per line.
 *
 * PARAMETERS:
 *     output - Output from floweye rtpolicy get command
 *
 * RETURNS:
 *     *RoutePolicyConfig - Parsed route policy configuration
 *     error              - Error if parsing fails
 *****************************************************************************/
func ParseRoutePolicyFromGet(output string) (*RoutePolicyConfig, error) {
	config := &RoutePolicyConfig{}

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse key=value pairs
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) != 2 {
				continue
			}
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			switch key {
			case "id":
				if id, err := strconv.Atoi(value); err == nil {
					config.ID = id
				}
			case "cookie":
				if cookie, err := strconv.ParseUint(value, 10, 32); err == nil {
					config.Cookie = uint32(cookie)
				}
			case "desc":
				config.Desc = value
			case "disable":
				config.Disable = value == "1"
			case "schtime":
				if schtime, err := strconv.Atoi(value); err == nil {
					config.SchTime = int32(schtime)
				}
			case "src":
				config.Src = value
			case "sport":
				config.SrcPort = value
			case "usrtype":
				config.UsrType = value
			case "pool":
				if pool, err := strconv.Atoi(value); err == nil {
					config.Pool = int32(pool)
				}
			case "dst":
				config.Dst = value
			case "dport":
				config.DstPort = value
			case "proto":
				config.Proto = value
			case "appname":
				config.App = value
			case "inif":
				config.InIf = value
			case "wanbw":
				if wanbw, err := strconv.Atoi(value); err == nil {
					config.WanBw = int32(wanbw)
				}
			case "wanbwout":
				if wanbwout, err := strconv.Atoi(value); err == nil {
					config.WanBwOut = int32(wanbwout)
				}
			case "vlan":
				config.VLAN = value
			case "ttl":
				config.TTL = value
			case "dscp":
				config.DSCP = value
			case "action":
				config.Action = value
			case "actpxyname":
				config.Proxy = value
			case "nexthop":
				config.NextHop = value
			case "newdstip":
				if value != "0.0.0.0" {
					config.NewDstIP = value
				}
			case "natip":
				if value != "" && value != " 0.0.0.0" {
					config.NatIP = strings.TrimSpace(value)
				}
			case "fullconenat":
				config.FullConeNat = value == "1"
			case "nosnat":
				config.NoSnat = value == "1"
			}
		}
	}

	// Note: Cookie value 0 is valid for system default policies, so we don't check for it
	// The parsing is considered successful if we reach this point

	// Set zone based on policy ID
	if config.ID != 0 {
		config.Zone = GetZoneFromPriority(config.ID)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: CompareRoutePolicyConfig
 *
 * DESCRIPTION:
 *     Compares route policy configuration data with local configuration.
 *     Uses unified internal data structure to eliminate repeated protobuf parsing.
 *     Checks if the configurations match to determine if update is needed.
 *     Updated to use unified RoutePolicyConfig structure for both parameters.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Route policy configuration data (converted from protobuf)
 *     localConfig - Local route policy configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareRoutePolicyConfig(logger *logger.Logger, configData *RoutePolicyConfig, localConfig *RoutePolicyConfig) bool {
	if configData == nil || localConfig == nil {
		logger.Debug("route policy comparison: nil input")
		return false
	}

	// Compare basic fields using converted data
	if configData.Cookie != localConfig.Cookie {
		logger.Debug("route policy cookie mismatch",
			zap.Uint32("config_cookie", configData.Cookie),
			zap.Uint32("local_cookie", localConfig.Cookie))
		return false
	}

	if configData.Desc != localConfig.Desc {
		logger.Debug("route policy description mismatch",
			zap.String("config_desc", configData.Desc),
			zap.String("local_desc", localConfig.Desc))
		return false
	}

	if configData.Disable != localConfig.Disable {
		logger.Debug("route policy disable status mismatch",
			zap.Bool("config_disable", configData.Disable),
			zap.Bool("local_disable", localConfig.Disable))
		return false
	}

	if configData.SchTime != localConfig.SchTime {
		logger.Debug("route policy schtime mismatch",
			zap.Int32("config_sch_time", configData.SchTime),
			zap.Int32("local_schtime", localConfig.SchTime))
		return false
	}

	// Compare zone
	if configData.Zone != localConfig.Zone {
		logger.Debug("route policy zone mismatch",
			zap.String("config_zone", configData.Zone.String()),
			zap.String("local_zone", localConfig.Zone.String()))
		return false
	}

	// Note: Previous field is not part of the actual configuration - it's only used for ordering
	// We don't compare Previous field because it's not stored in floweye and is only a sorting hint

	// Compare action type using converted data
	if configData.Action != strings.ToLower(localConfig.Action) {
		logger.Debug("route policy action mismatch",
			zap.String("config_action", configData.Action),
			zap.String("local_action", localConfig.Action))
		return false
	}

	// Compare action-specific configuration using converted data
	if !compareActionConfigData(logger, configData, localConfig) {
		return false
	}

	// Compare matching criteria using converted data
	if !compareMatchingCriteriaData(logger, configData, localConfig) {
		return false
	}

	logger.Debug("route policy configurations match",
		zap.Uint32("cookie", configData.Cookie),
		zap.String("desc", configData.Desc))

	return true
}

/*****************************************************************************
 * NAME: compareActionConfigData
 *
 * DESCRIPTION:
 *     Compares action-specific configuration using converted data structure.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Handles different action types (route, nat, dnat, proxy).
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Route policy configuration data (converted from protobuf)
 *     localConfig - Local route policy configuration
 *
 * RETURNS:
 *     bool - True if action configurations match, false otherwise
 *****************************************************************************/
func compareActionConfigData(logger *logger.Logger, configData *RoutePolicyConfig, localConfig *RoutePolicyConfig) bool {
	// Compare proxy (required for all actions) using converted data
	if configData.Proxy != localConfig.Proxy {
		logger.Debug("route policy proxy mismatch",
			zap.String("config_proxy", configData.Proxy),
			zap.String("local_proxy", localConfig.Proxy))
		return false
	}

	// Compare action-specific fields using converted data
	switch configData.Action {
	case "route":
		return compareRouteActionConfigData(logger, configData, localConfig)
	case "nat":
		return compareNatActionConfigData(logger, configData, localConfig)
	case "dnat":
		return compareDnatActionConfigData(logger, configData, localConfig)
	}

	return true
}

/*****************************************************************************
 * NAME: compareRouteActionConfigData
 *
 * DESCRIPTION:
 *     Compares route action specific configuration using converted data.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Route policy configuration data (converted from protobuf)
 *     localConfig - Local route policy configuration
 *
 * RETURNS:
 *     bool - True if route action configurations match, false otherwise
 *****************************************************************************/
func compareRouteActionConfigData(logger *logger.Logger, configData *RoutePolicyConfig, localConfig *RoutePolicyConfig) bool {
	// Compare next hop using converted data
	// Treat empty string and "0.0.0.0" as equivalent for NextHop
	configNextHop := configData.NextHop
	localNextHop := localConfig.NextHop

	// Normalize empty string to "0.0.0.0" for comparison
	if configNextHop == "" {
		configNextHop = "0.0.0.0"
	}
	if localNextHop == "" {
		localNextHop = "0.0.0.0"
	}

	if configNextHop != localNextHop {
		logger.Debug("route policy next hop mismatch",
			zap.String("config_nexthop", configData.NextHop),
			zap.String("local_nexthop", localConfig.NextHop),
			zap.String("normalized_config_nexthop", configNextHop),
			zap.String("normalized_local_nexthop", localNextHop))
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: compareNatActionConfigData
 *
 * DESCRIPTION:
 *     Compares NAT action specific configuration using converted data.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Route policy configuration data (converted from protobuf)
 *     localConfig - Local route policy configuration
 *
 * RETURNS:
 *     bool - True if NAT action configurations match, false otherwise
 *****************************************************************************/
func compareNatActionConfigData(logger *logger.Logger, configData *RoutePolicyConfig, localConfig *RoutePolicyConfig) bool {
	// Compare NAT IP pool using converted data with format normalization
	configNatIP := NormalizeNatIPFormat(configData.NatIP)
	localNatIP := NormalizeNatIPFormat(localConfig.NatIP)

	if configNatIP != localNatIP {
		logger.Debug("route policy NAT IP mismatch",
			zap.String("config_natip", configData.NatIP),
			zap.String("local_natip", localConfig.NatIP),
			zap.String("normalized_config_natip", configNatIP),
			zap.String("normalized_local_natip", localNatIP))
		return false
	}

	// Compare full cone NAT flag using converted data
	if configData.FullConeNat != localConfig.FullConeNat {
		logger.Debug("route policy full cone NAT mismatch",
			zap.Bool("config_fullconenat", configData.FullConeNat),
			zap.Bool("local_fullconenat", localConfig.FullConeNat))
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: compareDnatActionConfigData
 *
 * DESCRIPTION:
 *     Compares DNAT action specific configuration using converted data.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Route policy configuration data (converted from protobuf)
 *     localConfig - Local route policy configuration
 *
 * RETURNS:
 *     bool - True if DNAT action configurations match, false otherwise
 *****************************************************************************/
func compareDnatActionConfigData(logger *logger.Logger, configData *RoutePolicyConfig, localConfig *RoutePolicyConfig) bool {
	// Compare new destination IP using converted data
	if configData.NewDstIP != localConfig.NewDstIP {
		logger.Debug("route policy new dst IP mismatch",
			zap.String("config_newdstip", configData.NewDstIP),
			zap.String("local_newdstip", localConfig.NewDstIP))
		return false
	}

	// Compare NAT IP pool (for SNAT part of DNAT) using converted data with format normalization
	configNatIP := NormalizeNatIPFormat(configData.NatIP)
	localNatIP := NormalizeNatIPFormat(localConfig.NatIP)

	if configNatIP != localNatIP {
		logger.Debug("route policy DNAT NAT IP mismatch",
			zap.String("config_natip", configData.NatIP),
			zap.String("local_natip", localConfig.NatIP),
			zap.String("normalized_config_natip", configNatIP),
			zap.String("normalized_local_natip", localNatIP))
		return false
	}

	// Compare no SNAT flag using converted data
	if configData.NoSnat != localConfig.NoSnat {
		logger.Debug("route policy no SNAT mismatch",
			zap.Bool("config_nosnat", configData.NoSnat),
			zap.Bool("local_nosnat", localConfig.NoSnat))
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: compareMatchingCriteriaData
 *
 * DESCRIPTION:
 *     Compares matching criteria using converted data structure.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Includes source/destination addresses, ports, protocols, etc.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Route policy configuration data (converted from protobuf)
 *     localConfig - Local route policy configuration
 *
 * RETURNS:
 *     bool - True if matching criteria match, false otherwise
 *****************************************************************************/
func compareMatchingCriteriaData(logger *logger.Logger, configData *RoutePolicyConfig, localConfig *RoutePolicyConfig) bool {
	// Compare source criteria using robust IP format comparison
	if !CompareIPFormats(configData.Src, localConfig.Src) {
		logger.Debug("route policy source address mismatch",
			zap.String("config_src", configData.Src),
			zap.String("local_src", localConfig.Src))
		return false
	}

	if configData.SrcPort != localConfig.SrcPort {
		logger.Debug("route policy source port mismatch",
			zap.String("config_srcport", configData.SrcPort),
			zap.String("local_srcport", localConfig.SrcPort))
		return false
	}

	// Compare user type using converted data
	if configData.UsrType != localConfig.UsrType {
		logger.Debug("route policy user type mismatch",
			zap.String("config_usrtype", configData.UsrType),
			zap.String("local_usrtype", localConfig.UsrType))
		return false
	}

	// Compare pool (user group ID) using converted data
	if configData.Pool != localConfig.Pool {
		logger.Debug("route policy pool mismatch",
			zap.Int32("config_pool", configData.Pool),
			zap.Int32("local_pool", localConfig.Pool))
		return false
	}

	// Compare destination criteria using robust IP format comparison
	if !CompareIPFormats(configData.Dst, localConfig.Dst) {
		logger.Debug("route policy destination address mismatch",
			zap.String("config_dst", configData.Dst),
			zap.String("local_dst", localConfig.Dst))
		return false
	}

	if configData.DstPort != localConfig.DstPort {
		logger.Debug("route policy destination port mismatch",
			zap.String("config_dstport", configData.DstPort),
			zap.String("local_dstport", localConfig.DstPort))
		return false
	}

	// Compare protocol using converted data
	if configData.Proto != localConfig.Proto && configData.App != localConfig.App {
		logger.Debug("route policy protocol mismatch",
			zap.String("config_proto", configData.Proto),
			zap.String("config_app", configData.App),
			zap.String("local_proto", localConfig.Proto),
			zap.String("local_app", localConfig.App))
		return false
	}

	// Compare interface and QoS settings using converted data
	// Special handling for "any" interface: when config specifies "any",
	// floweye may assign a specific interface, so we accept any specific interface
	if configData.InIf != localConfig.InIf {
		// If config specifies "any", accept any specific interface assignment
		if configData.InIf != "any" {
			logger.Debug("route policy interface mismatch",
				zap.String("config_inif", configData.InIf),
				zap.String("local_inif", localConfig.InIf))
			return false
		}
	}

	if configData.WanBw != localConfig.WanBw {
		logger.Debug("route policy WAN bandwidth mismatch",
			zap.Int32("config_wanbw", configData.WanBw),
			zap.Int32("local_wanbw", localConfig.WanBw))
		return false
	}

	if configData.WanBwOut != localConfig.WanBwOut {
		logger.Debug("route policy WAN bandwidth out mismatch",
			zap.Int32("config_wanbwout", configData.WanBwOut),
			zap.Int32("local_wanbwout", localConfig.WanBwOut))
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: getIpStringFromAddress
 *
 * DESCRIPTION:
 *     Extracts IP address string from IpAddress protobuf message.
 *
 * PARAMETERS:
 *     ipAddr - IP address protobuf message
 *
 * RETURNS:
 *     string - IP address as string
 *****************************************************************************/
func getIpStringFromAddress(ipAddr *pb.IpAddress) string {
	if ipAddr == nil {
		return ""
	}

	switch ip := ipAddr.GetIp().(type) {
	case *pb.IpAddress_IpString:
		return ip.IpString
	case *pb.IpAddress_Ipv4:
		// Convert uint32 to IP string
		ipv4 := ip.Ipv4
		return fmt.Sprintf("%d.%d.%d.%d",
			(ipv4>>24)&0xFF,
			(ipv4>>16)&0xFF,
			(ipv4>>8)&0xFF,
			ipv4&0xFF)
	case *pb.IpAddress_V4Cidr:
		return fmt.Sprintf("%s/%d", getIpStringFromAddress(&pb.IpAddress{Ip: &pb.IpAddress_Ipv4{Ipv4: ip.V4Cidr.GetIp()}}), ip.V4Cidr.GetPrefixLength())
	default:
		return ""
	}
}

/*****************************************************************************
 * NAME: buildNatIpStringFromPool
 *
 * DESCRIPTION:
 *     Builds NAT IP string from NAT IP pool specification for comparison.
 *
 * PARAMETERS:
 *     natIpPool - NAT IP pool specification
 *
 * RETURNS:
 *     string - Formatted NAT IP string
 *****************************************************************************/
func buildNatIpStringFromPool(natIpPool *pb.NatIpPool) string {
	var parts []string

	// Add single IPs
	for _, ip := range natIpPool.GetIp() {
		parts = append(parts, getIpStringFromAddress(ip))
	}

	// Add IP ranges
	for _, ipRange := range natIpPool.GetIpRanges() {
		parts = append(parts, getIpStringFromAddress(ipRange.GetStartIp())+"-"+getIpStringFromAddress(ipRange.GetEndIp()))
	}

	return strings.Join(parts, ",")
}

/*****************************************************************************
 * NAME: GetZonePriorityRange
 *
 * DESCRIPTION:
 *     获取指定zone的优先级范围。
 *
 * PARAMETERS:
 *     zone - 策略zone
 *
 * RETURNS:
 *     int - 最小优先级
 *     int - 最大优先级
 *****************************************************************************/
func GetZonePriorityRange(zone pb.RoutePolicyZone) (int, int) {
	switch zone {
	case pb.RoutePolicyZone_CTRL_TIER_T1:
		return CTRL_TIER_T1_MIN_PRIORITY, CTRL_TIER_T1_MAX_PRIORITY
	case pb.RoutePolicyZone_CUST_TIER_T2:
		return CUST_TIER_T2_MIN_PRIORITY, CUST_TIER_T2_MAX_PRIORITY
	case pb.RoutePolicyZone_LPM_TIER_T3:
		return LPM_TIER_T3_MIN_PRIORITY, LPM_TIER_T3_MAX_PRIORITY
	case pb.RoutePolicyZone_DEF_WAN_TIER_T4:
		return DEF_WAN_TIER_T4_MIN_PRIORITY, DEF_WAN_TIER_T4_MAX_PRIORITY
	default:
		// 默认返回CUST_TIER_T2范围
		return CUST_TIER_T2_MIN_PRIORITY, CUST_TIER_T2_MAX_PRIORITY
	}
}

/*****************************************************************************
 * NAME: GetZoneFromPriority
 *
 * DESCRIPTION:
 *     根据优先级值确定所属的zone。
 *
 * PARAMETERS:
 *     priority - 优先级值
 *
 * RETURNS:
 *     pb.RoutePolicyZone - 对应的zone
 *****************************************************************************/
func GetZoneFromPriority(priority int) pb.RoutePolicyZone {
	if priority >= CTRL_TIER_T1_MIN_PRIORITY && priority <= CTRL_TIER_T1_MAX_PRIORITY {
		return pb.RoutePolicyZone_CTRL_TIER_T1
	} else if priority >= CUST_TIER_T2_MIN_PRIORITY && priority <= CUST_TIER_T2_MAX_PRIORITY {
		return pb.RoutePolicyZone_CUST_TIER_T2
	} else if priority >= LPM_TIER_T3_MIN_PRIORITY && priority <= LPM_TIER_T3_MAX_PRIORITY {
		return pb.RoutePolicyZone_LPM_TIER_T3
	} else if priority >= DEF_WAN_TIER_T4_MIN_PRIORITY && priority <= DEF_WAN_TIER_T4_MAX_PRIORITY {
		return pb.RoutePolicyZone_DEF_WAN_TIER_T4
	}
	// 默认返回CUST_TIER_T2
	return pb.RoutePolicyZone_CUST_TIER_T2
}

/*****************************************************************************
 * NAME: ConvertRoutePolicyTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a route policy task protobuf message to unified internal data structure.
 *     This is the single conversion point for the entire processing pipeline.
 *     Handles all protobuf field types and optional fields with appropriate defaults.
 *     Renamed from ConvertRoutePolicyTaskToConfigData for consistency.
 *
 * PARAMETERS:
 *     task - Route policy task from orchestrator
 *
 * RETURNS:
 *     *RoutePolicyConfig - Converted internal data structure
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertRoutePolicyTaskToConfig(task *pb.RoutePolicyTask, logger *logger.Logger) (*RoutePolicyConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("RoutePolicyTask is nil")
	}

	data := &RoutePolicyConfig{
		// Basic fields with defaults (ID is 0 for new policies)
		ID:      0, // Will be assigned during creation
		Cookie:  task.GetCookie(),
		Desc:    task.GetDesc(),
		Disable: task.GetDisable(),
		SchTime: task.GetSchTime(),
		Zone:    task.GetZone(),

		// Handle Previous field for ordering - distinguish between unset and 0
		Previous:    task.GetPrevious(),
		PreviousSet: task.Previous != nil,

		// Matching criteria defaults
		Src:     "any",
		SrcPort: "any",
		UsrType: "any",
		Pool:    0,
		Dst:     "any",
		DstPort: "any",
		Proto:   "any",
		App:     "any",

		// Interface and QoS defaults
		InIf:     "any",
		WanBw:    0,
		WanBwOut: 0,
		VLAN:     "0-0",
		TTL:      "0-255",
		DSCP:     "0",

		// Action configuration defaults
		Action:      "route",
		Proxy:       "",
		NextHop:     "",
		NewDstIP:    "",
		NatIP:       "",
		FullConeNat: false,
		NoSnat:      false,
	}

	// Convert source address selectors
	if len(task.GetSrc()) > 0 {
		data.Src, _ = BuildAddressSelectorsString(task.GetSrc(), logger)
	}

	// Convert source port specification
	if task.GetSrcPort() != nil {
		data.SrcPort = ParsePortSpec(task.GetSrcPort())
	}

	// Convert user type
	switch task.GetUsrType() {
	case pb.UserType_USER_TYPE_ANY:
		data.UsrType = "any"
	case pb.UserType_USER_TYPE_IPPXY:
		data.UsrType = "ippxy"
	case pb.UserType_USER_TYPE_NONIPPXY:
		data.UsrType = "nonippxy"
	default:
		data.UsrType = "any"
	}

	// Convert pool (user group ID)
	data.Pool = task.GetPool()

	// Convert destination address selectors
	if len(task.GetDst()) > 0 {
		data.Dst, _ = BuildAddressSelectorsString(task.GetDst(), logger)
	}

	// Convert destination port specification
	if task.GetDstPort() != nil {
		data.DstPort = ParsePortSpec(task.GetDstPort())
	}

	// Convert protocol specification
	if task.GetProto() != nil {
		data.Proto, data.App = ParseAppProtocolSpec(task.GetProto())
	}

	// Convert interface and QoS settings
	if task.GetInIf() != "" {
		data.InIf = task.GetInIf()
	}
	data.WanBw = task.GetWanBw()
	data.WanBwOut = task.GetWanBwOut()

	// Convert action type
	switch task.GetAction() {
	case pb.RoutePolicyAction_ROUTE_ACTION_ROUTE:
		data.Action = "route"
	case pb.RoutePolicyAction_ROUTE_ACTION_NAT:
		data.Action = "nat"
	case pb.RoutePolicyAction_ROUTE_ACTION_DNAT:
		data.Action = "dnat"
	case pb.RoutePolicyAction_ROUTE_ACTION_PROXY:
		// Map proxy action to route action since floweye doesn't support proxy action
		data.Action = "route"
	default:
		data.Action = "route"
	}

	// Convert action-specific configuration
	// Note: route_config and nat_config are mutually exclusive - only one can exist
	if task.GetRouteConfig() != nil {
		// Handle route action configuration
		routeConfig := task.GetRouteConfig()
		data.Proxy = routeConfig.GetProxy()
		if routeConfig.GetNextHop() != nil {
			data.NextHop, _ = ConvertIpAddressToString(routeConfig.GetNextHop())
		}
	} else if task.GetNatConfig() != nil {
		// Handle NAT/DNAT action configuration
		natConfig := task.GetNatConfig()
		data.Proxy = natConfig.GetProxy()
		if natConfig.GetNewDstIp() != nil {
			data.NewDstIP, _ = ConvertIpAddressToString(natConfig.GetNewDstIp())
		}
		if natConfig.GetNatIp() != nil {
			data.NatIP = convertNatIpPoolToString(natConfig.GetNatIp())
		}
		if natConfig.GetNextHop() != nil {
			data.NextHop, _ = ConvertIpAddressToString(natConfig.GetNextHop())
		}
		data.FullConeNat = natConfig.GetFullConeNat()
		data.NoSnat = natConfig.GetNoSnat()
	}

	return data, nil
}

/*****************************************************************************
 * NAME: convertNatIpPoolToString
 *
 * DESCRIPTION:
 *     Converts NAT IP pool from protobuf to string format.
 *     Handles single IPs and IP ranges in the pool.
 *
 * PARAMETERS:
 *     natIpPool - NAT IP pool from protobuf
 *
 * RETURNS:
 *     string - Formatted NAT IP pool string
 *****************************************************************************/
func convertNatIpPoolToString(natIpPool *pb.NatIpPool) string {
	if natIpPool == nil {
		return ""
	}

	var parts []string

	// Add single IPs
	for _, ip := range natIpPool.GetIp() {
		ipStr, _ := ConvertIpAddressToString(ip)
		parts = append(parts, ipStr)
	}

	// Add IP ranges
	for _, ipRange := range natIpPool.GetIpRanges() {
		startIP, _ := ConvertIpAddressToString(ipRange.GetStartIp())
		endIP, _ := ConvertIpAddressToString(ipRange.GetEndIp())
		parts = append(parts, startIP+"-"+endIP)
	}

	return strings.Join(parts, ",")
}

/*****************************************************************************
 * NAME: VerifyRoutePolicyConfigData
 *
 * DESCRIPTION:
 *     Verifies route policy configuration using converted data structure.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Reuses comparison logic per CONTRIBUTING.md guidelines.
 *
 * PARAMETERS:
 *     logger     - Logger for verification operations
 *     configData - Route policy configuration data (converted from protobuf)
 *
 * RETURNS:
 *     bool  - True if policy exists and matches expected configuration
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyRoutePolicyConfig(logger *logger.Logger, configData *RoutePolicyConfig) (bool, error) {
	logger.Debug("verifying route policy configuration using converted data",
		zap.Uint32("cookie", configData.Cookie))

	// Use single-object retrieval for performance
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "rtpolicy", "get", "cookie="+strconv.FormatUint(uint64(configData.Cookie), 10))
	if err != nil {
		logger.Debug("route policy not found during verification",
			zap.Uint32("cookie", configData.Cookie),
			zap.Error(err))
		return false, nil
	}

	// Parse the configuration
	localConfig, err := ParseRoutePolicyFromGet(output)
	if err != nil {
		return false, fmt.Errorf("failed to parse route policy config during verification: %w", err)
	}

	// Verify cookie matches
	if localConfig.Cookie != configData.Cookie {
		logger.Warn("route policy cookie mismatch during verification",
			zap.Uint32("expected_cookie", configData.Cookie),
			zap.Uint32("actual_cookie", localConfig.Cookie))
		return false, nil
	}

	// Reuse comparison logic per CONTRIBUTING.md guidelines
	if !CompareRoutePolicyConfig(logger, configData, localConfig) {
		logger.Debug("route policy configuration verification failed: configurations do not match",
			zap.Uint32("cookie", configData.Cookie))
		return false, nil
	}

	logger.Debug("route policy configuration verification successful",
		zap.Uint32("cookie", configData.Cookie))
	return true, nil
}
