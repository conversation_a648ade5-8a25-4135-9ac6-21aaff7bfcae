// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : common_protobuf_utils.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"

	"go.uber.org/zap"
)

// GroupResolver defines an interface for resolving group names to IDs.
// This allows different modules to provide their own resolution logic.
type GroupResolver interface {
	ResolveIPGroupNameToID(groupName string) (int, error)
	ResolveDomainGroupNameToID(groupName string) (int, error)
}

// DefaultGroupResolver provides a default implementation of the GroupResolver interface using existing functions.
type DefaultGroupResolver struct {
	logger *logger.Logger // The logger instance.
}

// NewDefaultGroupResolver creates a new instance of DefaultGroupResolver.
func NewDefaultGroupResolver(logger *logger.Logger) *DefaultGroupResolver {
	return &DefaultGroupResolver{logger: logger}
}

// ResolveIPGroupNameToID resolves an IP group name to its corresponding ID.
func (r *DefaultGroupResolver) ResolveIPGroupNameToID(groupName string) (int, error) {
	return GetIpGroupIdByName(r.logger, groupName)
}

// ResolveDomainGroupNameToID resolves a domain group name to its corresponding ID.
func (r *DefaultGroupResolver) ResolveDomainGroupNameToID(groupName string) (int, error) {
	return GetDomainGroupIdByName(r.logger, groupName)
}

// ConvertIpAddressToString converts a protobuf IP address to its string representation.
// It handles all IP address formats: IPv4, IPv6, CIDR, and string.
//
// Parameters:
//   - ip: The protobuf IP address.
func ConvertIpAddressToString(ip *pb.IpAddress) (string, error) {
	if ip == nil {
		return "", fmt.Errorf("IP address is nil")
	}

	switch addr := ip.GetIp().(type) {
	case *pb.IpAddress_Ipv4:
		// Convert uint32 to IP string.
		ipv4 := addr.Ipv4
		return fmt.Sprintf("%d.%d.%d.%d",
			(ipv4>>24)&0xFF,
			(ipv4>>16)&0xFF,
			(ipv4>>8)&0xFF,
			ipv4&0xFF), nil

	case *pb.IpAddress_Ipv6:
		// Convert []byte to IPv6 string.
		ipv6 := addr.Ipv6
		if len(ipv6) != 16 {
			return "", fmt.Errorf("invalid IPv6 address length: %d", len(ipv6))
		}
		return fmt.Sprintf("%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x",
			ipv6[0], ipv6[1], ipv6[2], ipv6[3], ipv6[4], ipv6[5], ipv6[6], ipv6[7],
			ipv6[8], ipv6[9], ipv6[10], ipv6[11], ipv6[12], ipv6[13], ipv6[14], ipv6[15]), nil

	case *pb.IpAddress_IpString:
		return addr.IpString, nil

	case *pb.IpAddress_V4Cidr:
		if addr.V4Cidr != nil {
			ipv4 := addr.V4Cidr.GetIp()
			prefixLen := addr.V4Cidr.GetPrefixLength()
			return fmt.Sprintf("%d.%d.%d.%d/%d",
				(ipv4>>24)&0xFF,
				(ipv4>>16)&0xFF,
				(ipv4>>8)&0xFF,
				ipv4&0xFF,
				prefixLen), nil
		}

	case *pb.IpAddress_V6Cidr:
		if addr.V6Cidr != nil {
			ipv6 := addr.V6Cidr.GetIp()
			prefixLen := addr.V6Cidr.GetPrefixLength()
			if len(ipv6) == 16 {
				return fmt.Sprintf("%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x/%d",
					ipv6[0], ipv6[1], ipv6[2], ipv6[3], ipv6[4], ipv6[5], ipv6[6], ipv6[7],
					ipv6[8], ipv6[9], ipv6[10], ipv6[11], ipv6[12], ipv6[13], ipv6[14], ipv6[15],
					prefixLen), nil
			}
		}
	}

	return "", fmt.Errorf("unsupported IP address format")
}

// BuildAddressSelectorsString builds an address selector string from protobuf address selectors.
// It handles IP addresses, ranges, groups, and user accounts, using the provided GroupResolver for name-to-ID resolution.
// It returns the formatted address selector string or an error if building fails.
//
// Parameters:
//   - selectors: A list of address selectors from protobuf.
//   - logger: The logger for error reporting.
func BuildAddressSelectorsString(selectors []*pb.AddressSelector, logger *logger.Logger) (string, error) {
	if len(selectors) == 0 {
		return "any", nil
	}

	var parts []string

	for _, selector := range selectors {
		switch sel := selector.GetSelector().(type) {
		case *pb.AddressSelector_Ip:
			ipStr, err := ConvertIpAddressToString(sel.Ip)
			if err != nil {
				return "", fmt.Errorf("failed to convert IP address: %w", err)
			}
			parts = append(parts, ","+ipStr+",")

		case *pb.AddressSelector_IpRange:
			startIP, err := ConvertIpAddressToString(sel.IpRange.GetStartIp())
			if err != nil {
				return "", fmt.Errorf("failed to convert start IP: %w", err)
			}
			endIP, err := ConvertIpAddressToString(sel.IpRange.GetEndIp())
			if err != nil {
				return "", fmt.Errorf("failed to convert end IP: %w", err)
			}
			parts = append(parts, ","+startIP+"-"+endIP+",")

		case *pb.AddressSelector_IpGroupName:
			ipGroupID, err := GetIpGroupIdByName(logger, sel.IpGroupName)
			if err != nil {
				logger.Warn("failed to resolve IP group name, using fallback",
					zap.String("group_name", sel.IpGroupName),
					zap.Error(err))
				parts = append(parts, ",ipgroup:"+sel.IpGroupName+",")
			} else if ipGroupID == -1 {
				logger.Warn("IP group not found, using fallback",
					zap.String("group_name", sel.IpGroupName))
				parts = append(parts, ",ipgroup:"+sel.IpGroupName+",")
			} else {
				parts = append(parts, ","+strconv.Itoa(ipGroupID)+",")
			}
		case *pb.AddressSelector_MacGroupId:
			parts = append(parts, ",mac."+strconv.Itoa(int(sel.MacGroupId))+",")

		case *pb.AddressSelector_UserGroupId:
			parts = append(parts, ",pppoe."+strconv.Itoa(int(sel.UserGroupId))+",")

		case *pb.AddressSelector_Username:
			parts = append(parts, ",acct."+sel.Username+",")

		case *pb.AddressSelector_DomainGroupName:
			domainGroupID, err := GetDomainGroupIdByName(logger, sel.DomainGroupName)
			if err != nil {
				logger.Warn("failed to resolve domain group name, using fallback",
					zap.String("group_name", sel.DomainGroupName),
					zap.Error(err))
				parts = append(parts, ",dns:"+sel.DomainGroupName+",")
			} else if domainGroupID == -1 {
				logger.Warn("domain group not found, using fallback",
					zap.String("group_name", sel.DomainGroupName))
				parts = append(parts, ",dns:"+sel.DomainGroupName+",")
			} else {
				parts = append(parts, ",dns."+strconv.Itoa(domainGroupID)+",")
			}

		default:
			return "", fmt.Errorf("unsupported address selector type")
		}
	}

	return strings.Join(parts, ""), nil
}

// BuildIntRangeString builds a range string from an integer range specification,
// handling both single values and ranges. It returns a formatted range string (e.g., "100" or "100-200").
//
// Parameters:
//   - rangeSpec: The integer range specification.
func BuildIntRangeString(rangeSpec *pb.IntRange) string {
	if rangeSpec == nil {
		return "0"
	}

	if rangeSpec.GetStart() == rangeSpec.GetEnd() {
		return strconv.Itoa(int(rangeSpec.GetStart()))
	}
	return strconv.Itoa(int(rangeSpec.GetStart())) + "-" + strconv.Itoa(int(rangeSpec.GetEnd()))
}

// BuildPortRangeString builds a port range string from a port range specification,
// handling both single ports and port ranges. It returns a formatted port range string (e.g., "80" or "80-443").
//
// Parameters:
//   - rangeSpec: The port range specification.
func BuildPortRangeString(rangeSpec *pb.PortRange) string {
	if rangeSpec == nil {
		return "any"
	}

	if rangeSpec.GetStart() == rangeSpec.GetEnd() {
		return strconv.Itoa(int(rangeSpec.GetStart()))
	}
	return strconv.Itoa(int(rangeSpec.GetStart())) + "-" + strconv.Itoa(int(rangeSpec.GetEnd()))
}

// ValidateAddressSelectors validates an array of address selectors for correctness,
// checking for valid IP addresses, ranges, and group references. It returns an error if validation fails.
//
// Parameters:
//   - selectors: The address selector array to validate.
func ValidateAddressSelectors(selectors []*pb.AddressSelector) error {
	for i, selector := range selectors {
		if selector == nil {
			return fmt.Errorf("address selector %d is nil", i)
		}

		switch sel := selector.GetSelector().(type) {
		case *pb.AddressSelector_Ip:
			if err := ValidateIpAddress(sel.Ip); err != nil {
				return fmt.Errorf("invalid IP address in selector %d: %w", i, err)
			}

		case *pb.AddressSelector_IpRange:
			if sel.IpRange == nil {
				return fmt.Errorf("IP range in selector %d is nil", i)
			}
			if err := ValidateIpAddress(sel.IpRange.GetStartIp()); err != nil {
				return fmt.Errorf("invalid start IP in range selector %d: %w", i, err)
			}
			if err := ValidateIpAddress(sel.IpRange.GetEndIp()); err != nil {
				return fmt.Errorf("invalid end IP in range selector %d: %w", i, err)
			}

		case *pb.AddressSelector_IpGroupName:
			if sel.IpGroupName == "" {
				return fmt.Errorf("IP group name in selector %d is empty", i)
			}

		case *pb.AddressSelector_MacGroupId:
			if sel.MacGroupId <= 0 {
				return fmt.Errorf("invalid MAC group ID in selector %d: %d", i, sel.MacGroupId)
			}

		case *pb.AddressSelector_UserGroupId:
			if sel.UserGroupId <= 0 {
				return fmt.Errorf("invalid user group ID in selector %d: %d", i, sel.UserGroupId)
			}

		case *pb.AddressSelector_Username:
			if sel.Username == "" {
				return fmt.Errorf("username in selector %d is empty", i)
			}

		case *pb.AddressSelector_DomainGroupName:
			if sel.DomainGroupName == "" {
				return fmt.Errorf("domain group name in selector %d is empty", i)
			}

		default:
			return fmt.Errorf("unsupported address selector type in selector %d", i)
		}
	}

	return nil
}

// ValidateIpAddress validates a protobuf IP address message for correctness.
// It returns an error if validation fails.
//
// Parameters:
//   - ip: The IP address to validate.
func ValidateIpAddress(ip *pb.IpAddress) error {
	if ip == nil {
		return fmt.Errorf("IP address is nil")
	}

	switch addr := ip.GetIp().(type) {
	case *pb.IpAddress_Ipv4:
		// IPv4 validation - any uint32 is valid.
		return nil

	case *pb.IpAddress_Ipv6:
		if len(addr.Ipv6) != 16 {
			return fmt.Errorf("invalid IPv6 address length: %d, expected 16", len(addr.Ipv6))
		}
		return nil

	case *pb.IpAddress_IpString:
		if addr.IpString == "" {
			return fmt.Errorf("IP string is empty")
		}
		return nil

	case *pb.IpAddress_V4Cidr:
		if addr.V4Cidr == nil {
			return fmt.Errorf("V4 CIDR is nil")
		}
		if addr.V4Cidr.GetPrefixLength() > 32 {
			return fmt.Errorf("invalid IPv4 prefix length: %d", addr.V4Cidr.GetPrefixLength())
		}
		return nil

	case *pb.IpAddress_V6Cidr:
		if addr.V6Cidr == nil {
			return fmt.Errorf("V6 CIDR is nil")
		}
		if len(addr.V6Cidr.GetIp()) != 16 {
			return fmt.Errorf("invalid IPv6 CIDR address length: %d", len(addr.V6Cidr.GetIp()))
		}
		if addr.V6Cidr.GetPrefixLength() > 128 {
			return fmt.Errorf("invalid IPv6 prefix length: %d", addr.V6Cidr.GetPrefixLength())
		}
		return nil

	default:
		return fmt.Errorf("unsupported IP address format")
	}
}

// ParsePortSpec parses a protobuf PortSpec to a string format for floweye commands.
// It handles multiple port ranges and single ports, returning a formatted string (e.g., "80", "80-443", "80,443,8000-8999").
//
// Parameters:
//   - portSpec: The protobuf port specification.
func ParsePortSpec(portSpec *pb.PortSpec) string {
	if portSpec == nil || len(portSpec.GetPorts()) == 0 {
		return "any"
	}

	var parts []string
	for _, portRange := range portSpec.GetPorts() {
		if portRange == nil {
			continue
		}
		parts = append(parts, BuildPortRangeString(portRange))
	}

	if len(parts) == 0 {
		return "any"
	}

	return strings.Join(parts, ",")
}

// ParseAppProtocolSpec parses a protobuf AppProtocolSpec to app and protocol strings for floweye commands.
// It handles application names and protocol types, returning the app name and protocol type strings.
//
// Parameters:
//   - appProtoSpec: The protobuf application protocol specification.
func ParseAppProtocolSpec(appProtoSpec *pb.AppProtocolSpec) (string, string) {
	if appProtoSpec == nil {
		return "any", "any"
	}

	app := appProtoSpec.GetAppName()
	if app == "" {
		app = "any"
	}

	proto := appProtoSpec.GetAppProtocol()
	if proto == "" {
		proto = "any"
	}

	return app, proto
}

// ParseKeyValueOutput parses key-value output from floweye commands into a map.
// This is a common utility function used across multiple modules.
//
// Parameters:
//   - output: The raw output string from a floweye command.
func ParseKeyValueOutput(output string) map[string]string {
	configMap := make(map[string]string)
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			configMap[key] = value
		}
	}

	return configMap
}
