/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      sr_path_monitor_example.go
 *
 * DESCRIPTION :    Example usage of SR Path monitoring system
 *
 * AUTHOR :         wei
 *
 * HISTORY :        06/19/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: SRPathMonitorManager
 *
 * DESCRIPTION:
 *     Manager for integrating SR path monitoring with SR proxy processor.
 *     Provides high-level interface for managing SR path monitoring.
 *
 * FIELDS:
 *     logger  - Logger instance
 *     monitor - SR path monitor instance
 *     running - Flag indicating if monitoring is active
 *****************************************************************************/
type SRPathMonitorManager struct {
	logger  *logger.Logger
	monitor SRPathMonitor
	running bool
}

/*****************************************************************************
 * NAME: NewSRPathMonitorManager
 *
 * DESCRIPTION:
 *     Creates a new SR path monitor manager with default configuration.
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     *SRPathMonitorManager - New manager instance
 *****************************************************************************/
func NewSRPathMonitorManager(logger *logger.Logger) *SRPathMonitorManager {
	// 创建默认配置
	config := &SRMonitorConfig{
		CheckInterval:  10 * time.Second, // 每10秒检查一次
		MaxFailCount:   3,                // 连续3次失败后切换
		IsPathActiveFn: DefaultIsPathActiveFn(logger),
		SetPathFn:      DefaultSetPathFn(logger),
	}

	return &SRPathMonitorManager{
		logger:  logger.WithModule("sr-path-monitor-manager"),
		monitor: NewSRPathMonitor(logger, config),
		running: false,
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the SR path monitoring system.
 *
 * PARAMETERS:
 *     ctx - Context for lifecycle management
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (m *SRPathMonitorManager) Start(ctx context.Context) error {
	if m.running {
		return nil // 已经运行，幂等操作
	}

	err := m.monitor.Start(ctx)
	if err != nil {
		return fmt.Errorf("failed to start SR path monitor: %w", err)
	}

	m.running = true
	m.logger.Info("SR path monitor manager started")
	return nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the SR path monitoring system.
 *
 * RETURNS:
 *     error - Error if stop fails
 *****************************************************************************/
func (m *SRPathMonitorManager) Stop() error {
	if !m.running {
		return nil // 已经停止，幂等操作
	}

	err := m.monitor.Stop()
	if err != nil {
		return fmt.Errorf("failed to stop SR path monitor: %w", err)
	}

	m.running = false
	m.logger.Info("SR path monitor manager stopped")
	return nil
}

/*****************************************************************************
 * NAME: RegisterFromProtobuf
 *
 * DESCRIPTION:
 *     Registers an SR instance for monitoring from protobuf message.
 *     Converts protobuf SRpath messages to internal SRPath structures.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *     pbPaths - Protobuf SRpath messages
 *
 * RETURNS:
 *     error - Error if registration fails
 *****************************************************************************/
func (m *SRPathMonitorManager) RegisterFromProtobuf(srName string, pbPaths []*pb.SrPath) error {
	if len(pbPaths) == 0 {
		return fmt.Errorf("no paths provided for SR instance: %s", srName)
	}

	// 转换 protobuf 路径到内部结构
	paths := make([]SRPath, len(pbPaths))
	for i, pbPath := range pbPaths {
		paths[i] = SRPath{
			Links: make([]int32, len(pbPath.GetLinks())),
		}
		copy(paths[i].Links, pbPath.GetLinks())
	}

	err := m.monitor.Register(srName, paths)
	if err != nil {
		return fmt.Errorf("failed to register SR instance %s: %w", srName, err)
	}

	m.logger.Info("SR instance registered for path monitoring",
		zap.String("name", srName),
		zap.Int("path_count", len(paths)))

	return nil
}

/*****************************************************************************
 * NAME: RegisterFromConfig
 *
 * DESCRIPTION:
 *     Registers an SR instance for monitoring from SrProxyConfig.
 *     Converts the links array to a single path for monitoring.
 *
 * PARAMETERS:
 *     config - SR proxy configuration
 *
 * RETURNS:
 *     error - Error if registration fails
 *****************************************************************************/
func (m *SRPathMonitorManager) RegisterFromConfig(config *SrProxyConfig) error {
	if config == nil {
		return fmt.Errorf("SR proxy config is nil")
	}

	if len(config.Links) == 0 {
		return fmt.Errorf("no links in SR proxy config for: %s", config.Name)
	}

	// 将当前的 links 作为一个路径注册
	// 注意：这里假设控制器会下发多个路径，但当前只有一个路径
	// 实际使用时，应该从控制器获取多个路径
	paths := []SRPath{
		{
			Links: make([]int32, len(config.Links)),
		},
	}
	copy(paths[0].Links, config.Links)

	err := m.monitor.Register(config.Name, paths)
	if err != nil {
		return fmt.Errorf("failed to register SR instance %s: %w", config.Name, err)
	}

	m.logger.Info("SR instance registered from config",
		zap.String("name", config.Name),
		zap.Any("links", config.Links))

	return nil
}

/*****************************************************************************
 * NAME: Unregister
 *
 * DESCRIPTION:
 *     Removes an SR instance from monitoring.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *
 * RETURNS:
 *     error - Error if unregistration fails
 *****************************************************************************/
func (m *SRPathMonitorManager) Unregister(srName string) error {
	err := m.monitor.Unregister(srName)
	if err != nil {
		return fmt.Errorf("failed to unregister SR instance %s: %w", srName, err)
	}

	m.logger.Info("SR instance unregistered from monitoring",
		zap.String("name", srName))

	return nil
}

/*****************************************************************************
 * NAME: GetStatus
 *
 * DESCRIPTION:
 *     Gets the current monitoring status of an SR instance.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *
 * RETURNS:
 *     *SRPathInfo - Current status information
 *     error       - Error if SR instance not found
 *****************************************************************************/
func (m *SRPathMonitorManager) GetStatus(srName string) (*SRPathInfo, error) {
	return m.monitor.GetStatus(srName)
}

/*****************************************************************************
 * NAME: ExampleUsage
 *
 * DESCRIPTION:
 *     Example of how to use the SR path monitoring system.
 *****************************************************************************/
func ExampleUsage() {
	// 创建 logger
	logConfig := logger.LogConfig{
		Level:  logger.LevelInfo,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	logger, _ := logger.NewLogger(logConfig)

	// 创建监控管理器
	manager := NewSRPathMonitorManager(logger)

	// 启动监控
	ctx := context.Background()
	if err := manager.Start(ctx); err != nil {
		logger.Error("Failed to start SR path monitor", zap.Error(err))
		return
	}
	defer manager.Stop()

	// 示例1：从 protobuf 消息注册
	pbPaths := []*pb.SrPath{
		{Links: []int32{443, 999}},
		{Links: []int32{444, 1000}},
		{Links: []int32{445, 1001}},
	}

	if err := manager.RegisterFromProtobuf("sr1", pbPaths); err != nil {
		logger.Error("Failed to register SR from protobuf", zap.Error(err))
		return
	}

	// 示例2：从配置注册
	config := &SrProxyConfig{
		Name:  "sr2",
		Links: []int32{555, 666},
	}

	if err := manager.RegisterFromConfig(config); err != nil {
		logger.Error("Failed to register SR from config", zap.Error(err))
		return
	}

	// 查看状态
	status, err := manager.GetStatus("sr1")
	if err != nil {
		logger.Error("Failed to get SR status", zap.Error(err))
		return
	}

	logger.Info("SR status",
		zap.String("name", status.Name),
		zap.Int("active_path", status.ActivePath),
		zap.Int("fail_count", status.FailCount),
		zap.Time("last_check", status.LastCheck))

	// 运行一段时间让监控系统工作
	time.Sleep(2 * time.Minute)

	// 注销监控
	if err := manager.Unregister("sr1"); err != nil {
		logger.Error("Failed to unregister SR", zap.Error(err))
	}

	if err := manager.Unregister("sr2"); err != nil {
		logger.Error("Failed to unregister SR", zap.Error(err))
	}

	logger.Info("SR path monitoring example completed")
}
