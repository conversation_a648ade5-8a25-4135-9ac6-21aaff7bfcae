/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      effective_time_processor_test.go
 *
 * DESCRIPTION :    策略时段处理器单元测试
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/19/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	"agent/internal/pb"
	"agent/internal/utils"
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// 设置测试日志记录器
func setupEffectiveTimeTestLogger() *logger.Logger {
	log, _ := logger.NewLogger(&logger.Config{
		Level:  "debug",
		Format: "console",
	})
	return log
}

// MockExecutor 是一个模拟的命令执行器
type MockExecutor struct {
	mock.Mock
}

// Execute 实现 utils.Executor 接口
func (m *MockExecutor) Execute(timeout int, command string, args ...string) (string, error) {
	mockArgs := m.Called(timeout, command, args)
	return mockArgs.String(0), mockArgs.Error(1)
}

// 设置模拟的命令执行器
func setupEffectiveTimeMockExecutor() (*MockExecutor, func()) {
	mockExecutor := new(MockExecutor)
	originalExecutor := utils.NewShellExecutor
	utils.NewShellExecutor = func() utils.Executor {
		return mockExecutor
	}
	return mockExecutor, func() {
		utils.NewShellExecutor = originalExecutor
	}
}

// 测试 NewEffectiveTimeProcessor
func TestNewEffectiveTimeProcessor(t *testing.T) {
	log := setupEffectiveTimeTestLogger()
	mockExecutor, cleanup := setupEffectiveTimeMockExecutor()
	defer cleanup()

	// 设置模拟期望
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "list"}).Return("", nil)

	// 创建处理器
	processor := NewEffectiveTimeProcessor(log, mockExecutor)

	// 验证处理器
	assert.NotNil(t, processor)
	assert.Equal(t, pb.TaskType_TASK_EFFECTIVE_TIME, processor.GetTaskType())
	assert.NotNil(t, processor.localConfigs)
	assert.False(t, processor.fullSyncInProgress)

	// 验证所有模拟调用
	mockExecutor.AssertExpectations(t)
}

// 测试 ProcessTask 处理 NEW_CONFIG 动作
func TestEffectiveTimeProcessTaskNewConfig(t *testing.T) {
	// 在 CI 环境中跳过测试
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupEffectiveTimeTestLogger()
	mockExecutor, cleanup := setupEffectiveTimeMockExecutor()
	defer cleanup()

	// 设置模拟期望
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "list"}).Return("", nil)
	mockExecutor.On("Execute", 10, "floweye", []string{
		"rtptime", "add",
		"id=10",
		"name=test-time",
		"startwday=1",
		"endwday=5",
		"start=08:00:00",
		"end=18:00:00",
	}).Return("", nil)
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "get", "id=10"}).Return(
		"id=10\nname=test-time\nstartwday=1\nstarthour=8\nstartmin=0\nstartsec=0\nendwday=5\nendhour=18\nendmin=0\nendsec=0",
		nil,
	)

	// 创建处理器
	processor := NewEffectiveTimeProcessor(log, mockExecutor)

	// 创建测试任务
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_EFFECTIVE_TIME,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_EffectiveTimeTask{
			EffectiveTimeTask: &pb.EffectiveTimeTask{
				Id:   10,
				Name: "test-time",
				TimeRange: &pb.TimeSpec{
					StartDay: 1,
					EndDay:   5,
					StartTime: &pb.DailyTime{
						Hour: 8,
						Min:  0,
						Sec:  0,
					},
					EndTime: &pb.DailyTime{
						Hour: 18,
						Min:  0,
						Sec:  0,
					},
				},
			},
		},
	}

	// 处理任务
	result, err := processor.ProcessTask(context.Background(), task)

	// 验证结果
	assert.NoError(t, err)
	assert.Contains(t, result, "成功创建策略时段")

	// 验证所有模拟调用
	mockExecutor.AssertExpectations(t)
}

// 测试 ProcessTask 处理 EDIT_CONFIG 动作
func TestEffectiveTimeProcessTaskEditConfig(t *testing.T) {
	// 在 CI 环境中跳过测试
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupEffectiveTimeTestLogger()
	mockExecutor, cleanup := setupEffectiveTimeMockExecutor()
	defer cleanup()

	// 设置模拟期望
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "list"}).Return(
		"10 test-time 1 08 00 00 5 18 00 00",
		nil,
	)
	mockExecutor.On("Execute", 10, "floweye", []string{
		"rtptime", "set",
		"id=10",
		"name=test-time",
		"startwday=2",
		"endwday=6",
		"start=09:00:00",
		"end=19:00:00",
	}).Return("", nil)
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "get", "id=10"}).Return(
		"id=10\nname=test-time\nstartwday=2\nstarthour=9\nstartmin=0\nstartsec=0\nendwday=6\nendhour=19\nendmin=0\nendsec=0",
		nil,
	)

	// 创建处理器
	processor := NewEffectiveTimeProcessor(log, mockExecutor)

	// 创建测试任务
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_EFFECTIVE_TIME,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		Payload: &pb.DeviceTask_EffectiveTimeTask{
			EffectiveTimeTask: &pb.EffectiveTimeTask{
				Id:   10,
				Name: "test-time",
				TimeRange: &pb.TimeSpec{
					StartDay: 2,
					EndDay:   6,
					StartTime: &pb.DailyTime{
						Hour: 9,
						Min:  0,
						Sec:  0,
					},
					EndTime: &pb.DailyTime{
						Hour: 19,
						Min:  0,
						Sec:  0,
					},
				},
			},
		},
	}

	// 处理任务
	result, err := processor.ProcessTask(context.Background(), task)

	// 验证结果
	assert.NoError(t, err)
	assert.Contains(t, result, "成功修改策略时段")

	// 验证所有模拟调用
	mockExecutor.AssertExpectations(t)
}

// 测试 ProcessTask 处理 DELETE_CONFIG 动作
func TestEffectiveTimeProcessTaskDeleteConfig(t *testing.T) {
	// 在 CI 环境中跳过测试
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupEffectiveTimeTestLogger()
	mockExecutor, cleanup := setupEffectiveTimeMockExecutor()
	defer cleanup()

	// 设置模拟期望
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "list"}).Return(
		"10 test-time 1 08 00 00 5 18 00 00",
		nil,
	).Once()
	mockExecutor.On("Execute", 10, "floweye", []string{
		"rtptime", "remove",
		"id=10",
	}).Return("", nil)
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "list"}).Return(
		"",
		nil,
	).Once()

	// 创建处理器
	processor := NewEffectiveTimeProcessor(log, mockExecutor)

	// 创建测试任务
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_EFFECTIVE_TIME,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_EffectiveTimeTask{
			EffectiveTimeTask: &pb.EffectiveTimeTask{
				Id:   10,
				Name: "test-time",
			},
		},
	}

	// 处理任务
	result, err := processor.ProcessTask(context.Background(), task)

	// 验证结果
	assert.NoError(t, err)
	assert.Contains(t, result, "成功删除策略时段")

	// 验证所有模拟调用
	mockExecutor.AssertExpectations(t)
}

// 测试 StartFullSync 和 EndFullSync
func TestEffectiveTimeFullSync(t *testing.T) {
	// 在 CI 环境中跳过测试
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupEffectiveTimeTestLogger()
	mockExecutor, cleanup := setupEffectiveTimeMockExecutor()
	defer cleanup()

	// 设置模拟期望
	mockExecutor.On("Execute", 10, "floweye", []string{"rtptime", "list"}).Return(
		"10 test-time 1 08 00 00 5 18 00 00\n20 work-time 2 09 00 00 6 17 30 00",
		nil,
	).Once()
	mockExecutor.On("Execute", 10, "floweye", []string{
		"rtptime", "remove",
		"id=10",
	}).Return("", nil)
	mockExecutor.On("Execute", 10, "floweye", []string{
		"rtptime", "remove",
		"id=20",
	}).Return("", nil)

	// 创建处理器
	processor := NewEffectiveTimeProcessor(log, mockExecutor)

	// 开始全量同步
	err := processor.StartFullSync()
	assert.NoError(t, err)
	assert.True(t, processor.fullSyncInProgress)

	// 结束全量同步
	processor.EndFullSync()
	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localConfigs)

	// 验证所有模拟调用
	mockExecutor.AssertExpectations(t)
}
