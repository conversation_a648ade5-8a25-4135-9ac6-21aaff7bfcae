/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      wangroup_processor_test.go
 *
 * DESCRIPTION :    Tests for WAN Group processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"agent/internal/logger"
	pb "agent/internal/pb"
)

// setupWanGroupTestLogger creates a logger for testing
func setupWanGroupTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level: "DEBUG",
		Outputs: []logger.Output{
			{
				Type: logger.TypeConsole,
			},
		},
	}
	log, _ := logger.NewLogger(logConfig)
	return log
}

// TestWanGroupProcessor_GetTaskType tests the GetTaskType method
func TestWanGroupProcessor_GetTaskType(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_WAN_GROUP, taskType)
}

// TestWanGroupProcessor_ProcessTask_NilTask tests handling of nil task data
func TestWanGroupProcessor_ProcessTask_NilTask(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		// No WAN group task data
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "wan group task is nil")
}

// TestWanGroupProcessor_ProcessTask_UnsupportedAction tests handling of unsupported action
func TestWanGroupProcessor_ProcessTask_UnsupportedAction(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	// Create a task with an unsupported action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN_GROUP,
		TaskAction: 99, // Invalid action
		Payload: &pb.DeviceTask_WanGroupTask{
			WanGroupTask: &pb.WanGroupTask{
				Name: "group1",
				Id:   1,
				Type: pb.WanGroupType_WAN_GROUP_TYPE_SRCDST,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unknown task action")
}

// TestWanGroupProcessor_ProcessTask_MissingName tests handling of missing name
func TestWanGroupProcessor_ProcessTask_MissingName(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	// Create a task with missing name
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanGroupTask{
			WanGroupTask: &pb.WanGroupTask{
				// Name is missing
				Id:   1,
				Type: pb.WanGroupType_WAN_GROUP_TYPE_SRCDST,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "WAN group name is required")
}

// TestWanGroupProcessor_ProcessTask_InvalidId tests handling of invalid ID
func TestWanGroupProcessor_ProcessTask_InvalidId(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	// Create a task with invalid ID (too small)
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanGroupTask{
			WanGroupTask: &pb.WanGroupTask{
				Name: "group1",
				Id:   0, // Invalid ID (too small)
				Type: pb.WanGroupType_WAN_GROUP_TYPE_SRCDST,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "WAN group ID must be between 1 and 128")

	// Create a task with invalid ID (too large)
	task = &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanGroupTask{
			WanGroupTask: &pb.WanGroupTask{
				Name: "group1",
				Id:   129, // Invalid ID (too large)
				Type: pb.WanGroupType_WAN_GROUP_TYPE_SRCDST,
			},
		},
	}

	_, err = processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "WAN group ID must be between 1 and 128")
}

// TestWanGroupProcessor_StartFullSync tests the StartFullSync method
func TestWanGroupProcessor_StartFullSync(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	// Since we can't execute floweye commands in the test environment,
	// we'll just verify that the full sync flag is set
	processor.fullSyncInProgress = true
	assert.True(t, processor.fullSyncInProgress)
}

// TestWanGroupProcessor_EndFullSync tests the EndFullSync method
func TestWanGroupProcessor_EndFullSync(t *testing.T) {
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	// Start full sync
	processor.fullSyncInProgress = true

	// End full sync
	processor.EndFullSync()
	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localConfigs)
}

// TestGetWanGroupTypeString tests the GetWanGroupTypeString function
func TestGetWanGroupTypeString(t *testing.T) {
	// Test all WAN group types
	assert.Equal(t, "srcdst", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_SRCDST))
	assert.Equal(t, "spdp", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_SPDP))
	assert.Equal(t, "src", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_SRC))
	assert.Equal(t, "srcsport", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_SRCSPORT))
	assert.Equal(t, "dst", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_DST))
	assert.Equal(t, "dstdport", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_DSTDPORT))
	assert.Equal(t, "leftbw", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_RX_LEFTBW))
	assert.Equal(t, "leftbwup", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_TX_LEFTBW))
	assert.Equal(t, "minflow", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_SESSION))
	assert.Equal(t, "failover", GetWanGroupTypeString(pb.WanGroupType_WAN_GROUP_TYPE_FAILOVER))

	// Test default case
	assert.Equal(t, "srcdst", GetWanGroupTypeString(99)) // Invalid type should return default
}

// TestWanGroupProcessor_refreshLocalConfigs tests the refreshLocalConfigs method
func TestWanGroupProcessor_refreshLocalConfigs(t *testing.T) {
	// This test is limited since we can't execute floweye commands in the test environment
	// We'll just verify that the method exists and can be called without panicking
	log := setupWanGroupTestLogger()
	processor := NewWanGroupProcessor(log)

	// The method should return an error in the test environment since floweye is not available
	err := processor.refreshLocalConfigs()
	assert.Error(t, err)
}
