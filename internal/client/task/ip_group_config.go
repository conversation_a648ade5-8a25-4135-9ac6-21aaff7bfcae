/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      ip_group_config.go
 *
 * DESCRIPTION :    IP Group configuration data structures and helper functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/20/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: IpGroupConfig
 *
 * DESCRIPTION:
 *     Represents a local IP Group configuration.
 *     Used for caching and comparing IP Group configurations.
 *
 * FIELDS:
 *     ID       - IP Group ID
 *     Name     - IP Group name
 *     Members  - List of IP Group members
 *****************************************************************************/
type IpGroupConfig struct {
	ID      int              // IP Group ID
	Name    string           // IP Group name
	Members []*IpGroupMember // List of IP Group members
}

/*****************************************************************************
 * NAME: IpGroupMember
 *
 * DESCRIPTION:
 *     Represents an IP Group member.
 *     Contains IP address and optional note information.
 *
 * FIELDS:
 *     IP       - IP address, format: ip, ip/mask, ip-ip
 *     Info     - Note information
 *****************************************************************************/
type IpGroupMember struct {
	IP   string // IP address, format: ip, ip/mask, ip-ip
	Info string // Note information
}

/*****************************************************************************
 * NAME: IpGroupConfigData
 *
 * DESCRIPTION:
 *     Extended configuration structure for IP Group processing.
 *     Includes both parsed members and raw file content for optimal processing.
 *
 * FIELDS:
 *     IpGroupConfig - Embedded base configuration
 *     FileContent   - Raw file content for batch processing
 *     UseFileMode   - Flag indicating whether to use file mode for adding members
 *****************************************************************************/
type IpGroupConfigData struct {
	*IpGroupConfig
	FileContent []byte // Raw file content for batch processing
	UseFileMode bool   // Flag indicating whether to use file mode
}

/*****************************************************************************
 * NAME: ConvertIpGroupTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf IpGroupTask message to unified IpGroupConfigData structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and preserves file content for optimal batch processing when needed.
 *
 * PARAMETERS:
 *     ipGroupTask - Protobuf IP Group task message to convert
 *
 * RETURNS:
 *     *IpGroupConfigData - Converted IP Group configuration structure
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertIpGroupTaskToConfig(ipGroupTask *pb.IpGroupTask) (*IpGroupConfigData, error) {
	if ipGroupTask == nil {
		return nil, fmt.Errorf("ipGroupTask is nil")
	}

	// Initialize base configuration
	baseConfig := &IpGroupConfig{
		Name:    ipGroupTask.GetName(),
		Members: make([]*IpGroupMember, 0),
	}

	// Initialize extended configuration
	config := &IpGroupConfigData{
		IpGroupConfig: baseConfig,
		FileContent:   nil,
		UseFileMode:   false,
	}

	// Check if file content is provided (prioritize file mode for performance)
	if ipGroupTask.GetFileContent() != nil && len(ipGroupTask.GetFileContent()) > 0 {
		// Use file mode for batch processing
		config.FileContent = ipGroupTask.GetFileContent()
		config.UseFileMode = true

		// Skip content parsing when using file mode for optimal performance
		// IP Group module uses full replacement mode, no need for incremental comparison
	} else if len(ipGroupTask.GetMembers()) > 0 {
		// Use member list mode
		config.UseFileMode = false

		for _, member := range ipGroupTask.GetMembers() {
			// Get IP string representation
			var ipStr string

			// Handle different IP address types
			if member.GetIp() != nil {
				// Single IP address
				ipStr = utils.GetIpString(member.GetIp())
			} else if member.GetIpRange() != nil {
				// IP range
				ipStr = utils.GetIpRangeString(member.GetIpRange())
			} else {
				// Skip invalid members
				continue
			}

			// Get info with default value
			info := ""
			if member.GetInfo() != "" {
				info = member.GetInfo()
			}

			// Create internal member structure
			internalMember := &IpGroupMember{
				IP:   ipStr,
				Info: info,
			}

			config.Members = append(config.Members, internalMember)
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: GetIpGroupConfigs
 *
 * DESCRIPTION:
 *     Retrieves all IP Group configurations from the local system.
 *     Uses the floweye table list command to get the IP Group list.
 *     Since we always do a full replacement, we don't need to get detailed configuration.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*IpGroupConfig - Map of IP Group name to configuration
 *     error                     - Error if retrieval fails
 *****************************************************************************/
func GetIpGroupConfigs(logger *logger.Logger) (map[string]*IpGroupConfig, error) {
	logger.Debug("Getting all IP Group configurations")

	// Initialize configurations map
	configs := make(map[string]*IpGroupConfig)

	// Execute floweye command to get IP Group list
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "table", "list")
	if err != nil {
		logger.Error("Failed to execute floweye table list command",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get IP Group list: %w", err)
	}

	// Parse output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line, format: <ID> <name>
		parts := strings.SplitN(line, " ", 2)
		if len(parts) != 2 {
			continue
		}

		id, err := strconv.Atoi(parts[0])
		if err != nil {
			logger.Warn("Failed to parse IP Group ID",
				zap.String("line", line),
				zap.Error(err))
			continue
		}

		name := strings.TrimSpace(parts[1])
		if name == "" {
			continue
		}

		// Create IP Group configuration with just ID and name
		// Since we always do a full replacement, we don't need to get members
		config := &IpGroupConfig{
			ID:      id,
			Name:    name,
			Members: []*IpGroupMember{},
		}

		configs[name] = config
	}

	logger.Info("Retrieved all IP Group configurations", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: GetIpGroupConfig
 *
 * DESCRIPTION:
 *     Retrieves the configuration of a specific IP Group.
 *     Uses the floweye table get command to get detailed configuration.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - IP Group name
 *     id     - IP Group ID
 *
 * RETURNS:
 *     *IpGroupConfig - IP Group configuration
 *     error         - Error if retrieval fails
 *****************************************************************************/
func GetIpGroupConfig(logger *logger.Logger, name string, id int) (*IpGroupConfig, error) {
	logger.Debug("Getting IP Group configuration", zap.String("name", name), zap.Int("id", id))

	// Create IP Group configuration
	config := &IpGroupConfig{
		ID:      id,
		Name:    name,
		Members: make([]*IpGroupMember, 0),
	}

	// Execute floweye command to get detailed IP Group configuration
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "table", "get", fmt.Sprintf("name=%s", name))
	if err != nil {
		logger.Error("Failed to execute floweye table get command",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get IP Group configuration: %w", err)
	}

	// Parse output, each line format: <IP> <note>
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line, format: <IP> <note>
		parts := strings.SplitN(line, " ", 2)
		ip := parts[0]
		info := ""
		if len(parts) > 1 {
			info = strings.TrimSpace(parts[1])
		}

		member := &IpGroupMember{
			IP:   ip,
			Info: info,
		}

		config.Members = append(config.Members, member)
	}

	logger.Debug("Retrieved IP Group configuration",
		zap.String("name", name),
		zap.Int("id", id),
		zap.Int("members", len(config.Members)))
	return config, nil
}

/*****************************************************************************
 * NAME: GetIpGroupIdByName
 *
 * DESCRIPTION:
 *     Gets the ID of an IP Group by its name.
 *     Uses the floweye table list command to get the IP Group list, then finds the IP Group with the specified name.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - IP Group name
 *
 * RETURNS:
 *     int   - IP Group ID, returns -1 if not found
 *     error - Error if retrieval fails
 *****************************************************************************/
func GetIpGroupIdByName(logger *logger.Logger, name string) (int, error) {
	logger.Debug("Getting IP Group ID by name", zap.String("name", name))

	// Execute floweye command to get IP Group list
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "table", "list")
	if err != nil {
		logger.Error("Failed to execute floweye table list command",
			zap.Error(err),
			zap.String("output", output))
		return -1, fmt.Errorf("failed to get IP Group list: %w", err)
	}

	// Parse output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line, format: <ID> <name>
		parts := strings.SplitN(line, " ", 2)
		if len(parts) != 2 {
			continue
		}

		id, err := strconv.Atoi(parts[0])
		if err != nil {
			logger.Warn("Failed to parse IP Group ID",
				zap.String("line", line),
				zap.Error(err))
			continue
		}

		groupName := strings.TrimSpace(parts[1])
		if groupName == name {
			logger.Debug("Found IP Group ID", zap.String("name", name), zap.Int("id", id))
			return id, nil
		}
	}

	logger.Debug("IP Group not found", zap.String("name", name))
	return -1, nil
}




