/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_mapping_config.go
 *
 * DESCRIPTION :    Define iWAN Mapping configuration data structures and related operations
 *
 * AUTHOR :         wei
 *
 * HISTORY :        wei     04/18/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
)

// IwanMappingConfig represents an iWAN Mapping configuration
type IwanMappingConfig struct {
	Proxy    string // Proxy line name
	ProxyID  int    // Proxy line ID
	Port     int    // Mapping port
	Server   string // iWAN service name
	ServerID int    // iWAN service ID
}

// IwanMappingKey represents the unique identifier for an iWAN Mapping
type IwanMappingKey struct {
	Proxy string
	Port  int
}

// String returns the string representation of IwanMappingKey
func (k IwanMappingKey) String() string {
	return fmt.Sprintf("%s:%d", k.Proxy, k.Port)
}

// NewIwanMappingKey creates a new IwanMappingKey
func NewIwanMappingKey(proxy string, port int) IwanMappingKey {
	return IwanMappingKey{
		Proxy: proxy,
		Port:  port,
	}
}

// NewIwanMappingKeyFromTask creates an IwanMappingKey from a task
func NewIwanMappingKeyFromTask(task *pb.IwanMappingTask) IwanMappingKey {
	return IwanMappingKey{
		Proxy: task.GetProxy(),
		Port:  int(task.GetPort()),
	}
}

// NewIwanMappingKeyFromConfig creates an IwanMappingKey from a config
func NewIwanMappingKeyFromConfig(config *IwanMappingConfig) IwanMappingKey {
	return IwanMappingKey{
		Proxy: config.Proxy,
		Port:  config.Port,
	}
}

/*****************************************************************************
 * NAME: ConvertIwanMappingTaskToConfig
 *
 * DESCRIPTION:
 *     Converts protobuf IwanMappingTask to internal IwanMappingConfig structure.
 *     This function performs the single conversion from protobuf to internal
 *     data structure, eliminating the need for repeated protobuf parsing
 *     throughout the processing pipeline.
 *
 * PARAMETERS:
 *     task - Protobuf IwanMappingTask to convert
 *
 * RETURNS:
 *     *IwanMappingConfig - Converted internal configuration structure
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertIwanMappingTaskToConfig(task *pb.IwanMappingTask) (*IwanMappingConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("iwanMappingTask is nil")
	}

	// Validate required fields
	if task.GetProxy() == "" {
		return nil, fmt.Errorf("proxy is required")
	}
	if task.GetPort() <= 0 {
		return nil, fmt.Errorf("port is required and must be positive")
	}

	config := &IwanMappingConfig{
		// Basic fields from protobuf
		Proxy: task.GetProxy(),
		Port:  int(task.GetPort()),
		// Handle optional server field - empty string for delete operations
		Server: task.GetServer(), // GetServer() returns "" if nil or empty

		// Fields not in protobuf - set to default values
		// These will be populated from floweye command output when needed
		ProxyID:  0,
		ServerID: 0,
	}

	return config, nil
}

/*****************************************************************************
 * NAME: GetIwanMappingConfigs
 *
 * DESCRIPTION:
 *     Retrieves all iWAN Mapping configurations from the system.
 *     Executes floweye iwanmap list command and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[IwanMappingKey]IwanMappingConfig - Map of iWAN Mapping configurations indexed by key
 *     error - Error if retrieving configurations fails
 *****************************************************************************/
func GetIwanMappingConfigs(logger *logger.Logger) (map[IwanMappingKey]IwanMappingConfig, error) {
	logger.Debug("Retrieving all iWAN Mapping configurations")

	// Execute floweye iwanmap list command
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "iwanmap", "list")
	if err != nil {
		return nil, fmt.Errorf("failed to execute iwanmap list command: %w", err)
	}

	// Create map to store configurations
	configs := make(map[IwanMappingKey]IwanMappingConfig)

	// Parse command output
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}

		// Parse line content
		// Format: <proxy_name> <proxy_id> <port> <iwan_service> <iwan_service_id> <unknown_field>
		fields := strings.Fields(line)
		if len(fields) < 6 {
			logger.Warn("Invalid iwanmap list output format", zap.String("line", line))
			continue
		}

		proxyName := fields[0]
		proxyID, err := strconv.Atoi(fields[1])
		if err != nil {
			logger.Warn("Invalid proxy ID", zap.String("value", fields[1]), zap.Error(err))
			continue
		}

		port, err := strconv.Atoi(fields[2])
		if err != nil {
			logger.Warn("Invalid port", zap.String("value", fields[2]), zap.Error(err))
			continue
		}

		serverName := fields[3]
		serverID, err := strconv.Atoi(fields[4])
		if err != nil {
			logger.Warn("Invalid server ID", zap.String("value", fields[4]), zap.Error(err))
			continue
		}

		// Create configuration object
		config := IwanMappingConfig{
			Proxy:    proxyName,
			ProxyID:  proxyID,
			Port:     port,
			Server:   serverName,
			ServerID: serverID,
		}

		// Add to configurations map
		key := NewIwanMappingKey(proxyName, port)
		configs[key] = config
	}

	logger.Debug("Retrieved iWAN Mapping configurations", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: CompareIwanMappingConfig
 *
 * DESCRIPTION:
 *     Compares an iWAN Mapping configuration with a local configuration.
 *     Checks if the expected configuration matches the local configuration.
 *     Uses converted data structure to eliminate repeated protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected iWAN Mapping configuration (converted from protobuf)
 *     localConfig    - Local iWAN Mapping configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareIwanMappingConfig(logger *logger.Logger, expectedConfig *IwanMappingConfig, localConfig *IwanMappingConfig) bool {
	if expectedConfig == nil || localConfig == nil {
		logger.Debug("One of the configurations is nil")
		return false
	}

	logger.Debug("Comparing iWAN Mapping configuration",
		zap.String("proxy", expectedConfig.Proxy),
		zap.Int("port", expectedConfig.Port))

	// Compare basic fields
	if expectedConfig.Proxy != localConfig.Proxy {
		logger.Debug("iWAN Mapping proxy mismatch",
			zap.String("expected", expectedConfig.Proxy),
			zap.String("actual", localConfig.Proxy))
		return false
	}

	if expectedConfig.Port != localConfig.Port {
		logger.Debug("iWAN Mapping port mismatch",
			zap.Int("expected", expectedConfig.Port),
			zap.Int("actual", localConfig.Port))
		return false
	}

	if expectedConfig.Server != localConfig.Server {
		logger.Debug("iWAN Mapping server mismatch",
			zap.String("expected", expectedConfig.Server),
			zap.String("actual", localConfig.Server))
		return false
	}

	// Configurations match
	logger.Debug("iWAN Mapping configurations match",
		zap.String("proxy", expectedConfig.Proxy),
		zap.Int("port", expectedConfig.Port))

	return true
}

/*****************************************************************************
 * NAME: VerifyIwanMappingConfig
 *
 * DESCRIPTION:
 *     Verifies if configuration was successfully applied using optimized
 *     single-object verification instead of refreshing all configurations.
 *     This improves performance in environments with many mappings.
 *     Updated to use internal IwanMappingConfig structure instead of protobuf.
 *
 * PARAMETERS:
 *     ctx - Context for the operation
 *     logger - Logger instance for logging operations
 *     localConfigs - Map of local configurations (not used in optimized version)
 *     configData - Internal configuration data to verify
 *
 * RETURNS:
 *     bool - Whether verification passed
 *     error - Error during verification
 *****************************************************************************/
func VerifyIwanMappingConfig(ctx context.Context, logger *logger.Logger, localConfigs map[IwanMappingKey]IwanMappingConfig, configData *IwanMappingConfig) (bool, error) {
	// Use optimized single-object verification instead of full refresh
	// This significantly improves performance in environments with many mappings
	actualConfig, err := GetSingleIwanMappingConfig(logger, configData.Proxy, configData.Port)
	if err != nil {
		// If error indicates mapping doesn't exist, check if this is expected
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "NEXIST") {
			// For delete operations, mapping should not exist
			if configData.Server == "" {
				return true, nil // Successfully deleted
			}
			// For add/edit operations, mapping should exist
			return false, nil
		}
		return false, fmt.Errorf("failed to get mapping configuration for verification: %w", err)
	}

	// Check if configuration matches the expected state
	// If server is empty, it indicates a delete operation, should check if configuration no longer exists
	if configData.Server == "" {
		// For delete operations, configuration should not exist
		return false, nil // Return false because configuration still exists
	}

	// For add or edit operations, use CompareIwanMappingConfig to verify
	if !CompareIwanMappingConfig(logger, configData, actualConfig) {
		logger.Error("iWAN Mapping configuration verification failed",
			zap.String("proxy", configData.Proxy),
			zap.Int("port", configData.Port))
		return false, nil
	}

	return true, nil
}

/*****************************************************************************
 * NAME: GetSingleIwanMappingConfig
 *
 * DESCRIPTION:
 *     Retrieves configuration for a single iWAN mapping using optimized
 *     approach. Since there's no direct single-mapping query command,
 *     we use the list command but only parse the specific mapping we need.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     proxy  - Proxy name to retrieve mapping for
 *     port   - Port number to retrieve mapping for
 *
 * RETURNS:
 *     *IwanMappingConfig - Mapping configuration
 *     error              - Error if retrieval fails
 *****************************************************************************/
func GetSingleIwanMappingConfig(logger *logger.Logger, proxy string, port int) (*IwanMappingConfig, error) {
	logger.Debug("retrieving single iWAN mapping configuration for verification",
		zap.String("proxy", proxy),
		zap.Int("port", port))

	// Execute floweye iwanmap list command
	// Note: There doesn't appear to be a single-mapping query command,
	// so we use list but optimize by only parsing the specific mapping
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "iwanmap", "list")
	if err != nil {
		return nil, fmt.Errorf("failed to get iWAN mapping list: %w", err)
	}

	// Parse output to find the specific mapping
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 6 {
			continue
		}

		// Parse proxy name and port
		proxyName := fields[0]
		proxyPort, err := strconv.Atoi(fields[2])
		if err != nil {
			continue
		}

		// Check if this is the mapping we're looking for
		if proxyName == proxy && proxyPort == port {
			// Found the mapping, parse the rest
			proxyID, err := strconv.Atoi(fields[1])
			if err != nil {
				return nil, fmt.Errorf("invalid proxy ID: %s", fields[1])
			}

			serverName := fields[3]
			serverID, err := strconv.Atoi(fields[4])
			if err != nil {
				return nil, fmt.Errorf("invalid server ID: %s", fields[4])
			}

			config := &IwanMappingConfig{
				Proxy:    proxyName,
				ProxyID:  proxyID,
				Port:     proxyPort,
				Server:   serverName,
				ServerID: serverID,
			}

			logger.Debug("successfully retrieved single iWAN mapping configuration",
				zap.String("proxy", proxy),
				zap.Int("port", port),
				zap.String("server", serverName))

			return config, nil
		}
	}

	// Mapping not found
	return nil, fmt.Errorf("iWAN mapping not found for proxy=%s, port=%d", proxy, port)
}
