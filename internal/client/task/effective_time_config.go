/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      effective_time_config.go
 *
 * DESCRIPTION :    Effective time configuration data structures and helper functions
 *
 * AUTHOR :			wei
 *
 * HISTORY :        04/19/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: EffectiveTimeConfig
 *
 * DESCRIPTION:
 *     Represents an effective time configuration
 *
 * FIELDS:
 *     ID        - Effective time ID (unique identifier)
 *     Name      - Effective time name
 *     StartWDay - Start day of week
 *     StartHour - Start hour
 *     StartMin  - Start minute
 *     StartSec  - Start second
 *     EndWDay   - End day of week
 *     EndHour   - End hour
 *     EndMin    - End minute
 *     EndSec    - End second
 *****************************************************************************/
type EffectiveTimeConfig struct {
	ID        int    // Effective time ID
	Name      string // Effective time name
	StartWDay int    // Start day of week
	StartHour int    // Start hour
	StartMin  int    // Start minute
	StartSec  int    // Start second
	EndWDay   int    // End day of week
	EndHour   int    // End hour
	EndMin    int    // End minute
	EndSec    int    // End second
}

/*****************************************************************************
 * NAME: ConvertEffectiveTimeTaskToConfig
 *
 * DESCRIPTION:
 *     Converts protobuf EffectiveTimeTask to internal EffectiveTimeConfig structure
 *     This is the single conversion point for protobuf optimization
 *
 * PARAMETERS:
 *     task - Protobuf EffectiveTimeTask to convert
 *
 * RETURNS:
 *     *EffectiveTimeConfig - Converted internal configuration
 *     error                - Error if conversion fails
 *****************************************************************************/
func ConvertEffectiveTimeTaskToConfig(task *pb.EffectiveTimeTask) (*EffectiveTimeConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("effectiveTimeTask is nil")
	}

	config := &EffectiveTimeConfig{
		ID:   int(task.GetId()),
		Name: task.GetName(),
	}

	// Handle time range conversion
	timeRange := task.GetTimeRange()
	if timeRange != nil {
		config.StartWDay = int(timeRange.GetStartDay())
		config.EndWDay = int(timeRange.GetEndDay())

		// Handle start time
		startTime := timeRange.GetStartTime()
		if startTime != nil {
			config.StartHour = int(startTime.GetHour())
			config.StartMin = int(startTime.GetMin())
			config.StartSec = int(startTime.GetSec())
		}

		// Handle end time
		endTime := timeRange.GetEndTime()
		if endTime != nil {
			config.EndHour = int(endTime.GetHour())
			config.EndMin = int(endTime.GetMin())
			config.EndSec = int(endTime.GetSec())
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: CompareEffectiveTimeConfig
 *
 * DESCRIPTION:
 *     Compares effective time configuration with local configuration
 *     Used by verification functions to avoid code duplication
 *
 * PARAMETERS:
 *     logger      - Logger instance
 *     configData  - Effective time configuration data to compare
 *     localConfig - Local effective time configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareEffectiveTimeConfig(logger *logger.Logger, configData *EffectiveTimeConfig, localConfig *EffectiveTimeConfig) bool {
	// Check if name matches
	if configData.Name != localConfig.Name {
		logger.Debug("Effective time name mismatch",
			zap.String("expected", configData.Name),
			zap.String("actual", localConfig.Name))
		return false
	}

	// Check if start day matches
	if configData.StartWDay != localConfig.StartWDay {
		logger.Debug("Effective time start day mismatch",
			zap.Int("expected_start_day", configData.StartWDay),
			zap.Int("actual_start_day", localConfig.StartWDay))
		return false
	}

	// Check if end day matches
	if configData.EndWDay != localConfig.EndWDay {
		logger.Debug("Effective time end day mismatch",
			zap.Int("expected_end_day", configData.EndWDay),
			zap.Int("actual_end_day", localConfig.EndWDay))
		return false
	}

	// Check if start time matches
	if configData.StartHour != localConfig.StartHour ||
		configData.StartMin != localConfig.StartMin ||
		configData.StartSec != localConfig.StartSec {
		logger.Debug("Effective time start time mismatch",
			zap.Int("expected_hour", configData.StartHour),
			zap.Int("expected_min", configData.StartMin),
			zap.Int("expected_sec", configData.StartSec),
			zap.Int("actual_hour", localConfig.StartHour),
			zap.Int("actual_min", localConfig.StartMin),
			zap.Int("actual_sec", localConfig.StartSec))
		return false
	}

	// Check if end time matches
	if configData.EndHour != localConfig.EndHour ||
		configData.EndMin != localConfig.EndMin ||
		configData.EndSec != localConfig.EndSec {
		logger.Debug("Effective time end time mismatch",
			zap.Int("expected_hour", configData.EndHour),
			zap.Int("expected_min", configData.EndMin),
			zap.Int("expected_sec", configData.EndSec),
			zap.Int("actual_hour", localConfig.EndHour),
			zap.Int("actual_min", localConfig.EndMin),
			zap.Int("actual_sec", localConfig.EndSec))
		return false
	}

	return true
}

/*****************************************************************************
 * NAME: GetEffectiveTimeConfigById
 *
 * DESCRIPTION:
 *     Retrieves effective time configuration by ID
 *
 * PARAMETERS:
 *     logger - Logger instance
 *     id     - Effective time ID
 *
 * RETURNS:
 *     *EffectiveTimeConfig - Retrieved configuration
 *     error                - Error if retrieval fails
 *****************************************************************************/
func GetEffectiveTimeConfigById(logger *logger.Logger, id int) (*EffectiveTimeConfig, error) {
	// Execute floweye command to get specific effective time
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "rtptime", "get", fmt.Sprintf("id=%d", id))
	if err != nil {
		logger.Error("Failed to execute floweye command to get effective time",
			zap.Int("id", id),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get effective time: %w", err)
	}

	// Parse output
	config := &EffectiveTimeConfig{ID: id}
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "name":
			config.Name = value
		case "startwday":
			config.StartWDay, _ = strconv.Atoi(value)
		case "starthour":
			config.StartHour, _ = strconv.Atoi(value)
		case "startmin":
			config.StartMin, _ = strconv.Atoi(value)
		case "startsec":
			config.StartSec, _ = strconv.Atoi(value)
		case "endwday":
			config.EndWDay, _ = strconv.Atoi(value)
		case "endhour":
			config.EndHour, _ = strconv.Atoi(value)
		case "endmin":
			config.EndMin, _ = strconv.Atoi(value)
		case "endsec":
			config.EndSec, _ = strconv.Atoi(value)
		}
	}

	// Check if required fields were retrieved
	if config.Name == "" {
		return nil, fmt.Errorf("failed to parse effective time config: name is empty")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: GetAllEffectiveTimeConfigs
 *
 * DESCRIPTION:
 *     Retrieves all effective time configurations
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     map[int]*EffectiveTimeConfig - Map of configurations indexed by ID
 *     error                        - Error if retrieval fails
 *****************************************************************************/
func GetAllEffectiveTimeConfigs(logger *logger.Logger) (map[int]*EffectiveTimeConfig, error) {
	// Execute floweye command to list all effective times
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "rtptime", "list")
	if err != nil {
		logger.Error("Failed to execute floweye command to list effective time",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to list effective time: %w", err)
	}

	// Parse output
	configs := make(map[int]*EffectiveTimeConfig)
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 10 {
			continue
		}

		id, err := strconv.Atoi(fields[0])
		if err != nil {
			continue
		}

		name := fields[1]
		startWDay, _ := strconv.Atoi(fields[2])
		startHour, _ := strconv.Atoi(fields[3])
		startMin, _ := strconv.Atoi(fields[4])
		startSec, _ := strconv.Atoi(fields[5])
		endWDay, _ := strconv.Atoi(fields[6])
		endHour, _ := strconv.Atoi(fields[7])
		endMin, _ := strconv.Atoi(fields[8])
		endSec, _ := strconv.Atoi(fields[9])

		configs[id] = &EffectiveTimeConfig{
			ID:        id,
			Name:      name,
			StartWDay: startWDay,
			StartHour: startHour,
			StartMin:  startMin,
			StartSec:  startSec,
			EndWDay:   endWDay,
			EndHour:   endHour,
			EndMin:    endMin,
			EndSec:    endSec,
		}
	}

	return configs, nil
}



/*****************************************************************************
 * NAME: VerifyEffectiveTimeConfig
 *
 * DESCRIPTION:
 *     Verifies if effective time configuration matches expected values
 *     Reuses CompareEffectiveTimeConfig to avoid code duplication
 *
 * PARAMETERS:
 *     ctx        - Context
 *     logger     - Logger instance
 *     configData - Effective time configuration data to verify
 *
 * RETURNS:
 *     bool  - True if configuration matches, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyEffectiveTimeConfig(ctx context.Context, logger *logger.Logger, configData *EffectiveTimeConfig) (bool, error) {
	// Validate input
	if configData.ID <= 0 {
		return false, fmt.Errorf("invalid effective time ID: %d", configData.ID)
	}

	// Get current configuration from system
	localConfig, err := GetEffectiveTimeConfigById(logger, configData.ID)
	if err != nil {
		return false, err
	}

	// Use comparison function to check if configurations match
	return CompareEffectiveTimeConfig(logger, configData, localConfig), nil
}
