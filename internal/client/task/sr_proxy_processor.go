/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      sr_proxy_processor.go
 *
 * DESCRIPTION :    SR Proxy task processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: SrProxyProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_SR_PROXY type tasks.
 *     Handles SR Proxy configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for SR Proxy processor operations
 *     localConfigs       - Cache of local SR Proxy configurations (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *     pathMonitor        - SR path monitoring manager
 *****************************************************************************/
type SrProxyProcessor struct {
	logger             *logger.Logger            // Logger for SR Proxy processor operations
	localConfigs       map[string]*SrProxyConfig // Cache of local SR Proxy configurations (used for full sync redundant deletion)
	workingConfigs     map[string]*SrProxyConfig // Working cache for operations (can be refreshed during full sync)
	fullSyncInProgress bool                      // Flag indicating if full sync is in progress
	pathMonitor        *SRPathMonitorManager     // SR path monitoring manager
}

/*****************************************************************************
 * NAME: NewSrProxyProcessor
 *
 * DESCRIPTION:
 *     Creates a new SR Proxy processor instance.
 *     Initializes the local configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *SrProxyProcessor - Initialized SR Proxy processor
 *****************************************************************************/
func NewSrProxyProcessor(log *logger.Logger) *SrProxyProcessor {
	processor := &SrProxyProcessor{
		logger:             log.WithModule("sr-proxy-processor"),
		localConfigs:       make(map[string]*SrProxyConfig),
		workingConfigs:     make(map[string]*SrProxyConfig),
		fullSyncInProgress: false,
		pathMonitor:        NewSRPathMonitorManager(log),
	}

	// 启动路径监控系统
	ctx := context.Background()
	if err := processor.pathMonitor.Start(ctx); err != nil {
		log.Error("Failed to start SR path monitor", zap.Error(err))
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - The task type (TASK_SR_PROXY)
 *****************************************************************************/
func (p *SrProxyProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_SR_PROXY
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes an SR Proxy configuration task.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - The task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *SrProxyProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get SR Proxy task data
	srProxyTask := task.GetSrProxyTask()
	if srProxyTask == nil {
		return "SR Proxy task data is empty", fmt.Errorf("SR Proxy task data is nil")
	}

	// Log task details
	p.logger.Info("Processing SR Proxy task",
		zap.String("name", srProxyTask.GetName()),
		zap.String("action", task.TaskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handleConfigChange(ctx, srProxyTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeleteConfig(ctx, srProxyTask)
	default:
		return fmt.Sprintf("unsupported task action: %s", task.TaskAction.String()),
			fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
	}
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles SR Proxy configuration creation or modification.
 *     Determines if a configuration needs to be created or modified.
 *
 * PARAMETERS:
 *     ctx         - Context for the operation
 *     srProxyTask - SR Proxy task containing the configuration
 *     taskAction  - Task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if configuration fails
 *****************************************************************************/
func (p *SrProxyProcessor) handleConfigChange(ctx context.Context, srProxyTask *pb.SrProxyTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified SrProxyConfig structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	requestedConfig, err := ConvertSrProxyTaskToConfig(srProxyTask)
	if err != nil {
		p.logger.Error("failed to convert SR Proxy task to config",
			zap.String("name", srProxyTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert SR Proxy configuration: %v", err), err
	}

	p.logger.Info("handling SR Proxy config change",
		zap.String("name", requestedConfig.Name),
		zap.String("action", taskAction.String()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields using converted data
	if requestedConfig.Name == "" {
		return "SR Proxy name is required", fmt.Errorf("SR Proxy name is required")
	}

	if requestedConfig.Mtu < 500 || requestedConfig.Mtu > 4700 {
		return fmt.Sprintf("Invalid MTU value: %d, must be between 500 and 4700", requestedConfig.Mtu),
			fmt.Errorf("invalid MTU value: %d", requestedConfig.Mtu)
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := requestedConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if SR Proxy exists
	_, exists := p.workingConfigs[requestedConfig.Name]

	/*
		// If SR Proxy exists, check if configuration is the same using converted data
		if exists && CompareSrProxyConfig(p.logger, requestedConfig, existingConfig) {
			p.logger.Info("SR Proxy configuration is unchanged, skipping update",
				zap.String("name", requestedConfig.Name))

			return "SR Proxy configuration is unchanged", nil
		}
	*/

	// Determine if we need to create or modify using converted data
	var cmdArgs []string

	if exists {
		// Modify existing SR Proxy
		cmdArgs = []string{"nat", "setsrpxy", "name=" + requestedConfig.Name, "newname=" + requestedConfig.Name}
	} else {
		// Create new SR Proxy
		cmdArgs = []string{"nat", "addsrpxy", "name=" + requestedConfig.Name}
	}

	// Add parameters in the exact order as shown in floweye documentation
	// Example: floweye nat addsrpxy name=sr1 ifname= mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 links=40,80,996 fromin=1 keepalive=1 encrypt=AES256 password=pass256

	// Add ifname parameter (not in protobuf, using default empty value)
	cmdArgs = append(cmdArgs, "ifname=")

	// Add MTU using converted data
	cmdArgs = append(cmdArgs, fmt.Sprintf("mtu=%d", requestedConfig.Mtu))

	// Add ping parameters (not in protobuf, using defaults)
	cmdArgs = append(cmdArgs, "ping_disable=0")
	cmdArgs = append(cmdArgs, "pingip=0.0.0.0")
	cmdArgs = append(cmdArgs, "pingip2=0.0.0.0")
	cmdArgs = append(cmdArgs, "maxdelay=0")

	// Add Links using converted data
	if len(requestedConfig.Links) > 0 {
		linkStrs := make([]string, 0, len(requestedConfig.Links))
		for _, link := range requestedConfig.Links {
			linkStrs = append(linkStrs, fmt.Sprintf("%d", link))
		}
		cmdArgs = append(cmdArgs, "links="+strings.Join(linkStrs, ","))
	}

	// Add FromIn using converted data
	fromInValue := 0
	if requestedConfig.FromIn {
		fromInValue = 1
	}
	cmdArgs = append(cmdArgs, fmt.Sprintf("fromin=%d", fromInValue))

	// Add Keepalive using converted data
	keepaliveValue := 0
	if requestedConfig.Keepalive {
		keepaliveValue = 1
	}
	cmdArgs = append(cmdArgs, fmt.Sprintf("keepalive=%d", keepaliveValue))

	// Add encryption configuration using converted data
	var encryptParam string
	switch requestedConfig.EncryptType {
	case 0: // SR_ENCRYPT_NONE
		encryptParam = "NULL"
	case 1: // SR_ENCRYPT_AES128
		encryptParam = "AES128"
	case 2: // SR_ENCRYPT_AES256
		encryptParam = "AES256"
	default:
		encryptParam = "NULL" // Default to no encryption
	}
	cmdArgs = append(cmdArgs, "encrypt="+encryptParam)

	// Add password if encryption is enabled
	if requestedConfig.EncryptType != 0 && requestedConfig.Password != "" {
		cmdArgs = append(cmdArgs, "password="+requestedConfig.Password)
	} else {
		cmdArgs = append(cmdArgs, "password=")
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command for SR Proxy configuration",
		zap.String("name", requestedConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for SR Proxy configuration",
			zap.String("name", requestedConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure SR Proxy: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", requestedConfig.Name),
		zap.String("output", output))

	// Verify configuration was applied correctly using converted data (first path links)
	success, verifyErr := VerifySrProxyConfig(p.logger, requestedConfig)
	if verifyErr != nil {
		p.logger.Error("Failed to verify SR Proxy configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify SR Proxy configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("SR Proxy configuration verification failed")
		return "SR Proxy configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("SR Proxy configuration applied successfully",
		zap.String("name", requestedConfig.Name))

	// Remove from local configs if in full sync mode
	if p.fullSyncInProgress {
		delete(p.localConfigs, requestedConfig.Name)
	}

	// Refresh working configs to include the newly created/updated configuration
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error as the main operation succeeded
	}

	// 处理路径监控：注销原有监控(如果有)，注册现有配置监控
	if err := p.handlePathMonitoring(srProxyTask); err != nil {
		p.logger.Warn("Failed to handle path monitoring",
			zap.String("name", requestedConfig.Name),
			zap.Error(err))
	}

	if exists {
		return "SR Proxy configuration updated successfully", nil
	}
	return "SR Proxy configuration created successfully", nil
}

/*****************************************************************************
 * NAME: handlePathMonitoring
 *
 * DESCRIPTION:
 *     Handles SR path monitoring registration.
 *     Unregisters existing monitoring (if any) and registers new monitoring
 *     if multiple paths are provided.
 *
 * PARAMETERS:
 *     srProxyTask - SR Proxy task containing the configuration
 *
 * RETURNS:
 *     error - Error if monitoring setup fails
 *****************************************************************************/
func (p *SrProxyProcessor) handlePathMonitoring(srProxyTask *pb.SrProxyTask) error {
	srName := srProxyTask.GetName()
	paths := srProxyTask.GetPaths()

	// 注销原有监控(如果有)
	if err := p.pathMonitor.Unregister(srName); err != nil {
		p.logger.Debug("Failed to unregister existing path monitoring",
			zap.String("name", srName),
			zap.Error(err))
	}

	// 如果有多个路径，注册新的监控
	if len(paths) > 1 {
		if err := p.pathMonitor.RegisterFromProtobuf(srName, paths); err != nil {
			return fmt.Errorf("failed to register path monitoring for %s: %w", srName, err)
		}

		p.logger.Info("SR path monitoring registered",
			zap.String("name", srName),
			zap.Int("path_count", len(paths)))
	} else {
		p.logger.Debug("Single path provided, no monitoring needed",
			zap.String("name", srName),
			zap.Int("path_count", len(paths)))
	}

	return nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles SR Proxy configuration deletion.
 *
 * PARAMETERS:
 *     ctx         - Context for the operation
 *     srProxyTask - SR Proxy task containing the configuration to delete
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if deletion fails
 *****************************************************************************/
func (p *SrProxyProcessor) handleDeleteConfig(ctx context.Context, srProxyTask *pb.SrProxyTask) (string, error) {
	p.logger.Info("handling delete SR Proxy config",
		zap.String("name", srProxyTask.GetName()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields
	if srProxyTask.GetName() == "" {
		return "SR Proxy name is required", fmt.Errorf("SR Proxy name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := srProxyTask.GetName() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if SR Proxy exists in working configuration
	_, exists := p.workingConfigs[srProxyTask.GetName()]

	// If SR Proxy doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("SR Proxy not found in local configuration, nothing to delete",
			zap.String("name", srProxyTask.GetName()))
		return "SR Proxy not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"nat", "rmvproxy", srProxyTask.GetName(),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete SR Proxy",
		zap.String("name", srProxyTask.GetName()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("SR Proxy already does not exist, treating as successful delete",
				zap.String("name", srProxyTask.GetName()))
		} else {
			p.logger.Error("Failed to execute floweye command to delete SR Proxy",
				zap.String("name", srProxyTask.GetName()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete SR Proxy: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", srProxyTask.GetName()),
		zap.String("output", output))

	// Skip post-delete verification for improved performance and reliability

	// 注销路径监控
	if err := p.pathMonitor.Unregister(srProxyTask.GetName()); err != nil {
		p.logger.Warn("Failed to unregister path monitoring during deletion",
			zap.String("name", srProxyTask.GetName()),
			zap.Error(err))
	}

	p.logger.Info("SR Proxy deleted successfully",
		zap.String("name", srProxyTask.GetName()))

	return "SR Proxy deleted successfully", nil
}

/*****************************************************************************
 * NAME: fetchSrProxyConfigs
 *
 * DESCRIPTION:
 *     Fetches SR Proxy configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*SrProxyConfig - SR Proxy configurations by name
 *     error                     - Error if fetch fails
 *****************************************************************************/
func (p *SrProxyProcessor) fetchSrProxyConfigs() (map[string]*SrProxyConfig, error) {
	p.logger.Debug("fetching SR Proxy configurations from floweye")

	configs, err := GetSrProxyConfigs(p.logger)
	if err != nil {
		p.logger.Error("failed to get SR Proxy configurations", zap.Error(err))
		return nil, fmt.Errorf("failed to get SR Proxy configurations: %w", err)
	}

	p.logger.Debug("fetched SR Proxy configurations", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local SR Proxy configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *SrProxyProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local SR Proxy configurations")

	configs, err := p.fetchSrProxyConfigs()
	if err != nil {
		return err
	}

	p.localConfigs = configs
	p.logger.Debug("refreshed local SR Proxy configurations", zap.Int("count", len(configs)))
	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working SR Proxy configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *SrProxyProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working SR Proxy configurations")

	configs, err := p.fetchSrProxyConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	p.workingConfigs = configs
	p.logger.Debug("refreshed working SR Proxy configurations", zap.Int("count", len(configs)))
	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like create, update, delete, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *SrProxyProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *SrProxyProcessor) StartFullSync() error {
	p.logger.Info("Starting full sync for SR Proxy")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Deletes any local configurations that were not in the sync.
 *     Implements the TaskProcessor interface.
 *     Uses safe cleanup pattern to avoid concurrent map modification issues.
 *****************************************************************************/
func (p *SrProxyProcessor) EndFullSync() {
	p.logger.Info("Ending full sync for SR Proxy")

	// If full sync is not in progress, nothing to do
	if !p.fullSyncInProgress {
		p.logger.Info("Full sync not in progress, nothing to do")
		return
	}

	// Create a copy of remaining items to avoid modifying map during iteration
	remainingItems := make(map[string]*SrProxyConfig)
	for key, config := range p.localConfigs {
		remainingItems[key] = config
	}

	// Process remaining items (keep fullSyncInProgress = true during cleanup)
	for name := range remainingItems {
		p.logger.Info("Deleting SR Proxy not in full sync", zap.String("name", name))

		// Create a delete task for the remaining item
		deleteTask := &pb.SrProxyTask{Name: name}
		_, err := p.handleDeleteConfig(context.Background(), deleteTask)
		if err != nil {
			p.logger.Error("Failed to delete SR Proxy during full sync cleanup",
				zap.String("name", name),
				zap.Error(err))
		}
	}

	// Verify cleanup and set flag to false
	if len(p.localConfigs) > 0 {
		p.logger.Warn("Some SR Proxy items not cleaned up",
			zap.Int("count", len(p.localConfigs)))
	}

	// Set flag to false only after cleanup is complete
	p.fullSyncInProgress = false
	p.localConfigs = make(map[string]*SrProxyConfig)
	p.workingConfigs = make(map[string]*SrProxyConfig)

	p.logger.Info("Full sync for SR Proxy completed")
}
