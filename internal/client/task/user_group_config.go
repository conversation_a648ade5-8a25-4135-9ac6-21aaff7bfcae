/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_group_config.go
 *
 * DESCRIPTION :    User group configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: ConvertUserGroupTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a UserGroupTask protobuf message to a UserGroupConfig structure.
 *     This function performs one-time conversion at the entry point to eliminate
 *     repeated protobuf parsing throughout the processing pipeline.
 *     Follows the unified internal data structure pattern for protobuf optimization.
 *
 * PARAMETERS:
 *     task - UserGroupTask protobuf message to convert
 *
 * RETURNS:
 *     *UserGroupConfig - Converted user group configuration
 *     error - Error if conversion fails
 *****************************************************************************/
func ConvertUserGroupTaskToConfig(task *pb.UserGroupTask) (*UserGroupConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("userGroupTask is nil")
	}

	config := &UserGroupConfig{
		// Basic required fields
		ID:   task.GetId(),
		PID:  task.GetPid(),
		Name: task.GetName(),

		// IPv4 range fields with defaults
		StartIP: "0.0.0.0",
		EndIP:   "0.0.0.0",

		// IPv6 fields with defaults
		Prefix:    "::",
		PrefixLen: "",

		// Bandwidth limits with defaults (0 means no limit)
		RateIn:   0,
		RateOut:  0,
		RateIn6:  0,
		RateOut6: 0,

		// DNS with default
		DNS: "0.0.0.0",

		// Max online time with default (0 means no limit)
		MaxOnlineTime: 0,

		// Expired account policy with default
		ClntEpa: "reject",
	}

	// Handle IPv4 range if specified
	if task.GetV4Range() != nil {
		v4Range := task.GetV4Range()
		if v4Range.GetStartIp() != nil {
			config.StartIP = utils.GetIpString(v4Range.GetStartIp())
		}
		if v4Range.GetEndIp() != nil {
			config.EndIP = utils.GetIpString(v4Range.GetEndIp())
		}
	}

	// Handle IPv6 range if specified
	if task.GetV6Range() != nil {
		config.Prefix = utils.GetIpString(task.GetV6Range())
		// Note: pfxlen is handled separately in protobuf, but floweye expects it as part of prefix
		// For now, we'll use the default empty string for PrefixLen
	}

	// Handle IPv4 bandwidth limits if specified
	if task.GetV4Rate() != nil {
		v4Rate := task.GetV4Rate()
		if v4Rate.GetRateIn() != 0 {
			config.RateIn = v4Rate.GetRateIn()
		}
		if v4Rate.GetRateOut() != 0 {
			config.RateOut = v4Rate.GetRateOut()
		}
	}

	// Handle IPv6 bandwidth limits if specified
	if task.GetV6Rate() != nil {
		v6Rate := task.GetV6Rate()
		if v6Rate.GetRateIn() != 0 {
			config.RateIn6 = v6Rate.GetRateIn()
		}
		if v6Rate.GetRateOut() != 0 {
			config.RateOut6 = v6Rate.GetRateOut()
		}
	}

	// Handle DNS servers if specified
	if len(task.GetDns()) > 0 {
		var dnsServers []string
		for _, dnsAddr := range task.GetDns() {
			if dnsAddr != nil {
				dnsServers = append(dnsServers, utils.GetIpString(dnsAddr))
			}
		}
		if len(dnsServers) > 0 {
			config.DNS = strings.Join(dnsServers, ",")
		}
	}

	// Handle max online time if specified
	if task.GetMaxOnlineTime() != 0 {
		config.MaxOnlineTime = task.GetMaxOnlineTime()
	}

	// Handle expired account policy if specified
	switch task.GetClntEpa() {
	case pb.UserExpiredPolicy_USER_EXPIRED_POLICY_REJECT:
		config.ClntEpa = "reject"
	case pb.UserExpiredPolicy_USER_EXPIRED_POLICY_LOGIN:
		config.ClntEpa = "login"
	case pb.UserExpiredPolicy_USER_EXPIRED_POLICY_PASS:
		config.ClntEpa = "pass"
	default:
		// Keep default "reject"
	}

	return config, nil
}

/*****************************************************************************
 * NAME: UserGroupConfig
 *
 * DESCRIPTION:
 *     Represents a user group configuration.
 *     Contains all the configuration parameters for a user group.
 *
 * FIELDS:
 *     ID            - User group ID, range: 1-2063
 *     PID           - Parent group ID
 *     Name          - User group name
 *     StartIP       - Start IP address of the IPv4 range
 *     EndIP         - End IP address of the IPv4 range
 *     Prefix        - IPv6 prefix address
 *     PrefixLen     - IPv6 prefix length
 *     RateIn        - IPv4 inbound bandwidth limit in kbps, 0 means no limit
 *     RateOut       - IPv4 outbound bandwidth limit in kbps, 0 means no limit
 *     RateIn6       - IPv6 inbound bandwidth limit in kbps, 0 means no limit
 *     RateOut6      - IPv6 outbound bandwidth limit in kbps, 0 means no limit
 *     DNS           - DNS servers, comma-separated
 *     MaxOnlineTime - Maximum online time in hours, 0 means no limit
 *     ClntEpa       - Expired account policy: "reject", "login", or "pass"
 *****************************************************************************/
type UserGroupConfig struct {
	ID            int32
	PID           int32
	Name          string
	StartIP       string
	EndIP         string
	Prefix        string
	PrefixLen     string
	RateIn        int32
	RateOut       int32
	RateIn6       int32
	RateOut6      int32
	DNS           string
	MaxOnlineTime int32
	ClntEpa       string // reject, login, pass
}

/*****************************************************************************
 * NAME: GetLocalUserGroupConfigs
 *
 * DESCRIPTION:
 *     Retrieves all user group configurations from the system.
 *     First executes floweye command to get all user group IDs,
 *     then executes floweye command to get detailed configuration for each ID.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[int32]*UserGroupConfig - Map of user group configurations indexed by ID
 *     error - Error if retrieving configurations fails
 *****************************************************************************/
func GetLocalUserGroupConfigs(logger *logger.Logger) (map[int32]*UserGroupConfig, error) {
	logger.Debug("Getting local user group configurations")

	// Initialize configurations map
	configs := make(map[int32]*UserGroupConfig)

	// Execute floweye command to get all user group IDs
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "pppoeippool", "list")
	if err != nil {
		logger.Error("Failed to execute floweye command to list user groups",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to list user groups: %w", err)
	}

	// Parse output to get user group IDs
	var userGroupIDs []int32
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse user group information
		// Format: ID Name PID Parent StartIP EndIP Size BitmapSz RateIn RateOut RateIn6 RateOut6 IPLeft NextIP AcctCnt ClntEpa IFName1 IFVlan1 Prefix/PfxLen
		fields := strings.Fields(line)
		if len(fields) < 19 {
			logger.Warn("Invalid user group line format",
				zap.String("line", line),
				zap.Int("field_count", len(fields)))
			continue
		}

		// Parse ID
		id, err := strconv.ParseInt(fields[0], 10, 32)
		if err != nil {
			logger.Warn("Failed to parse user group ID",
				zap.String("id", fields[0]),
				zap.Error(err))
			continue
		}

		// Add ID to list
		userGroupIDs = append(userGroupIDs, int32(id))
	}

	// Get detailed configuration for each user group ID
	for _, id := range userGroupIDs {
		config, err := GetUserGroupConfig(logger, id)
		if err != nil {
			logger.Warn("Failed to get user group configuration",
				zap.Int32("id", id),
				zap.Error(err))
			continue
		}

		// Add to configurations map
		configs[id] = config
	}

	logger.Info("Retrieved local user group configurations",
		zap.Int("count", len(configs)))

	return configs, nil
}

/*****************************************************************************
 * NAME: GetUserGroupConfig
 *
 * DESCRIPTION:
 *     Retrieves a specific user group configuration from the system.
 *     Executes floweye command to get the user group and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     id     - ID of the user group to retrieve
 *
 * RETURNS:
 *     *UserGroupConfig - User group configuration
 *     error - Error if retrieving configuration fails
 *****************************************************************************/
func GetUserGroupConfig(logger *logger.Logger, id int32) (*UserGroupConfig, error) {
	logger.Debug("Getting user group configuration", zap.Int32("id", id))

	// Execute floweye command to get user group
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "pppoeippool", "get", "id="+strconv.Itoa(int(id)))
	if err != nil {
		logger.Error("Failed to execute floweye command to get user group",
			zap.Int32("id", id),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get user group: %w", err)
	}

	// Parse output
	lines := strings.Split(output, "\n")
	configMap := make(map[string]string)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		configMap[key] = value
	}

	// Check if user group exists
	idStr, ok := configMap["id"]
	if !ok {
		logger.Error("User group ID not found in configuration")
		return nil, fmt.Errorf("user group not found")
	}

	// Parse ID
	configID, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		logger.Error("Failed to parse user group ID",
			zap.String("id", idStr),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse user group ID: %w", err)
	}

	// Parse PID
	pidStr, ok := configMap["pid"]
	if !ok {
		logger.Error("User group PID not found in configuration")
		return nil, fmt.Errorf("user group PID not found")
	}

	pid, err := strconv.ParseInt(pidStr, 10, 32)
	if err != nil {
		logger.Error("Failed to parse user group PID",
			zap.String("pid", pidStr),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse user group PID: %w", err)
	}

	// Parse rate limits
	rateIn, _ := strconv.ParseInt(configMap["ratein"], 10, 32)
	rateOut, _ := strconv.ParseInt(configMap["rateout"], 10, 32)
	rateIn6, _ := strconv.ParseInt(configMap["ratein6"], 10, 32)
	rateOut6, _ := strconv.ParseInt(configMap["rateout6"], 10, 32)

	// Parse max online time
	maxOnlineTime, _ := strconv.ParseInt(configMap["maxonlinetime"], 10, 32)

	// Create user group config
	config := &UserGroupConfig{
		ID:            int32(configID),
		PID:           int32(pid),
		Name:          configMap["name"],
		StartIP:       configMap["start"],
		EndIP:         configMap["end"],
		RateIn:        int32(rateIn),
		RateOut:       int32(rateOut),
		RateIn6:       int32(rateIn6),
		RateOut6:      int32(rateOut6),
		DNS:           configMap["dns"],
		MaxOnlineTime: int32(maxOnlineTime),
		ClntEpa:       configMap["clntepa"],
		Prefix:        configMap["prefix"],
		PrefixLen:     configMap["pfxlen"],
	}

	logger.Debug("Retrieved user group configuration",
		zap.Int32("id", id),
		zap.String("name", config.Name))

	return config, nil
}

/*****************************************************************************
 * NAME: VerifyUserGroupConfig
 *
 * DESCRIPTION:
 *     Verifies that a user group configuration was applied correctly.
 *     Gets the current configuration from the system and compares it with the expected values.
 *     Uses the unified internal data structure pattern to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger     - Logger instance for logging operations
 *     configData - Converted user group configuration data containing the expected configuration
 *
 * RETURNS:
 *     bool  - True if verification succeeds, false otherwise
 *     error - Error if verification process fails
 *****************************************************************************/
func VerifyUserGroupConfig(logger *logger.Logger, configData *UserGroupConfig) (bool, error) {
	logger.Debug("Verifying user group configuration",
		zap.Int32("id", configData.ID))

	// Get user group configuration
	localConfig, err := GetUserGroupConfig(logger, configData.ID)
	if err != nil {
		logger.Error("Failed to get user group configuration",
			zap.Int32("id", configData.ID),
			zap.Error(err))
		return false, err
	}

	// Use CompareUserGroupConfig to verify the configuration
	if !CompareUserGroupConfig(logger, configData, localConfig) {
		logger.Error("User group configuration verification failed",
			zap.Int32("id", configData.ID))
		return false, nil
	}

	// Verification passed
	logger.Info("User group configuration verified successfully",
		zap.Int32("id", configData.ID),
		zap.String("name", configData.Name))

	return true, nil
}

/*****************************************************************************
 * NAME: CompareUserGroupConfig
 *
 * DESCRIPTION:
 *     Compares a converted user group configuration with a local configuration.
 *     Checks if the configuration in the converted data matches the local configuration.
 *     Uses the unified internal data structure pattern to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger       - Logger instance for logging operations
 *     configData   - Converted user group configuration data to compare
 *     localConfig  - Local user group configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareUserGroupConfig(logger *logger.Logger, configData *UserGroupConfig, localConfig *UserGroupConfig) bool {

	logger.Debug("Comparing user group configuration",
		zap.Int32("id", configData.ID),
		zap.String("name", configData.Name))

	// Compare basic fields
	if configData.ID != localConfig.ID ||
		configData.Name != localConfig.Name ||
		configData.PID != localConfig.PID {
		return false
	}

	// Compare IPv4 range
	if configData.StartIP != localConfig.StartIP || configData.EndIP != localConfig.EndIP {
		return false
	}

	// Compare IPv6 prefix
	if configData.Prefix != localConfig.Prefix {
		return false
	}

	// Compare bandwidth limits
	if configData.RateIn != localConfig.RateIn || configData.RateOut != localConfig.RateOut ||
		configData.RateIn6 != localConfig.RateIn6 || configData.RateOut6 != localConfig.RateOut6 {
		return false
	}

	// Compare max online time
	if configData.MaxOnlineTime != localConfig.MaxOnlineTime {
		return false
	}

	// Compare expired account policy
	if configData.ClntEpa != localConfig.ClntEpa {
		return false
	}

	// Configurations match
	logger.Debug("User group configurations match",
		zap.Int32("id", configData.ID),
		zap.String("name", configData.Name))

	return true
}
