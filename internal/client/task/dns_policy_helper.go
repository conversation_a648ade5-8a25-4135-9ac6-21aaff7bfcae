/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_policy_helper.go
 *
 * DESCRIPTION :    DNS policy helper functions for command building and ordering
 *
 * AUTHOR :         wei
 *
 * HISTORY :        17/06/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: addMatchingCriteria
 *
 * DESCRIPTION:
 *     Adds matching criteria to the floweye command arguments.
 *     Handles source/destination IPs, user groups, domain groups, etc.
 *
 * PARAMETERS:
 *     cmdArgs - Pointer to command arguments slice
 *     task    - DNS policy task containing matching criteria
 *
 * RETURNS:
 *     error - Error if building criteria fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) addMatchingCriteria(cmdArgs *[]string, task *pb.DnsPolicyTask) error {
	// Add source IP criteria using common utility
	// Note: resolver removed - dependency resolution now handled in BuildAddressSelectorsString
	if len(task.GetInIp()) > 0 {
		inIPStr, err := BuildAddressSelectorsString(task.GetInIp(), p.logger)
		if err != nil {
			return fmt.Errorf("failed to build source IP criteria: %w", err)
		}
		*cmdArgs = append(*cmdArgs, "inip="+inIPStr)
	} else {
		*cmdArgs = append(*cmdArgs, "inip=")
	}

	// Add destination IP criteria using common utility
	if len(task.GetOutIp()) > 0 {
		outIPStr, err := BuildAddressSelectorsString(task.GetOutIp(), p.logger)
		if err != nil {
			return fmt.Errorf("failed to build destination IP criteria: %w", err)
		}
		*cmdArgs = append(*cmdArgs, "outip="+outIPStr)
	} else {
		*cmdArgs = append(*cmdArgs, "outip=")
	}

	// Add user group
	if task.GetPool() != 0 {
		*cmdArgs = append(*cmdArgs, "pool="+strconv.Itoa(int(task.GetPool())))
	} else {
		*cmdArgs = append(*cmdArgs, "pool=0")
	}

	// Add user type
	usrType := convertUserTypeToString(task.GetUsrType())
	*cmdArgs = append(*cmdArgs, "usrtype="+usrType)

	// Add domain group
	domainGroups := task.GetDomainGroup()
	if len(domainGroups) > 0 && domainGroups[0] != "" {
		// Resolve domain group name to ID (use first domain group)
		domainGroupID, err := p.resolveDomainGroupNameToID(domainGroups[0])
		if err != nil {
			return fmt.Errorf("failed to resolve domain group name '%s': %w", domainGroups[0], err)
		}
		*cmdArgs = append(*cmdArgs, "dns="+strconv.Itoa(domainGroupID))
	} else {
		*cmdArgs = append(*cmdArgs, "dns=0")
	}

	// Add query type
	atype := convertQueryTypeToString(task.GetAType())
	*cmdArgs = append(*cmdArgs, "atype="+atype)

	// Add interface settings
	if task.GetInIf() != "" {
		*cmdArgs = append(*cmdArgs, "inif="+task.GetInIf())
	} else {
		*cmdArgs = append(*cmdArgs, "inif=any")
	}

	// Add bridge
	if task.GetBridge() != 0 {
		*cmdArgs = append(*cmdArgs, "bridge="+strconv.Itoa(int(task.GetBridge())))
	} else {
		*cmdArgs = append(*cmdArgs, "bridge=0")
	}

	// Add VLAN using common utility
	vlanStr := BuildIntRangeString(task.GetVlan())
	*cmdArgs = append(*cmdArgs, "vlan="+vlanStr)

	// Add application protocol
	if task.GetApp() != nil {
		*cmdArgs = append(*cmdArgs, "app="+task.GetApp().GetAppName())
	} else {
		*cmdArgs = append(*cmdArgs, "app=any")
	}

	return nil
}

/*****************************************************************************
 * NAME: addActionConfiguration
 *
 * DESCRIPTION:
 *     Adds action configuration to the floweye command arguments.
 *     Handles different action types and their specific parameters.
 *
 * PARAMETERS:
 *     cmdArgs - Pointer to command arguments slice
 *     task    - DNS policy task containing action configuration
 *
 * RETURNS:
 *     error - Error if building action configuration fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) addActionConfiguration(cmdArgs *[]string, task *pb.DnsPolicyTask) error {
	// Add action type
	action := convertActionTypeToString(task.GetAction())
	*cmdArgs = append(*cmdArgs, "action="+action)

	// Add action-specific configuration
	switch task.GetAction() {
	case pb.DnsPolicyAction_DNS_ACTION_PASS, pb.DnsPolicyAction_DNS_ACTION_IPPXY:
		if task.GetActionPass() != nil {
			passConfig := task.GetActionPass()

			// Add next (continue matching) configuration
			nextValue := "1"
			if passConfig.GetNext() {
				nextValue = "0" // next=true means continue matching (next=0)
			}
			*cmdArgs = append(*cmdArgs, "next="+nextValue)

			// Add IP QPS limiting
			if passConfig.IpQps != nil {
				*cmdArgs = append(*cmdArgs, "ipqps="+strconv.Itoa(int(*passConfig.IpQps)))
			} else {
				*cmdArgs = append(*cmdArgs, "ipqps=0")
			}
		} else {
			*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0")
		}

		// Add default values for pass/ippxy action
		*cmdArgs = append(*cmdArgs, "actarg=null", "nosnat=0", "dnslist=")

	case pb.DnsPolicyAction_DNS_ACTION_RDR:
		if task.GetActionRdr() != nil {
			rdrConfig := task.GetActionRdr()

			// Add redirect line
			*cmdArgs = append(*cmdArgs, "actarg="+rdrConfig.GetActArg())

			// Add nosnat configuration
			nosnatValue := "0"
			if rdrConfig.NoSnat != nil && *rdrConfig.NoSnat {
				nosnatValue = "1"
			}
			*cmdArgs = append(*cmdArgs, "nosnat="+nosnatValue)

			// Add DNS list
			if len(rdrConfig.GetDnsList()) > 0 {
				dnsListStr := p.buildDNSList(rdrConfig.GetDnsList())
				*cmdArgs = append(*cmdArgs, "dnslist="+dnsListStr)
			} else {
				*cmdArgs = append(*cmdArgs, "dnslist=")
			}
		} else {
			return fmt.Errorf("rdr action requires redirect configuration")
		}

		// Add default values for rdr action
		*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0")

	case pb.DnsPolicyAction_DNS_ACTION_REPLY:
		if task.GetActionReply() != nil {
			replyConfig := task.GetActionReply()

			// Add reply IPs
			if len(replyConfig.GetActArg()) > 0 {
				replyIPStr := p.buildReplyIPs(replyConfig.GetActArg())
				*cmdArgs = append(*cmdArgs, "actarg="+replyIPStr)
			} else {
				return fmt.Errorf("reply action requires reply IP addresses")
			}
		} else {
			return fmt.Errorf("reply action requires reply configuration")
		}

		// Add default values for reply action
		*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0", "nosnat=0", "dnslist=")

	case pb.DnsPolicyAction_DNS_ACTION_LIMIT:
		if task.GetActionLimit() != nil {
			limitConfig := task.GetActionLimit()

			// Add next (continue matching) configuration
			nextValue := "1"
			if limitConfig.GetNext() {
				nextValue = "0" // next=true means continue matching (next=0)
			}
			*cmdArgs = append(*cmdArgs, "next="+nextValue)

			// Add IP QPS limiting
			if limitConfig.IpQps != nil {
				*cmdArgs = append(*cmdArgs, "ipqps="+strconv.Itoa(int(*limitConfig.IpQps)))
			} else {
				*cmdArgs = append(*cmdArgs, "ipqps=0")
			}

			// Add total QPS limiting
			if limitConfig.ActArg != nil {
				*cmdArgs = append(*cmdArgs, "actarg="+strconv.Itoa(int(*limitConfig.ActArg)))
			} else {
				*cmdArgs = append(*cmdArgs, "actarg=0")
			}
		} else {
			*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0", "actarg=0")
		}

		// Add default values for limit action
		*cmdArgs = append(*cmdArgs, "nosnat=0", "dnslist=")

	case pb.DnsPolicyAction_DNS_ACTION_DENY, pb.DnsPolicyAction_DNS_ACTION_ZEROREPLY:
		// Add default values for deny/zeroreply actions
		*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0", "actarg=null", "nosnat=0", "dnslist=")

	default:
		return fmt.Errorf("unsupported action type: %v", task.GetAction())
	}

	return nil
}

// Note: buildAddressSelectors function removed - now using BuildAddressSelectorsString from common_protobuf_utils.go

/*****************************************************************************
 * NAME: buildDNSList
 *
 * DESCRIPTION:
 *     Builds DNS list string from IP addresses for rdr action.
 *
 * PARAMETERS:
 *     dnsList - List of DNS IP addresses
 *
 * RETURNS:
 *     string - Formatted DNS list string
 *****************************************************************************/
func (p *DnsPolicyProcessor) buildDNSList(dnsList []*pb.IpAddress) string {
	var ips []string
	for _, ip := range dnsList {
		ips = append(ips, utils.GetIpString(ip))
	}
	return strings.Join(ips, ",")
}

/*****************************************************************************
 * NAME: buildReplyIPs
 *
 * DESCRIPTION:
 *     Builds reply IP string from IP addresses for reply action.
 *
 * PARAMETERS:
 *     replyIPs - List of reply IP addresses
 *
 * RETURNS:
 *     string - Formatted reply IP string
 *****************************************************************************/
func (p *DnsPolicyProcessor) buildReplyIPs(replyIPs []*pb.IpAddress) string {
	var ips []string
	for _, ip := range replyIPs {
		ips = append(ips, utils.GetIpString(ip))
	}
	return strings.Join(ips, ",")
}

/*****************************************************************************
 * NAME: resolveIPGroupNameToID
 *
 * DESCRIPTION:
 *     Resolves IP group name to ID using the IP group module.
 *     Uses the GetIpGroupIdByName function from ip_group_config.go.
 *
 * PARAMETERS:
 *     groupName - IP group name to resolve
 *
 * RETURNS:
 *     int   - IP group ID
 *     error - Error if resolution fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) resolveIPGroupNameToID(groupName string) (int, error) {
	p.logger.Debug("resolving IP group name to ID",
		zap.String("group_name", groupName))

	// Use the IP group module's API to resolve name to ID
	groupID, err := GetIpGroupIdByName(p.logger, groupName)
	if err != nil {
		return 0, fmt.Errorf("failed to resolve IP group name to ID: %w", err)
	}

	if groupID == -1 {
		return 0, fmt.Errorf("IP group not found: %s", groupName)
	}

	p.logger.Debug("resolved IP group name to ID",
		zap.String("group_name", groupName),
		zap.Int("group_id", groupID))

	return groupID, nil
}

/*****************************************************************************
 * NAME: resolveDomainGroupNameToID
 *
 * DESCRIPTION:
 *     Resolves domain group name to ID using the domain group module.
 *     Uses the GetDomainGroupIdByName function from domain_group_config.go.
 *
 * PARAMETERS:
 *     groupName - Domain group name to resolve
 *
 * RETURNS:
 *     int   - Domain group ID
 *     error - Error if resolution fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) resolveDomainGroupNameToID(groupName string) (int, error) {
	p.logger.Debug("resolving domain group name to ID",
		zap.String("group_name", groupName))

	// Use the domain group module's API to resolve name to ID
	groupID, err := GetDomainGroupIdByName(p.logger, groupName)
	if err != nil {
		return 0, fmt.Errorf("failed to resolve domain group name to ID: %w", err)
	}

	if groupID == -1 {
		return 0, fmt.Errorf("domain group not found: %s", groupName)
	}

	p.logger.Debug("resolved domain group name to ID",
		zap.String("group_name", groupName),
		zap.Int("group_id", groupID))

	return groupID, nil
}

/*****************************************************************************
 * NAME: addMatchingCriteriaFromConfig
 *
 * DESCRIPTION:
 *     Adds matching criteria to the floweye command arguments using converted config data.
 *     Uses internal data structure instead of protobuf access.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - Converted DNS policy configuration data
 *
 * RETURNS:
 *     error - Error if building criteria fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) addMatchingCriteriaFromConfig(cmdArgs *[]string, configData *DnsPolicyConfig) error {
	// Add source IP criteria (already converted)
	*cmdArgs = append(*cmdArgs, "inip="+configData.InIP)

	// Add destination IP criteria (already converted)
	*cmdArgs = append(*cmdArgs, "outip="+configData.OutIP)

	// Add user group
	*cmdArgs = append(*cmdArgs, "pool="+strconv.Itoa(configData.Pool))

	// Add user type (already converted)
	*cmdArgs = append(*cmdArgs, "usrtype="+configData.UsrType)

	// Add domain group (resolve name to ID if needed)
	if configData.DNSName != "" {
		domainGroupID, err := p.resolveDomainGroupNameToID(configData.DNSName)
		if err != nil {
			return fmt.Errorf("failed to resolve domain group name '%s': %w", configData.DNSName, err)
		}
		*cmdArgs = append(*cmdArgs, "dns="+strconv.Itoa(domainGroupID))
	} else {
		*cmdArgs = append(*cmdArgs, "dns=0")
	}

	// Add query type (already converted)
	*cmdArgs = append(*cmdArgs, "atype="+configData.AType)

	// Add interface settings (already converted)
	*cmdArgs = append(*cmdArgs, "inif="+configData.InIf)

	// Add bridge
	*cmdArgs = append(*cmdArgs, "bridge="+strconv.Itoa(configData.Bridge))

	// Add VLAN (already converted)
	*cmdArgs = append(*cmdArgs, "vlan="+configData.VLAN)

	// Add application protocol (default to any for now)
	*cmdArgs = append(*cmdArgs, "app=any")

	return nil
}

/*****************************************************************************
 * NAME: addActionConfigurationFromConfig
 *
 * DESCRIPTION:
 *     Adds action configuration to the floweye command arguments using converted config data.
 *     Uses internal data structure instead of protobuf access.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - Converted DNS policy configuration data
 *
 * RETURNS:
 *     error - Error if building action configuration fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) addActionConfigurationFromConfig(cmdArgs *[]string, configData *DnsPolicyConfig) error {
	// Add action type (already converted)
	*cmdArgs = append(*cmdArgs, "action="+configData.Action)

	// Add action-specific configuration using converted data
	switch configData.Action {
	case "pass", "ippxy":
		// Add next (continue matching) configuration
		nextValue := "1" // default: stop matching
		if configData.Next {
			nextValue = "0" // next=true means continue matching (next=0)
		}
		*cmdArgs = append(*cmdArgs, "next="+nextValue)

		// Add IP QPS limiting
		*cmdArgs = append(*cmdArgs, "ipqps="+strconv.Itoa(configData.IPQps))

		// Add default values for pass/ippxy action (no nosnat for pass action)
		*cmdArgs = append(*cmdArgs, "actarg="+configData.ActArg)

	case "rdr":
		if configData.ActArg == "" {
			return fmt.Errorf("rdr action requires redirect line configuration")
		}

		// Add redirect line
		*cmdArgs = append(*cmdArgs, "actarg="+configData.ActArg)

		// Add nosnat configuration
		nosnatValue := "0"
		if configData.NoSnat {
			nosnatValue = "1"
		}
		*cmdArgs = append(*cmdArgs, "nosnat="+nosnatValue)

		// Add DNS list
		*cmdArgs = append(*cmdArgs, "dnslist="+configData.DNSList)

		// Add default values for rdr action
		*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0")

	case "reply":
		if configData.ActArg == "" {
			return fmt.Errorf("reply action requires reply IP addresses")
		}

		// Add reply IPs
		*cmdArgs = append(*cmdArgs, "actarg="+configData.ActArg)

		// Add default values for reply action (no nosnat for reply action)
		*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0")

	case "limit":
		// Add next (continue matching) configuration
		nextValue := "1" // default: stop matching
		if configData.Next {
			nextValue = "0" // next=true means continue matching (next=0)
		}
		*cmdArgs = append(*cmdArgs, "next="+nextValue)

		// Add IP QPS limiting
		*cmdArgs = append(*cmdArgs, "ipqps="+strconv.Itoa(configData.IPQps))

		// Add total QPS limiting
		*cmdArgs = append(*cmdArgs, "actarg="+configData.ActArg)

	case "deny", "zeroreply":
		// Add default values for deny/zeroreply actions (no nosnat for deny/zeroreply actions)
		*cmdArgs = append(*cmdArgs, "next=1", "ipqps=0", "actarg=null")

	default:
		return fmt.Errorf("unsupported action type: %s", configData.Action)
	}

	return nil
}
