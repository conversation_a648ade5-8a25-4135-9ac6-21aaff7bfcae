/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dhcp_processor_test.go
 *
 * DESCRIPTION :    Tests for DHCP processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Mock logger for testing
func setupDhcpTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level: "DEBUG",
		Outputs: []logger.Output{
			{
				Type: logger.TypeConsole,
			},
		},
	}
	log, _ := logger.NewLogger(logConfig)
	return log
}

func TestDhcpProcessor_GetTaskType(t *testing.T) {
	log := setupDhcpTestLogger()
	processor := NewDhcpProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_DHCP, taskType)
}

func TestDhcpProcessor_ProcessTask_NilTask(t *testing.T) {
	log := setupDhcpTestLogger()
	processor := NewDhcpProcessor(log)

	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DHCP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		// No DHCP task data
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "dhcp server task data is nil")
}

func TestDhcpProcessor_ProcessTask_UnsupportedAction(t *testing.T) {
	log := setupDhcpTestLogger()
	processor := NewDhcpProcessor(log)

	// Create a task with an unsupported action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DHCP,
		TaskAction: 99, // Invalid action
		Payload: &pb.DeviceTask_DhcpTask{
			DhcpTask: &pb.DhcpServerTask{
				Name:       "lan1",
				DhcpEnable: true,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported task action")
}

func TestDhcpProcessor_ProcessTask_MissingName(t *testing.T) {
	log := setupDhcpTestLogger()
	processor := NewDhcpProcessor(log)

	// Create a task with missing name
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DHCP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_DhcpTask{
			DhcpTask: &pb.DhcpServerTask{
				// Name is missing
				DhcpEnable: true,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "lan name is required")
}
