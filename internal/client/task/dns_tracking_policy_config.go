/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_tracking_policy_config.go
 *
 * DESCRIPTION :    DNS tracking policy configuration structures and functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        17/06/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: DnsTrackingPolicyConfig
 *
 * DESCRIPTION:
 *     Represents a DNS tracking policy configuration from the local PA system.
 *     Contains all DNS tracking policy parameters including domain groups and tracking settings.
 *
 * FIELDS:
 *     ID           - Policy ID (local identifier)
 *     Cookie       - Policy cookie (unique identifier)
 *     Enable       - Whether the policy is enabled
 *     DnsID        - Domain group ID
 *     DnsName      - Domain group name
 *     Pxy          - Primary proxy/line name
 *     BkupPxy      - Backup proxy/line name
 *     TrackHost    - Whether to track host
 *     CacheTTL     - DNS cache TTL value
 *     Desc         - Policy description
 *     DnsAddr      - DNS server address
 *****************************************************************************/
type DnsTrackingPolicyConfig struct {
	ID        int     // Policy ID (local identifier)
	Cookie    uint32  // Policy cookie (unique identifier)
	Enable    bool    // Whether the policy is enabled
	DnsID     int     // Domain group ID
	DnsName   string  // Domain group name
	Pxy       string  // Primary proxy/line name
	BkupPxy   string  // Backup proxy/line name
	TrackHost bool    // Whether to track host
	CacheTTL  int     // DNS cache TTL value
	Desc      string  // Policy description
	DnsAddr   string  // DNS server address
	Previous  *uint32 // Previous policy cookie for ordering (nil if not specified)
}

/*****************************************************************************
 * NAME: ConvertDnsTrackingPolicyTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a DNS tracking policy task protobuf message to internal config structure.
 *     Performs one-time conversion to eliminate repeated protobuf parsing.
 *     Handles optional fields with appropriate defaults.
 *
 * PARAMETERS:
 *     task - DNS tracking policy task protobuf message
 *
 * RETURNS:
 *     *DnsTrackingPolicyConfig - Converted internal configuration structure
 *     error                    - Error if conversion fails
 *****************************************************************************/
func ConvertDnsTrackingPolicyTaskToConfig(task *pb.DnsTrackingPolicyTask) (*DnsTrackingPolicyConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("dnsTrackingPolicyTask is nil")
	}

	// Debug: log the disable value from protobuf
	logger := zap.L()
	logger.Debug("ConvertDnsTrackingPolicyTaskToConfig: protobuf disable value",
		zap.Bool("task_disable", task.GetDisable()),
		zap.Uint32("cookie", task.GetCookie()))

	config := &DnsTrackingPolicyConfig{
		Cookie:    task.GetCookie(),
		Enable:    !task.GetDisable(), // Convert disable to enable
		Pxy:       task.GetPxy(),
		BkupPxy:   task.GetBackupPxy(),
		TrackHost: false, // Default value
		CacheTTL:  0,     // Default value
		Desc:      "",    // Default value
		DnsAddr:   "",    // Default value
	}

	// Debug: log the converted enable value
	logger.Debug("ConvertDnsTrackingPolicyTaskToConfig: converted enable value",
		zap.Bool("config_enable", config.Enable),
		zap.Uint32("cookie", config.Cookie))

	// Handle optional fields with defaults
	if task.TrackHost != nil {
		config.TrackHost = *task.TrackHost
	}

	if task.CacheTtl != nil {
		config.CacheTTL = int(*task.CacheTtl)
	}

	if task.Desc != nil {
		config.Desc = *task.Desc
	}

	if task.DnsAddr != nil {
		config.DnsAddr = utils.GetIpString(task.DnsAddr)
	}

	// Handle domain groups - use first domain group for DNS tracking
	if len(task.GetDomainGroup()) > 0 {
		config.DnsName = task.GetDomainGroup()[0]
	}

	// Handle previous field for ordering
	if task.Previous != nil {
		config.Previous = task.Previous
	}

	return config, nil
}

/*****************************************************************************
 * NAME: GetLocalDnsTrackingPolicyConfigs
 *
 * DESCRIPTION:
 *     Retrieves all DNS tracking policy configurations from the local PA system.
 *     Uses the floweye dnrt list command to get the policy list.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[uint32]*DnsTrackingPolicyConfig - Map of policy cookies to configurations
 *     error                               - Error if retrieval fails
 *****************************************************************************/
func GetLocalDnsTrackingPolicyConfigs(logger *logger.Logger) (map[uint32]*DnsTrackingPolicyConfig, error) {
	logger.Debug("Retrieving all DNS tracking policy configurations")

	// Execute floweye command to list all DNS tracking policies
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dnrt", "list", "json=1")
	if err != nil {
		logger.Error("Failed to execute floweye command to list DNS tracking policies", zap.Error(err))
		return nil, fmt.Errorf("failed to list DNS tracking policies: %w", err)
	}

	// Parse output
	configs := make(map[uint32]*DnsTrackingPolicyConfig)
	// Parse output - floweye returns JSON objects separated by commas on a single line
	output = strings.TrimSpace(output)
	if output == "" {
		logger.Debug("Empty output from floweye dnrt list")
		return configs, nil
	}

	// Split by "},{"  to separate multiple JSON objects
	var jsonObjects []string
	if strings.Contains(output, "},{") {
		// Multiple objects: split and reconstruct each object
		parts := strings.Split(output, "},{")
		for i, part := range parts {
			if i == 0 {
				// First object: add closing brace
				jsonObjects = append(jsonObjects, part+"}")
			} else if i == len(parts)-1 {
				// Last object: add opening brace
				jsonObjects = append(jsonObjects, "{"+part)
			} else {
				// Middle objects: add both braces
				jsonObjects = append(jsonObjects, "{"+part+"}")
			}
		}
	} else {
		// Single object
		jsonObjects = append(jsonObjects, output)
	}

	// Parse each JSON object
	for _, jsonObj := range jsonObjects {
		jsonObj = strings.TrimSpace(jsonObj)
		if jsonObj == "" || !strings.HasPrefix(jsonObj, "{") {
			continue
		}

		// Parse JSON object
		config, err := ParseDnsTrackingPolicyFromJSON(jsonObj)
		if err != nil {
			logger.Warn("Failed to parse DNS tracking policy JSON", zap.String("json", jsonObj), zap.Error(err))
			continue
		}

		configs[config.Cookie] = config
	}

	logger.Debug("Retrieved DNS tracking policy configurations", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: GetDnsTrackingPolicyConfig
 *
 * DESCRIPTION:
 *     Retrieves a specific DNS tracking policy configuration by ID.
 *     Uses the floweye dnrt get command to get detailed policy information.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     id     - Policy ID
 *
 * RETURNS:
 *     *DnsTrackingPolicyConfig - DNS tracking policy configuration
 *     error                    - Error if retrieval fails
 *****************************************************************************/
func GetDnsTrackingPolicyConfig(logger *logger.Logger, id int) (*DnsTrackingPolicyConfig, error) {
	logger.Debug("Retrieving DNS tracking policy configuration", zap.Int("id", id))

	// Execute floweye command to get DNS tracking policy
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dnrt", "get", "id="+strconv.Itoa(id))
	if err != nil {
		logger.Error("Failed to execute floweye command to get DNS tracking policy",
			zap.Int("id", id),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get DNS tracking policy: %w", err)
	}

	// Parse output
	config, err := ParseDnsTrackingPolicyFromKeyValue(output)
	if err != nil {
		logger.Error("Failed to parse DNS tracking policy configuration",
			zap.Int("id", id),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to parse DNS tracking policy configuration: %w", err)
	}

	logger.Debug("Retrieved DNS tracking policy configuration",
		zap.Int("id", id),
		zap.Uint32("cookie", config.Cookie))
	return config, nil
}

/*****************************************************************************
 * NAME: GetDnsTrackingPolicyConfigByCookie
 *
 * DESCRIPTION:
 *     Retrieves a specific DNS tracking policy configuration by cookie.
 *     Uses the floweye dnrt get command with cookie parameter to get detailed policy information.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     cookie - Policy cookie
 *
 * RETURNS:
 *     *DnsTrackingPolicyConfig - DNS tracking policy configuration
 *     error                    - Error if retrieval fails
 *****************************************************************************/
func GetDnsTrackingPolicyConfigByCookie(logger *logger.Logger, cookie uint32) (*DnsTrackingPolicyConfig, error) {
	logger.Debug("Retrieving DNS tracking policy configuration by cookie", zap.Uint32("cookie", cookie))

	// Execute floweye command to get DNS tracking policy by cookie
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dnrt", "get", "cookie="+strconv.FormatUint(uint64(cookie), 10))
	if err != nil {
		logger.Error("Failed to execute floweye command to get DNS tracking policy by cookie",
			zap.Uint32("cookie", cookie),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get DNS tracking policy by cookie: %w", err)
	}

	// Parse output
	config, err := ParseDnsTrackingPolicyFromKeyValue(output)
	if err != nil {
		logger.Error("Failed to parse DNS tracking policy configuration by cookie",
			zap.Uint32("cookie", cookie),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to parse DNS tracking policy configuration: %w", err)
	}

	logger.Debug("Retrieved DNS tracking policy configuration by cookie",
		zap.Uint32("cookie", cookie),
		zap.Int("id", config.ID))
	return config, nil
}

/*****************************************************************************
 * NAME: ParseDnsTrackingPolicyFromJSON
 *
 * DESCRIPTION:
 *     Parses a DNS tracking policy configuration from JSON format.
 *     Used for parsing output from floweye dnrt list json=1 command.
 *
 * PARAMETERS:
 *     jsonStr - JSON string containing policy configuration
 *
 * RETURNS:
 *     *DnsTrackingPolicyConfig - Parsed DNS tracking policy configuration
 *     error                    - Error if parsing fails
 *****************************************************************************/
func ParseDnsTrackingPolicyFromJSON(jsonStr string) (*DnsTrackingPolicyConfig, error) {
	// Remove braces and split by comma
	jsonStr = strings.Trim(jsonStr, "{}")
	pairs := strings.Split(jsonStr, ",")

	config := &DnsTrackingPolicyConfig{}
	for _, pair := range pairs {
		parts := strings.SplitN(pair, ":", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.Trim(strings.TrimSpace(parts[0]), "\"")
		value := strings.Trim(strings.TrimSpace(parts[1]), "\"")

		switch key {
		case "polno":
			if id, err := strconv.Atoi(value); err == nil {
				config.ID = id
			}
		case "cookie":
			if cookie, err := strconv.ParseUint(value, 10, 32); err == nil {
				config.Cookie = uint32(cookie)
			}
		case "enable":
			config.Enable = value == "1"
		case "dnsid":
			if dnsID, err := strconv.Atoi(value); err == nil {
				config.DnsID = dnsID
			}
		case "dnsname":
			config.DnsName = value
		case "pxy":
			config.Pxy = value
		case "bkuppxy":
			config.BkupPxy = value
		case "trackhost":
			config.TrackHost = value == "1"
		case "cachettl":
			if cacheTTL, err := strconv.Atoi(value); err == nil {
				config.CacheTTL = cacheTTL
			}
		case "desc":
			config.Desc = value
		case "dnsaddr":
			config.DnsAddr = value
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ParseDnsTrackingPolicyFromKeyValue
 *
 * DESCRIPTION:
 *     Parses a DNS tracking policy configuration from key=value format.
 *     Used for parsing output from floweye dnrt get command.
 *
 * PARAMETERS:
 *     output - Key=value formatted string containing policy configuration
 *
 * RETURNS:
 *     *DnsTrackingPolicyConfig - Parsed DNS tracking policy configuration
 *     error                    - Error if parsing fails
 *****************************************************************************/
func ParseDnsTrackingPolicyFromKeyValue(output string) (*DnsTrackingPolicyConfig, error) {
	// Special handling for floweye dnrt get output which has two 'id' fields:
	// First id is the policy ID, second id is the cookie value
	lines := strings.Split(strings.TrimSpace(output), "\n")

	config := &DnsTrackingPolicyConfig{}
	idCount := 0

	// Parse each line manually to handle duplicate 'id' keys
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "id":
			idCount++
			if id, err := strconv.Atoi(value); err == nil {
				if idCount == 1 {
					// First id is the policy ID
					config.ID = id
				} else if idCount == 2 {
					// Second id is the cookie value
					config.Cookie = uint32(id)
				}
			}
		case "cookie":
			if cookie, err := strconv.ParseUint(value, 10, 32); err == nil {
				config.Cookie = uint32(cookie)
			}
		case "enable":
			config.Enable = value == "1"
		case "dnsid":
			if dnsID, err := strconv.Atoi(value); err == nil {
				config.DnsID = dnsID
			}
		case "dnsname":
			config.DnsName = value
		case "pxy":
			config.Pxy = value
		case "bkuppxy":
			config.BkupPxy = value
		case "trackhost":
			config.TrackHost = value == "1"
		case "cachettl":
			if cacheTTL, err := strconv.Atoi(value); err == nil {
				config.CacheTTL = cacheTTL
			}
		case "desc":
			config.Desc = value
		case "dnsaddr":
			config.DnsAddr = value
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: CompareDnsTrackingPolicyConfig
 *
 * DESCRIPTION:
 *     Compares a DNS tracking policy configuration with local configuration.
 *     Uses converted internal data structure instead of protobuf access.
 *     Checks if the configurations match to determine if updates are needed.
 *
 * PARAMETERS:
 *     logger      - Logger instance for logging operations
 *     configData  - Converted DNS tracking policy configuration data
 *     localConfig - Local DNS tracking policy configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareDnsTrackingPolicyConfig(logger *logger.Logger, configData *DnsTrackingPolicyConfig, localConfig *DnsTrackingPolicyConfig) bool {
	logger.Debug("Comparing DNS tracking policy configurations",
		zap.Uint32("config_cookie", configData.Cookie),
		zap.Uint32("local_cookie", localConfig.Cookie))

	// Compare basic fields using converted data structure
	if configData.Cookie != localConfig.Cookie {
		logger.Debug("Cookie mismatch",
			zap.Uint32("config_cookie", configData.Cookie),
			zap.Uint32("local_cookie", localConfig.Cookie))
		return false
	}

	if configData.Enable != localConfig.Enable {
		logger.Debug("Enable/disable mismatch",
			zap.Bool("config_enable", configData.Enable),
			zap.Bool("local_enable", localConfig.Enable))
		return false
	}

	if configData.Pxy != localConfig.Pxy {
		logger.Debug("Primary proxy mismatch",
			zap.String("config_pxy", configData.Pxy),
			zap.String("local_pxy", localConfig.Pxy))
		return false
	}

	if configData.BkupPxy != localConfig.BkupPxy {
		logger.Debug("Backup proxy mismatch",
			zap.String("config_backup_pxy", configData.BkupPxy),
			zap.String("local_bkuppxy", localConfig.BkupPxy))
		return false
	}

	// Compare optional fields using converted data structure
	if configData.TrackHost != localConfig.TrackHost {
		logger.Debug("Track host mismatch",
			zap.Bool("config_track_host", configData.TrackHost),
			zap.Bool("local_trackhost", localConfig.TrackHost))
		return false
	}

	if configData.CacheTTL != localConfig.CacheTTL {
		logger.Debug("Cache TTL mismatch",
			zap.Int("config_cache_ttl", configData.CacheTTL),
			zap.Int("local_cachettl", localConfig.CacheTTL))
		return false
	}

	if configData.Desc != localConfig.Desc {
		logger.Debug("Description mismatch",
			zap.String("config_desc", configData.Desc),
			zap.String("local_desc", localConfig.Desc))
		return false
	}

	// Compare DNS address using robust IP format comparison
	// For DNS address, treat empty string and "0.0.0.0" as equivalent
	configDnsAddr := configData.DnsAddr
	localDnsAddr := localConfig.DnsAddr

	// Normalize empty string to "0.0.0.0" for DNS address comparison
	if configDnsAddr == "" {
		configDnsAddr = "0.0.0.0"
	}
	if localDnsAddr == "" {
		localDnsAddr = "0.0.0.0"
	}

	if !CompareIPFormats(configDnsAddr, localDnsAddr) {
		logger.Debug("DNS address mismatch",
			zap.String("config_dnsaddr", configData.DnsAddr),
			zap.String("local_dnsaddr", localConfig.DnsAddr))
		return false
	}

	// Compare domain groups using converted data structure
	if configData.DnsName != localConfig.DnsName {
		logger.Debug("Domain group mismatch",
			zap.String("config_domain_group", configData.DnsName),
			zap.String("local_domain_group", localConfig.DnsName))
		return false
	}

	logger.Debug("DNS tracking policy configurations match")
	return true
}

/*****************************************************************************
 * NAME: VerifyDnsTrackingPolicyConfig
 *
 * DESCRIPTION:
 *     Verifies if a DNS tracking policy configuration was applied successfully.
 *     Retrieves the current configuration and compares with the expected configuration.
 *     Reuses comparison logic per CONTRIBUTING.md guidelines.
 *
 * PARAMETERS:
 *     logger     - Logger instance for logging operations
 *     configData - Converted DNS tracking policy configuration data
 *     id         - Policy ID to verify
 *
 * RETURNS:
 *     bool  - True if verification passes, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyDnsTrackingPolicyConfig(logger *logger.Logger, configData *DnsTrackingPolicyConfig, id int) (bool, error) {
	logger.Debug("Verifying DNS tracking policy configuration",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("id", id))

	// Get current configuration
	currentConfig, err := GetDnsTrackingPolicyConfig(logger, id)
	if err != nil {
		logger.Error("Failed to get current DNS tracking policy configuration for verification",
			zap.Int("id", id),
			zap.Error(err))
		return false, fmt.Errorf("failed to get current configuration: %w", err)
	}

	// Reuse comparison logic per CONTRIBUTING.md guidelines
	matches := CompareDnsTrackingPolicyConfig(logger, configData, currentConfig)
	if !matches {
		logger.Warn("DNS tracking policy configuration verification failed",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("id", id))
		return false, nil
	}

	logger.Debug("DNS tracking policy configuration verification passed",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("id", id))
	return true, nil
}
