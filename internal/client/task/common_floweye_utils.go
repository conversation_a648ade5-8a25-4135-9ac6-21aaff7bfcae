// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : common_floweye_utils.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package task

import (
	"sort"
	"strings"
)

// NormalizeAddressFormat normalizes address formats for comparison between config and local values.
// It converts the floweye format "rng,start_ip,end_ip" to the standard ",start_ip-end_ip,".
// This function handles multiple address specifications and various floweye internal formats
// and is used across multiple modules (route policy, flow control policy, etc.). It returns the normalized address string.
//
// Parameters:
//   - address: The address string in either format.
func NormalizeAddressFormat(address string) string {
	if address == "" || address == "any" {
		return ""
	}

	// Handle multiple address specifications separated by semicolons first.
	// This must be checked before single IP/range formats.
	// This handles complex formats like "ip,32,***********;rng,************,**************".
	if strings.Contains(address, ";") {
		var normalizedParts []string
		specs := strings.Split(address, ";")

		for _, spec := range specs {
			spec = strings.TrimSpace(spec)
			if spec == "" {
				continue
			}

			if strings.HasPrefix(spec, "rng,") {
				parts := strings.Split(spec, ",")
				if len(parts) >= 3 {
					normalizedParts = append(normalizedParts, ","+parts[1]+"-"+parts[2]+",")
				}
			} else if strings.HasPrefix(spec, "ip,") {
				parts := strings.Split(spec, ",")
				if len(parts) >= 3 {
					// CIDR format: ip,prefix_len,address
					prefixLen := parts[1]
					ipAddr := parts[2]
					if prefixLen == "32" {
						// For /32, just use the IP address without CIDR notation.
						normalizedParts = append(normalizedParts, ","+ipAddr+",")
					} else {
						// For other prefix lengths, include CIDR notation.
						normalizedParts = append(normalizedParts, ","+ipAddr+"/"+prefixLen+",")
					}
				} else if len(parts) >= 2 {
					// Legacy single IP format: ip,address
					normalizedParts = append(normalizedParts, ","+parts[1]+",")
				}
			}
		}

		if len(normalizedParts) > 0 {
			return strings.Join(normalizedParts, "")
		}
	}

	// Handle single IP format: "ip,***********" or CIDR format: "ip,32,***********".
	// Only process if it's not a semicolon-separated format.
	if !strings.Contains(address, ";") && strings.HasPrefix(address, "ip,") {
		parts := strings.Split(address, ",")
		if len(parts) >= 3 {
			// CIDR format: "ip,32,***********" -> ",***********/32,"
			prefixLen := parts[1]
			ipAddr := parts[2]
			if prefixLen == "32" {
				// For /32, just use the IP address without CIDR notation.
				return "," + ipAddr + ","
			} else {
				// For other prefix lengths, include CIDR notation.
				return "," + ipAddr + "/" + prefixLen + ","
			}
		} else if len(parts) >= 2 {
			// Legacy format: "ip,***********" -> ",***********,"
			return "," + parts[1] + ","
		}
	}

	// Handle floweye internal format: "rng,***********,***********00".
	// Only process if it's not a semicolon-separated format.
	if !strings.Contains(address, ";") && strings.HasPrefix(address, "rng,") {
		parts := strings.Split(address, ",")
		if len(parts) >= 3 {
			return "," + parts[1] + "-" + parts[2] + ","
		}
	}

	// Handle multiple address specifications separated by commas (legacy format).
	// This handles complex formats like "rng,***********,***********00,ip,***********".
	// Only process if it's not a semicolon-separated format.
	if !strings.Contains(address, ";") && (strings.Contains(address, "rng,") || strings.Contains(address, "ip,")) {
		var normalizedParts []string
		parts := strings.Split(address, ",")

		for i := 0; i < len(parts); i++ {
			if parts[i] == "rng" && i+2 < len(parts) {
				// Range format: rng,start_ip,end_ip
				normalizedParts = append(normalizedParts, ","+parts[i+1]+"-"+parts[i+2]+",")
				i += 2 // Skip the next two parts.
			} else if parts[i] == "ip" && i+2 < len(parts) {
				// CIDR format: ip,prefix_len,address
				prefixLen := parts[i+1]
				ipAddr := parts[i+2]
				if prefixLen == "32" {
					// For /32, just use the IP address without CIDR notation.
					normalizedParts = append(normalizedParts, ","+ipAddr+",")
				} else {
					// For other prefix lengths, include CIDR notation.
					normalizedParts = append(normalizedParts, ","+ipAddr+"/"+prefixLen+",")
				}
				i += 2 // Skip the next two parts.
			} else if parts[i] == "ip" && i+1 < len(parts) {
				// Legacy single IP format: ip,address
				normalizedParts = append(normalizedParts, ","+parts[i+1]+",")
				i += 1 // Skip the next part.
			}
		}

		if len(normalizedParts) > 0 {
			return strings.Join(normalizedParts, "")
		}
	}

	// Already in standard format or single IP.
	return address
}

// NormalizeNatIPFormat normalizes NAT IP formats for comparison between config and local values.
// It converts the floweye format "rng,start_ip,end_ip" to the standard "start_ip-end_ip".
// This function handles multiple IP specifications separated by semicolons and is used in route policy and other NAT-related modules.
// It returns the normalized NAT IP string.
//
// Parameters:
//   - natIP: The NAT IP string in either format.
func NormalizeNatIPFormat(natIP string) string {
	if natIP == "" {
		return ""
	}

	// Handle single IP range format: "rng,**************,**************".
	if strings.HasPrefix(natIP, "rng,") {
		parts := strings.Split(natIP, ",")
		if len(parts) >= 3 {
			return parts[1] + "-" + parts[2]
		}
	}

	// Handle single IP format: "ip,***********" or CIDR format: "ip,32,***********".
	if strings.HasPrefix(natIP, "ip,") {
		parts := strings.Split(natIP, ",")
		if len(parts) >= 3 {
			// CIDR format: "ip,32,***********" -> "***********" (for NAT IP, we don't include prefix).
			return parts[2]
		} else if len(parts) >= 2 {
			// Legacy format: "ip,***********" -> "***********".
			return parts[1]
		}
	}

	// Handle multiple IP specifications separated by semicolons.
	if strings.Contains(natIP, ";") {
		specs := strings.Split(natIP, ";")
		var normalizedParts []string

		for _, spec := range specs {
			spec = strings.TrimSpace(spec)
			if spec == "" {
				continue
			}

			if strings.HasPrefix(spec, "rng,") {
				parts := strings.Split(spec, ",")
				if len(parts) >= 3 {
					normalizedParts = append(normalizedParts, parts[1]+"-"+parts[2])
				}
			} else if strings.HasPrefix(spec, "ip,") {
				parts := strings.Split(spec, ",")
				if len(parts) >= 3 {
					normalizedParts = append(normalizedParts, parts[2])
				}
			} else {
				normalizedParts = append(normalizedParts, spec)
			}
		}

		return strings.Join(normalizedParts, ",")
	}

	// Already in standard format or single IP.
	return natIP
}

// ConvertIPFormatForCommand converts an IP address from the floweye internal format to the command format.
// Internal format: "ip,32,***********" or "ip,32,0.0.0.0".
// Command format: ",***********," or ",0.0.0.0,".
// This function is used in flow control policy and potentially other modules, and returns the IP address in command format.
//
// Parameters:
//   - internalFormat: The IP address in floweye internal format.
func ConvertIPFormatForCommand(internalFormat string) string {
	// Handle empty or already converted format.
	if internalFormat == "" || strings.HasPrefix(internalFormat, ",") {
		return internalFormat
	}

	// Handle complex floweye internal format with multiple IP addresses and ranges.
	// Example: "ip,32,*******;rng,*************,***************".
	var convertedParts []string

	// Split by semicolon to handle multiple IP specifications.
	specs := strings.Split(internalFormat, ";")

	for _, spec := range specs {
		spec = strings.TrimSpace(spec)
		if spec == "" {
			continue
		}

		if strings.HasPrefix(spec, "ip,") {
			// Handle single IP: "ip,32,***********".
			parts := strings.Split(spec, ",")
			if len(parts) >= 3 {
				prefixLen := parts[1]
				ipAddr := parts[2]
				if prefixLen == "32" {
					// For /32, just use the IP address without CIDR notation.
					convertedParts = append(convertedParts, ","+ipAddr+",")
				} else {
					// For other prefix lengths, include CIDR notation.
					convertedParts = append(convertedParts, ","+ipAddr+"/"+prefixLen+",")
				}
			} else if len(parts) >= 2 {
				// Legacy format: "ip,***********".
				ipAddr := parts[1]
				convertedParts = append(convertedParts, ","+ipAddr+",")
			}
		} else if strings.HasPrefix(spec, "rng,") {
			// Handle IP range: "rng,*************,***************".
			parts := strings.Split(spec, ",")
			if len(parts) >= 3 {
				startIP := parts[1]
				endIP := parts[2]
				convertedParts = append(convertedParts, ","+startIP+"-"+endIP+",")
			}
		} else {
			// If not in expected format, keep as-is.
			convertedParts = append(convertedParts, spec)
		}
	}

	// Join all converted parts.
	if len(convertedParts) > 0 {
		return strings.Join(convertedParts, "")
	}

	// If no conversion was possible, return original.
	return internalFormat
}

// CompareIPFormats compares two IP address specifications, handling different formats.
// This is the most robust IP comparison function that handles:
//   - Command format (",***********,") vs floweye internal format ("ip,32,***********").
//   - Multiple IP specifications separated by semicolons.
//   - IP ranges in both formats.
//   - Sorting to avoid order differences.
// This function should be used for all IP address comparisons across modules.
// It returns true if the IP specifications are equivalent, false otherwise.
//
// Parameters:
//   - configIP: The IP address specification from the configuration.
//   - localIP: The IP address specification from floweye.
func CompareIPFormats(configIP, localIP string) bool {
	// Handle special cases where "any" and empty string are equivalent.
	if (configIP == "any" && localIP == "") || (configIP == "" && localIP == "any") {
		return true
	}

	// normalizeIPList converts an IP string to a standard format for comparison.
	normalizeIPList := func(ip string) []string {
		if ip == "" || ip == "any" {
			return []string{}
		}

		var result []string

		// Handle command format with multiple IPs: ",***********,,********-********00,".
		if strings.HasPrefix(ip, ",") && strings.HasSuffix(ip, ",") {
			// Split by comma and filter out empty parts.
			parts := strings.Split(strings.Trim(ip, ","), ",")
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if part != "" {
					// Normalize CIDR /32 to plain IP for comparison consistency.
					if strings.HasSuffix(part, "/32") {
						part = strings.TrimSuffix(part, "/32")
					}
					result = append(result, part)
				}
			}
			return result
		}

		// Handle floweye internal format with multiple IPs: "ip,32,***********;rng,********,********00".
		if strings.Contains(ip, ";") {
			// Split by semicolon for multiple IP specifications.
			specs := strings.Split(ip, ";")
			for _, spec := range specs {
				spec = strings.TrimSpace(spec)
				if spec == "" {
					continue
				}

				// Handle individual IP specification.
				if strings.HasPrefix(spec, "ip,") {
					parts := strings.Split(spec, ",")
					if len(parts) >= 3 {
						// CIDR format: "ip,32,***********".
						prefixLen := parts[1]
						ipAddr := parts[2]
						if prefixLen == "32" {
							// For /32, just use the IP address without CIDR notation.
							result = append(result, ipAddr)
						} else {
							// For other prefix lengths, include CIDR notation.
							result = append(result, ipAddr+"/"+prefixLen)
						}
					} else if len(parts) >= 2 {
						// Legacy format: "ip,***********".
						result = append(result, parts[1])
					}
				} else if strings.HasPrefix(spec, "rng,") {
					parts := strings.Split(spec, ",")
					if len(parts) >= 3 {
						result = append(result, parts[1]+"-"+parts[2])
					}
				} else {
					result = append(result, spec)
				}
			}
			return result
		}

		// Handle single IP specification.
		if strings.HasPrefix(ip, "ip,") {
			parts := strings.Split(ip, ",")
			if len(parts) >= 3 {
				// CIDR format: "ip,32,***********".
				prefixLen := parts[1]
				ipAddr := parts[2]
				if prefixLen == "32" {
					// For /32, just use the IP address without CIDR notation.
					return []string{ipAddr}
				} else {
					// For other prefix lengths, include CIDR notation.
					return []string{ipAddr + "/" + prefixLen}
				}
			} else if len(parts) >= 2 {
				// Legacy format: "ip,***********".
				return []string{parts[1]}
			}
		}

		if strings.HasPrefix(ip, "rng,") {
			parts := strings.Split(ip, ",")
			if len(parts) >= 3 {
				return []string{parts[1] + "-" + parts[2]}
			}
		}

		// Return as-is if no special format detected.
		return []string{ip}
	}

	configList := normalizeIPList(configIP)
	localList := normalizeIPList(localIP)

	// Sort both lists for comparison.
	sort.Strings(configList)
	sort.Strings(localList)

	// Compare the sorted lists.
	if len(configList) != len(localList) {
		return false
	}

	for i, configItem := range configList {
		if configItem != localList[i] {
			return false
		}
	}

	return true
}