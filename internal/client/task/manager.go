/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      manager.go
 *
 * DESCRIPTION :    Task manager implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	"agent/internal/metrics"
)

// InitializeTaskManager initializes the task manager and registers all processors
func InitializeTaskManager(log *logger.Logger) *TaskManager {
	// Create metrics
	metrics := metrics.NewMetrics()

	// Create task manager
	taskManager := NewTaskManager(log, metrics)

	// Register interface processor
	interfaceProcessor := NewInterfaceProcessor(log)
	taskManager.RegisterProcessor(interfaceProcessor)

	// Register WAN processor
	wanProcessor := NewWanProcessor(log)
	taskManager.RegisterProcessor(wanProcessor)

	// Register LAN processor
	lanProcessor := NewLanProcessor(log)
	taskManager.RegisterProcessor(lanProcessor)

	// Register DHCP processor
	dhcpProcessor := NewDhcpProcessor(log)
	taskManager.RegisterProcessor(dhcpProcessor)

	// Register WAN Group processor
	wanGroupProcessor := NewWanGroupProcessor(log)
	taskManager.RegisterProcessor(wanGroupProcessor)

	// Register User Group processor
	userGroupProcessor := NewUserGroupProcessor(log)
	taskManager.RegisterProcessor(userGroupProcessor)

	// Register User processor
	userProcessor := NewUserProcessor(log)
	taskManager.RegisterProcessor(userProcessor)

	// Register iWAN Proxy processor
	iwanProxyProcessor := NewIwanProxyProcessor(log)
	taskManager.RegisterProcessor(iwanProxyProcessor)

	// Register iWAN Service processor
	iwanServiceProcessor := NewIwanServiceProcessor(log)
	taskManager.RegisterProcessor(iwanServiceProcessor)

	// Register iWAN Mapping processor
	iwanMappingProcessor := NewIwanMappingProcessor(log)
	taskManager.RegisterProcessor(iwanMappingProcessor)

	// Register SR Proxy processor
	srProxyProcessor := NewSrProxyProcessor(log)
	taskManager.RegisterProcessor(srProxyProcessor)

	// Register IP Group processor
	ipGroupProcessor := NewIpGroupProcessor(log)
	taskManager.RegisterProcessor(ipGroupProcessor)

	// Register Domain Group processor
	domainGroupProcessor := NewDomainGroupProcessor(log)
	taskManager.RegisterProcessor(domainGroupProcessor)

	// Register Effective Time processor
	effectiveTimeProcessor := NewEffectiveTimeProcessor(log)
	taskManager.RegisterProcessor(effectiveTimeProcessor)

	// Register Traffic Channel processor
	trafficChannelProcessor := NewTrafficChannelProcessor(log)
	taskManager.RegisterProcessor(trafficChannelProcessor)

	// Register Traffic Statistics processor
	trafficStatProcessor := NewTrafficStatProcessor(log)
	taskManager.RegisterProcessor(trafficStatProcessor)

	// Register Flow Control processor
	flowControlProcessor := NewFlowControlProcessor(log)
	taskManager.RegisterProcessor(flowControlProcessor)

	// Register Route Policy processor
	routePolicyProcessor := NewRoutePolicyProcessor(log)
	taskManager.RegisterProcessor(routePolicyProcessor)

	// Register DNS Policy processor
	dnsPolicyProcessor := NewDnsPolicyProcessor(log)
	taskManager.RegisterProcessor(dnsPolicyProcessor)

	// Register DNS Tracking Policy processor
	dnsTrackingPolicyProcessor := NewDnsTrackingPolicyProcessor(log)
	taskManager.RegisterProcessor(dnsTrackingPolicyProcessor)

	log.Info("task manager initialized with all processors")

	return taskManager
}
