/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_group_processor.go
 *
 * DESCRIPTION :    User group processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

// UserGroupConfig is defined in user_group_config.go

/*****************************************************************************
 * NAME: UserGroupProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_USER_GROUP type tasks.
 *     Handles user group configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for user group processor operations
 *     localConfigs       - Cache of local user group configurations (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type UserGroupProcessor struct {
	logger             *logger.Logger             // Logger for user group processor operations
	localConfigs       map[int32]*UserGroupConfig // Cache of local user group configurations (used for full sync redundant deletion)
	workingConfigs     map[int32]*UserGroupConfig // Working cache for operations (can be refreshed during full sync)
	fullSyncInProgress bool                       // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewUserGroupProcessor
 *
 * DESCRIPTION:
 *     Creates a new user group processor instance.
 *     Initializes the local and working configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *UserGroupProcessor - Initialized user group processor
 *****************************************************************************/
func NewUserGroupProcessor(log *logger.Logger) *UserGroupProcessor {
	processor := &UserGroupProcessor{
		logger:             log.WithModule("user-group-processor"),
		localConfigs:       make(map[int32]*UserGroupConfig),
		workingConfigs:     make(map[int32]*UserGroupConfig),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - The task type (TASK_USER_GROUP)
 *****************************************************************************/
func (p *UserGroupProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_USER_GROUP
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a user group configuration task.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - The task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *UserGroupProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get user group task data
	userGroupTask := task.GetUserGroupTask()
	if userGroupTask == nil {
		return "User group task data is empty", fmt.Errorf("user group task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "user_group", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Int32("id", userGroupTask.Id),
		zap.Int32("pid", userGroupTask.Pid),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, userGroupTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, userGroupTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles creating or updating a user group configuration.
 *     Used for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     userGroupTask - User group task data
 *     taskAction - The task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *UserGroupProcessor) handleConfigChange(ctx context.Context, userGroupTask *pb.UserGroupTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertUserGroupTaskToConfig(userGroupTask)
	if err != nil {
		p.logger.Error("failed to convert user group task to config",
			zap.String("name", userGroupTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert user group configuration: %v", err), err
	}

	// Validate required fields using converted data
	if configData.ID <= 0 || configData.ID > 2063 {
		return "User group ID must be between 1 and 2063", fmt.Errorf("user group ID must be between 1 and 2063")
	}

	if configData.Name == "" {
		return "User group name is required", fmt.Errorf("user group name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		id := configData.ID // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, id)
		}()
	}

	p.logger.Info("Processing user group configuration",
		zap.Int32("id", configData.ID),
		zap.String("name", configData.Name),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get configurations for operation (uses working configs during full sync)
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Check if user group exists
	_, exists := p.workingConfigs[configData.ID]

	/*
		// If user group exists, check if configuration is the same
		if exists {
			existingConfig := p.workingConfigs[configData.ID]
			if CompareUserGroupConfig(p.logger, configData, existingConfig) {
				p.logger.Info("User group configuration is unchanged, skipping update",
					zap.Int32("id", configData.ID),
					zap.String("name", configData.Name))

				return "User group configuration is unchanged", nil
			}
		}

	*/
	// Build command arguments using converted data
	cmdArgs, err := p.buildUserGroupCommand(configData, exists)
	if err != nil {
		return fmt.Sprintf("Failed to build user group command: %v", err), err
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command for user group configuration",
		zap.Int32("id", configData.ID),
		zap.String("name", configData.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for user group configuration",
			zap.Int32("id", configData.ID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure user group: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.Int32("id", configData.ID),
		zap.String("output", output))

	// Refresh working configs to include the newly created/updated user group
	// This ensures subsequent operations have access to latest configs
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Error("failed to refresh configs after creation/update",
			zap.Int32("id", configData.ID),
			zap.String("name", configData.Name),
			zap.Error(err))
		return fmt.Sprintf("User group %s succeeded but failed to refresh configs: %v",
			map[bool]string{true: "update", false: "creation"}[exists], err), err
	}

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyUserGroupConfig(p.logger, configData)
	if verifyErr != nil {
		p.logger.Error("Failed to verify user group configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify user group configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("User group configuration verification failed")
		return "User group configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("User group configuration applied successfully",
		zap.Int32("id", configData.ID),
		zap.String("name", configData.Name))

	if exists {
		return "User group configuration updated successfully", nil
	}
	return "User group configuration created successfully", nil
}

/*****************************************************************************
 * NAME: buildUserGroupCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for user group configuration.
 *     Uses the unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     configData - Converted user group configuration data
 *     exists     - Whether the user group already exists (for add vs set)
 *
 * RETURNS:
 *     []string - Command arguments for floweye
 *     error    - Error if building command fails
 *****************************************************************************/
func (p *UserGroupProcessor) buildUserGroupCommand(configData *UserGroupConfig, exists bool) ([]string, error) {
	var cmdArgs []string

	if exists {
		// Update existing user group
		cmdArgs = append(cmdArgs, "pppoeippool", "set", "id="+strconv.Itoa(int(configData.ID)))
	} else {
		// Create new user group
		cmdArgs = append(cmdArgs, "pppoeippool", "add", "id="+strconv.Itoa(int(configData.ID)))
	}

	// Add common parameters using converted data
	cmdArgs = append(cmdArgs, "name="+configData.Name)
	cmdArgs = append(cmdArgs, "pid="+strconv.Itoa(int(configData.PID)))

	// Add IPv4 range
	cmdArgs = append(cmdArgs, "start="+configData.StartIP)
	cmdArgs = append(cmdArgs, "end="+configData.EndIP)

	// Add IPv6 prefix
	if configData.Prefix != "" {
		cmdArgs = append(cmdArgs, "prefix="+configData.Prefix)
	} else {
		cmdArgs = append(cmdArgs, "prefix=::")
	}

	// Add IPv6 prefix length
	if configData.PrefixLen != "" {
		cmdArgs = append(cmdArgs, "pfxlen="+configData.PrefixLen)
	} else {
		cmdArgs = append(cmdArgs, "pfxlen=")
	}

	// Add bandwidth limits
	cmdArgs = append(cmdArgs, "ratein="+strconv.Itoa(int(configData.RateIn)))
	cmdArgs = append(cmdArgs, "rateout="+strconv.Itoa(int(configData.RateOut)))
	cmdArgs = append(cmdArgs, "ratein6="+strconv.Itoa(int(configData.RateIn6)))
	cmdArgs = append(cmdArgs, "rateout6="+strconv.Itoa(int(configData.RateOut6)))

	// Add DNS servers
	cmdArgs = append(cmdArgs, "dns="+configData.DNS)

	// Add max online time
	cmdArgs = append(cmdArgs, "maxonlinetime="+strconv.Itoa(int(configData.MaxOnlineTime)))

	// Add expired account policy
	cmdArgs = append(cmdArgs, "clntepa="+configData.ClntEpa)

	// Add VLAN first (required by floweye command)
	cmdArgs = append(cmdArgs, "vlan_first=0")

	return cmdArgs, nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles deleting a user group configuration.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     userGroupTask - User group task data
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *UserGroupProcessor) handleDeleteConfig(ctx context.Context, userGroupTask *pb.UserGroupTask) (string, error) {
	// Validate required fields
	if userGroupTask.GetId() <= 0 {
		return "User group ID is required", fmt.Errorf("user group ID is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		id := userGroupTask.GetId() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, id)
		}()
	}

	p.logger.Info("Processing user group deletion",
		zap.Int32("id", userGroupTask.GetId()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get configurations for operation (uses working configs during full sync)
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Check if user group exists in working configuration
	_, exists := p.workingConfigs[userGroupTask.GetId()]

	// If user group doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("User group not found in working configuration, nothing to delete",
			zap.Int32("id", userGroupTask.GetId()))
		return "User group not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"pppoeippool", "remove", "id=" + strconv.Itoa(int(userGroupTask.GetId())),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete user group",
		zap.Int32("id", userGroupTask.GetId()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("User group already does not exist, treating as successful delete",
				zap.Int32("id", userGroupTask.GetId()))
		} else {
			p.logger.Error("Failed to execute floweye command to delete user group",
				zap.Int32("id", userGroupTask.GetId()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete user group: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.Int32("id", userGroupTask.GetId()),
		zap.String("output", output))

	p.logger.Info("User group deleted successfully",
		zap.Int32("id", userGroupTask.GetId()))

	return "User group deleted successfully", nil
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *UserGroupProcessor) StartFullSync() error {
	p.logger.Info("Starting full sync for user groups")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Deletes any local configurations that were not included in the sync.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *UserGroupProcessor) EndFullSync() {
	p.logger.Info("Ending full sync for user groups")

	// Delete any remaining local configurations
	if p.fullSyncInProgress {
		p.logger.Info("Cleaning up user groups not included in full sync",
			zap.Int("count", len(p.localConfigs)))

		for id, config := range p.localConfigs {
			// Skip default group (ID 1)
			if id == 1 {
				p.logger.Info("Skipping default user group during cleanup",
					zap.Int32("id", id),
					zap.String("name", config.Name))
				continue
			}

			// Delete user group
			p.logger.Info("Deleting user group not included in full sync",
				zap.Int32("id", id),
				zap.String("name", config.Name))

			cmdArgs := []string{
				"pppoeippool", "remove", "id=" + strconv.Itoa(int(id)),
			}

			output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
			if err != nil {
				p.logger.Error("Failed to delete user group during cleanup",
					zap.Int32("id", id),
					zap.Error(err),
					zap.String("output", output))
			}
		}
	}

	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[int32]*UserGroupConfig)
	p.workingConfigs = make(map[int32]*UserGroupConfig)
}

/*****************************************************************************
 * NAME: fetchUserGroupConfigs
 *
 * DESCRIPTION:
 *     Fetches user group configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[int32]*UserGroupConfig - User group configurations by ID
 *     error                      - Error if fetch fails
 *****************************************************************************/
func (p *UserGroupProcessor) fetchUserGroupConfigs() (map[int32]*UserGroupConfig, error) {
	p.logger.Debug("Fetching user group configurations from floweye")

	// Get user group configurations using existing function
	configs, err := GetLocalUserGroupConfigs(p.logger)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user group configs: %w", err)
	}

	p.logger.Debug("Fetched user group configurations",
		zap.Int("count", len(configs)))

	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local user group configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *UserGroupProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local user group configurations")

	configs, err := p.fetchUserGroupConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs

	p.logger.Debug("refreshed local user group configurations",
		zap.Int("user_groups", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working user group configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *UserGroupProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working user group configurations")

	configs, err := p.fetchUserGroupConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs

	p.logger.Debug("refreshed working user group configurations",
		zap.Int("user_groups", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like creation, update, deletion, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *UserGroupProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}
