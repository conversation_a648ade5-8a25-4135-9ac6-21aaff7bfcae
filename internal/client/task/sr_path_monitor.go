/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      sr_path_monitor.go
 *
 * DESCRIPTION :    SR Path monitoring and switching system for SDWAN CPE
 *
 * AUTHOR :         wei
 *
 * HISTORY :        06/19/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	"agent/internal/utils"
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: SRPath
 *
 * DESCRIPTION:
 *     Represents a single SR path configuration.
 *     Contains the path links information.
 *
 * FIELDS:
 *     Links - List of iWAN Proxy segment identifiers for this path
 *****************************************************************************/
type SRPath struct {
	Links []int32 // iWAN Proxy分段标识列表
}

/*****************************************************************************
 * NAME: SRPathInfo
 *
 * DESCRIPTION:
 *     Represents SR instance information for monitoring.
 *     Contains all available paths and current active path.
 *
 * FIELDS:
 *     Name         - SR instance name
 *     Paths        - All available paths
 *     ActivePath   - Current active path index
 *     FailCount    - Consecutive failure count for current path
 *     LastCheck    - Last check timestamp
 *****************************************************************************/
type SRPathInfo struct {
	Name       string    // SR实例名称
	Paths      []SRPath  // 所有可用的路径
	ActivePath int       // 当前使用的路径索引
	FailCount  int       // 当前路径连续失败次数
	LastCheck  time.Time // 最后检查时间
}

/*****************************************************************************
 * NAME: SRMonitorConfig
 *
 * DESCRIPTION:
 *     Configuration for SR path monitoring.
 *
 * FIELDS:
 *     CheckInterval   - Interval between path checks
 *     MaxFailCount    - Maximum failures before switching path
 *     IsPathActiveFn  - Function to check if a path is active
 *     SetPathFn       - Function to set a new path
 *****************************************************************************/
type SRMonitorConfig struct {
	CheckInterval  time.Duration                                // 检测间隔
	MaxFailCount   int                                          // 最大失败次数
	IsPathActiveFn func(srName string, path SRPath) bool       // 判断路径是否活跃的回调函数
	SetPathFn      func(srName string, path SRPath) error      // 设置新路径的回调函数
}

/*****************************************************************************
 * NAME: SRPathMonitor
 *
 * DESCRIPTION:
 *     Interface for SR path monitoring operations.
 *****************************************************************************/
type SRPathMonitor interface {
	Register(srName string, paths []SRPath) error
	Unregister(srName string) error
	Start(ctx context.Context) error
	Stop() error
	GetStatus(srName string) (*SRPathInfo, error)
}

/*****************************************************************************
 * NAME: srPathMonitorImpl
 *
 * DESCRIPTION:
 *     Implementation of SR path monitoring system.
 *
 * FIELDS:
 *     logger      - Logger instance
 *     config      - Monitor configuration
 *     srInstances - Map of monitored SR instances
 *     mutex       - Read-write mutex for thread safety
 *     ctx         - Context for lifecycle management
 *     cancel      - Cancel function for stopping monitor
 *     running     - Flag indicating if monitor is running
 *****************************************************************************/
type srPathMonitorImpl struct {
	logger      *logger.Logger
	config      *SRMonitorConfig
	srInstances map[string]*SRPathInfo
	mutex       sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	running     bool
}

/*****************************************************************************
 * NAME: NewSRPathMonitor
 *
 * DESCRIPTION:
 *     Creates a new SR path monitor instance.
 *
 * PARAMETERS:
 *     logger - Logger instance
 *     config - Monitor configuration
 *
 * RETURNS:
 *     SRPathMonitor - New monitor instance
 *****************************************************************************/
func NewSRPathMonitor(logger *logger.Logger, config *SRMonitorConfig) SRPathMonitor {
	return &srPathMonitorImpl{
		logger:      logger.WithModule("sr-path-monitor"),
		config:      config,
		srInstances: make(map[string]*SRPathInfo),
		running:     false,
	}
}

/*****************************************************************************
 * NAME: Register
 *
 * DESCRIPTION:
 *     Registers an SR instance for monitoring.
 *     This operation is idempotent - calling it multiple times with the same
 *     srName will overwrite the previous configuration.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *     paths  - List of available paths
 *
 * RETURNS:
 *     error - Error if registration fails
 *****************************************************************************/
func (m *srPathMonitorImpl) Register(srName string, paths []SRPath) error {
	if srName == "" {
		return fmt.Errorf("SR name cannot be empty")
	}

	if len(paths) == 0 {
		return fmt.Errorf("at least one path must be provided")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 创建或更新SR实例信息
	srInfo := &SRPathInfo{
		Name:       srName,
		Paths:      make([]SRPath, len(paths)),
		ActivePath: 0, // 默认使用第一个路径
		FailCount:  0,
		LastCheck:  time.Now(),
	}

	// 深拷贝路径信息
	for i, path := range paths {
		srInfo.Paths[i] = SRPath{
			Links: make([]int32, len(path.Links)),
		}
		copy(srInfo.Paths[i].Links, path.Links)
	}

	m.srInstances[srName] = srInfo

	m.logger.Info("SR instance registered for monitoring",
		zap.String("name", srName),
		zap.Int("path_count", len(paths)),
		zap.Int("active_path", srInfo.ActivePath))

	return nil
}

/*****************************************************************************
 * NAME: Unregister
 *
 * DESCRIPTION:
 *     Removes an SR instance from monitoring.
 *
 * PARAMETERS:
 *     srName - SR instance name to remove
 *
 * RETURNS:
 *     error - Error if unregistration fails
 *****************************************************************************/
func (m *srPathMonitorImpl) Unregister(srName string) error {
	if srName == "" {
		return fmt.Errorf("SR name cannot be empty")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.srInstances[srName]; !exists {
		m.logger.Warn("SR instance not found for unregistration",
			zap.String("name", srName))
		return nil // 幂等操作，不存在也返回成功
	}

	delete(m.srInstances, srName)

	m.logger.Info("SR instance unregistered from monitoring",
		zap.String("name", srName))

	return nil
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the background monitoring process.
 *
 * PARAMETERS:
 *     ctx - Context for lifecycle management
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (m *srPathMonitorImpl) Start(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.running {
		return fmt.Errorf("monitor is already running")
	}

	m.ctx, m.cancel = context.WithCancel(ctx)
	m.running = true

	// 启动后台监控协程
	go m.monitorLoop()

	m.logger.Info("SR path monitor started",
		zap.Duration("check_interval", m.config.CheckInterval),
		zap.Int("max_fail_count", m.config.MaxFailCount))

	return nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the background monitoring process.
 *
 * RETURNS:
 *     error - Error if stop fails
 *****************************************************************************/
func (m *srPathMonitorImpl) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.running {
		return nil // 已经停止，幂等操作
	}

	if m.cancel != nil {
		m.cancel()
	}

	m.running = false

	m.logger.Info("SR path monitor stopped")

	return nil
}

/*****************************************************************************
 * NAME: GetStatus
 *
 * DESCRIPTION:
 *     Gets the current status of an SR instance.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *
 * RETURNS:
 *     *SRPathInfo - Current status information
 *     error       - Error if SR instance not found
 *****************************************************************************/
func (m *srPathMonitorImpl) GetStatus(srName string) (*SRPathInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	srInfo, exists := m.srInstances[srName]
	if !exists {
		return nil, fmt.Errorf("SR instance not found: %s", srName)
	}

	// 返回副本以避免并发修改
	result := &SRPathInfo{
		Name:       srInfo.Name,
		Paths:      make([]SRPath, len(srInfo.Paths)),
		ActivePath: srInfo.ActivePath,
		FailCount:  srInfo.FailCount,
		LastCheck:  srInfo.LastCheck,
	}

	for i, path := range srInfo.Paths {
		result.Paths[i] = SRPath{
			Links: make([]int32, len(path.Links)),
		}
		copy(result.Paths[i].Links, path.Links)
	}

	return result, nil
}

/*****************************************************************************
 * NAME: monitorLoop
 *
 * DESCRIPTION:
 *     Main monitoring loop that runs in background.
 *     Periodically checks all registered SR instances and switches paths
 *     when necessary.
 *****************************************************************************/
func (m *srPathMonitorImpl) monitorLoop() {
	ticker := time.NewTicker(m.config.CheckInterval)
	defer ticker.Stop()

	m.logger.Info("SR path monitoring loop started")

	for {
		select {
		case <-m.ctx.Done():
			m.logger.Info("SR path monitoring loop stopped")
			return

		case <-ticker.C:
			m.checkAllPaths()
		}
	}
}

/*****************************************************************************
 * NAME: checkAllPaths
 *
 * DESCRIPTION:
 *     Checks all registered SR instances and their current paths.
 *     Switches paths if current path is not active for too long.
 *****************************************************************************/
func (m *srPathMonitorImpl) checkAllPaths() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if len(m.srInstances) == 0 {
		return // 没有需要监控的实例
	}

	m.logger.Debug("Checking all SR paths", zap.Int("instance_count", len(m.srInstances)))

	for srName, srInfo := range m.srInstances {
		m.checkSinglePath(srName, srInfo)
	}
}

/*****************************************************************************
 * NAME: checkSinglePath
 *
 * DESCRIPTION:
 *     Checks a single SR instance's current path and switches if necessary.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *     srInfo - SR instance information
 *****************************************************************************/
func (m *srPathMonitorImpl) checkSinglePath(srName string, srInfo *SRPathInfo) {
	if srInfo.ActivePath >= len(srInfo.Paths) {
		m.logger.Error("Invalid active path index",
			zap.String("name", srName),
			zap.Int("active_path", srInfo.ActivePath),
			zap.Int("path_count", len(srInfo.Paths)))
		return
	}

	currentPath := srInfo.Paths[srInfo.ActivePath]
	srInfo.LastCheck = time.Now()

	// 检查当前路径是否活跃
	isActive := m.config.IsPathActiveFn(srName, currentPath)

	if isActive {
		// 路径活跃，重置失败计数
		if srInfo.FailCount > 0 {
			m.logger.Info("SR path recovered",
				zap.String("name", srName),
				zap.Int("path_index", srInfo.ActivePath),
				zap.Int("previous_fail_count", srInfo.FailCount))
		}
		srInfo.FailCount = 0
	} else {
		// 路径不活跃，增加失败计数
		srInfo.FailCount++
		m.logger.Warn("SR path check failed",
			zap.String("name", srName),
			zap.Int("path_index", srInfo.ActivePath),
			zap.Int("fail_count", srInfo.FailCount),
			zap.Int("max_fail_count", m.config.MaxFailCount))

		// 检查是否需要切换路径
		if srInfo.FailCount >= m.config.MaxFailCount {
			m.switchToNextPath(srName, srInfo)
		}
	}
}

/*****************************************************************************
 * NAME: switchToNextPath
 *
 * DESCRIPTION:
 *     Switches to the next available path for an SR instance.
 *
 * PARAMETERS:
 *     srName - SR instance name
 *     srInfo - SR instance information
 *****************************************************************************/
func (m *srPathMonitorImpl) switchToNextPath(srName string, srInfo *SRPathInfo) {
	if len(srInfo.Paths) <= 1 {
		m.logger.Warn("Only one path available, cannot switch",
			zap.String("name", srName))
		return
	}

	// 计算下一个路径索引
	nextPathIndex := (srInfo.ActivePath + 1) % len(srInfo.Paths)
	nextPath := srInfo.Paths[nextPathIndex]

	m.logger.Info("Switching SR path",
		zap.String("name", srName),
		zap.Int("from_path", srInfo.ActivePath),
		zap.Int("to_path", nextPathIndex),
		zap.Any("new_links", nextPath.Links))

	// 调用设置路径的回调函数
	if err := m.config.SetPathFn(srName, nextPath); err != nil {
		m.logger.Error("Failed to switch SR path",
			zap.String("name", srName),
			zap.Int("to_path", nextPathIndex),
			zap.Error(err))
		return
	}

	// 更新活跃路径和重置失败计数
	srInfo.ActivePath = nextPathIndex
	srInfo.FailCount = 0

	m.logger.Info("SR path switched successfully",
		zap.String("name", srName),
		zap.Int("new_active_path", nextPathIndex))
}

/*****************************************************************************
 * NAME: DefaultIsPathActiveFn
 *
 * DESCRIPTION:
 *     Default implementation for checking if an SR path is active.
 *     Uses floweye command: floweye nat getproxy sr1 | grep sre_srpxy.active
 *     sre_srpxy.active=1 means active.
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     func - Function that checks if a path is active
 *****************************************************************************/
func DefaultIsPathActiveFn(logger *logger.Logger) func(string, SRPath) bool {
	return func(srName string, path SRPath) bool {
		// 使用 floweye 命令检查 SR proxy 状态
		output, err := utils.ExecuteCommand(logger, 5, "floweye", "nat", "getproxy", srName)
		if err != nil {
			logger.Debug("Failed to get SR proxy status",
				zap.String("name", srName),
				zap.Error(err))
			return false
		}

		// 解析输出，检查 sre_srpxy.active 字段
		configMap := parseSRKeyValueOutput(output)
		activeStatus, exists := configMap["sre_srpxy.active"]
		if !exists {
			logger.Debug("No sre_srpxy.active field found in SR proxy output",
				zap.String("name", srName))
			return false
		}

		// sre_srpxy.active=1 表示 active
		isActive := activeStatus == "1"
		logger.Debug("SR proxy active status check",
			zap.String("name", srName),
			zap.String("sre_srpxy.active", activeStatus),
			zap.Bool("is_active", isActive))

		return isActive
	}
}

/*****************************************************************************
 * NAME: DefaultSetPathFn
 *
 * DESCRIPTION:
 *     Default implementation for setting an SR path.
 *     Uses floweye command to update SR proxy links.
 *
 * PARAMETERS:
 *     logger - Logger instance
 *
 * RETURNS:
 *     func - Function that sets a new path
 *****************************************************************************/
func DefaultSetPathFn(logger *logger.Logger) func(string, SRPath) error {
	return func(srName string, path SRPath) error {
		if len(path.Links) == 0 {
			return fmt.Errorf("path links cannot be empty")
		}

		// 构建 links 参数
		linkStrs := make([]string, len(path.Links))
		for i, link := range path.Links {
			linkStrs[i] = strconv.Itoa(int(link))
		}
		linksParam := strings.Join(linkStrs, ",")

		// 执行 floweye 命令设置新路径
		cmdArgs := []string{"nat", "setsrpxy", srName, "links=" + linksParam}

		logger.Info("Setting SR path",
			zap.String("name", srName),
			zap.String("links", linksParam),
			zap.Strings("cmd_args", cmdArgs))

		output, err := utils.ExecuteCommand(logger, 10, "floweye", cmdArgs...)
		if err != nil {
			logger.Error("Failed to set SR path",
				zap.String("name", srName),
				zap.String("links", linksParam),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Errorf("failed to set SR path: %w", err)
		}

		logger.Info("SR path set successfully",
			zap.String("name", srName),
			zap.String("links", linksParam))

		return nil
	}
}

/*****************************************************************************
 * NAME: parseSRKeyValueOutput
 *
 * DESCRIPTION:
 *     Parses key-value output from floweye commands into a map.
 *     Handles output format like "key=value" lines.
 *
 * PARAMETERS:
 *     output - Raw output string from floweye command
 *
 * RETURNS:
 *     map[string]string - Map of key-value pairs
 *****************************************************************************/
func parseSRKeyValueOutput(output string) map[string]string {
	configMap := make(map[string]string)
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			configMap[key] = value
		}
	}

	return configMap
}
