/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_proxy_config.go
 *
 * DESCRIPTION :    iWAN Proxy configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: IwanProxyConfig
 *
 * DESCRIPTION:
 *     Unified configuration structure for iWAN Proxy interface processing.
 *     Serves as both system configuration storage and internal processing structure.
 *     Extended to support all protobuf fields while maintaining compatibility.
 *
 * FIELDS:
 *     Name          - iWAN Proxy interface name
 *     Ifname        - Carrier line name
 *     Ifname2       - Backup carrier line name (added for protobuf support)
 *     MTU           - Maximum transmission unit
 *     SvrAddr       - Server IP/domain name
 *     SvrPort       - Server port
 *     Username      - iWAN account
 *     Password      - iWAN password
 *     Encrypt       - Whether to encrypt
 *     Link          - Segment agreement
 *     PingIP        - Heartbeat server 1
 *     PingIP2       - Heartbeat server 2
 *     MaxDelay      - Maximum delay in ms
 *     DnsPxy        - DNS proxy switch
 *****************************************************************************/
type IwanProxyConfig struct {
	Name     string
	Ifname   string
	Ifname2  string // Backup carrier line name
	MTU      int32
	SvrAddr  string
	SvrPort  int32
	Username string
	Password string
	Encrypt  bool
	Link     int32
	PingIP   string
	PingIP2  string
	MaxDelay int32
	DnsPxy   bool
}

/*****************************************************************************
 * NAME: IwanProxyListItem
 *
 * DESCRIPTION:
 *     Represents an iWAN Proxy item from floweye nat listproxy JSON output.
 *     Used for parsing the JSON response from floweye command.
 *
 * FIELDS:
 *     ID       - Proxy ID
 *     Name     - Proxy name
 *     Type     - Proxy type (should be "iwan" for iWAN proxies)
 *     State    - Proxy state
 *     Disable  - Whether proxy is disabled
 *     MTU      - Maximum transmission unit
 *     If       - Interface name
 *     IP       - IP address
 *     GW       - Gateway address
 *     Mask     - Network mask
 *     VLAN     - VLAN configuration
 *****************************************************************************/
type IwanProxyListItem struct {
	ID      int    `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"`
	State   int    `json:"state"`
	Disable int    `json:"disable"`
	MTU     int    `json:"mtu"`
	If      string `json:"if"`
	IP      string `json:"ip"`
	GW      string `json:"gw"`
	Mask    string `json:"mask"`
	VLAN    string `json:"vlan"`
}

/*****************************************************************************
 * NAME: ConvertIwanProxyTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf IwanProxyTask message to unified IwanProxyConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing IwanProxyConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     iwanTask - Protobuf iWAN Proxy task message to convert
 *
 * RETURNS:
 *     *IwanProxyConfig - Converted iWAN Proxy configuration structure
 *     error            - Error if conversion fails
 *****************************************************************************/
func ConvertIwanProxyTaskToConfig(iwanTask *pb.IwanProxyTask) (*IwanProxyConfig, error) {
	if iwanTask == nil {
		return nil, fmt.Errorf("iwanProxyTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &IwanProxyConfig{
		Name:     iwanTask.GetName(),
		Ifname:   iwanTask.GetIfname(),
		Ifname2:  iwanTask.GetIfname2(),
		MTU:      iwanTask.GetMtu(),
		SvrAddr:  iwanTask.GetSvrAddr(),
		SvrPort:  iwanTask.GetSvrPort(),
		Username: iwanTask.GetUsername(),
		Password: iwanTask.GetPassword(),
		Encrypt:  iwanTask.GetEncrypt(),
		Link:     iwanTask.GetLink(),
		DnsPxy:   iwanTask.GetDnsPxy(),
		// Default values for heartbeat fields
		PingIP:   "0.0.0.0",
		PingIP2:  "0.0.0.0",
		MaxDelay: 0,
	}

	// Extract heartbeat configuration if present
	if iwanTask.GetHeartbeat() != nil {
		heartbeat := iwanTask.GetHeartbeat()

		// Convert ping IP addresses
		if heartbeat.GetPingIp() != nil {
			config.PingIP = utils.GetIpString(heartbeat.GetPingIp())
		}
		if heartbeat.GetPingIp2() != nil {
			config.PingIP2 = utils.GetIpString(heartbeat.GetPingIp2())
		}

		// Set max delay
		config.MaxDelay = heartbeat.GetMaxDelay()
	}

	return config, nil
}

/*****************************************************************************
 * NAME: GetLocalIwanProxyConfigs
 *
 * DESCRIPTION:
 *     Retrieves all iWAN Proxy configurations from the system.
 *     First executes floweye command to get all iWAN Proxy names,
 *     then executes floweye command to get detailed configuration for each name.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*IwanProxyConfig - Map of iWAN Proxy configurations indexed by name
 *     error - Error if retrieving configurations fails
 *****************************************************************************/
func GetLocalIwanProxyConfigs(logger *logger.Logger) (map[string]*IwanProxyConfig, error) {
	logger.Debug("Getting local iWAN Proxy configurations")

	// Initialize configurations map
	configs := make(map[string]*IwanProxyConfig)

	// Execute floweye command to get proxy list with JSON output
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "listproxy", "type=wan", "json=1")
	if err != nil {
		logger.Error("Failed to execute floweye command for proxy list",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to execute floweye command: %w", err)
	}

	logger.Debug("Floweye command executed successfully",
		zap.String("output", output))

	// Parse JSON output to get iWAN Proxy names
	var iwanProxyNames []string
	output = strings.TrimSpace(output)
	if output == "" {
		logger.Debug("No proxy configurations found")
		return configs, nil
	}

	// Parse JSON output using unified floweye JSON parser
	var proxyItems []IwanProxyListItem
	if err := utils.ParseFloweyeJSONToType(output, &proxyItems); err != nil {
		logger.Error("Failed to parse JSON proxy list",
			zap.String("output", output),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse JSON output: %w", err)
	}

	// Filter for iWAN type only (according to floweye documentation)
	for _, proxyItem := range proxyItems {
		if proxyItem.Type != "iwan" {
			logger.Debug("Skipping non-iWAN proxy",
				zap.String("name", proxyItem.Name),
				zap.String("type", proxyItem.Type))
			continue
		}

		logger.Debug("Found iWAN proxy",
			zap.String("name", proxyItem.Name),
			zap.Int("id", proxyItem.ID))

		// Add iWAN Proxy name to list
		iwanProxyNames = append(iwanProxyNames, proxyItem.Name)
	}

	// Get detailed configuration for each iWAN Proxy
	for _, name := range iwanProxyNames {
		config, err := GetIwanProxyConfig(logger, name)
		if err != nil {
			logger.Warn("Failed to get iWAN Proxy configuration",
				zap.String("name", name),
				zap.Error(err))
			continue
		}

		// Add to configurations map
		configs[name] = config
	}

	logger.Info("Retrieved local iWAN Proxy configurations",
		zap.Int("count", len(configs)))

	return configs, nil
}

/*****************************************************************************
 * NAME: GetIwanProxyConfig
 *
 * DESCRIPTION:
 *     Retrieves a specific iWAN Proxy configuration from the system.
 *     Executes floweye command to get the iWAN Proxy and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the iWAN Proxy to retrieve
 *
 * RETURNS:
 *     *IwanProxyConfig - iWAN Proxy configuration
 *     error - Error if retrieving configuration fails
 *****************************************************************************/
func GetIwanProxyConfig(logger *logger.Logger, name string) (*IwanProxyConfig, error) {
	logger.Debug("Getting iWAN Proxy configuration", zap.String("name", name))

	// Execute floweye command to get iWAN Proxy
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "getproxy", name)
	if err != nil {
		logger.Error("Failed to execute floweye command to get iWAN Proxy",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get iWAN Proxy: %w", err)
	}

	// Parse output
	lines := strings.Split(output, "\n")
	configMap := make(map[string]string)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		configMap[key] = value
	}

	// Check if iWAN Proxy exists
	iwanProxyName, ok := configMap["name"]
	if !ok {
		logger.Error("iWAN Proxy name not found in configuration")
		return nil, fmt.Errorf("iWAN Proxy not found")
	}

	// Parse MTU
	mtu := int32(0)
	if mtuStr, ok := configMap["cfgmtu"]; ok {
		mtuVal, err := strconv.ParseInt(mtuStr, 10, 32)
		if err == nil {
			mtu = int32(mtuVal)
		}
	}

	// Parse server port
	svrPort := int32(0)
	if svrPortStr, ok := configMap["svrport"]; ok {
		svrPortVal, err := strconv.ParseInt(svrPortStr, 10, 32)
		if err == nil {
			svrPort = int32(svrPortVal)
		}
	}

	// Parse encrypt (use cfgencrypt field according to floweye documentation)
	encrypt := false
	if encryptStr, ok := configMap["cfgencrypt"]; ok {
		encrypt = encryptStr == "1" || encryptStr == "true"
	}

	// Parse max delay
	maxDelay := int32(0)
	if maxDelayStr, ok := configMap["maxdelay"]; ok {
		maxDelayVal, err := strconv.ParseInt(maxDelayStr, 10, 32)
		if err == nil {
			maxDelay = int32(maxDelayVal)
		}
	}

	// Parse DNS proxy
	dnsPxy := false
	if dnsPxyStr, ok := configMap["dnspxy"]; ok {
		dnsPxy = dnsPxyStr == "1" || dnsPxyStr == "true"
	}

	// Parse link (use srlink_link field according to floweye output)
	link := int32(0)
	if linkStr, ok := configMap["srlink_link"]; ok && linkStr != "" {
		linkVal, err := strconv.ParseInt(linkStr, 10, 32)
		if err == nil {
			link = int32(linkVal)
		}
	}

	// Create iWAN Proxy config
	config := &IwanProxyConfig{
		Name:     iwanProxyName,
		Ifname:   configMap["ifname"],
		Ifname2:  configMap["ifname2"], // Backup carrier line name
		MTU:      mtu,
		SvrAddr:  configMap["svraddr"],
		SvrPort:  svrPort,
		Username: configMap["username"],
		Password: configMap["password"],
		Encrypt:  encrypt,
		Link:     link,
		PingIP:   configMap["pingip"],
		PingIP2:  configMap["pingip2"],
		MaxDelay: maxDelay,
		DnsPxy:   dnsPxy,
	}

	logger.Debug("Retrieved iWAN Proxy configuration",
		zap.String("name", name),
		zap.String("ifname", config.Ifname))

	return config, nil
}

/*****************************************************************************
 * NAME: VerifyIwanProxyConfig
 *
 * DESCRIPTION:
 *     Verifies that an iWAN Proxy configuration was applied correctly.
 *     Gets the current configuration from the system and compares it with the expected values.
 *     Uses converted data structure to eliminate repeated protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected iWAN Proxy configuration (converted from protobuf)
 *
 * RETURNS:
 *     bool  - True if verification succeeds, false otherwise
 *     error - Error if verification process fails
 *****************************************************************************/
func VerifyIwanProxyConfig(logger *logger.Logger, expectedConfig *IwanProxyConfig) (bool, error) {
	logger.Debug("Verifying iWAN Proxy configuration",
		zap.String("name", expectedConfig.Name))

	// Get iWAN Proxy configuration
	config, err := GetIwanProxyConfig(logger, expectedConfig.Name)
	if err != nil {
		logger.Error("Failed to get iWAN Proxy configuration",
			zap.String("name", expectedConfig.Name),
			zap.Error(err))
		return false, err
	}

	// Use CompareIwanProxyConfig to verify the configuration
	if !CompareIwanProxyConfig(logger, expectedConfig, config) {
		logger.Error("iWAN Proxy configuration verification failed",
			zap.String("name", expectedConfig.Name))
		return false, nil
	}

	// Verification passed
	logger.Info("iWAN Proxy configuration verified successfully",
		zap.String("name", expectedConfig.Name))

	return true, nil
}

/*****************************************************************************
 * NAME: CompareIwanProxyConfig
 *
 * DESCRIPTION:
 *     Compares an iWAN Proxy configuration with a local configuration.
 *     Checks if the expected configuration matches the local configuration.
 *     Uses converted data structure to eliminate repeated protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected iWAN Proxy configuration (converted from protobuf)
 *     localConfig    - Local iWAN Proxy configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareIwanProxyConfig(logger *logger.Logger, expectedConfig *IwanProxyConfig, localConfig *IwanProxyConfig) bool {
	logger.Debug("Comparing iWAN Proxy configuration",
		zap.String("name", expectedConfig.Name))

	// Compare basic fields
	if expectedConfig.Name != localConfig.Name ||
		expectedConfig.Ifname != localConfig.Ifname ||
		expectedConfig.MTU != localConfig.MTU ||
		expectedConfig.SvrAddr != localConfig.SvrAddr ||
		expectedConfig.SvrPort != localConfig.SvrPort ||
		expectedConfig.Username != localConfig.Username ||
		expectedConfig.Password != localConfig.Password {
		return false
	}

	// Compare Ifname2 with default value logic
	// If expected Ifname2 is empty, it should default to Ifname
	expectedIfname2 := expectedConfig.Ifname2
	if expectedIfname2 == "" {
		expectedIfname2 = expectedConfig.Ifname
	}
	if expectedIfname2 != localConfig.Ifname2 {
		return false
	}

	// Compare optional fields
	if expectedConfig.Encrypt != localConfig.Encrypt ||
		expectedConfig.Link != localConfig.Link {
		return false
	}

	// TODO: Temporarily skip DNS proxy validation due to floweye command bug
	// The floweye command doesn't properly set dnspxy=1 even when passed correctly
	// if expectedConfig.DnsPxy != localConfig.DnsPxy {
	//     return false
	// }

	// Compare heartbeat configuration
	if expectedConfig.PingIP != localConfig.PingIP ||
		expectedConfig.PingIP2 != localConfig.PingIP2 ||
		expectedConfig.MaxDelay != localConfig.MaxDelay {
		return false
	}

	// Configurations match
	logger.Debug("iWAN Proxy configurations match",
		zap.String("name", expectedConfig.Name))

	return true

}
