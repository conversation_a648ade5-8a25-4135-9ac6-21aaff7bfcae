/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_stat_config.go
 *
 * DESCRIPTION :    Traffic statistics configuration data structures and parsing functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/15/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: TrafficStatConfig
 *
 * DESCRIPTION:
 *     Represents a traffic statistics configuration.
 *     Contains statistics name, tracking settings, and runtime data.
 *
 * FIELDS:
 *     ID       - Traffic statistics ID (auto-generated by system)
 *     Name     - Traffic statistics name (unique identifier)
 *     TrackIP  - Track IP flag (true=track IP, false=don't track)
 *     UpBps    - Upload bits per second (runtime data)
 *     DnBps    - Download bits per second (runtime data)
 *     UpBytes  - Upload bytes total (runtime data)
 *     DnBytes  - Download bytes total (runtime data)
 *****************************************************************************/
type TrafficStatConfig struct {
	ID      int    // Traffic statistics ID (auto-generated by system)
	Name    string // Traffic statistics name (unique identifier)
	TrackIP bool   // Track IP flag (true=track IP, false=don't track)
	UpBps   int64  // Upload bits per second (runtime data)
	DnBps   int64  // Download bits per second (runtime data)
	UpBytes int64  // Upload bytes total (runtime data)
	DnBytes int64  // Download bytes total (runtime data)
}

/*****************************************************************************
 * NAME: ConvertTrafficStatTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a TrafficStatTask protobuf message to TrafficStatConfig structure.
 *     This is the single conversion point for protobuf to internal data structure.
 *     Handles all protobuf field types and applies appropriate default values.
 *
 * PARAMETERS:
 *     task - TrafficStatTask protobuf message
 *
 * RETURNS:
 *     *TrafficStatConfig - Converted traffic statistics configuration
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertTrafficStatTaskToConfig(task *pb.TrafficStatTask) (*TrafficStatConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("trafficStatTask is nil")
	}

	config := &TrafficStatConfig{
		// Basic fields from protobuf
		Name:    task.GetName(),
		TrackIP: task.GetTrackIp(),

		// Runtime fields with default values (not set from protobuf)
		ID:      0, // Will be set by system after creation
		UpBps:   0, // Runtime data, default to 0
		DnBps:   0, // Runtime data, default to 0
		UpBytes: 0, // Runtime data, default to 0
		DnBytes: 0, // Runtime data, default to 0
	}

	// Validate required fields
	if config.Name == "" {
		return nil, fmt.Errorf("traffic statistics name is required")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ParseTrafficStatFromList
 *
 * DESCRIPTION:
 *     Parses traffic statistics configurations from floweye ntmso list output.
 *     Expected JSON format: {"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
 *
 * PARAMETERS:
 *     output - Output from floweye ntmso list command
 *
 * RETURNS:
 *     []*TrafficStatConfig - List of parsed traffic statistics configurations
 *     error                - Error if parsing fails
 *****************************************************************************/
func ParseTrafficStatFromList(output string) ([]*TrafficStatConfig, error) {
	var configs []*TrafficStatConfig

	// Parse JSON output using unified floweye JSON parser
	jsonObjects, err := utils.ParseFloweyeJSON(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse floweye JSON output: %w", err)
	}

	for i, obj := range jsonObjects {
		// Marshal back to JSON then parse with existing function
		jsonBytes, err := json.Marshal(obj)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal object at index %d: %w", i, err)
		}

		config, err := parseTrafficStatFromJSON(string(jsonBytes))
		if err != nil {
			return nil, fmt.Errorf("failed to parse traffic stat JSON at index %d: %w", i, err)
		}
		configs = append(configs, config)
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: parseTrafficStatFromJSON
 *
 * DESCRIPTION:
 *     Parses a single traffic statistics configuration from JSON string.
 *     Handles the JSON format returned by floweye ntmso list command.
 *
 * PARAMETERS:
 *     jsonStr - JSON string for a single traffic statistics configuration
 *
 * RETURNS:
 *     *TrafficStatConfig - Parsed traffic statistics configuration
 *     error              - Error if parsing fails
 *****************************************************************************/
func parseTrafficStatFromJSON(jsonStr string) (*TrafficStatConfig, error) {
	config := &TrafficStatConfig{}

	// Remove braces and split by comma
	jsonStr = strings.Trim(jsonStr, "{}")
	pairs := strings.Split(jsonStr, ",")

	for _, pair := range pairs {
		// Split by colon to get key-value pairs
		kv := strings.SplitN(pair, ":", 2)
		if len(kv) != 2 {
			continue
		}

		key := strings.Trim(strings.TrimSpace(kv[0]), "\"")
		value := strings.Trim(strings.TrimSpace(kv[1]), "\"")

		switch key {
		case "id":
			if id, err := strconv.Atoi(value); err == nil {
				config.ID = id
			}
		case "name":
			config.Name = value
		case "trackip":
			if trackip, err := strconv.Atoi(value); err == nil {
				config.TrackIP = trackip != 0
			}
		case "upbps":
			if upbps, err := strconv.ParseInt(value, 10, 64); err == nil {
				config.UpBps = upbps
			}
		case "dnbps":
			if dnbps, err := strconv.ParseInt(value, 10, 64); err == nil {
				config.DnBps = dnbps
			}
		case "upbytes":
			if upbytes, err := strconv.ParseInt(value, 10, 64); err == nil {
				config.UpBytes = upbytes
			}
		case "dnbytes":
			if dnbytes, err := strconv.ParseInt(value, 10, 64); err == nil {
				config.DnBytes = dnbytes
			}
		}
	}

	if config.Name == "" {
		return nil, fmt.Errorf("failed to parse traffic statistics name from JSON")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ParseTrafficStatFromGet
 *
 * DESCRIPTION:
 *     Parses traffic statistics configuration from floweye ntmso get output.
 *     Expected format: key=value pairs, one per line.
 *
 * PARAMETERS:
 *     output - Output from floweye ntmso get command
 *
 * RETURNS:
 *     *TrafficStatConfig - Parsed traffic statistics configuration
 *     error              - Error if parsing fails
 *****************************************************************************/
func ParseTrafficStatFromGet(output string) (*TrafficStatConfig, error) {
	config := &TrafficStatConfig{}

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse key=value pairs
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) != 2 {
				continue
			}
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			switch key {
			case "id":
				if id, err := strconv.Atoi(value); err == nil {
					config.ID = id
				}
			case "name":
				config.Name = value
			case "trackip":
				if trackip, err := strconv.Atoi(value); err == nil {
					config.TrackIP = trackip != 0
				}
			case "upbps":
				if upbps, err := strconv.ParseInt(value, 10, 64); err == nil {
					config.UpBps = upbps
				}
			case "dnbps":
				if dnbps, err := strconv.ParseInt(value, 10, 64); err == nil {
					config.DnBps = dnbps
				}
			case "upbytes":
				if upbytes, err := strconv.ParseInt(value, 10, 64); err == nil {
					config.UpBytes = upbytes
				}
			case "dnbytes":
				if dnbytes, err := strconv.ParseInt(value, 10, 64); err == nil {
					config.DnBytes = dnbytes
				}
			}
		}
	}

	if config.Name == "" {
		return nil, fmt.Errorf("failed to parse traffic statistics name from output")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: CompareTrafficStatConfig
 *
 * DESCRIPTION:
 *     Compares a traffic statistics configuration with local configuration.
 *     Only compares configurable fields (name and trackip).
 *     Uses converted internal data structure instead of protobuf message.
 *
 * PARAMETERS:
 *     logger      - Logger for debug output
 *     config      - Traffic statistics configuration from converted protobuf
 *     localConfig - Local traffic statistics configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareTrafficStatConfig(logger *logger.Logger, config *TrafficStatConfig, localConfig *TrafficStatConfig) bool {
	if config == nil || localConfig == nil {
		logger.Debug("one of the configurations is nil")
		return false
	}

	// Compare name
	if config.Name != localConfig.Name {
		logger.Debug("traffic stat name mismatch",
			zap.String("config_name", config.Name),
			zap.String("local_name", localConfig.Name))
		return false
	}

	// Compare trackip flag
	if config.TrackIP != localConfig.TrackIP {
		logger.Debug("traffic stat trackip mismatch",
			zap.Bool("config_track_ip", config.TrackIP),
			zap.Bool("local_trackip", localConfig.TrackIP))
		return false
	}

	logger.Debug("traffic stat configurations match",
		zap.String("name", config.Name),
		zap.Bool("track_ip", config.TrackIP))

	return true
}

/*****************************************************************************
 * NAME: VerifyTrafficStatConfig
 *
 * DESCRIPTION:
 *     Verifies that a traffic statistics configuration was applied correctly.
 *     Reuses CompareTrafficStatConfig function for consistency.
 *     Uses converted internal data structure instead of protobuf message.
 *
 * PARAMETERS:
 *     logger - Logger for debug output
 *     config - Traffic statistics configuration to verify
 *
 * RETURNS:
 *     bool  - True if verification succeeds, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyTrafficStatConfig(logger *logger.Logger, config *TrafficStatConfig) (bool, error) {
	if config == nil {
		return false, fmt.Errorf("traffic statistics configuration is nil")
	}

	// Get current configuration from system
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "ntmso", "list")
	if err != nil {
		logger.Error("failed to list traffic statistics for verification", zap.Error(err))
		return false, fmt.Errorf("failed to list traffic statistics: %w", err)
	}

	// Parse current configurations
	currentConfigs, err := ParseTrafficStatFromList(output)
	if err != nil {
		logger.Error("failed to parse traffic statistics list for verification", zap.Error(err))
		return false, fmt.Errorf("failed to parse traffic statistics list: %w", err)
	}

	// Find the configuration by name
	var currentConfig *TrafficStatConfig
	for _, cfg := range currentConfigs {
		if cfg.Name == config.Name {
			currentConfig = cfg
			break
		}
	}

	if currentConfig == nil {
		logger.Debug("traffic statistics configuration not found in system",
			zap.String("name", config.Name))
		return false, nil
	}

	// Reuse comparison logic for verification
	matches := CompareTrafficStatConfig(logger, config, currentConfig)
	if !matches {
		logger.Debug("traffic statistics configuration verification failed - configurations do not match",
			zap.String("name", config.Name))
		return false, nil
	}

	logger.Debug("traffic statistics configuration verification successful",
		zap.String("name", config.Name))
	return true, nil
}
