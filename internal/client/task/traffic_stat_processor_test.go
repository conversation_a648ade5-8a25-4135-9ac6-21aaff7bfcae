/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_stat_processor_test.go
 *
 * DESCRIPTION :    Unit tests for traffic statistics processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/15/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewTrafficStatProcessor(t *testing.T) {
	log := logger.NewLogger()
	processor := NewTrafficStatProcessor(log)

	assert.NotNil(t, processor)
	assert.Equal(t, pb.TaskType_TASK_TRAFFIC_STAT, processor.GetTaskType())
	assert.NotNil(t, processor.localConfigs)
	assert.False(t, processor.fullSyncInProgress)
}

func TestParseTrafficStatFromList(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []*TrafficStatConfig
		hasError bool
	}{
		{
			name:     "empty input",
			input:    "",
			expected: []*TrafficStatConfig{},
			hasError: false,
		},
		{
			name:  "single traffic stat",
			input: `{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}`,
			expected: []*TrafficStatConfig{
				{
					ID:      1,
					Name:    "stat_inner",
					TrackIP: true,
					UpBps:   0,
					DnBps:   0,
					UpBytes: 0,
					DnBytes: 0,
				},
			},
			hasError: false,
		},
		{
			name:  "multiple traffic stats",
			input: `{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}`,
			expected: []*TrafficStatConfig{
				{
					ID:      1,
					Name:    "stat_inner",
					TrackIP: true,
					UpBps:   0,
					DnBps:   0,
					UpBytes: 0,
					DnBytes: 0,
				},
				{
					ID:      2,
					Name:    "stat_inner2",
					TrackIP: false,
					UpBps:   0,
					DnBps:   0,
					UpBytes: 0,
					DnBytes: 0,
				},
			},
			hasError: false,
		},
		{
			name:  "traffic stat with non-zero values",
			input: `{"id":3,"name":"stat_test","trackip":1,"upbps":1024,"dnbps":2048,"upbytes":1048576,"dnbytes":2097152}`,
			expected: []*TrafficStatConfig{
				{
					ID:      3,
					Name:    "stat_test",
					TrackIP: true,
					UpBps:   1024,
					DnBps:   2048,
					UpBytes: 1048576,
					DnBytes: 2097152,
				},
			},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTrafficStatFromList(tt.input)

			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.expected), len(result))
				for i, expected := range tt.expected {
					assert.Equal(t, expected.ID, result[i].ID)
					assert.Equal(t, expected.Name, result[i].Name)
					assert.Equal(t, expected.TrackIP, result[i].TrackIP)
					assert.Equal(t, expected.UpBps, result[i].UpBps)
					assert.Equal(t, expected.DnBps, result[i].DnBps)
					assert.Equal(t, expected.UpBytes, result[i].UpBytes)
					assert.Equal(t, expected.DnBytes, result[i].DnBytes)
				}
			}
		})
	}
}

func TestParseTrafficStatFromGet(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected *TrafficStatConfig
		hasError bool
	}{
		{
			name: "valid get output",
			input: `id=1
name=stat_inner
trackip=1
upbps=0
dnbps=0
upbytes=0
dnbytes=0
thread_flow1_trackip=1`,
			expected: &TrafficStatConfig{
				ID:      1,
				Name:    "stat_inner",
				TrackIP: true,
				UpBps:   0,
				DnBps:   0,
				UpBytes: 0,
				DnBytes: 0,
			},
			hasError: false,
		},
		{
			name: "get output with non-zero values",
			input: `id=2
name=stat_test
trackip=0
upbps=1024
dnbps=2048
upbytes=1048576
dnbytes=2097152`,
			expected: &TrafficStatConfig{
				ID:      2,
				Name:    "stat_test",
				TrackIP: false,
				UpBps:   1024,
				DnBps:   2048,
				UpBytes: 1048576,
				DnBytes: 2097152,
			},
			hasError: false,
		},
		{
			name:     "missing name",
			input:    `id=1\ntrackip=1`,
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTrafficStatFromGet(tt.input)

			if tt.hasError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expected.ID, result.ID)
				assert.Equal(t, tt.expected.Name, result.Name)
				assert.Equal(t, tt.expected.TrackIP, result.TrackIP)
				assert.Equal(t, tt.expected.UpBps, result.UpBps)
				assert.Equal(t, tt.expected.DnBps, result.DnBps)
				assert.Equal(t, tt.expected.UpBytes, result.UpBytes)
				assert.Equal(t, tt.expected.DnBytes, result.DnBytes)
			}
		})
	}
}

func TestCompareTrafficStatConfig(t *testing.T) {
	log := logger.NewLogger()

	tests := []struct {
		name        string
		task        *pb.TrafficStatTask
		localConfig *TrafficStatConfig
		expected    bool
	}{
		{
			name: "matching configurations",
			task: &pb.TrafficStatTask{
				Name:    "stat_test",
				Trackip: true,
			},
			localConfig: &TrafficStatConfig{
				ID:      1,
				Name:    "stat_test",
				TrackIP: true,
			},
			expected: true,
		},
		{
			name: "different trackip",
			task: &pb.TrafficStatTask{
				Name:    "stat_test",
				Trackip: true,
			},
			localConfig: &TrafficStatConfig{
				ID:      1,
				Name:    "stat_test",
				TrackIP: false,
			},
			expected: false,
		},
		{
			name: "different name",
			task: &pb.TrafficStatTask{
				Name:    "stat_test1",
				Trackip: true,
			},
			localConfig: &TrafficStatConfig{
				ID:      1,
				Name:    "stat_test2",
				TrackIP: true,
			},
			expected: false,
		},
		{
			name:        "nil task",
			task:        nil,
			localConfig: &TrafficStatConfig{},
			expected:    false,
		},
		{
			name: "nil local config",
			task: &pb.TrafficStatTask{
				Name:    "stat_test",
				Trackip: true,
			},
			localConfig: nil,
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompareTrafficStatConfig(log, tt.task, tt.localConfig)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBoolToIntString(t *testing.T) {
	tests := []struct {
		name     string
		input    bool
		expected string
	}{
		{
			name:     "true to 1",
			input:    true,
			expected: "1",
		},
		{
			name:     "false to 0",
			input:    false,
			expected: "0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := boolToIntString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
