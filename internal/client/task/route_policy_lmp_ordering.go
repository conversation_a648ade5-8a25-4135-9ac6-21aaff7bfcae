/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      route_policy_lmp_ordering.go
 *
 * DESCRIPTION :    Route policy LMP (Longest Prefix Match) ordering logic implementation
 *                  包含LPM Tier-T3 zone的自动排序逻辑，按目标IP前缀长度排序
 *
 * AUTHOR :         wei
 *
 * HISTORY :        05/06/2025  create
 ******************************************************************************/

package task

import (
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// LPMPolicyInfo represents route policy information for LPM sorting
type LPMPolicyInfo struct {
	ID             int
	Cookie         uint32
	DestinationIPs []IPWithPrefix // Extracted destination IPs with prefix lengths
}

// IPWithPrefix represents an IP address with its prefix length for LPM sorting
type IPWithPrefix struct {
	IP           uint32 // IPv4 address as uint32 for numerical comparison
	PrefixLength uint32 // CIDR prefix length (0-32)
	OriginalStr  string // Original string representation for logging
}

/*****************************************************************************
 * NAME: handleLPMSortingData
 *
 * DESCRIPTION:
 *     Handles automatic LPM sorting for LPM_TIER_T3 zone using converted data.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Implements automatic sorting by destination IP prefix length.
 *
 * PARAMETERS:
 *     p          - Route policy processor instance
 *     ctx        - Context for the operation
 *     configData - Route policy configuration data (converted from protobuf)
 *
 * RETURNS:
 *     error - Error if LMP sorting fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) handleLPMSortingData(ctx context.Context, configData *RoutePolicyConfig) error {
	cookie := configData.Cookie

	// Get the current policy ID for this cookie
	currentPolicyID, err := p.getCurrentPolicyIDByCookie(cookie)
	if err != nil {
		return fmt.Errorf("failed to get current policy ID for LMP sorting: %w", err)
	}

	p.logger.Info("handling LPM sorting for route policy using converted data",
		zap.Uint32("cookie", cookie),
		zap.Int("policy_id", currentPolicyID),
		zap.String("zone", "LPM_TIER_T3"),
		zap.Bool("full_sync_in_progress", p.fullSyncInProgress))

	// During full sync, we cannot refresh localConfigs as it's used for redundant config deletion
	// We will query floweye directly for the latest state

	// Get current route policy list for LPM sorting
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "route", "list", "json=1")
	if err != nil {
		return fmt.Errorf("failed to get route policy list for LPM sorting: %w", err)
	}

	// Parse current route policies
	currentPolicies, err := ParseRoutePolicyFromList(output)
	if err != nil {
		return fmt.Errorf("failed to parse route policy list for LPM sorting: %w", err)
	}

	// Filter policies in LPM_TIER_T3 zone and sort by LPM criteria
	var lmpPolicies []*RoutePolicyConfig
	for _, policy := range currentPolicies {
		p.logger.Debug("checking policy for LPM zone",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("id", policy.ID),
			zap.String("zone", policy.Zone.String()),
			zap.Bool("is_lmp_zone", policy.Zone == pb.RoutePolicyZone_LPM_TIER_T3))
		if policy.Zone == pb.RoutePolicyZone_LPM_TIER_T3 {
			// Get detailed configuration to obtain correct cookie
			detailOutput, err := utils.ExecuteCommand(p.logger, 10, "floweye", "rtpolicy", "get", "id="+strconv.Itoa(policy.ID))
			if err != nil {
				p.logger.Warn("failed to get detailed config for LMP policy, skipping",
					zap.Int("policy_id", policy.ID),
					zap.Error(err))
				continue
			}

			detailConfig, err := ParseRoutePolicyFromGet(detailOutput)
			if err != nil {
				p.logger.Warn("failed to parse detailed config for LMP policy, skipping",
					zap.Int("policy_id", policy.ID),
					zap.Error(err))
				continue
			}

			// Use detailed config with correct cookie
			lmpPolicies = append(lmpPolicies, detailConfig)
		}
	}

	p.logger.Debug("filtered LMP policies",
		zap.Int("total_policies", len(currentPolicies)),
		zap.Int("lmp_policies", len(lmpPolicies)))

	// Sort LPM policies by destination IP prefix length (higher prefix length first)
	// then by IP numerical values for equal prefix lengths
	p.sortLMPPolicies(lmpPolicies)

	// Find the current policy in the sorted list
	var currentPolicyIndex = -1
	var foundPolicyID int
	for i, policy := range lmpPolicies {
		if policy.Cookie == cookie {
			currentPolicyIndex = i
			foundPolicyID = policy.ID
			break
		}
	}

	if currentPolicyIndex == -1 {
		return fmt.Errorf("current route policy with cookie %d not found in LPM zone", cookie)
	}

	// Calculate target position based on LPM sorting
	minPriority, _ := GetZonePriorityRange(pb.RoutePolicyZone_LPM_TIER_T3)
	targetPosition := minPriority + currentPolicyIndex

	// If already in correct position, no need to move
	if foundPolicyID == targetPosition {
		p.logger.Debug("route policy already in correct LPM position",
			zap.Uint32("cookie", cookie),
			zap.Int("current_id", foundPolicyID),
			zap.Int("target_position", targetPosition))
		return nil
	}

	// Move to target position
	orderInfo := make([]RoutePolicyOrderInfo, len(lmpPolicies))
	for i, policy := range lmpPolicies {
		orderInfo[i] = RoutePolicyOrderInfo{
			ID:     policy.ID,
			Cookie: policy.Cookie,
		}
	}

	return p.moveRoutePolicyToPositionBidirectionalWithZone(foundPolicyID, targetPosition, pb.RoutePolicyZone_LPM_TIER_T3, orderInfo)
}

/*****************************************************************************
 * NAME: sortLMPPolicies
 *
 * DESCRIPTION:
 *     Sorts LMP policies by destination IP prefix length and IP numerical values.
 *     Primary sort: higher prefix length first
 *     Secondary sort: IP numerical values for equal prefix lengths
 *
 * PARAMETERS:
 *     p        - Route policy processor instance
 *     policies - List of route policy configurations to sort
 *****************************************************************************/
func (p *RoutePolicyProcessor) sortLMPPolicies(policies []*RoutePolicyConfig) {
	// 使用 sort.Slice 进行排序，比手动冒泡排序更高效且代码更简洁
	sort.Slice(policies, func(i, j int) bool {
		return p.shouldSwapLMPPolicies(policies[j], policies[i]) // 注意参数顺序，实现升序排列
	})
}

/*****************************************************************************
 * NAME: shouldSwapLMPPolicies
 *
 * DESCRIPTION:
 *     Determines if two LMP policies should be swapped in sorting order.
 *     Returns true if policy1 should come after policy2.
 *
 * PARAMETERS:
 *     p       - Route policy processor instance
 *     policy1 - First policy to compare
 *     policy2 - Second policy to compare
 *
 * RETURNS:
 *     bool - True if policies should be swapped
 *****************************************************************************/
func (p *RoutePolicyProcessor) shouldSwapLMPPolicies(policy1, policy2 *RoutePolicyConfig) bool {
	// Extract prefix lengths from destination addresses
	prefix1 := p.extractPrefixLength(policy1.Dst)
	prefix2 := p.extractPrefixLength(policy2.Dst)

	// Primary sort: higher prefix length first
	if prefix1 != prefix2 {
		return prefix1 < prefix2 // Swap if policy1 has lower prefix length
	}

	// Secondary sort: IP numerical values for equal prefix lengths
	ip1 := p.extractIPNumerical(policy1.Dst)
	ip2 := p.extractIPNumerical(policy2.Dst)

	return ip1 > ip2 // Swap if policy1 has higher IP numerical value
}

/*****************************************************************************
 * NAME: extractPrefixLength
 *
 * DESCRIPTION:
 *     Extracts prefix length from destination address string.
 *     Handles CIDR notation and returns appropriate prefix length.
 *
 * PARAMETERS:
 *     p   - Route policy processor instance
 *     dst - Destination address string
 *
 * RETURNS:
 *     int - Prefix length (32 for single IP, actual prefix for CIDR)
 *****************************************************************************/
func (p *RoutePolicyProcessor) extractPrefixLength(dst string) int {
	if dst == "any" {
		return 0 // Lowest priority for "any"
	}

	// Handle CIDR notation
	if strings.Contains(dst, "/") {
		parts := strings.Split(dst, "/")
		if len(parts) == 2 {
			if prefixLen, err := strconv.Atoi(parts[1]); err == nil {
				return prefixLen
			}
		}
	}

	// Single IP address gets prefix length 32
	return 32
}

/*****************************************************************************
 * NAME: extractIPNumerical
 *
 * DESCRIPTION:
 *     Extracts numerical value from IP address for sorting.
 *     Converts IP address to uint32 for numerical comparison.
 *
 * PARAMETERS:
 *     p   - Route policy processor instance
 *     dst - Destination address string
 *
 * RETURNS:
 *     uint32 - Numerical representation of IP address
 *****************************************************************************/
func (p *RoutePolicyProcessor) extractIPNumerical(dst string) uint32 {
	if dst == "any" {
		return 0 // Lowest priority for "any"
	}

	// Extract IP part (remove CIDR suffix if present)
	ipStr := dst
	if strings.Contains(dst, "/") {
		parts := strings.Split(dst, "/")
		if len(parts) > 0 {
			ipStr = parts[0]
		}
	}

	// Parse IP address
	parts := strings.Split(ipStr, ".")
	if len(parts) != 4 {
		return 0 // Invalid IP format
	}

	var result uint32
	for i, part := range parts {
		if octet, err := strconv.Atoi(part); err == nil && octet >= 0 && octet <= 255 {
			result |= uint32(octet) << (8 * (3 - i))
		}
	}

	return result
}

/*****************************************************************************
 * NAME: extractLPMInfoFromPolicy
 *
 * DESCRIPTION:
 *     Extracts LPM information from a route policy by querying its detailed configuration.
 *     Parses destination IP addresses and CIDR information for LPM sorting.
 *
 * PARAMETERS:
 *     p        - Route policy processor instance
 *     policyID - Policy ID to extract information from
 *     cookie   - Policy cookie for logging
 *
 * RETURNS:
 *     *LPMPolicyInfo - LPM information for the policy
 *     error          - Error if extraction fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) extractLPMInfoFromPolicy(policyID int, cookie uint32) (*LPMPolicyInfo, error) {
	// Get detailed configuration for the policy
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "rtpolicy", "get", "id="+strconv.Itoa(policyID))
	if err != nil {
		return nil, fmt.Errorf("failed to get detailed route policy config: %w", err)
	}

	config, err := ParseRoutePolicyFromGet(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse detailed route policy config: %w", err)
	}

	// Extract destination IPs from the configuration
	destinationIPs, err := p.extractDestinationIPs(config.Dst)
	if err != nil {
		return nil, fmt.Errorf("failed to extract destination IPs: %w", err)
	}

	return &LPMPolicyInfo{
		ID:             policyID,
		Cookie:         cookie,
		DestinationIPs: destinationIPs,
	}, nil
}

/*****************************************************************************
 * NAME: extractDestinationIPs
 *
 * DESCRIPTION:
 *     Extracts destination IP addresses with prefix lengths from address string.
 *     Parses various IP formats including CIDR notation.
 *
 * PARAMETERS:
 *     p      - Route policy processor instance
 *     dstStr - Destination address string from policy configuration
 *
 * RETURNS:
 *     []IPWithPrefix - List of IP addresses with prefix lengths
 *     error          - Error if parsing fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) extractDestinationIPs(dstStr string) ([]IPWithPrefix, error) {
	var ips []IPWithPrefix

	if dstStr == "" || dstStr == "any" {
		// No specific destination IPs, return empty list
		return ips, nil
	}

	// Split by comma to handle multiple addresses
	addresses := strings.Split(dstStr, ",")

	for _, addr := range addresses {
		addr = strings.TrimSpace(addr)
		if addr == "" {
			continue
		}

		// Skip non-IP addresses (like ipgroup:, usergroup:, etc.)
		if strings.Contains(addr, ":") && !strings.Contains(addr, "/") {
			continue
		}

		// Parse IP address with optional CIDR
		ipWithPrefix, err := p.parseIPWithPrefix(addr)
		if err != nil {
			p.logger.Warn("failed to parse IP address, skipping",
				zap.String("address", addr),
				zap.Error(err))
			continue
		}

		ips = append(ips, ipWithPrefix)
	}

	return ips, nil
}

/*****************************************************************************
 * NAME: parseIPWithPrefix
 *
 * DESCRIPTION:
 *     Parses an IP address string with optional CIDR prefix into IPWithPrefix structure.
 *     Handles both single IPs and CIDR notation.
 *
 * PARAMETERS:
 *     p     - Route policy processor instance
 *     ipStr - IP address string (e.g., "***********/24", "********")
 *
 * RETURNS:
 *     IPWithPrefix - Parsed IP with prefix length
 *     error        - Error if parsing fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) parseIPWithPrefix(ipStr string) (IPWithPrefix, error) {
	var result IPWithPrefix
	result.OriginalStr = ipStr

	// Check if it's CIDR notation
	if strings.Contains(ipStr, "/") {
		parts := strings.Split(ipStr, "/")
		if len(parts) != 2 {
			return result, fmt.Errorf("invalid CIDR format: %s", ipStr)
		}

		// Parse IP address
		ip, err := p.parseIPv4ToUint32(parts[0])
		if err != nil {
			return result, fmt.Errorf("failed to parse IP address %s: %w", parts[0], err)
		}

		// Parse prefix length
		prefix, err := strconv.ParseUint(parts[1], 10, 32)
		if err != nil {
			return result, fmt.Errorf("failed to parse prefix length %s: %w", parts[1], err)
		}

		if prefix > 32 {
			return result, fmt.Errorf("invalid prefix length %d, must be 0-32", prefix)
		}

		result.IP = ip
		result.PrefixLength = uint32(prefix)
	} else {
		// Single IP address, assume /32 prefix
		ip, err := p.parseIPv4ToUint32(ipStr)
		if err != nil {
			return result, fmt.Errorf("failed to parse IP address %s: %w", ipStr, err)
		}

		result.IP = ip
		result.PrefixLength = 32 // Single IP is /32
	}

	return result, nil
}

/*****************************************************************************
 * NAME: parseIPv4ToUint32
 *
 * DESCRIPTION:
 *     Parses an IPv4 address string to uint32 for numerical comparison.
 *
 * PARAMETERS:
 *     p     - Route policy processor instance
 *     ipStr - IPv4 address string (e.g., "***********")
 *
 * RETURNS:
 *     uint32 - IP address as uint32
 *     error  - Error if parsing fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) parseIPv4ToUint32(ipStr string) (uint32, error) {
	parts := strings.Split(ipStr, ".")
	if len(parts) != 4 {
		return 0, fmt.Errorf("invalid IPv4 format: %s", ipStr)
	}

	var ip uint32
	for i, part := range parts {
		octet, err := strconv.ParseUint(part, 10, 8)
		if err != nil {
			return 0, fmt.Errorf("invalid octet %s in IP %s: %w", part, ipStr, err)
		}
		ip |= uint32(octet) << (8 * (3 - i))
	}

	return ip, nil
}

/*****************************************************************************
 * NAME: sortPoliciesForLPM
 *
 * DESCRIPTION:
 *     Sorts route policies according to LPM rules:
 *     1. Primary sort: Higher prefix length first (more specific routes)
 *     2. Secondary sort: Lower IP numerical value first (for equal prefix lengths)
 *
 * PARAMETERS:
 *     p        - Route policy processor instance
 *     policies - List of LPM policy information to sort
 *
 * RETURNS:
 *     []LPMPolicyInfo - Sorted list of policies
 *****************************************************************************/
func (p *RoutePolicyProcessor) sortPoliciesForLPM(policies []LPMPolicyInfo) []LPMPolicyInfo {
	// 创建副本避免修改原始切片
	sorted := make([]LPMPolicyInfo, len(policies))
	copy(sorted, policies)

	// 使用 sort.Slice 进行排序，比手动冒泡排序更高效且代码更简洁
	sort.Slice(sorted, func(i, j int) bool {
		// 获取每个策略的最具体 IP（最高前缀长度）和最小 IP
		maxPrefix1, minIP1 := p.getMostSpecificIP(sorted[i].DestinationIPs)
		maxPrefix2, minIP2 := p.getMostSpecificIP(sorted[j].DestinationIPs)

		// 主要排序：前缀长度越高优先级越高（更具体的路由优先）
		if maxPrefix1 != maxPrefix2 {
			return maxPrefix1 > maxPrefix2 // 更高的前缀长度排在前面
		}

		// 次要排序：前缀长度相同时，IP 数值越小优先级越高（保证稳定排序）
		return minIP1 < minIP2 // 更小的 IP 数值排在前面
	})

	// 记录排序结果用于调试
	p.logger.Debug("LPM 策略排序完成")
	for i, policy := range sorted {
		maxPrefix, minIP := p.getMostSpecificIP(policy.DestinationIPs)
		p.logger.Debug("LPM 策略排序",
			zap.Int("位置", i+1),
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("策略ID", policy.ID),
			zap.Uint32("最高前缀长度", maxPrefix),
			zap.Uint32("最小IP", minIP),
			zap.Int("目标IP数量", len(policy.DestinationIPs)))
	}

	return sorted
}

/*****************************************************************************
 * NAME: getMostSpecificIP
 *
 * DESCRIPTION:
 *     Gets the most specific IP (highest prefix length) and lowest IP value
 *     from a list of destination IPs for LPM comparison.
 *
 * PARAMETERS:
 *     p   - Route policy processor instance
 *     ips - List of IP addresses with prefix lengths
 *
 * RETURNS:
 *     uint32 - Highest prefix length found
 *     uint32 - Lowest IP numerical value found
 *****************************************************************************/
func (p *RoutePolicyProcessor) getMostSpecificIP(ips []IPWithPrefix) (uint32, uint32) {
	if len(ips) == 0 {
		// No specific IPs, treat as least specific (prefix 0) and highest IP value
		return 0, 0xFFFFFFFF
	}

	var maxPrefix uint32 = 0
	var minIP uint32 = 0xFFFFFFFF

	for _, ip := range ips {
		if ip.PrefixLength > maxPrefix {
			maxPrefix = ip.PrefixLength
		}
		if ip.IP < minIP {
			minIP = ip.IP
		}
	}

	return maxPrefix, minIP
}

/*****************************************************************************
 * NAME: calculateLPMTargetPosition
 *
 * DESCRIPTION:
 *     Calculates the target position for a policy based on LPM sorting.
 *     Maps the sorted index to actual policy ID within the LPM_TIER_T3 zone.
 *
 * PARAMETERS:
 *     p              - Route policy processor instance
 *     sortedPolicies - List of policies sorted by LPM rules
 *     currentIndex   - Index of current policy in sorted list
 *
 * RETURNS:
 *     int   - Target position (policy ID) for the current policy
 *     error - Error if calculation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) calculateLPMTargetPosition(sortedPolicies []LPMPolicyInfo, currentIndex int) (int, error) {
	minPriority, _ := GetZonePriorityRange(pb.RoutePolicyZone_LPM_TIER_T3)

	// Target position is based on the sorted order within the zone
	// First policy gets minPriority, second gets minPriority+1, etc.
	targetPosition := minPriority + currentIndex

	p.logger.Debug("calculated LPM target position",
		zap.Int("current_index", currentIndex),
		zap.Int("target_position", targetPosition),
		zap.Int("min_priority", minPriority),
		zap.Int("total_policies", len(sortedPolicies)))

	return targetPosition, nil
}
