/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_channel_config.go
 *
 * DESCRIPTION :    Traffic channel configuration data structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        30/05/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: TrafficChannelPriority
 *
 * DESCRIPTION:
 *     Represents a traffic channel priority configuration.
 *     Contains priority level, bandwidth settings, and description.
 *
 * FIELDS:
 *     Pri     - Priority level (1-16, 1 is highest)
 *     MaxRate - Maximum bandwidth in kbps
 *     Gbw     - Guaranteed bandwidth in kbps
 *     Desc    - Description
 *****************************************************************************/
type TrafficChannelPriority struct {
	Pri     int    // Priority level (1-16, 1 is highest)
	MaxRate int    // Maximum bandwidth in kbps
	Gbw     int    // Guaranteed bandwidth in kbps
	Desc    string // Description
}

/*****************************************************************************
 * NAME: TrafficChannelConfig
 *
 * DESCRIPTION:
 *     Represents a traffic channel configuration.
 *     Contains channel name, bandwidth, quota, and priority settings.
 *
 * FIELDS:
 *     Name       - Traffic channel name (unique identifier)
 *     Rate       - Channel bandwidth in kbits/s
 *     Quota      - Daily quota in Mbytes (0 means unlimited)
 *     Priorities - Priority settings (up to 16 priorities)
 *****************************************************************************/
type TrafficChannelConfig struct {
	Name       string                    // Traffic channel name (unique identifier)
	Rate       int                       // Channel bandwidth in kbits/s
	Quota      int                       // Daily quota in Mbytes (0 means unlimited)
	Priorities []*TrafficChannelPriority // Priority settings (up to 16 priorities)
}

/*****************************************************************************
 * NAME: ParseTrafficChannelFromGetBwo
 *
 * DESCRIPTION:
 *     Parses traffic channel configuration from floweye policy getbwo output.
 *     Extracts basic channel information like name, rate, and quota.
 *
 * PARAMETERS:
 *     output - Output from floweye policy getbwo command
 *
 * RETURNS:
 *     *TrafficChannelConfig - Parsed traffic channel configuration
 *     error                 - Error if parsing fails
 *****************************************************************************/
func ParseTrafficChannelFromGetBwo(output string) (*TrafficChannelConfig, error) {
	config := &TrafficChannelConfig{
		Priorities: make([]*TrafficChannelPriority, 0),
	}

	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Extract values using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "name":
			config.Name = value
		case "rate":
			if val, err := strconv.Atoi(value); err == nil {
				config.Rate = val
			}
		case "quota":
			if val, err := strconv.Atoi(value); err == nil {
				config.Quota = val
			}
		}
	}

	if config.Name == "" {
		return nil, fmt.Errorf("failed to parse traffic channel name from output")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ParseTrafficChannelPrioritiesFromGetHtb
 *
 * DESCRIPTION:
 *     Parses traffic channel priority configurations from floweye policy gethtb output.
 *     Extracts priority settings including priority level, bandwidth, and description.
 *
 * PARAMETERS:
 *     output - Output from floweye policy gethtb command
 *
 * RETURNS:
 *     []*TrafficChannelPriority - Parsed priority configurations
 *     error                     - Error if parsing fails
 *****************************************************************************/
func ParseTrafficChannelPrioritiesFromGetHtb(output string) ([]*TrafficChannelPriority, error) {
	priorities := make([]*TrafficChannelPriority, 0)

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || i == 0 { // Skip empty lines and header
			continue
		}

		// Parse priority line: pri gbw maxrate bytes outbps dropbps desc
		fields := strings.Fields(line)
		if len(fields) < 6 {
			continue
		}

		pri, err := strconv.Atoi(fields[0])
		if err != nil {
			continue
		}

		gbw, err := strconv.Atoi(fields[1])
		if err != nil {
			continue
		}

		maxRate, err := strconv.Atoi(fields[2])
		if err != nil {
			continue
		}

		// Only include priorities that have non-zero values
		if gbw > 0 || maxRate > 0 {
			priority := &TrafficChannelPriority{
				Pri:     pri,
				Gbw:     gbw,
				MaxRate: maxRate,
			}

			// Add description if available
			if len(fields) > 6 {
				priority.Desc = strings.Join(fields[6:], " ")
			}

			priorities = append(priorities, priority)
		}
	}

	return priorities, nil
}

/*****************************************************************************
 * NAME: ConvertTrafficChannelTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf TrafficChannelTask message to unified TrafficChannelConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing TrafficChannelConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     task - Protobuf traffic channel task message to convert
 *
 * RETURNS:
 *     *TrafficChannelConfig - Converted traffic channel configuration structure
 *     error                 - Error if conversion fails
 *****************************************************************************/
func ConvertTrafficChannelTaskToConfig(task *pb.TrafficChannelTask) (*TrafficChannelConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("trafficChannelTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &TrafficChannelConfig{
		Name:       task.GetName(),
		Rate:       int(task.GetRate()),
		Quota:      0, // Default value for optional field
		Priorities: make([]*TrafficChannelPriority, 0),
	}

	// Handle optional quota field
	if task.Quota != nil {
		config.Quota = int(task.GetQuota())
	}

	// Convert priorities
	for _, pbPriority := range task.GetPriorities() {
		priority := &TrafficChannelPriority{
			Pri:     int(pbPriority.GetPri()),
			MaxRate: int(pbPriority.GetMaxRate()),
			Gbw:     int(pbPriority.GetGbw()),
			Desc:    "", // Default value for optional field
		}

		// Handle optional description field
		if pbPriority.Desc != nil {
			priority.Desc = pbPriority.GetDesc()
		}

		config.Priorities = append(config.Priorities, priority)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: CompareTrafficChannelConfig
 *
 * DESCRIPTION:
 *     Compares a converted traffic channel configuration with local configuration.
 *     Returns true if configurations match, false otherwise.
 *
 * PARAMETERS:
 *     logger         - Logger for comparison operations
 *     expectedConfig - Expected traffic channel configuration (converted from protobuf)
 *     localConfig    - Local traffic channel configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareTrafficChannelConfig(logger *logger.Logger, expectedConfig *TrafficChannelConfig, localConfig *TrafficChannelConfig) bool {
	// Compare basic fields
	if expectedConfig.Name != localConfig.Name {
		logger.Debug("traffic channel name mismatch",
			zap.String("expected_name", expectedConfig.Name),
			zap.String("local_name", localConfig.Name))
		return false
	}

	if expectedConfig.Rate != localConfig.Rate {
		logger.Debug("traffic channel rate mismatch",
			zap.Int("expected_rate", expectedConfig.Rate),
			zap.Int("local_rate", localConfig.Rate))
		return false
	}

	if expectedConfig.Quota != localConfig.Quota {
		logger.Debug("traffic channel quota mismatch",
			zap.Int("expected_quota", expectedConfig.Quota),
			zap.Int("local_quota", localConfig.Quota))
		return false
	}

	// Compare priorities
	if len(expectedConfig.Priorities) != len(localConfig.Priorities) {
		logger.Debug("traffic channel priorities count mismatch",
			zap.Int("expected_count", len(expectedConfig.Priorities)),
			zap.Int("local_count", len(localConfig.Priorities)))
		return false
	}

	// Create maps for easier comparison
	expectedPriorityMap := make(map[int]*TrafficChannelPriority)
	for _, p := range expectedConfig.Priorities {
		expectedPriorityMap[p.Pri] = p
	}

	localPriorityMap := make(map[int]*TrafficChannelPriority)
	for _, p := range localConfig.Priorities {
		localPriorityMap[p.Pri] = p
	}

	// Compare each priority
	for pri, expectedPri := range expectedPriorityMap {
		localPri, exists := localPriorityMap[pri]
		if !exists {
			logger.Debug("priority not found in local config",
				zap.Int("priority", pri))
			return false
		}

		if expectedPri.MaxRate != localPri.MaxRate {
			logger.Debug("priority maxrate mismatch",
				zap.Int("priority", pri),
				zap.Int("expected_maxrate", expectedPri.MaxRate),
				zap.Int("local_maxrate", localPri.MaxRate))
			return false
		}

		if expectedPri.Gbw != localPri.Gbw {
			logger.Debug("priority gbw mismatch",
				zap.Int("priority", pri),
				zap.Int("expected_gbw", expectedPri.Gbw),
				zap.Int("local_gbw", localPri.Gbw))
			return false
		}

		if expectedPri.Desc != localPri.Desc {
			logger.Debug("priority description mismatch",
				zap.Int("priority", pri),
				zap.String("expected_desc", expectedPri.Desc),
				zap.String("local_desc", localPri.Desc))
			return false
		}
	}

	return true
}

/*****************************************************************************
 * NAME: VerifyTrafficChannelConfig
 *
 * DESCRIPTION:
 *     Verifies that a traffic channel configuration matches the expected configuration.
 *     Reuses the comparison logic from CompareTrafficChannelConfig as mandated by CONTRIBUTING.md.
 *
 * PARAMETERS:
 *     logger         - Logger for verification operations
 *     expectedConfig - Expected traffic channel configuration
 *
 * RETURNS:
 *     bool  - True if verification succeeds, false otherwise
 *     error - Error if verification fails due to system issues
 *****************************************************************************/
func VerifyTrafficChannelConfig(logger *logger.Logger, expectedConfig *TrafficChannelConfig) (bool, error) {
	// Get current configuration from the system
	actualConfig, err := GetTrafficChannelConfig(logger, expectedConfig.Name)
	if err != nil {
		logger.Error("failed to get traffic channel configuration for verification",
			zap.String("name", expectedConfig.Name),
			zap.Error(err))
		return false, fmt.Errorf("failed to get traffic channel configuration: %w", err)
	}

	// Reuse comparison logic as mandated by CONTRIBUTING.md
	return CompareTrafficChannelConfig(logger, expectedConfig, actualConfig), nil
}

/*****************************************************************************
 * NAME: GetTrafficChannelConfig
 *
 * DESCRIPTION:
 *     Retrieves a single traffic channel configuration from the system.
 *     Used for verification purposes to avoid full refresh operations.
 *
 * PARAMETERS:
 *     logger - Logger for operations
 *     name   - Traffic channel name to retrieve
 *
 * RETURNS:
 *     *TrafficChannelConfig - Retrieved traffic channel configuration
 *     error                 - Error if retrieval fails
 *****************************************************************************/
func GetTrafficChannelConfig(logger *logger.Logger, name string) (*TrafficChannelConfig, error) {
	// Get basic channel configuration
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "policy", "getbwo", "name="+name)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel basic config: %w", err)
	}

	config, err := ParseTrafficChannelFromGetBwo(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse channel basic config: %w", err)
	}

	// Get priority configurations
	htbOutput, err := utils.ExecuteCommand(logger, 10, "floweye", "policy", "gethtb", name)
	if err != nil {
		logger.Warn("failed to get channel priority config",
			zap.String("name", name),
			zap.Error(err))
		// Continue without priorities - they might not be configured
	} else {
		priorities, err := ParseTrafficChannelPrioritiesFromGetHtb(htbOutput)
		if err != nil {
			logger.Warn("failed to parse channel priority config",
				zap.String("name", name),
				zap.Error(err))
		} else {
			config.Priorities = priorities
		}
	}

	return config, nil
}
