/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      sr_proxy_config.go
 *
 * DESCRIPTION :    SR Proxy configuration data structures and helper functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: SrProxyConfig
 *
 * DESCRIPTION:
 *     Represents a local SR Proxy configuration.
 *     Used to cache and compare SR Proxy configurations.
 *
 * FIELDS:
 *     Name         - SR Proxy name
 *     Mtu          - Maximum transmission unit
 *     Links        - iWAN segment identifiers
 *     FromIn       - Internal connection flag
 *     Keepalive    - Keepalive enabled flag
 *     EncryptType  - Encryption type (0=none, 1=AES128, 2=AES256)
 *     Password     - Encryption password
 *****************************************************************************/
type SrProxyConfig struct {
	Name        string
	Mtu         int
	Links       []int32
	FromIn      bool
	Keepalive   bool
	EncryptType int
	Password    string
}

/*****************************************************************************
 * NAME: GetSrProxyConfigs
 *
 * DESCRIPTION:
 *     Retrieves all SR Proxy configurations from the local system.
 *     Uses floweye nat listproxy and getproxy commands.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*SrProxyConfig - Map of SR Proxy name to configuration
 *     error                     - Error if retrieval fails
 *****************************************************************************/
func GetSrProxyConfigs(logger *logger.Logger) (map[string]*SrProxyConfig, error) {
	logger.Debug("Getting local SR Proxy configurations")

	// Initialize configurations map
	configs := make(map[string]*SrProxyConfig)

	// Execute floweye command to list all SR Proxies
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "listproxy", "type=srpxy", "json=1")
	if err != nil {
		logger.Error("Failed to execute floweye command to list SR Proxies",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to list SR Proxies: %w", err)
	}

	// Parse JSON output
	if output == "" {
		logger.Info("No SR Proxies found")
		return configs, nil
	}

	// The output is a JSON array without brackets, so we need to add them
	output = "[" + output + "]"

	// Define a struct to parse the JSON output
	type SrProxyListItem struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
		Mtu  int    `json:"mtu"`
	}

	var srProxies []SrProxyListItem
	if err := utils.ParseFloweyeJSONToType(output, &srProxies); err != nil {
		logger.Error("Failed to parse SR Proxy list JSON",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to parse SR Proxy list: %w", err)
	}

	// Get detailed configuration for each SR Proxy
	for _, srProxy := range srProxies {
		config, err := GetSrProxyConfig(logger, srProxy.Name)
		if err != nil {
			logger.Error("Failed to get SR Proxy configuration",
				zap.String("name", srProxy.Name),
				zap.Error(err))
			continue
		}
		configs[srProxy.Name] = config
	}

	logger.Info("Retrieved SR Proxy configurations", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: GetSrProxyConfig
 *
 * DESCRIPTION:
 *     Retrieves a specific SR Proxy configuration from the local system.
 *     Uses floweye nat getproxy command.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - SR Proxy name
 *
 * RETURNS:
 *     *SrProxyConfig - SR Proxy configuration
 *     error         - Error if retrieval fails
 *****************************************************************************/
func GetSrProxyConfig(logger *logger.Logger, name string) (*SrProxyConfig, error) {
	logger.Debug("Getting SR Proxy configuration", zap.String("name", name))

	// Execute floweye command to get SR Proxy
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "getproxy", name)
	if err != nil {
		logger.Error("Failed to execute floweye command to get SR Proxy",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get SR Proxy: %w", err)
	}

	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Create SR Proxy configuration with defaults
	config := &SrProxyConfig{
		Name: name,
	}

	// Parse configuration fields using switch statement for better performance
	for key, value := range configMap {
		switch key {
		case "mtu":
			if val, err := strconv.Atoi(value); err == nil {
				config.Mtu = val
			} else {
				logger.Error("Failed to parse MTU",
					zap.String("name", name),
					zap.String("mtu", value),
					zap.Error(err))
			}

		case "sre_links":
			if value != "" {
				linkStrs := strings.Split(value, ",")
				links := make([]int32, 0, len(linkStrs))
				for _, linkStr := range linkStrs {
					if link, err := strconv.Atoi(linkStr); err == nil {
						links = append(links, int32(link))
					} else {
						logger.Error("Failed to parse link",
							zap.String("name", name),
							zap.String("link", linkStr),
							zap.Error(err))
					}
				}
				config.Links = links
			}

		case "fromin":
			if val, err := strconv.Atoi(value); err == nil {
				config.FromIn = val != 0
			} else {
				logger.Error("Failed to parse FromIn",
					zap.String("name", name),
					zap.String("fromin", value),
					zap.Error(err))
			}

		case "sre_keepalive":
			if val, err := strconv.Atoi(value); err == nil {
				config.Keepalive = val != 0
			} else {
				logger.Error("Failed to parse Keepalive",
					zap.String("name", name),
					zap.String("keepalive", value),
					zap.Error(err))
			}

		case "encrypt_algo":
			// Parse encryption algorithm from floweye output
			switch value {
			case "NULL", "":
				config.EncryptType = 0 // SR_ENCRYPT_NONE
			case "AES128":
				config.EncryptType = 1 // SR_ENCRYPT_AES128
			case "AES256":
				config.EncryptType = 2 // SR_ENCRYPT_AES256
			default:
				logger.Error("Unknown encryption algorithm",
					zap.String("name", name),
					zap.String("encrypt_algo", value))
				config.EncryptType = 0 // Default to none
			}

		case "encrypt_password":
			config.Password = value
		}
	}

	logger.Debug("Retrieved SR Proxy configuration",
		zap.String("name", name),
		zap.Int("mtu", config.Mtu),
		zap.Any("links", config.Links),
		zap.Bool("fromin", config.FromIn),
		zap.Bool("keepalive", config.Keepalive),
		zap.Int("encrypt_type", config.EncryptType),
		zap.String("password", config.Password))

	return config, nil
}

/*****************************************************************************
 * NAME: ConvertSrProxyTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf SrProxyTask message to unified SrProxyConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing SrProxyConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     srProxyTask - Protobuf SR Proxy task message to convert
 *
 * RETURNS:
 *     *SrProxyConfig - Converted SR Proxy configuration structure
 *     error          - Error if conversion fails
 *****************************************************************************/
func ConvertSrProxyTaskToConfig(srProxyTask *pb.SrProxyTask) (*SrProxyConfig, error) {
	if srProxyTask == nil {
		return nil, fmt.Errorf("srProxyTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &SrProxyConfig{
		Name:        srProxyTask.GetName(),
		Mtu:         int(srProxyTask.GetMtu()),
		FromIn:      srProxyTask.GetFromIn(),
		Keepalive:   srProxyTask.GetKeepalive(),
		Links:       make([]int32, 0),
		EncryptType: 0,  // Default to no encryption
		Password:    "", // Default to empty password
	}

	// Extract links from the first path only (按照paths中第一个path links进行下发)
	paths := srProxyTask.GetPaths()
	if len(paths) > 0 && paths[0] != nil {
		config.Links = make([]int32, len(paths[0].GetLinks()))
		copy(config.Links, paths[0].GetLinks())
	}

	// Handle encryption configuration if present
	if encryptConfig := srProxyTask.GetEncryptConfig(); encryptConfig != nil {
		// Convert SrEncryptType enum to integer
		switch encryptConfig.GetEncryptType() {
		case pb.SrEncryptType_SR_ENCRYPT_NONE:
			config.EncryptType = 0
		case pb.SrEncryptType_SR_ENCRYPT_AES128:
			config.EncryptType = 1
		case pb.SrEncryptType_SR_ENCRYPT_AES256:
			config.EncryptType = 2
		default:
			config.EncryptType = 0 // Default to no encryption for unknown types
		}

		// Set password if encryption is enabled
		if config.EncryptType != 0 {
			config.Password = encryptConfig.GetPassword()
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: CompareSrProxyConfig
 *
 * DESCRIPTION:
 *     Compares a requested SR Proxy configuration with a local configuration.
 *     Used to determine if a configuration change is needed.
 *
 * PARAMETERS:
 *     logger        - Logger instance for logging operations
 *     requestedConfig - Requested SR Proxy configuration (converted from protobuf)
 *     localConfig   - Local SR Proxy configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareSrProxyConfig(logger *logger.Logger, requestedConfig *SrProxyConfig, localConfig *SrProxyConfig) bool {

	// Compare MTU
	if requestedConfig.Mtu != localConfig.Mtu {
		logger.Debug("SR Proxy MTU mismatch",
			zap.String("name", requestedConfig.Name),
			zap.Int("requested", requestedConfig.Mtu),
			zap.Int("local", localConfig.Mtu))
		return false
	}

	// Compare FromIn
	if requestedConfig.FromIn != localConfig.FromIn {
		logger.Debug("SR Proxy FromIn mismatch",
			zap.String("name", requestedConfig.Name),
			zap.Bool("requested", requestedConfig.FromIn),
			zap.Bool("local", localConfig.FromIn))
		return false
	}

	// Compare Keepalive
	if requestedConfig.Keepalive != localConfig.Keepalive {
		logger.Debug("SR Proxy Keepalive mismatch",
			zap.String("name", requestedConfig.Name),
			zap.Bool("requested", requestedConfig.Keepalive),
			zap.Bool("local", localConfig.Keepalive))
		return false
	}

	// Compare EncryptType
	if requestedConfig.EncryptType != localConfig.EncryptType {
		logger.Debug("SR Proxy EncryptType mismatch",
			zap.String("name", requestedConfig.Name),
			zap.Int("requested", requestedConfig.EncryptType),
			zap.Int("local", localConfig.EncryptType))
		return false
	}

	// Compare Password (only if encryption is enabled)
	if requestedConfig.EncryptType != 0 && requestedConfig.Password != localConfig.Password {
		logger.Debug("SR Proxy Password mismatch",
			zap.String("name", requestedConfig.Name))
		return false
	}

	// Compare Links
	if len(requestedConfig.Links) != len(localConfig.Links) {
		logger.Debug("SR Proxy Links length mismatch",
			zap.String("name", requestedConfig.Name),
			zap.Int("requested", len(requestedConfig.Links)),
			zap.Int("local", len(localConfig.Links)))
		return false
	}

	// Compare links order - must be exactly the same
	for i, link := range requestedConfig.Links {
		if link != localConfig.Links[i] {
			logger.Debug("SR Proxy Links order mismatch",
				zap.String("name", requestedConfig.Name),
				zap.Int("position", i),
				zap.Int32("requested", link),
				zap.Int32("local", localConfig.Links[i]))
			return false
		}
	}

	logger.Debug("SR Proxy configurations match", zap.String("name", requestedConfig.Name))
	return true
}

/*****************************************************************************
 * NAME: VerifySrProxyConfig
 *
 * DESCRIPTION:
 *     Verifies that the SR Proxy configuration was applied correctly.
 *     Compares the requested configuration with the actual configuration on the system.
 *     Verification is based on the first path links as per requirement.
 *     Reuses CompareSrProxyConfig to avoid code duplication and ensure consistency.
 *
 * PARAMETERS:
 *     logger          - Logger instance for logging operations
 *     requestedConfig - SR Proxy configuration containing the requested configuration (converted from protobuf)
 *
 * RETURNS:
 *     bool  - True if configuration matches, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifySrProxyConfig(logger *logger.Logger, requestedConfig *SrProxyConfig) (bool, error) {
	logger.Info("verifying SR Proxy configuration", zap.String("name", requestedConfig.Name))

	// Get current SR Proxy configuration
	actualConfig, err := GetSrProxyConfig(logger, requestedConfig.Name)
	if err != nil {
		return false, fmt.Errorf("failed to get SR Proxy configuration: %w", err)
	}

	// Use CompareSrProxyConfig to perform the actual comparison
	// This ensures consistency and avoids code duplication
	configsMatch := CompareSrProxyConfig(logger, requestedConfig, actualConfig)

	if configsMatch {
		logger.Info("SR Proxy configuration verified successfully",
			zap.String("name", requestedConfig.Name))
		return true, nil
	} else {
		logger.Error("SR Proxy configuration verification failed",
			zap.String("name", requestedConfig.Name))
		return false, nil
	}
}
