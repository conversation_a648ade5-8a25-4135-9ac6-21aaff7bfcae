/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_policy_config.go
 *
 * DESCRIPTION :    DNS policy configuration structures and parsing functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/15/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: DnsPolicyConfig
 *
 * DESCRIPTION:
 *     Represents a DNS policy configuration from the local PA system.
 *     Contains all DNS policy parameters including matching criteria and actions.
 *
 * FIELDS:
 *     ID           - Policy ID (local identifier)
 *     Cookie       - Policy cookie (unique identifier)
 *     Previous     - Previous policy cookie for ordering
 *     Disable      - Whether the policy is disabled
 *     SchTime      - Policy time schedule ID
 *     InIP         - Source IP specifications
 *     OutIP        - Destination IP specifications
 *     Pool         - User group ID
 *     UsrType      - User type (any/ippxy/nonippxy)
 *     DNS          - Domain group ID
 *     DNSName      - Domain group name
 *     AType        - Query type (any/onlya/onlyaaaa)
 *     InIf         - Source interface
 *     Bridge       - Bridge interface ID
 *     VLAN         - VLAN ID
 *     Action       - Policy action (pass/deny/rdr/reply/limit/ippxy/zeroreply)
 *     ActArg       - Action argument (varies by action type)
 *     IPQps        - Single user QPS limit
 *     Next         - Whether to continue matching
 *     NoSnat       - Whether to not change source address (for rdr action)
 *     DNSList      - DNS list for rdr action
 *****************************************************************************/
type DnsPolicyConfig struct {
	ID       int    // Policy ID (local identifier)
	Cookie   uint32 // Policy cookie (unique identifier)
	Previous uint32 // Previous policy cookie for ordering
	Disable  bool   // Whether the policy is disabled
	SchTime  int    // Policy time schedule ID

	// Matching criteria
	InIP    string // Source IP specifications
	OutIP   string // Destination IP specifications
	Pool    int    // User group ID
	UsrType string // User type (any/ippxy/nonippxy)
	DNS     int    // Domain group ID
	DNSName string // Domain group name
	AType   string // Query type (any/onlya/onlyaaaa)
	InIf    string // Source interface
	Bridge  int    // Bridge interface ID
	VLAN    string // VLAN ID

	// Action configuration
	Action  string // Policy action (pass/deny/rdr/reply/limit/ippxy/zeroreply)
	ActArg  string // Action argument (varies by action type)
	IPQps   int    // Single user QPS limit
	Next    bool   // Whether to continue matching
	NoSnat  bool   // Whether to not change source address (for rdr action)
	DNSList string // DNS list for rdr action
}

/*****************************************************************************
 * NAME: ParseDnsPolicyFromJSONOutput
 *
 * DESCRIPTION:
 *     Parses DNS policy configuration from floweye dnspolicy list JSON output.
 *     Extracts policy information including matching criteria and actions.
 *
 * PARAMETERS:
 *     jsonData - JSON data from floweye dnspolicy list command
 *
 * RETURNS:
 *     []*DnsPolicyConfig - List of parsed DNS policy configurations
 *     error              - Error if parsing fails
 *****************************************************************************/
func ParseDnsPolicyFromJSONOutput(jsonData string) ([]*DnsPolicyConfig, error) {
	// Parse JSON output using unified floweye JSON parser
	policies, err := utils.ParseFloweyeJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse floweye JSON output: %w", err)
	}

	var configs []*DnsPolicyConfig
	for _, policyData := range policies {
		config := &DnsPolicyConfig{}

		// Parse basic fields
		if polno, ok := policyData["polno"].(float64); ok {
			config.ID = int(polno)
		}
		if disabled, ok := policyData["disabled"].(float64); ok {
			config.Disable = int(disabled) == 1
		}
		if schID, ok := policyData["sch_id"].(float64); ok {
			config.SchTime = int(schID)
		}

		// Parse matching criteria
		if srcip, ok := policyData["srcip"].(string); ok {
			config.InIP = srcip
		}
		if dstip, ok := policyData["dstip"].(string); ok {
			config.OutIP = dstip
		}
		if ugroupID, ok := policyData["ugroup_id"].(float64); ok {
			config.Pool = int(ugroupID)
		}
		if usrtype, ok := policyData["usrtype"].(string); ok {
			config.UsrType = usrtype
		}
		if dnsid, ok := policyData["dnsid"].(float64); ok {
			config.DNS = int(dnsid)
		}
		if dnsname, ok := policyData["dnsname"].(string); ok {
			config.DNSName = dnsname
		}
		if atype, ok := policyData["atype"].(string); ok {
			config.AType = atype
		}
		if inif, ok := policyData["inif"].(string); ok {
			config.InIf = inif
		}
		if bridge, ok := policyData["bridge"].(float64); ok {
			config.Bridge = int(bridge)
		}
		if vlan, ok := policyData["vlan"].(string); ok {
			config.VLAN = vlan
		}

		// Parse action fields
		if action, ok := policyData["action"].(string); ok {
			config.Action = action
		}
		if actarg, ok := policyData["actarg"].(string); ok {
			config.ActArg = actarg
		}
		if ipqps, ok := policyData["ipqps"].(float64); ok {
			config.IPQps = int(ipqps)
		}
		if next, ok := policyData["next"].(float64); ok {
			config.Next = int(next) == 0 // next=0 means continue matching
		}
		if nosnat, ok := policyData["nosnat"].(float64); ok {
			config.NoSnat = int(nosnat) == 1
		}
		if dnslist, ok := policyData["dnslist"].(string); ok {
			config.DNSList = dnslist
		}

		configs = append(configs, config)
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: ParseDnsPolicyFromGetOutput
 *
 * DESCRIPTION:
 *     Parses DNS policy configuration from floweye dnspolicy get output.
 *     Extracts detailed policy information including cookie from key=value format.
 *
 * PARAMETERS:
 *     output - Output from floweye dnspolicy get command
 *
 * RETURNS:
 *     *DnsPolicyConfig - Parsed DNS policy configuration
 *     error            - Error if parsing fails
 *****************************************************************************/
func ParseDnsPolicyFromGetOutput(output string) (*DnsPolicyConfig, error) {
	config := &DnsPolicyConfig{}

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse key=value pairs
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) != 2 {
				continue
			}
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			switch key {
			case "id":
				if id, err := strconv.Atoi(value); err == nil {
					config.ID = id
				}
			case "cookie":
				if cookie, err := strconv.ParseUint(value, 10, 32); err == nil {
					config.Cookie = uint32(cookie)
				}
			case "disable":
				config.Disable = value == "1"
			case "schtime":
				if schtime, err := strconv.Atoi(value); err == nil {
					config.SchTime = schtime
				}
			case "inip":
				config.InIP = value
			case "outip":
				config.OutIP = value
			case "pool":
				if pool, err := strconv.Atoi(value); err == nil {
					config.Pool = pool
				}
			case "usrtype":
				config.UsrType = value
			case "dns":
				if dns, err := strconv.Atoi(value); err == nil {
					config.DNS = dns
				}
			case "atype":
				config.AType = value
			case "inif":
				config.InIf = value
			case "bridge":
				if bridge, err := strconv.Atoi(value); err == nil {
					config.Bridge = bridge
				}
			case "vlan":
				config.VLAN = value
			case "action":
				config.Action = value
			case "actarg":
				config.ActArg = value
			case "ipqps":
				if ipqps, err := strconv.Atoi(value); err == nil {
					config.IPQps = ipqps
				}
			case "next":
				config.Next = value == "0" // next=0 means continue matching
			case "nosnat":
				config.NoSnat = value == "1"
			case "dnslist":
				config.DNSList = value
			}
		}
	}

	if config.Cookie == 0 {
		return nil, fmt.Errorf("failed to parse DNS policy cookie from output")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ConvertDnsPolicyTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a DNS policy task protobuf message to internal DnsPolicyConfig structure.
 *     Performs one-time conversion to eliminate repeated protobuf parsing.
 *     Handles all protobuf field types and applies appropriate default values.
 *
 * PARAMETERS:
 *     task - DNS policy task protobuf message
 *
 * RETURNS:
 *     *DnsPolicyConfig - Converted internal configuration structure
 *     error            - Error if conversion fails
 *****************************************************************************/
func ConvertDnsPolicyTaskToConfig(task *pb.DnsPolicyTask, logger *logger.Logger) (*DnsPolicyConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("DnsPolicyTask is nil")
	}

	config := &DnsPolicyConfig{
		Cookie:  task.GetCookie(),
		Disable: task.GetDisable(),
		SchTime: int(task.GetSchTime()),
	}

	// Handle previous field for ordering
	if task.Previous != nil {
		config.Previous = *task.Previous
	} else {
		config.Previous = 4294967295 // Default to -1 (append position) as uint32
	}

	// Convert source IP addresses
	if len(task.GetInIp()) > 0 {
		inIPStr, err := BuildAddressSelectorsString(task.GetInIp(), logger)
		if err != nil {
			return nil, fmt.Errorf("failed to convert source IP addresses: %w", err)
		}
		config.InIP = inIPStr
	} else {
		config.InIP = "any"
	}

	// Convert destination IP addresses
	if len(task.GetOutIp()) > 0 {
		outIPStr, err := BuildAddressSelectorsString(task.GetOutIp(), logger)
		if err != nil {
			return nil, fmt.Errorf("failed to convert destination IP addresses: %w", err)
		}
		config.OutIP = outIPStr
	} else {
		config.OutIP = "any"
	}

	// Convert user group
	config.Pool = int(task.GetPool())

	// Convert user type
	config.UsrType = convertUserTypeToString(task.GetUsrType())

	// Convert domain groups (use first domain group if multiple)
	domainGroups := task.GetDomainGroup()
	if len(domainGroups) > 0 && domainGroups[0] != "" {
		config.DNSName = domainGroups[0]
		// DNS ID will be resolved during command building
		config.DNS = 0
	} else {
		config.DNSName = ""
		config.DNS = 0
	}

	// Convert query type
	config.AType = convertQueryTypeToString(task.GetAType())

	// Convert interface settings
	if task.GetInIf() != "" {
		config.InIf = task.GetInIf()
	} else {
		config.InIf = "any"
	}

	config.Bridge = int(task.GetBridge())

	// Convert VLAN
	config.VLAN = BuildIntRangeString(task.GetVlan())

	// Convert action and action-specific configuration
	config.Action = convertActionTypeToString(task.GetAction())

	// Handle action-specific configurations
	switch task.GetAction() {
	case pb.DnsPolicyAction_DNS_ACTION_PASS, pb.DnsPolicyAction_DNS_ACTION_IPPXY:
		if task.GetActionPass() != nil {
			passConfig := task.GetActionPass()
			config.Next = !passConfig.GetNext() // next=true means continue matching (stored as false)
			if passConfig.IpQps != nil {
				config.IPQps = int(*passConfig.IpQps)
			}
		}
		config.ActArg = "null"
		config.NoSnat = false
		config.DNSList = ""

	case pb.DnsPolicyAction_DNS_ACTION_RDR:
		if task.GetActionRdr() != nil {
			rdrConfig := task.GetActionRdr()
			config.ActArg = rdrConfig.GetActArg()
			if rdrConfig.NoSnat != nil {
				config.NoSnat = *rdrConfig.NoSnat
			}
			if len(rdrConfig.GetDnsList()) > 0 {
				config.DNSList = buildDNSListFromIPs(rdrConfig.GetDnsList())
			}
		}
		config.Next = false // rdr action defaults to stop matching
		config.IPQps = 0

	case pb.DnsPolicyAction_DNS_ACTION_REPLY:
		if task.GetActionReply() != nil {
			replyConfig := task.GetActionReply()
			if len(replyConfig.GetActArg()) > 0 {
				config.ActArg = buildDNSListFromIPs(replyConfig.GetActArg())
			}
		}
		config.Next = false // reply action defaults to stop matching
		config.IPQps = 0
		config.NoSnat = false
		config.DNSList = ""

	case pb.DnsPolicyAction_DNS_ACTION_LIMIT:
		if task.GetActionLimit() != nil {
			limitConfig := task.GetActionLimit()
			config.Next = !limitConfig.GetNext() // next=true means continue matching
			if limitConfig.IpQps != nil {
				config.IPQps = int(*limitConfig.IpQps)
			}
			if limitConfig.ActArg != nil {
				config.ActArg = strconv.Itoa(int(*limitConfig.ActArg))
			}
		}
		config.NoSnat = false
		config.DNSList = ""

	case pb.DnsPolicyAction_DNS_ACTION_DENY, pb.DnsPolicyAction_DNS_ACTION_ZEROREPLY:
		config.ActArg = "null"
		config.Next = false // deny/zeroreply actions default to stop matching
		config.IPQps = 0
		config.NoSnat = false
		config.DNSList = ""

	default:
		return nil, fmt.Errorf("unsupported DNS policy action: %v", task.GetAction())
	}

	return config, nil
}

/*****************************************************************************
 * NAME: buildDNSListFromIPs
 *
 * DESCRIPTION:
 *     Builds a DNS list string from IP addresses.
 *
 * PARAMETERS:
 *     ips - List of IP addresses
 *
 * RETURNS:
 *     string - Comma-separated DNS list string
 *****************************************************************************/
func buildDNSListFromIPs(ips []*pb.IpAddress) string {
	if len(ips) == 0 {
		return ""
	}

	var dnsEntries []string
	for _, ip := range ips {
		ipStr, err := ConvertIpAddressToString(ip)
		if err != nil {
			continue
		}
		dnsEntries = append(dnsEntries, ipStr)
	}

	return strings.Join(dnsEntries, ",")
}

/*****************************************************************************
 * NAME: convertActionTypeToString
 *
 * DESCRIPTION:
 *     Converts DNS policy action enum to string representation.
 *
 * PARAMETERS:
 *     action - DNS policy action enum
 *
 * RETURNS:
 *     string - String representation of the action
 *****************************************************************************/
func convertActionTypeToString(action pb.DnsPolicyAction) string {
	switch action {
	case pb.DnsPolicyAction_DNS_ACTION_PASS:
		return "pass"
	case pb.DnsPolicyAction_DNS_ACTION_DENY:
		return "deny"
	case pb.DnsPolicyAction_DNS_ACTION_RDR:
		return "rdr"
	case pb.DnsPolicyAction_DNS_ACTION_REPLY:
		return "reply"
	case pb.DnsPolicyAction_DNS_ACTION_LIMIT:
		return "limit"
	case pb.DnsPolicyAction_DNS_ACTION_IPPXY:
		return "ippxy"
	case pb.DnsPolicyAction_DNS_ACTION_ZEROREPLY:
		return "zeroreply"
	default:
		return "pass"
	}
}

/*****************************************************************************
 * NAME: CompareDnsPolicyConfig
 *
 * DESCRIPTION:
 *     Compares a DNS policy configuration with local configuration.
 *     Checks if the configurations match to determine if update is needed.
 *     Uses converted internal data structure instead of protobuf access.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Converted DNS policy configuration data
 *     localConfig - Local DNS policy configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareDnsPolicyConfig(logger *logger.Logger, configData *DnsPolicyConfig, localConfig *DnsPolicyConfig) bool {
	if configData == nil || localConfig == nil {
		logger.Debug("DNS policy comparison: nil input")
		return false
	}

	// Compare basic fields
	if configData.Cookie != localConfig.Cookie {
		logger.Debug("DNS policy cookie mismatch",
			zap.Uint32("config_cookie", configData.Cookie),
			zap.Uint32("local_cookie", localConfig.Cookie))
		return false
	}

	if configData.Disable != localConfig.Disable {
		logger.Debug("DNS policy disable status mismatch",
			zap.Bool("config_disable", configData.Disable),
			zap.Bool("local_disable", localConfig.Disable))
		return false
	}

	if configData.SchTime != localConfig.SchTime {
		logger.Debug("DNS policy schedule time mismatch",
			zap.Int("config_sch_time", configData.SchTime),
			zap.Int("local_schtime", localConfig.SchTime))
		return false
	}

	// Compare source IP addresses using robust IP format comparison
	if !CompareIPFormats(configData.InIP, localConfig.InIP) {
		logger.Debug("DNS policy source IP mismatch",
			zap.String("config_inip", configData.InIP),
			zap.String("local_inip", localConfig.InIP))
		return false
	}

	// Compare destination IP addresses using robust IP format comparison
	if !CompareIPFormats(configData.OutIP, localConfig.OutIP) {
		logger.Debug("DNS policy destination IP mismatch",
			zap.String("config_outip", configData.OutIP),
			zap.String("local_outip", localConfig.OutIP))
		return false
	}

	// Compare user group
	if configData.Pool != localConfig.Pool {
		logger.Debug("DNS policy pool mismatch",
			zap.Int("config_pool", configData.Pool),
			zap.Int("local_pool", localConfig.Pool))
		return false
	}

	// Compare user type
	if configData.UsrType != localConfig.UsrType {
		logger.Debug("DNS policy user type mismatch",
			zap.String("config_usrtype", configData.UsrType),
			zap.String("local_usrtype", localConfig.UsrType))
		return false
	}

	// Compare domain group
	if configData.DNSName != localConfig.DNSName {
		logger.Debug("DNS policy domain group mismatch",
			zap.String("config_domain_group", configData.DNSName),
			zap.String("local_dns_name", localConfig.DNSName))
		return false
	}

	// Compare query type
	if configData.AType != localConfig.AType {
		logger.Debug("DNS policy query type mismatch",
			zap.String("config_atype", configData.AType),
			zap.String("local_atype", localConfig.AType))
		return false
	}

	// Compare interface settings
	if configData.InIf != localConfig.InIf {
		logger.Debug("DNS policy interface mismatch",
			zap.String("config_inif", configData.InIf),
			zap.String("local_inif", localConfig.InIf))
		return false
	}

	if configData.Bridge != localConfig.Bridge {
		logger.Debug("DNS policy bridge mismatch",
			zap.Int("config_bridge", configData.Bridge),
			zap.Int("local_bridge", localConfig.Bridge))
		return false
	}

	// Compare VLAN configuration
	if configData.VLAN != localConfig.VLAN {
		logger.Debug("DNS policy VLAN mismatch",
			zap.String("config_vlan", configData.VLAN),
			zap.String("local_vlan", localConfig.VLAN))
		return false
	}

	// Compare action configuration
	if configData.Action != localConfig.Action {
		logger.Debug("DNS policy action mismatch",
			zap.String("config_action", configData.Action),
			zap.String("local_action", localConfig.Action))
		return false
	}

	// Compare action-specific configurations
	if !compareDnsActionConfigData(logger, configData, localConfig) {
		return false
	}

	logger.Debug("DNS policy configurations match",
		zap.Uint32("cookie", configData.Cookie))
	return true
}

// Helper functions for enum conversions
func convertUserTypeToString(userType pb.UserType) string {
	switch userType {
	case pb.UserType_USER_TYPE_IPPXY:
		return "ippxy"
	case pb.UserType_USER_TYPE_NONIPPXY:
		return "nonippxy"
	default:
		return "any"
	}
}

func convertQueryTypeToString(queryType pb.DnsQueryType) string {
	switch queryType {
	case pb.DnsQueryType_DNS_QUERY_TYPE_IPV4:
		return "onlya"
	case pb.DnsQueryType_DNS_QUERY_TYPE_IPV6:
		return "onlyaaaa"
	default:
		return "any"
	}
}

func compareDnsActionConfigData(logger *logger.Logger, configData *DnsPolicyConfig, localConfig *DnsPolicyConfig) bool {
	// Compare action-specific fields using converted data
	switch configData.Action {
	case "pass", "ippxy":
		if configData.Next != localConfig.Next {
			logger.Debug("DNS policy next status mismatch",
				zap.Bool("config_next", configData.Next),
				zap.Bool("local_next", localConfig.Next))
			return false
		}
		if configData.IPQps != localConfig.IPQps {
			logger.Debug("DNS policy IP QPS mismatch",
				zap.Int("config_ipqps", configData.IPQps),
				zap.Int("local_ipqps", localConfig.IPQps))
			return false
		}

	case "rdr":
		if configData.ActArg != localConfig.ActArg {
			logger.Debug("DNS policy rdr actarg mismatch",
				zap.String("config_actarg", configData.ActArg),
				zap.String("local_actarg", localConfig.ActArg))
			return false
		}
		if configData.NoSnat != localConfig.NoSnat {
			logger.Debug("DNS policy nosnat mismatch",
				zap.Bool("config_nosnat", configData.NoSnat),
				zap.Bool("local_nosnat", localConfig.NoSnat))
			return false
		}
		if configData.DNSList != localConfig.DNSList {
			logger.Debug("DNS policy dnslist mismatch",
				zap.String("config_dnslist", configData.DNSList),
				zap.String("local_dnslist", localConfig.DNSList))
			return false
		}

	case "reply":
		if configData.ActArg != localConfig.ActArg {
			logger.Debug("DNS policy reply actarg mismatch",
				zap.String("config_actarg", configData.ActArg),
				zap.String("local_actarg", localConfig.ActArg))
			return false
		}

	case "limit":
		if configData.Next != localConfig.Next {
			logger.Debug("DNS policy next status mismatch",
				zap.Bool("config_next", configData.Next),
				zap.Bool("local_next", localConfig.Next))
			return false
		}
		if configData.IPQps != localConfig.IPQps {
			logger.Debug("DNS policy IP QPS mismatch",
				zap.Int("config_ipqps", configData.IPQps),
				zap.Int("local_ipqps", localConfig.IPQps))
			return false
		}
		if configData.ActArg != localConfig.ActArg {
			logger.Debug("DNS policy limit actarg mismatch",
				zap.String("config_actarg", configData.ActArg),
				zap.String("local_actarg", localConfig.ActArg))
			return false
		}
	}
	return true
}

/*****************************************************************************
 * NAME: VerifyDnsPolicyConfig
 *
 * DESCRIPTION:
 *     Verifies that a DNS policy configuration was applied correctly.
 *     Reuses CompareDnsPolicyConfig function to ensure consistency.
 *     Follows CONTRIBUTING.md guidelines for verification function design.
 *
 * PARAMETERS:
 *     logger     - Logger for verification operations
 *     configData - Expected DNS policy configuration data
 *
 * RETURNS:
 *     bool  - True if verification succeeds, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyDnsPolicyConfig(logger *logger.Logger, configData *DnsPolicyConfig) (bool, error) {
	if configData == nil {
		return false, fmt.Errorf("configData is nil")
	}

	logger.Debug("verifying DNS policy configuration",
		zap.Uint32("cookie", configData.Cookie))

	// Get actual configuration from the system
	actualConfig, err := GetDnsPolicyConfigByCookie(logger, configData.Cookie)
	if err != nil {
		return false, fmt.Errorf("failed to get DNS policy configuration: %w", err)
	}

	if actualConfig == nil {
		return false, fmt.Errorf("DNS policy not found with cookie %d", configData.Cookie)
	}

	// Use the comparison function to verify configuration matches
	// This ensures consistency with the comparison logic used elsewhere
	matches := CompareDnsPolicyConfig(logger, configData, actualConfig)
	if !matches {
		logger.Debug("DNS policy verification failed - configurations do not match",
			zap.Uint32("cookie", configData.Cookie))
		return false, nil
	}

	logger.Debug("DNS policy verification succeeded",
		zap.Uint32("cookie", configData.Cookie))
	return true, nil
}

/*****************************************************************************
 * NAME: GetDnsPolicyConfigByCookie
 *
 * DESCRIPTION:
 *     Retrieves DNS policy configuration by cookie using single-object retrieval.
 *     Uses floweye dnspolicy get command for performance optimization.
 *
 * PARAMETERS:
 *     logger - Logger for retrieval operations
 *     cookie - Policy cookie to retrieve
 *
 * RETURNS:
 *     *DnsPolicyConfig - Retrieved DNS policy configuration
 *     error            - Error if retrieval fails
 *****************************************************************************/
func GetDnsPolicyConfigByCookie(logger *logger.Logger, cookie uint32) (*DnsPolicyConfig, error) {
	logger.Debug("retrieving DNS policy configuration by cookie",
		zap.Uint32("cookie", cookie))

	// Use floweye dnspolicy get command with cookie parameter
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dnspolicy", "get", "cookie="+strconv.FormatUint(uint64(cookie), 10))
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS policy by cookie: %w", err)
	}

	// Parse the output
	config, err := ParseDnsPolicyFromGetOutput(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse DNS policy configuration: %w", err)
	}

	// Resolve domain group ID to name if DNS ID is set
	if config.DNS > 0 {
		domainGroupName, err := GetDomainGroupNameByID(logger, config.DNS)
		if err != nil {
			logger.Warn("failed to resolve domain group ID to name",
				zap.Int("dns_id", config.DNS),
				zap.Error(err))
			// Don't fail the entire operation, just leave DNSName empty
		} else {
			config.DNSName = domainGroupName
		}
	}

	logger.Debug("successfully retrieved DNS policy configuration",
		zap.Uint32("cookie", cookie),
		zap.Int("id", config.ID),
		zap.String("dns_name", config.DNSName))

	return config, nil
}

// Note: Removed duplicate functions that are now available in common_protobuf_utils.go:
// - compareIPAddresses -> use CompareAddressSelectorsWithString
// - buildAddressSelectorsString -> use BuildAddressSelectorsString
// - getIpString -> use ConvertIpAddressToString
// - compareVLANConfig -> use BuildIntRangeString
// - compareAppProtocol -> simplified inline comparison
