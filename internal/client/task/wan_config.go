/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      wan_config.go
 *
 * DESCRIPTION :    WAN configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: WanConfig
 *
 * DESCRIPTION:
 *     Unified configuration structure for WAN interface processing.
 *     Serves as both system configuration storage and internal processing structure.
 *     Eliminates the need for separate WanConfigData by extending existing structure.
 *
 * FIELDS:
 *     System fields (from floweye commands):
 *       Id       - WAN interface ID
 *       Name     - WAN interface name
 *       Type     - WAN interface type (proxy, dhcpwan, pppoe)
 *       State    - Interface state (0: down, 1: up)
 *
 *     Basic configuration:
 *       Ifname   - Network interface name
 *       Mtu      - Maximum transmission unit
 *       Addr     - IP address
 *       Gateway  - Gateway address
 *       Dns      - DNS server address
 *
 *     Heartbeat configuration:
 *       PingIp   - Heartbeat server IP
 *       PingIp2  - Secondary heartbeat server IP
 *       MaxDelay - Maximum delay in ms
 *
 *     Common configuration:
 *       DnsPxy      - DNS proxy enabled
 *       PingDisable - Disable ping response
 *       CloneMac    - MAC address to clone
 *
 *     PPPoE specific fields:
 *       Username - PPPoE username
 *       Password - PPPoE password
 *       AcName   - PPPoE AC name
 *       SvcName  - PPPoE service name
 *       WaitTime - PPPoE wait time in seconds (added for protobuf support)
 *
 *     Static IP specific fields:
 *       GwPxy    - Gateway type (0: normal, 1: internet) (added for protobuf support)
 *       NatIp    - NAT IP configuration (added for protobuf support)
 *
 *     DHCP configuration:
 *       DhcpOptions - DHCP options map (key: option number, value: "type,value")
 *****************************************************************************/
type WanConfig struct {
	// System fields (from floweye commands)
	Id    int    // WAN interface ID
	Name  string // WAN interface name
	Type  string // WAN interface type (proxy, dhcpwan, pppoe)
	State int    // Interface state (0: down, 1: up)

	// Basic configuration
	Ifname  string // Network interface name
	Mtu     int    // Maximum transmission unit
	Addr    string // IP address
	Gateway string // Gateway address
	Dns     string // DNS server address

	// Heartbeat configuration
	PingIp   string // Heartbeat server IP
	PingIp2  string // Secondary heartbeat server IP
	MaxDelay int    // Maximum delay in ms

	// Common configuration
	DnsPxy      int    // DNS proxy enabled
	PingDisable int    // Disable ping response
	CloneMac    string // MAC address to clone

	// PPPoE specific fields
	Username string // PPPoE username
	Password string // PPPoE password
	AcName   string // PPPoE AC name
	SvcName  string // PPPoE service name
	WaitTime int    // PPPoE wait time in seconds (added for protobuf support)

	// Static IP specific fields (added for protobuf support)
	GwPxy int    // Gateway type (0: normal, 1: internet)
	NatIp string // NAT IP configuration

	// DHCP configuration
	DhcpOptions map[string]string // DHCP options (key: option number, value: "type,value")
}

/*****************************************************************************
 * NAME: ConvertWanTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf WanTask message to unified WanConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing WanConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     wanTask - Protobuf WAN task message to convert
 *
 * RETURNS:
 *     *WanConfig - Converted WAN configuration structure
 *     error      - Error if conversion fails
 *****************************************************************************/
func ConvertWanTaskToConfig(wanTask *pb.WanTask) (*WanConfig, error) {
	if wanTask == nil {
		return nil, fmt.Errorf("wanTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &WanConfig{
		Name:        wanTask.GetName(),
		Ifname:      wanTask.GetIfname(),
		Mtu:         int(wanTask.GetMtu()),
		DhcpOptions: make(map[string]string),
		// Default values
		CloneMac: "00-00-00-00-00-00",
		PingIp:   "0.0.0.0",
		PingIp2:  "0.0.0.0",
		NatIp:    "0.0.0.0",
	}

	// Determine WAN type and extract type-specific configuration
	switch {
	case wanTask.GetStaticIp() != nil:
		config.Type = "proxy" // Static IP uses proxy type in floweye
		staticIp := wanTask.GetStaticIp()

		// Gateway type conversion
		if staticIp.GetGwPxy() == pb.WanGatewayType_WAN_GATEWAY_TYPE_INTERNET {
			config.GwPxy = 1
		} else {
			config.GwPxy = 0
		}

		// IP address fields
		if staticIp.GetAddr() != nil {
			config.Addr = utils.GetIpString(staticIp.GetAddr())
		}
		if staticIp.GetGateway() != nil {
			config.Gateway = utils.GetIpString(staticIp.GetGateway())
		}
		if staticIp.GetDns() != nil {
			config.Dns = utils.GetIpString(staticIp.GetDns())
		}
		if staticIp.GetNatIp() != nil {
			config.NatIp = utils.GetNatIpString(staticIp.GetNatIp())
		} else {
			config.NatIp = "0.0.0.0"
		}

	case wanTask.GetDhcp() != nil:
		config.Type = "dhcpwan" // DHCP uses dhcpwan type in floweye
		dhcp := wanTask.GetDhcp()

		// DHCP options conversion - store in DhcpOptions map
		if dhcp.GetOption12() != nil {
			valueType := "str"
			if dhcp.GetOption12().GetValueType() == pb.DhcpOptionValueType_DHCP_OPTION_TYPE_HEX {
				valueType = "hex"
			}
			config.DhcpOptions["12"] = valueType + "," + dhcp.GetOption12().GetValue()
		}

		if dhcp.GetOption61() != nil {
			valueType := "str"
			if dhcp.GetOption61().GetValueType() == pb.DhcpOptionValueType_DHCP_OPTION_TYPE_HEX {
				valueType = "hex"
			}
			config.DhcpOptions["61"] = valueType + "," + dhcp.GetOption61().GetValue()
		}

		if dhcp.GetOption60() != nil {
			valueType := "str"
			if dhcp.GetOption60().GetValueType() == pb.DhcpOptionValueType_DHCP_OPTION_TYPE_HEX {
				valueType = "hex"
			}
			config.DhcpOptions["60"] = valueType + "," + dhcp.GetOption60().GetValue()
		}

	case wanTask.GetPppoe() != nil:
		config.Type = "pppoe" // PPPoE uses pppoe type in floweye
		pppoe := wanTask.GetPppoe()

		// PPPoE configuration
		config.Username = pppoe.GetUsername()
		config.Password = pppoe.GetPassword()
		if pppoe.GetAcName() != "" {
			config.AcName = pppoe.GetAcName()
		} else {
			config.AcName = "NULL"
		}
		if pppoe.GetSvcName() != "" {
			config.SvcName = pppoe.GetSvcName()
		} else {
			config.SvcName = "NULL"
		}

		waitTime := pppoe.GetWaitTime()
		if waitTime != 0 {
			config.WaitTime = int(waitTime)
		} else {
			config.WaitTime = 5
		}

	default:
		return nil, fmt.Errorf("unknown WAN type")
	}

	// Extract heartbeat configuration if present
	if wanTask.GetHeartbeat() != nil {
		heartbeat := wanTask.GetHeartbeat()
		if heartbeat.GetPingIp() != nil {
			config.PingIp = utils.GetIpString(heartbeat.GetPingIp())
		} else {
			config.PingIp = "0.0.0.0"
		}
		if heartbeat.GetPingIp2() != nil {
			config.PingIp2 = utils.GetIpString(heartbeat.GetPingIp2())
		} else {
			config.PingIp2 = "0.0.0.0"
		}
		maxDelay := heartbeat.GetMaxDelay()
		if maxDelay != 0 {
			config.MaxDelay = int(maxDelay)
		}
	}

	// Extract common configuration if present
	if wanTask.GetCommon() != nil {
		common := wanTask.GetCommon()

		// Check if DnsPxy field is set (protobuf optional field)
		if common.DnsPxy != nil {
			if *common.DnsPxy {
				config.DnsPxy = 1
			} else {
				config.DnsPxy = 0
			}
		}

		// Check if CloneMac field is set (protobuf optional field)
		if common.CloneMac != nil {
			config.CloneMac = *common.CloneMac
		} else {
			config.CloneMac = "00-00-00-00-00-00"
		}

		// Check if PingDisable field is set (protobuf optional field)
		if common.PingDisable != nil {
			if *common.PingDisable {
				config.PingDisable = 1
			} else {
				config.PingDisable = 0
			}
		}
	}

	return config, nil
}

/*****************************************************************************
 * NAME: GetLocalWanConfigs
 *
 * DESCRIPTION:
 *     Retrieves all local WAN configurations from the system.
 *     Executes the floweye nat listproxy command and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*WanConfig - Map of WAN names to their configurations
 *     error                 - Error if retrieval fails
 *****************************************************************************/
func GetLocalWanConfigs(logger *logger.Logger) (map[string]*WanConfig, error) {
	logger.Info("Starting to retrieve all local WAN configurations")

	// Execute floweye nat listproxy command with JSON output
	// Execute floweye command to list WAN configurations
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "listproxy", "json=1", "type=wan")
	if err != nil {
		logger.Error("failed to execute floweye nat listproxy command",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get WAN list: %v", err)
	}

	// Parse JSON output to extract WAN names only
	wans := make(map[string]*WanConfig)

	// Parse JSON output using unified floweye JSON parser
	wanList, err := utils.ParseFloweyeJSON(output)
	if err != nil {
		logger.Error("failed to parse floweye JSON output",
			zap.String("output", output),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse JSON output: %v", err)
	}

	// Extract WAN configurations
	for _, wanEntry := range wanList {
		// Only extract name and type for filtering
		if name, ok := wanEntry["name"].(string); ok {
			if wanType, ok := wanEntry["type"].(string); ok &&
				(wanType == "proxy" || wanType == "dhcpwan" || wanType == "pppoe") {
				// Create a minimal config with just the name
				wans[name] = &WanConfig{Name: name, DhcpOptions: make(map[string]string)}
			}
		}
	}

	// Log summary of all WANs
	wanNames := make([]string, 0, len(wans))
	for name := range wans {
		wanNames = append(wanNames, name)
	}

	logger.Info("Retrieved all local WAN configurations",
		zap.Int("count", len(wans)),
		zap.Strings("wan_names", wanNames))

	// Get detailed configuration for each WAN using GetWanConfig
	for name := range wans {
		detailedConfig, err := GetWanConfig(logger, name)
		if err != nil {
			logger.Warn("failed to get detailed config for WAN",
				zap.String("wan", name),
				zap.Error(err))
			continue
		}

		// Replace the basic config with the detailed config
		wans[name] = detailedConfig
	}

	// Log detailed configuration for each WAN
	for name, config := range wans {
		logger.Info("WAN configuration details",
			zap.String("name", name),
			zap.String("type", config.Type),
			zap.String("ifname", config.Ifname),
			zap.Int("mtu", config.Mtu),
			zap.String("addr", config.Addr),
			zap.String("gateway", config.Gateway),
			zap.String("dns", config.Dns),
			zap.String("ping_ip", config.PingIp),
			zap.Int("max_delay", config.MaxDelay),
			zap.String("clone_mac", config.CloneMac),
			zap.String("username", config.Username),
			zap.String("ac_name", config.AcName),
			zap.String("svc_name", config.SvcName))
	}

	return wans, nil
}

/*****************************************************************************
 * NAME: GetWanConfig
 *
 * DESCRIPTION:
 *     Retrieves the configuration of a specific WAN interface.
 *     Executes the floweye nat getproxy command and parses the output.
 *
 * PARAMETERS:
 *     logger  - Logger instance for logging operations
 *     wanName - Name of the WAN interface to retrieve configuration for
 *
 * RETURNS:
 *     *WanConfig - WAN configuration structure
 *     error      - Error if retrieval fails
 *****************************************************************************/
func GetWanConfig(logger *logger.Logger, wanName string) (*WanConfig, error) {
	logger.Debug("retrieving configuration for WAN", zap.String("wan", wanName))

	// Execute floweye nat getproxy command
	output, err := utils.ExecuteCommand(logger, 5, "floweye", "nat", "getproxy", wanName)
	if err != nil {
		logger.Error("failed to execute floweye nat getproxy command",
			zap.Error(err),
			zap.String("wan", wanName),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get WAN config: %v", err)
	}

	// Parse output into a map for easier access
	configMap := make(map[string]string)
	dhcpOptions := make([]string, 0) // Store all dhcp_option lines separately
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			if key == "dhcp_option" {
				dhcpOptions = append(dhcpOptions, value)
			} else {
				configMap[key] = value
			}
		}
	}

	// Create configuration object with default values
	config := &WanConfig{
		Name:        wanName,
		DhcpOptions: make(map[string]string),
	}

	// For PPPoE, we need to track both mtu and cfgmtu to choose the right one
	var regularMtu, cfgMtu int
	var hasRegularMtu, hasCfgMtu bool

	// Extract values using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "proxyid":
			if val, err := strconv.Atoi(value); err == nil {
				config.Id = val
			}
		case "type":
			config.Type = value
		case "state":
			if val, err := strconv.Atoi(value); err == nil {
				config.State = val
			}
		case "ifname":
			config.Ifname = value
		case "mtu":
			// Store regular mtu value, will decide which to use later based on WAN type
			if val, err := strconv.Atoi(value); err == nil {
				regularMtu = val
				hasRegularMtu = true
				logger.Debug("parsed mtu field",
					zap.String("wan", wanName),
					zap.Int("mtu", val))
			}
		case "cfgmtu":
			// Store cfgmtu value, will decide which to use later based on WAN type
			if val, err := strconv.Atoi(value); err == nil {
				cfgMtu = val
				hasCfgMtu = true
				logger.Debug("parsed cfgmtu field",
					zap.String("wan", wanName),
					zap.Int("cfgmtu", val))
			}
		case "addr":
			config.Addr = value
		case "gateway":
			config.Gateway = value
		case "dnsaddr":
			config.Dns = value
		case "pingip":
			config.PingIp = value
		case "pingip2":
			config.PingIp2 = value
		case "maxdelay":
			if val, err := strconv.Atoi(value); err == nil {
				config.MaxDelay = val
			}
		case "dnspxy":
			if val, err := strconv.Atoi(value); err == nil {
				config.DnsPxy = val
			}
		case "ping_disable":
			if val, err := strconv.Atoi(value); err == nil {
				config.PingDisable = val
			}
		case "clonemac":
			config.CloneMac = value
		case "username":
			config.Username = value
		case "password":
			config.Password = value
		case "acname":
			config.AcName = value
		case "svcname":
			config.SvcName = value
		}
	}

	// Set MTU based on WAN type
	if config.Type == "pppoe" {
		// For PPPoE, prefer cfgmtu (user configured) over mtu (system internal)
		if hasCfgMtu {
			config.Mtu = cfgMtu
			logger.Debug("using cfgmtu for PPPoE",
				zap.String("wan", wanName),
				zap.Int("cfgmtu", cfgMtu))
		} else if hasRegularMtu {
			config.Mtu = regularMtu
			logger.Debug("using mtu for PPPoE (no cfgmtu found)",
				zap.String("wan", wanName),
				zap.Int("mtu", regularMtu))
		}
	} else {
		// For static IP and DHCP, use regular mtu
		if hasRegularMtu {
			config.Mtu = regularMtu
			logger.Debug("using mtu for non-PPPoE",
				zap.String("wan", wanName),
				zap.String("type", config.Type),
				zap.Int("mtu", regularMtu))
		}
	}

	// DHCP options
	// Parse all dhcp_option entries
	dhcpOptionRegex := regexp.MustCompile(`^(\d+),(\w+),(.+)$`)
	for _, dhcpOptionValue := range dhcpOptions {
		matches := dhcpOptionRegex.FindStringSubmatch(dhcpOptionValue)
		if len(matches) >= 4 {
			optionNumber := matches[1]
			optionType := strings.ToLower(matches[2]) // Convert to lowercase to match expected format
			optionValue := matches[3]
			config.DhcpOptions[optionNumber] = optionType + "," + optionValue
			logger.Debug("parsed DHCP option",
				zap.String("wan", wanName),
				zap.String("option", optionNumber),
				zap.String("type", optionType),
				zap.String("value", optionValue),
				zap.String("full", config.DhcpOptions[optionNumber]))
		} else {
			logger.Debug("failed to parse DHCP option",
				zap.String("wan", wanName),
				zap.String("raw_value", dhcpOptionValue))
		}
	}

	logger.Debug("retrieved WAN configuration",
		zap.String("wan", wanName),
		zap.String("type", config.Type),
		zap.String("ifname", config.Ifname),
		zap.String("addr", config.Addr))
	return config, nil
}

/*****************************************************************************
 * NAME: VerifyWanConfig
 *
 * DESCRIPTION:
 *     Verifies that the WAN configuration was applied correctly.
 *     Uses unified WanConfig structure to eliminate repeated protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected WAN configuration from protobuf conversion
 *
 * RETURNS:
 *     bool  - True if configuration matches, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyWanConfig(logger *logger.Logger, expectedConfig *WanConfig) (bool, error) {
	if expectedConfig == nil {
		return false, fmt.Errorf("expectedConfig is nil")
	}

	logger.Info("verifying WAN configuration", zap.String("name", expectedConfig.Name))

	// Get current WAN configuration
	actualConfig, err := GetWanConfig(logger, expectedConfig.Name)
	if err != nil {
		return false, err
	}

	// Verify basic parameters
	if actualConfig.Ifname != expectedConfig.Ifname {
		logger.Error("WAN ifname mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.String("expected", expectedConfig.Ifname),
			zap.String("actual", actualConfig.Ifname))
		return false, nil
	}

	// For PPPoE, we need to check cfgmtu instead of mtu
	// because PPPoE has both mtu (system internal) and cfgmtu (user configured)
	if actualConfig.Type == "pppoe" {
		// For PPPoE, we already extracted cfgmtu as Mtu in parseWanConfig
		// So the comparison should work correctly
		if actualConfig.Mtu != expectedConfig.Mtu {
			logger.Error("WAN MTU mismatch (PPPoE cfgmtu)",
				zap.String("wan", expectedConfig.Name),
				zap.Int("expected", expectedConfig.Mtu),
				zap.Int("actual", actualConfig.Mtu))
			return false, nil
		}
	} else {
		// For static IP and DHCP, use regular mtu
		if actualConfig.Mtu != expectedConfig.Mtu {
			logger.Error("WAN MTU mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.Int("expected", expectedConfig.Mtu),
				zap.Int("actual", actualConfig.Mtu))
			return false, nil
		}
	}

	// Verify WAN type specific parameters
	switch expectedConfig.Type {
	case "proxy":
		// Verify static IP configuration
		if expectedConfig.Addr != "" && expectedConfig.Addr != actualConfig.Addr {
			logger.Error("WAN IP address mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.Addr),
				zap.String("actual", actualConfig.Addr))
			return false, nil
		}

		if expectedConfig.Gateway != "" && expectedConfig.Gateway != actualConfig.Gateway {
			logger.Error("WAN gateway mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.Gateway),
				zap.String("actual", actualConfig.Gateway))
			return false, nil
		}

		if expectedConfig.Dns != "" && expectedConfig.Dns != actualConfig.Dns {
			logger.Error("WAN DNS mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.Dns),
				zap.String("actual", actualConfig.Dns))
			return false, nil
		}

	case "dhcpwan":
		// Verify DHCP configuration
		if actualConfig.Type != "dhcpwan" {
			logger.Error("WAN type mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", "dhcpwan"),
				zap.String("actual", actualConfig.Type))
			return false, nil
		}

		// TODO: DHCP options verification temporarily disabled
		// Will be re-enabled after resolving DHCP option parsing issues
		logger.Debug("DHCP options verification temporarily disabled",
			zap.String("wan", expectedConfig.Name))

	case "pppoe":
		// Verify PPPoE configuration
		if actualConfig.Type != "pppoe" {
			logger.Error("WAN type mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", "pppoe"),
				zap.String("actual", actualConfig.Type))
			return false, nil
		}

		if expectedConfig.Username != actualConfig.Username {
			logger.Error("WAN PPPoE username mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.Username),
				zap.String("actual", actualConfig.Username))
			return false, nil
		}

		if expectedConfig.Password != actualConfig.Password {
			logger.Error("WAN PPPoE password mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.Password),
				zap.String("actual", actualConfig.Password))
			return false, nil
		}

		if expectedConfig.AcName != "" && expectedConfig.AcName != actualConfig.AcName {
			logger.Error("WAN PPPoE AC name mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.AcName),
				zap.String("actual", actualConfig.AcName))
			return false, nil
		}

		if expectedConfig.SvcName != "" && expectedConfig.SvcName != actualConfig.SvcName {
			logger.Error("WAN PPPoE service name mismatch",
				zap.String("wan", expectedConfig.Name),
				zap.String("expected", expectedConfig.SvcName),
				zap.String("actual", actualConfig.SvcName))
			return false, nil
		}
	}

	// Verify heartbeat configuration if specified
	if expectedConfig.PingIp != "" && expectedConfig.PingIp != actualConfig.PingIp {
		logger.Error("WAN ping IP mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.String("expected", expectedConfig.PingIp),
			zap.String("actual", actualConfig.PingIp))
		return false, nil
	}

	if expectedConfig.PingIp2 != "" && expectedConfig.PingIp2 != actualConfig.PingIp2 {
		logger.Error("WAN ping IP2 mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.String("expected", expectedConfig.PingIp2),
			zap.String("actual", actualConfig.PingIp2))
		return false, nil
	}

	if expectedConfig.MaxDelay != 0 && expectedConfig.MaxDelay != actualConfig.MaxDelay {
		logger.Error("WAN max delay mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.Int("expected", expectedConfig.MaxDelay),
			zap.Int("actual", actualConfig.MaxDelay))
		return false, nil
	}

	// Verify common configuration if specified
	if expectedConfig.DnsPxy != 0 && expectedConfig.DnsPxy != actualConfig.DnsPxy {
		logger.Error("WAN DNS proxy mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.Int("expected", expectedConfig.DnsPxy),
			zap.Int("actual", actualConfig.DnsPxy))
		return false, nil
	}

	if expectedConfig.PingDisable != 0 && expectedConfig.PingDisable != actualConfig.PingDisable {
		logger.Error("WAN ping disable mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.Int("expected", expectedConfig.PingDisable),
			zap.Int("actual", actualConfig.PingDisable))
		return false, nil
	}

	if expectedConfig.CloneMac != "" && strings.ToLower(expectedConfig.CloneMac) != strings.ToLower(actualConfig.CloneMac) {
		logger.Error("WAN clone MAC mismatch",
			zap.String("wan", expectedConfig.Name),
			zap.String("expected", expectedConfig.CloneMac),
			zap.String("actual", actualConfig.CloneMac))
		return false, nil
	}

	logger.Info("WAN configuration verified successfully",
		zap.String("name", expectedConfig.Name))
	return true, nil
}

/*****************************************************************************
 * NAME: CompareWanConfig
 *
 * DESCRIPTION:
 *     Compares the requested configuration with the local configuration.
 *     Uses unified WanConfig structure to eliminate repeated protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected WAN configuration from protobuf conversion
 *     localConfig    - Local WAN configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareWanConfig(logger *logger.Logger, expectedConfig *WanConfig, localConfig *WanConfig) bool {
	if localConfig == nil || expectedConfig == nil {
		return false
	}

	// Compare basic parameters
	if expectedConfig.Ifname != localConfig.Ifname {
		return false
	}

	if expectedConfig.Mtu != localConfig.Mtu {
		return false
	}

	// Compare WAN type specific parameters
	switch expectedConfig.Type {
	case "proxy":
		// Check if the local config is a proxy type
		if localConfig.Type != "proxy" {
			return false
		}

		// Compare static IP configuration
		if expectedConfig.Addr != "" && expectedConfig.Addr != localConfig.Addr {
			return false
		}

		if expectedConfig.Gateway != "" && expectedConfig.Gateway != localConfig.Gateway {
			return false
		}

		if expectedConfig.Dns != "" && expectedConfig.Dns != localConfig.Dns {
			return false
		}

	case "dhcpwan":
		// Compare DHCP configuration
		if localConfig.Type != "dhcpwan" {
			return false
		}

		// Compare DHCP options
		if expectedOption, exists := expectedConfig.DhcpOptions["12"]; exists {
			if localOption, localExists := localConfig.DhcpOptions["12"]; !localExists || expectedOption != localOption {
				return false
			}
		}

		if expectedOption, exists := expectedConfig.DhcpOptions["61"]; exists {
			if localOption, localExists := localConfig.DhcpOptions["61"]; !localExists || expectedOption != localOption {
				return false
			}
		}

		if expectedOption, exists := expectedConfig.DhcpOptions["60"]; exists {
			if localOption, localExists := localConfig.DhcpOptions["60"]; !localExists || expectedOption != localOption {
				return false
			}
		}

	case "pppoe":
		// Compare PPPoE configuration
		if localConfig.Type != "pppoe" {
			return false
		}

		if expectedConfig.Username != localConfig.Username {
			return false
		}

		if expectedConfig.Password != localConfig.Password {
			return false
		}

		if expectedConfig.AcName != "" && expectedConfig.AcName != localConfig.AcName {
			return false
		}

		if expectedConfig.SvcName != "" && expectedConfig.SvcName != localConfig.SvcName {
			return false
		}
	}

	// Compare heartbeat configuration if specified
	if expectedConfig.PingIp != "" && expectedConfig.PingIp != localConfig.PingIp {
		return false
	}

	if expectedConfig.PingIp2 != "" && expectedConfig.PingIp2 != localConfig.PingIp2 {
		return false
	}

	if expectedConfig.MaxDelay != 0 && expectedConfig.MaxDelay != localConfig.MaxDelay {
		return false
	}

	// Compare common configuration if specified
	if expectedConfig.DnsPxy != 0 && expectedConfig.DnsPxy != localConfig.DnsPxy {
		return false
	}

	if expectedConfig.PingDisable != 0 && expectedConfig.PingDisable != localConfig.PingDisable {
		return false
	}

	if expectedConfig.CloneMac != "" && expectedConfig.CloneMac != localConfig.CloneMac {
		return false
	}

	return true
}
