/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_processor_test.go
 *
 * DESCRIPTION :    Unit tests for user processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	pb "agent/internal/pb"
	"context"
	"errors"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test GetTaskType
func TestUserProcessor_GetTaskType(t *testing.T) {
	log := setupUserTestLogger()
	processor := NewUserProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_USER, taskType)
}

// Test ProcessTask with nil payload
func TestUserProcessor_ProcessTask_NilPayload(t *testing.T) {
	log := setupUserTestLogger()
	processor := NewUserProcessor(log)

	// Create a task with nil payload
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload:    nil,
	}

	// Call the function
	_, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "user task data is nil")
}

// Test ProcessTask with unsupported action
func TestUserProcessor_ProcessTask_UnsupportedAction(t *testing.T) {
	log := setupUserTestLogger()
	processor := NewUserProcessor(log)

	// Create a task with an unsupported action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER,
		TaskAction: 99, // Invalid action
		Payload: &pb.DeviceTask_UserTask{
			UserTask: &pb.UserTask{
				Name:   "user1",
				PoolId: 1,
			},
		},
	}

	// Call the function
	_, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported task action")
}

// Test ProcessTask with missing required fields
func TestUserProcessor_ProcessTask_MissingRequiredFields(t *testing.T) {
	log := setupUserTestLogger()
	processor := NewUserProcessor(log)

	// Test cases for missing required fields
	testCases := []struct {
		name     string
		userTask *pb.UserTask
		errorMsg string
	}{
		{
			name: "Missing Name",
			userTask: &pb.UserTask{
				Name:   "", // Missing name
				PoolId: 1,
				Password: "password1",
				Start: &pb.Date{
					Year:  2025,
					Month: 1,
					Day:   1,
				},
				Expire: &pb.Date{
					Year:  2026,
					Month: 1,
					Day:   1,
				},
			},
			errorMsg: "User name is required",
		},
		{
			name: "Missing PoolId",
			userTask: &pb.UserTask{
				Name:   "user1",
				PoolId: 0, // Invalid pool ID
				Password: "password1",
				Start: &pb.Date{
					Year:  2025,
					Month: 1,
					Day:   1,
				},
				Expire: &pb.Date{
					Year:  2026,
					Month: 1,
					Day:   1,
				},
			},
			errorMsg: "User pool ID must be greater than 0",
		},
		{
			name: "Missing Password",
			userTask: &pb.UserTask{
				Name:   "user1",
				PoolId: 1,
				Password: "", // Missing password
				Start: &pb.Date{
					Year:  2025,
					Month: 1,
					Day:   1,
				},
				Expire: &pb.Date{
					Year:  2026,
					Month: 1,
					Day:   1,
				},
			},
			errorMsg: "User password is required",
		},
		{
			name: "Missing Start Date",
			userTask: &pb.UserTask{
				Name:   "user1",
				PoolId: 1,
				Password: "password1",
				Start: nil, // Missing start date
				Expire: &pb.Date{
					Year:  2026,
					Month: 1,
					Day:   1,
				},
			},
			errorMsg: "User start date is required",
		},
		{
			name: "Missing Expire Date",
			userTask: &pb.UserTask{
				Name:   "user1",
				PoolId: 1,
				Password: "password1",
				Start: &pb.Date{
					Year:  2025,
					Month: 1,
					Day:   1,
				},
				Expire: nil, // Missing expire date
			},
			errorMsg: "User expire date is required",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a task with missing required fields
			task := &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_USER,
				TaskAction: pb.TaskAction_NEW_CONFIG,
				Payload: &pb.DeviceTask_UserTask{
					UserTask: tc.userTask,
				},
			}

			// Call the function
			_, err := processor.ProcessTask(context.Background(), task)

			// Verify results
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.errorMsg)
		})
	}
}

// Test ProcessTask with NEW_CONFIG action
func TestUserProcessor_ProcessTask_NewConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"1 default user2 password2 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user2"}).Return(
		"name=user2\n"+
		"poolid=1\n"+
		"passwd=password2\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Mock the add command
	mockExecuteCommand.On("Execute", 10, "floweye", mock.Anything).Return(
		"User added successfully",
		nil,
	)

	// Mock the enable command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "config", "accten=user1"}).Return(
		"User enabled successfully",
		nil,
	)

	// Mock the get command for verification
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user1"}).Return(
		"name=user1\n"+
		"poolid=1\n"+
		"passwd=password1\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Create a user task
	userTask := &pb.UserTask{
		Name:     "user1",
		PoolId:   1,
		Password: "password1",
		Start: &pb.Date{
			Year:  2025,
			Month: 1,
			Day:   1,
		},
		Expire: &pb.Date{
			Year:  2026,
			Month: 1,
			Day:   1,
		},
		Enable: true,
	}

	// Create a task with NEW_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_UserTask{
			UserTask: userTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User configuration created successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with EDIT_CONFIG action
func TestUserProcessor_ProcessTask_EditConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"1 default user1 password1 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user1"}).Return(
		"name=user1\n"+
		"poolid=1\n"+
		"passwd=password1\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Mock the set command
	mockExecuteCommand.On("Execute", 10, "floweye", mock.Anything).Return(
		"User updated successfully",
		nil,
	)

	// Mock the disable command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "config", "acctdis=user1"}).Return(
		"User disabled successfully",
		nil,
	)

	// Create a user task with updated values
	userTask := &pb.UserTask{
		Name:     "user1",
		PoolId:   1,
		Password: "password2", // Updated password
		Start: &pb.Date{
			Year:  2025,
			Month: 1,
			Day:   1,
		},
		Expire: &pb.Date{
			Year:  2026,
			Month: 1,
			Day:   1,
		},
		Enable: false, // Disable user
	}

	// Create a task with EDIT_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		Payload: &pb.DeviceTask_UserTask{
			UserTask: userTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User configuration updated successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with DELETE_CONFIG action
func TestUserProcessor_ProcessTask_DeleteConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"1 default user1 password1 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user1"}).Return(
		"name=user1\n"+
		"poolid=1\n"+
		"passwd=password1\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Mock the remove command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "remove", "name=user1"}).Return(
		"User removed successfully",
		nil,
	)

	// Mock refreshLocalConfigs after deletion
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"",
		nil,
	)

	// Create a user task
	userTask := &pb.UserTask{
		Name: "user1",
	}

	// Create a task with DELETE_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_UserTask{
			UserTask: userTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User deleted successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with DELETE_CONFIG action for non-existent user
func TestUserProcessor_ProcessTask_DeleteConfig_NonExistent(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"1 default user2 password2 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user2"}).Return(
		"name=user2\n"+
		"poolid=1\n"+
		"passwd=password2\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Create a user task for a non-existent user
	userTask := &pb.UserTask{
		Name: "user1", // Non-existent name
	}

	// Create a task with DELETE_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_UserTask{
			UserTask: userTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User not found, nothing to delete")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test StartFullSync
func TestUserProcessor_StartFullSync(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"1 default user1 password1 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL\n"+
		"1 default user2 password2 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user1"}).Return(
		"name=user1\n"+
		"poolid=1\n"+
		"passwd=password1\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user2"}).Return(
		"name=user2\n"+
		"poolid=1\n"+
		"passwd=password2\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Call the function
	err := processor.StartFullSync()

	// Verify results
	assert.NoError(t, err)
	assert.True(t, processor.fullSyncInProgress)
	assert.Equal(t, 2, len(processor.localConfigs))

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test StartFullSync with error
func TestUserProcessor_StartFullSync_Error(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs with error
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"",
		errors.New("command failed"),
	)

	// Call the function
	err := processor.StartFullSync()

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to refresh local configurations")
	assert.False(t, processor.fullSyncInProgress)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test EndFullSync
func TestUserProcessor_EndFullSync(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Set fullSyncInProgress to true
	processor.fullSyncInProgress = true

	// Add some local configs
	processor.localConfigs = map[string]*UserConfig{
		"user1": {
			Name:   "user1",
			PoolID: 1,
		},
		"user2": {
			Name:   "user2",
			PoolID: 1,
		},
	}

	// Mock the remove command for each user
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "remove", "name=user1"}).Return(
		"User removed successfully",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "remove", "name=user2"}).Return(
		"User removed successfully",
		nil,
	)

	// Call the function
	processor.EndFullSync()

	// Verify results
	assert.False(t, processor.fullSyncInProgress)
	assert.Equal(t, 0, len(processor.localConfigs))

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test refreshLocalConfigs
func TestUserProcessor_refreshLocalConfigs(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"1 default user1 password1 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL\n"+
		"1 default user2 password2 0.0.0.0 00-00-00-00-00-00 2025-01-01 2026-01-01 0 0 0 0 0 0 0 0 NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user1"}).Return(
		"name=user1\n"+
		"poolid=1\n"+
		"passwd=password1\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "get", "name=user2"}).Return(
		"name=user2\n"+
		"poolid=1\n"+
		"passwd=password2\n"+
		"start=2025-01-01\n"+
		"expire=2026-01-01\n"+
		"enabled=0\n"+
		"maxonline=0\n"+
		"bindip=0.0.0.0\n"+
		"bindmac=00-00-00-00-00-00\n"+
		"outvlan=0\n"+
		"other=NULL",
		nil,
	)

	// Call the function
	err := processor.refreshLocalConfigs()

	// Verify results
	assert.NoError(t, err)
	assert.Equal(t, 2, len(processor.localConfigs))

	// Verify user1
	user1, exists := processor.localConfigs["user1"]
	assert.True(t, exists)
	assert.Equal(t, "user1", user1.Name)
	assert.Equal(t, int32(1), user1.PoolID)
	assert.Equal(t, "password1", user1.Password)

	// Verify user2
	user2, exists := processor.localConfigs["user2"]
	assert.True(t, exists)
	assert.Equal(t, "user2", user2.Name)
	assert.Equal(t, int32(1), user2.PoolID)
	assert.Equal(t, "password2", user2.Password)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test refreshLocalConfigs with error
func TestUserProcessor_refreshLocalConfigs_Error(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserProcessor(log)

	// Mock refreshLocalConfigs with error
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeacct", "list"}).Return(
		"",
		errors.New("command failed"),
	)

	// Call the function
	err := processor.refreshLocalConfigs()

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to list users")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}
