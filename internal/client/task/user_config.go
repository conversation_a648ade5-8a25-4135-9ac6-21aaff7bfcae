/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_config.go
 *
 * DESCRIPTION :    User configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: UserConfig
 *
 * DESCRIPTION:
 *     Represents a user configuration.
 *     Contains all the configuration parameters for a user.
 *
 * FIELDS:
 *     Name          - User account name
 *     PoolID        - User group ID
 *     Password      - User password
 *     StartDate     - Account start date (YYYY-MM-DD)
 *     ExpireDate    - Account expiration date (YYYY-MM-DD)
 *     Enabled       - Whether the account is enabled
 *     MaxOnline     - Maximum number of concurrent logins, 0 means no limit
 *     BindIP        - Bound IP address, 0.0.0.0 means not bound
 *     BindMAC       - Bound MAC addresses, comma-separated
 *     OutVLAN       - Bound VLAN ID, 0 means not bound
 *     CName         - User's real name
 *     Card          - User's ID card number
 *     Phone         - User's phone number
 *     Other         - Other user information
 *****************************************************************************/
type UserConfig struct {
	Name       string
	PoolID     int32
	Password   string
	StartDate  string
	ExpireDate string
	Enabled    bool
	MaxOnline  int32
	BindIP     string
	BindMAC    string
	OutVLAN    int32
	CName      string
	Card       string
	Phone      string
	Other      string
}

/*****************************************************************************
 * NAME: GetLocalUserConfigs
 *
 * DESCRIPTION:
 *     Retrieves all user configurations from the system.
 *     First executes floweye command to get all user names,
 *     then executes floweye command to get detailed configuration for each user.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*UserConfig - Map of user configurations indexed by name
 *     error - Error if retrieving configurations fails
 *****************************************************************************/
func GetLocalUserConfigs(logger *logger.Logger) (map[string]*UserConfig, error) {
	logger.Debug("Getting local user configurations")

	// Initialize configurations map
	configs := make(map[string]*UserConfig)

	// Execute floweye command to get all users
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "pppoeacct", "list")
	if err != nil {
		logger.Error("Failed to execute floweye command to list users",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Parse detailed configuration directly from list output
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse user information from floweye pppoeacct list output
		fields := strings.Fields(line)
		if len(fields) < 20 {
			continue
		}

		// Extract fields according to documented format
		userName := fields[2]
		poolIDStr := fields[0]
		password := fields[3]
		startDate := fields[6]
		expireDate := fields[7]
		enabledStr := fields[10]
		maxOnlineStr := fields[14]
		bindIP := fields[15]
		outVLANStr := fields[18]
		userInfo := "NULL"
		if len(fields) > 19 {
			userInfo = fields[19]
		}

		// Parse pool ID
		poolID, err := strconv.ParseInt(poolIDStr, 10, 32)
		if err != nil {
			logger.Warn("Failed to parse pool ID",
				zap.String("name", userName),
				zap.String("poolid", poolIDStr),
				zap.Error(err))
			continue
		}

		// Parse enabled status (0 = enabled, 1 = disabled in floweye pppoeacct list)
		enabled := true
		if enabledVal, err := strconv.ParseInt(enabledStr, 10, 32); err == nil {
			enabled = enabledVal == 0
		}

		// Parse max online
		maxOnline := int32(0)
		if maxOnlineVal, err := strconv.ParseInt(maxOnlineStr, 10, 32); err == nil {
			maxOnline = int32(maxOnlineVal)
		}

		// Parse out VLAN
		outVLAN := int32(0)
		if outVLANVal, err := strconv.ParseInt(outVLANStr, 10, 32); err == nil {
			outVLAN = int32(outVLANVal)
		}

		// Parse bind MAC from field 12
		bindMAC := "00-00-00-00-00-00"
		if fields[12] != "NULL" {
			bindMAC = fields[12]
		}

		// Parse user identity information from userInfo field
		cname := ""
		card := ""
		phone := ""
		other := ""
		if userInfo != "NULL" && userInfo != "" {
			parts := strings.Split(userInfo, ";")
			if len(parts) >= 1 {
				cname = parts[0]
			}
			if len(parts) >= 2 {
				card = parts[1]
			}
			if len(parts) >= 3 {
				phone = parts[2]
			}
			if len(parts) >= 4 {
				other = parts[3]
			}
		}

		// Create user config
		config := &UserConfig{
			Name:       userName,
			PoolID:     int32(poolID),
			Password:   password,
			StartDate:  startDate,
			ExpireDate: expireDate,
			Enabled:    enabled,
			MaxOnline:  maxOnline,
			BindIP:     bindIP,
			BindMAC:    bindMAC,
			OutVLAN:    outVLAN,
			CName:      cname,
			Card:       card,
			Phone:      phone,
			Other:      other,
		}

		// Add to configurations map
		configs[userName] = config
	}

	logger.Info("Retrieved local user configurations",
		zap.Int("count", len(configs)))

	return configs, nil
}

/*****************************************************************************
 * NAME: GetUserConfig
 *
 * DESCRIPTION:
 *     Retrieves a specific user configuration from the system.
 *     Executes floweye command to get the user and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the user to retrieve
 *
 * RETURNS:
 *     *UserConfig - User configuration
 *     error - Error if retrieving configuration fails
 *****************************************************************************/
func GetUserConfig(logger *logger.Logger, name string) (*UserConfig, error) {
	logger.Debug("Getting user configuration", zap.String("name", name))

	// Execute floweye command to get user
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "pppoeacct", "get", "name="+name)
	if err != nil {
		logger.Error("Failed to execute floweye command to get user",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Parse output
	lines := strings.Split(output, "\n")
	configMap := make(map[string]string)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		configMap[key] = value
	}

	// Check if user exists
	userName, ok := configMap["name"]
	if !ok {
		logger.Error("User name not found in configuration")
		return nil, fmt.Errorf("user not found")
	}

	// Parse pool ID
	poolID, err := strconv.ParseInt(configMap["poolid"], 10, 32)
	if err != nil {
		logger.Error("Failed to parse user pool ID",
			zap.String("poolid", configMap["poolid"]),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse user pool ID: %w", err)
	}

	// Note: floweye pppoeacct get command does not include enabled status
	// The enabled status can only be obtained from floweye pppoeacct list command
	// For now, we assume the user is enabled by default when retrieved individually
	enabled := true

	// Parse max online
	maxOnline := int32(0)
	if maxOnlineStr, ok := configMap["maxonline"]; ok {
		maxOnlineVal, err := strconv.ParseInt(maxOnlineStr, 10, 32)
		if err == nil {
			maxOnline = int32(maxOnlineVal)
		}
	}

	// Parse out VLAN
	outVLAN := int32(0)
	if outVLANStr, ok := configMap["outvlan"]; ok {
		outVLANVal, err := strconv.ParseInt(outVLANStr, 10, 32)
		if err == nil {
			outVLAN = int32(outVLANVal)
		}
	}

	// Parse user identity information from individual fields
	cname := ""
	card := ""
	phone := ""
	other := ""

	// Get individual identity fields
	if cnameVal, ok := configMap["cname"]; ok {
		cname = cnameVal
		logger.Debug("Parsed cname field", zap.String("cname", cname))
	}
	if cardVal, ok := configMap["card"]; ok {
		card = cardVal
		logger.Debug("Parsed card field", zap.String("card", card))
	}
	if phoneVal, ok := configMap["phone"]; ok {
		phone = phoneVal
		logger.Debug("Parsed phone field", zap.String("phone", phone))
	}
	if otherVal, ok := configMap["other"]; ok {
		other = otherVal
		logger.Debug("Parsed other field", zap.String("other", other))
	}

	// Log all parsed identity values before creating config
	logger.Info("Creating user config with identity values",
		zap.String("name", userName),
		zap.String("cname", cname),
		zap.String("card", card),
		zap.String("phone", phone),
		zap.String("other", other))

	// Create user config
	config := &UserConfig{
		Name:       userName,
		PoolID:     int32(poolID),
		Password:   configMap["passwd"],
		StartDate:  configMap["start"],
		ExpireDate: configMap["expire"],
		Enabled:    enabled,
		MaxOnline:  maxOnline,
		BindIP:     configMap["bindip"],
		BindMAC:    configMap["bindmac"],
		OutVLAN:    outVLAN,
		CName:      cname,
		Card:       card,
		Phone:      phone,
		Other:      other,
	}

	logger.Debug("Retrieved user configuration",
		zap.String("name", name),
		zap.Int32("poolID", config.PoolID))

	return config, nil
}

/*****************************************************************************
 * NAME: ConvertUserTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf UserTask message to unified UserConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing UserConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     userTask - Protobuf user task message to convert
 *
 * RETURNS:
 *     *UserConfig - Converted user configuration structure
 *     error       - Error if conversion fails
 *****************************************************************************/
func ConvertUserTaskToConfig(userTask *pb.UserTask) (*UserConfig, error) {
	if userTask == nil {
		return nil, fmt.Errorf("userTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &UserConfig{
		Name:     userTask.GetName(),
		PoolID:   userTask.GetPoolId(),
		Password: userTask.GetPassword(),
		Enabled:  userTask.GetEnable(),
		// Default values for optional fields
		MaxOnline: 0,
		BindIP:    "0.0.0.0",
		BindMAC:   "00-00-00-00-00-00",
		OutVLAN:   0,
		CName:     "",
		Card:      "",
		Phone:     "",
		Other:     "",
	}

	// Convert date fields
	if userTask.GetStart() != nil {
		config.StartDate = formatDate(userTask.GetStart())
	}
	if userTask.GetExpire() != nil {
		config.ExpireDate = formatDate(userTask.GetExpire())
	}

	// Extract user restriction configuration if present
	if userTask.GetRestriction() != nil {
		restriction := userTask.GetRestriction()

		// Max online users
		config.MaxOnline = restriction.GetMaxOnline()

		// Bind IP address
		if restriction.GetBindIp() != nil {
			config.BindIP = utils.GetIpString(restriction.GetBindIp())
		}

		// Bind MAC addresses
		if len(restriction.GetBindMac()) > 0 {
			config.BindMAC = strings.Join(restriction.GetBindMac(), ",")
		}

		// Out VLAN
		config.OutVLAN = restriction.GetOutVlan()
	}

	// Extract user identity configuration if present
	if userTask.GetIdentity() != nil {
		identity := userTask.GetIdentity()

		config.CName = identity.GetCname()
		config.Card = identity.GetCard()
		config.Phone = identity.GetPhone()
		config.Other = identity.GetOther()
	}

	return config, nil
}

/*****************************************************************************
 * NAME: VerifyUserConfig
 *
 * DESCRIPTION:
 *     Verifies that a user configuration was applied correctly.
 *     Gets the current configuration from the system and compares it with the expected values.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected user configuration
 *
 * RETURNS:
 *     bool  - True if verification succeeds, false otherwise
 *     error - Error if verification process fails
 *****************************************************************************/
func VerifyUserConfig(logger *logger.Logger, expectedConfig *UserConfig) (bool, error) {
	logger.Debug("Verifying user configuration",
		zap.String("name", expectedConfig.Name))

	// Get user configuration
	config, err := GetUserConfig(logger, expectedConfig.Name)
	if err != nil {
		logger.Error("Failed to get user configuration",
			zap.String("name", expectedConfig.Name),
			zap.Error(err))
		return false, err
	}

	// Use CompareUserConfig to verify the configuration
	if !CompareUserConfig(logger, expectedConfig, config) {
		logger.Error("User configuration verification failed",
			zap.String("name", expectedConfig.Name))
		return false, nil
	}

	// Verification passed
	logger.Info("User configuration verified successfully",
		zap.String("name", expectedConfig.Name))

	return true, nil
}

/*****************************************************************************
 * NAME: CompareUserConfig
 *
 * DESCRIPTION:
 *     Compares the requested configuration with the local configuration.
 *     Determines if the requested configuration matches the existing configuration.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging operations
 *     expectedConfig - Expected user configuration
 *     localConfig    - Local user configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareUserConfig(logger *logger.Logger, expectedConfig *UserConfig, localConfig *UserConfig) bool {
	if localConfig == nil {
		return false
	}

	logger.Debug("Comparing user configuration",
		zap.String("name", expectedConfig.Name))

	// Compare basic fields
	if expectedConfig.Name != localConfig.Name ||
		expectedConfig.PoolID != localConfig.PoolID {
		return false
	}

	// Compare password if provided
	if expectedConfig.Password != "" && expectedConfig.Password != localConfig.Password {
		return false
	}

	// Compare dates
	if expectedConfig.StartDate != "" && expectedConfig.StartDate != localConfig.StartDate {
		return false
	}

	if expectedConfig.ExpireDate != "" && expectedConfig.ExpireDate != localConfig.ExpireDate {
		return false
	}

	// Compare user restriction fields with default value handling
	if expectedConfig.MaxOnline != localConfig.MaxOnline {
		logger.Debug("MaxOnline mismatch",
			zap.Int32("expected", expectedConfig.MaxOnline),
			zap.Int32("actual", localConfig.MaxOnline))
		return false
	}

	// Handle BindIP comparison with default value normalization
	expectedBindIP := normalizeBindIP(expectedConfig.BindIP)
	localBindIP := normalizeBindIP(localConfig.BindIP)
	if expectedBindIP != localBindIP {
		logger.Debug("BindIP mismatch",
			zap.String("expected", expectedBindIP),
			zap.String("actual", localBindIP))
		return false
	}

	// Handle BindMAC comparison with default value normalization
	expectedBindMAC := normalizeBindMAC(expectedConfig.BindMAC)
	localBindMAC := normalizeBindMAC(localConfig.BindMAC)
	if expectedBindMAC != localBindMAC {
		logger.Debug("BindMAC mismatch",
			zap.String("expected", expectedBindMAC),
			zap.String("actual", localBindMAC))
		return false
	}

	if expectedConfig.OutVLAN != localConfig.OutVLAN {
		logger.Debug("OutVLAN mismatch",
			zap.Int32("expected", expectedConfig.OutVLAN),
			zap.Int32("actual", localConfig.OutVLAN))
		return false
	}

	// Compare user identity fields with default value handling
	if expectedConfig.CName != localConfig.CName {
		logger.Debug("CName mismatch",
			zap.String("expected", expectedConfig.CName),
			zap.String("actual", localConfig.CName))
		return false
	}

	if expectedConfig.Card != localConfig.Card {
		logger.Debug("Card mismatch",
			zap.String("expected", expectedConfig.Card),
			zap.String("actual", localConfig.Card))
		return false
	}

	if expectedConfig.Phone != localConfig.Phone {
		logger.Debug("Phone mismatch",
			zap.String("expected", expectedConfig.Phone),
			zap.String("actual", localConfig.Phone))
		return false
	}

	if expectedConfig.Other != localConfig.Other {
		logger.Debug("Other mismatch",
			zap.String("expected", expectedConfig.Other),
			zap.String("actual", localConfig.Other))
		return false
	}

	// Configurations match
	logger.Debug("User configurations match",
		zap.String("name", expectedConfig.Name))

	return true

}

/*****************************************************************************
 * NAME: EnableUser
 *
 * DESCRIPTION:
 *     Enables a user account.
 *     Executes floweye command to enable the user.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the user to enable
 *
 * RETURNS:
 *     error - Error if enabling user fails
 *****************************************************************************/
func EnableUser(logger *logger.Logger, name string) error {
	logger.Info("Enabling user", zap.String("name", name))

	// Execute floweye command to enable user
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "pppoeacct", "config", "accten="+name)
	if err != nil {
		logger.Error("Failed to execute floweye command to enable user",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to enable user: %w", err)
	}

	logger.Debug("User enabled successfully",
		zap.String("name", name),
		zap.String("output", output))

	return nil
}

/*****************************************************************************
 * NAME: DisableUser
 *
 * DESCRIPTION:
 *     Disables a user account.
 *     Executes floweye command to disable the user.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the user to disable
 *
 * RETURNS:
 *     error - Error if disabling user fails
 *****************************************************************************/
func DisableUser(logger *logger.Logger, name string) error {
	logger.Info("Disabling user", zap.String("name", name))

	// Execute floweye command to disable user
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "pppoeacct", "config", "acctdis="+name)
	if err != nil {
		logger.Error("Failed to execute floweye command to disable user",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to disable user: %w", err)
	}

	logger.Debug("User disabled successfully",
		zap.String("name", name),
		zap.String("output", output))

	return nil
}

/*****************************************************************************
 * NAME: formatDate
 *
 * DESCRIPTION:
 *     Formats a Date message into a string in the format YYYY-MM-DD.
 *
 * PARAMETERS:
 *     date - Date message to format
 *
 * RETURNS:
 *     string - Formatted date string
 *****************************************************************************/
func formatDate(date *pb.Date) string {
	if date == nil {
		return ""
	}

	return fmt.Sprintf("%04d-%02d-%02d", date.GetYear(), date.GetMonth(), date.GetDay())
}

/*****************************************************************************
 * NAME: parseDate
 *
 * DESCRIPTION:
 *     Parses a date string in the format YYYY-MM-DD into a Date message.
 *
 * PARAMETERS:
 *     dateStr - Date string to parse
 *
 * RETURNS:
 *     *pb.Date - Parsed Date message
 *     error    - Error if parsing fails
 *****************************************************************************/
func parseDate(dateStr string) (*pb.Date, error) {
	// Parse date string
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// Create Date message
	date := &pb.Date{
		Year:  int32(t.Year()),
		Month: int32(t.Month()),
		Day:   int32(t.Day()),
	}

	return date, nil
}

/*****************************************************************************
 * NAME: normalizeBindIP
 *
 * DESCRIPTION:
 *     Normalizes bind IP address for comparison.
 *     Treats "0.0.0.0" and empty string as equivalent (no binding).
 *
 * PARAMETERS:
 *     bindIP - IP address string to normalize
 *
 * RETURNS:
 *     string - Normalized IP address
 *****************************************************************************/
func normalizeBindIP(bindIP string) string {
	if bindIP == "" || bindIP == "0.0.0.0" {
		return "0.0.0.0"
	}
	return bindIP
}

/*****************************************************************************
 * NAME: normalizeBindMAC
 *
 * DESCRIPTION:
 *     Normalizes bind MAC address for comparison.
 *     Treats "00-00-00-00-00-00", empty string, and comma-separated empty values as equivalent (no binding).
 *
 * PARAMETERS:
 *     bindMAC - MAC address string to normalize
 *
 * RETURNS:
 *     string - Normalized MAC address
 *****************************************************************************/
func normalizeBindMAC(bindMAC string) string {
	if bindMAC == "" || bindMAC == "00-00-00-00-00-00" || bindMAC == "00:00:00:00:00:00" {
		return ""
	}
	// Handle comma-separated MAC addresses
	macs := strings.Split(bindMAC, ",")
	var validMACs []string
	for _, mac := range macs {
		mac = strings.TrimSpace(mac)
		if mac != "" && mac != "00-00-00-00-00-00" && mac != "00:00:00:00:00:00" {
			validMACs = append(validMACs, mac)
		}
	}
	if len(validMACs) == 0 {
		return ""
	}
	return strings.Join(validMACs, ",")
}
