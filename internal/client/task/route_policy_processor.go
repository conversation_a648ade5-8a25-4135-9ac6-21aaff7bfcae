/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      route_policy_processor.go
 *
 * DESCRIPTION :    Route policy processor implementation
 *
 *                  重构说明：
 *                  - 排序逻辑已拆分到 route_policy_ordering.go
 *                  - LMP排序逻辑已拆分到 route_policy_lmp_ordering.go
 *                  - 删除了无用函数 getIpString 和旧版本 handleLPMSorting
 *                  - 保持了原有的 TaskProcessor 接口和公共方法签名
 *
 * AUTHOR :         wei
 *
 * HISTORY :        05/06/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: RoutePolicyProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_ROUTE_POLICY type tasks.
 *     Handles route policy configuration operations.
 *
 * FIELDS:
 *     logger                 - Logger for route policy processor operations
 *     zoneConfigs            - Cache of local route policy configurations (used for full sync redundant deletion)
 *     cookieToID             - Map of policy cookies to local IDs (used for full sync redundant deletion)
 *     cookieToZone           - Map of policy cookies to zones (used for full sync redundant deletion)
 *     zoneMaxIDs             - Maximum ID for each zone (used for full sync redundant deletion)
 *     workingZoneConfigs     - Working cache for operations (can be refreshed during full sync)
 *     workingCookieToID      - Working map of policy cookies to local IDs
 *     workingCookieToZone    - Working map of policy cookies to zones
 *     workingZoneMaxIDs      - Working maximum ID for each zone
 *     fullSyncInProgress     - Flag indicating if full sync is in progress
 *****************************************************************************/
type RoutePolicyProcessor struct {
	logger              *logger.Logger                                       // Logger for route policy processor operations
	zoneConfigs         map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig // Zone-based configuration cache (used for full sync redundant deletion)
	cookieToID          map[uint32]int                                       // Map of policy cookies to local IDs (used for full sync redundant deletion)
	cookieToZone        map[uint32]pb.RoutePolicyZone                        // Map of policy cookies to zones (used for full sync redundant deletion)
	zoneMaxIDs          map[pb.RoutePolicyZone]int                           // Maximum ID for each zone (used for full sync redundant deletion)
	workingZoneConfigs  map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig // Working cache for operations (can be refreshed during full sync)
	workingCookieToID   map[uint32]int                                       // Working map of policy cookies to local IDs
	workingCookieToZone map[uint32]pb.RoutePolicyZone                        // Working map of policy cookies to zones
	workingZoneMaxIDs   map[pb.RoutePolicyZone]int                           // Working maximum ID for each zone
	fullSyncInProgress  bool                                                 // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewRoutePolicyProcessor
 *
 * DESCRIPTION:
 *     Creates a new route policy processor instance.
 *     Initializes both local and working configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *RoutePolicyProcessor - Initialized route policy processor
 *****************************************************************************/
func NewRoutePolicyProcessor(log *logger.Logger) *RoutePolicyProcessor {
	processor := &RoutePolicyProcessor{
		logger:              log.WithModule("route-policy-processor"),
		zoneConfigs:         make(map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig),
		cookieToID:          make(map[uint32]int),
		cookieToZone:        make(map[uint32]pb.RoutePolicyZone),
		zoneMaxIDs:          make(map[pb.RoutePolicyZone]int),
		workingZoneConfigs:  make(map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig),
		workingCookieToID:   make(map[uint32]int),
		workingCookieToZone: make(map[uint32]pb.RoutePolicyZone),
		workingZoneMaxIDs:   make(map[pb.RoutePolicyZone]int),
		fullSyncInProgress:  false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *
 * RETURNS:
 *     pb.TaskType - TASK_ROUTE_POLICY
 *****************************************************************************/
func (p *RoutePolicyProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_ROUTE_POLICY
}

/*****************************************************************************
 * NAME: initializeZoneCaches
 *
 * DESCRIPTION:
 *     初始化zone相关的缓存结构。
 *     为每个zone创建独立的配置缓存。
 *****************************************************************************/
func (p *RoutePolicyProcessor) initializeZoneCaches() {
	// Initialize local zone configuration caches (for full sync redundant deletion)
	p.zoneConfigs[pb.RoutePolicyZone_CTRL_TIER_T1] = make(map[uint32]*RoutePolicyConfig)
	p.zoneConfigs[pb.RoutePolicyZone_CUST_TIER_T2] = make(map[uint32]*RoutePolicyConfig)
	p.zoneConfigs[pb.RoutePolicyZone_LPM_TIER_T3] = make(map[uint32]*RoutePolicyConfig)
	p.zoneConfigs[pb.RoutePolicyZone_DEF_WAN_TIER_T4] = make(map[uint32]*RoutePolicyConfig)

	// Initialize working zone configuration caches (for all operations)
	p.workingZoneConfigs[pb.RoutePolicyZone_CTRL_TIER_T1] = make(map[uint32]*RoutePolicyConfig)
	p.workingZoneConfigs[pb.RoutePolicyZone_CUST_TIER_T2] = make(map[uint32]*RoutePolicyConfig)
	p.workingZoneConfigs[pb.RoutePolicyZone_LPM_TIER_T3] = make(map[uint32]*RoutePolicyConfig)
	p.workingZoneConfigs[pb.RoutePolicyZone_DEF_WAN_TIER_T4] = make(map[uint32]*RoutePolicyConfig)

	// Initialize local zone max IDs based on priority ranges
	p.zoneMaxIDs[pb.RoutePolicyZone_CTRL_TIER_T1] = CTRL_TIER_T1_MIN_PRIORITY - 1
	p.zoneMaxIDs[pb.RoutePolicyZone_CUST_TIER_T2] = CUST_TIER_T2_MIN_PRIORITY - 1
	p.zoneMaxIDs[pb.RoutePolicyZone_LPM_TIER_T3] = LPM_TIER_T3_MIN_PRIORITY - 1
	p.zoneMaxIDs[pb.RoutePolicyZone_DEF_WAN_TIER_T4] = DEF_WAN_TIER_T4_MIN_PRIORITY - 1

	// Initialize working zone max IDs based on priority ranges
	p.workingZoneMaxIDs[pb.RoutePolicyZone_CTRL_TIER_T1] = CTRL_TIER_T1_MIN_PRIORITY - 1
	p.workingZoneMaxIDs[pb.RoutePolicyZone_CUST_TIER_T2] = CUST_TIER_T2_MIN_PRIORITY - 1
	p.workingZoneMaxIDs[pb.RoutePolicyZone_LPM_TIER_T3] = LPM_TIER_T3_MIN_PRIORITY - 1
	p.workingZoneMaxIDs[pb.RoutePolicyZone_DEF_WAN_TIER_T4] = DEF_WAN_TIER_T4_MIN_PRIORITY - 1
}

/*****************************************************************************
 * NAME: fetchRoutePolicyConfigs
 *
 * DESCRIPTION:
 *     Fetches route policy configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig - Zone-based configurations
 *     map[uint32]int                                       - Cookie to ID mapping
 *     map[uint32]pb.RoutePolicyZone                        - Cookie to zone mapping
 *     map[pb.RoutePolicyZone]int                           - Zone max IDs
 *     error                                                - Error if fetch fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) fetchRoutePolicyConfigs() (
	map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig,
	map[uint32]int,
	map[uint32]pb.RoutePolicyZone,
	map[pb.RoutePolicyZone]int,
	error) {

	// Initialize return structures
	zoneConfigs := make(map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig)
	cookieToID := make(map[uint32]int)
	cookieToZone := make(map[uint32]pb.RoutePolicyZone)
	zoneMaxIDs := make(map[pb.RoutePolicyZone]int)

	// Initialize zone caches
	zoneConfigs[pb.RoutePolicyZone_CTRL_TIER_T1] = make(map[uint32]*RoutePolicyConfig)
	zoneConfigs[pb.RoutePolicyZone_CUST_TIER_T2] = make(map[uint32]*RoutePolicyConfig)
	zoneConfigs[pb.RoutePolicyZone_LPM_TIER_T3] = make(map[uint32]*RoutePolicyConfig)
	zoneConfigs[pb.RoutePolicyZone_DEF_WAN_TIER_T4] = make(map[uint32]*RoutePolicyConfig)

	// Initialize zone max IDs based on priority ranges
	zoneMaxIDs[pb.RoutePolicyZone_CTRL_TIER_T1] = CTRL_TIER_T1_MIN_PRIORITY - 1
	zoneMaxIDs[pb.RoutePolicyZone_CUST_TIER_T2] = CUST_TIER_T2_MIN_PRIORITY - 1
	zoneMaxIDs[pb.RoutePolicyZone_LPM_TIER_T3] = LPM_TIER_T3_MIN_PRIORITY - 1
	zoneMaxIDs[pb.RoutePolicyZone_DEF_WAN_TIER_T4] = DEF_WAN_TIER_T4_MIN_PRIORITY - 1

	// Get list of route policies
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "route", "list", "json=1")
	if err != nil {
		p.logger.Error("failed to list route policies", zap.Error(err))
		return nil, nil, nil, nil, fmt.Errorf("failed to list route policies: %w", err)
	}

	// Parse route policies from list output
	configs, err := ParseRoutePolicyFromList(output)
	if err != nil {
		p.logger.Error("failed to parse route policies list", zap.Error(err))
		return nil, nil, nil, nil, fmt.Errorf("failed to parse route policies list: %w", err)
	}

	totalCount := 0
	// For each policy, get detailed configuration including cookie
	for _, config := range configs {
		// Get detailed configuration using ID
		detailedConfig, err := p.getRoutePolicyDetailedConfig(config.ID)
		if err != nil {
			p.logger.Error("failed to get detailed route policy configuration",
				zap.Int("policy_id", config.ID),
				zap.Error(err))
			continue
		}

		// Determine zone based on priority (ID)
		detailedConfig.Zone = GetZoneFromPriority(detailedConfig.ID)

		// Store in zone-specific cache
		zoneConfigs[detailedConfig.Zone][detailedConfig.Cookie] = detailedConfig

		// Store mappings for quick lookup
		cookieToID[detailedConfig.Cookie] = detailedConfig.ID
		cookieToZone[detailedConfig.Cookie] = detailedConfig.Zone

		// Update zone max ID (only if the ID actually belongs to this zone)
		minPriority, maxPriority := GetZonePriorityRange(detailedConfig.Zone)
		if detailedConfig.ID >= minPriority && detailedConfig.ID <= maxPriority {
			if detailedConfig.ID > zoneMaxIDs[detailedConfig.Zone] {
				zoneMaxIDs[detailedConfig.Zone] = detailedConfig.ID
			}
		}

		totalCount++
	}

	return zoneConfigs, cookieToID, cookieToZone, zoneMaxIDs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local route policy configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local route policy configurations")

	zoneConfigs, cookieToID, cookieToZone, zoneMaxIDs, err := p.fetchRoutePolicyConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.zoneConfigs = zoneConfigs
	p.cookieToID = cookieToID
	p.cookieToZone = cookieToZone
	p.zoneMaxIDs = zoneMaxIDs

	totalCount := 0
	for _, zoneMap := range p.zoneConfigs {
		totalCount += len(zoneMap)
	}

	p.logger.Debug("refreshed local route policy configurations",
		zap.Int("total_count", totalCount),
		zap.Int("ctrl_tier_t1", len(p.zoneConfigs[pb.RoutePolicyZone_CTRL_TIER_T1])),
		zap.Int("cust_tier_t2", len(p.zoneConfigs[pb.RoutePolicyZone_CUST_TIER_T2])),
		zap.Int("lpm_tier_t3", len(p.zoneConfigs[pb.RoutePolicyZone_LPM_TIER_T3])),
		zap.Int("def_wan_tier_t4", len(p.zoneConfigs[pb.RoutePolicyZone_DEF_WAN_TIER_T4])))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working route policy configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working route policy configurations")

	zoneConfigs, cookieToID, cookieToZone, zoneMaxIDs, err := p.fetchRoutePolicyConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingZoneConfigs = zoneConfigs
	p.workingCookieToID = cookieToID
	p.workingCookieToZone = cookieToZone
	p.workingZoneMaxIDs = zoneMaxIDs

	totalCount := 0
	for _, zoneMap := range p.workingZoneConfigs {
		totalCount += len(zoneMap)
	}

	p.logger.Debug("refreshed working route policy configurations",
		zap.Int("total_count", totalCount),
		zap.Int("ctrl_tier_t1", len(p.workingZoneConfigs[pb.RoutePolicyZone_CTRL_TIER_T1])),
		zap.Int("cust_tier_t2", len(p.workingZoneConfigs[pb.RoutePolicyZone_CUST_TIER_T2])),
		zap.Int("lmp_tier_t3", len(p.workingZoneConfigs[pb.RoutePolicyZone_LPM_TIER_T3])),
		zap.Int("def_wan_tier_t4", len(p.workingZoneConfigs[pb.RoutePolicyZone_DEF_WAN_TIER_T4])))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like ordering, enable/disable, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: getRoutePolicyDetailedConfig
 *
 * DESCRIPTION:
 *     Gets detailed configuration for a specific route policy.
 *     Uses floweye rtpolicy get command to fetch complete policy details.
 *
 * PARAMETERS:
 *     policyID - Policy ID to fetch details for
 *
 * RETURNS:
 *     *RoutePolicyConfig - Detailed route policy configuration
 *     error              - Error if operation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) getRoutePolicyDetailedConfig(policyID int) (*RoutePolicyConfig, error) {
	// Execute floweye command to get detailed policy configuration
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "rtpolicy", "get", "id="+strconv.Itoa(policyID))
	if err != nil {
		return nil, fmt.Errorf("failed to get detailed route policy config: %w", err)
	}

	config, err := ParseRoutePolicyFromGet(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse detailed route policy config: %w", err)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: getAllConfigs
 *
 * DESCRIPTION:
 *     获取所有zone的配置，按需合并。
 *     用于需要遍历所有策略的场景（使用local配置，用于全量同步）。
 *
 * RETURNS:
 *     map[uint32]*RoutePolicyConfig - 所有策略配置的合并map
 *****************************************************************************/
func (p *RoutePolicyProcessor) getAllConfigs() map[uint32]*RoutePolicyConfig {
	allConfigs := make(map[uint32]*RoutePolicyConfig)
	for _, zoneMap := range p.zoneConfigs {
		for cookie, config := range zoneMap {
			allConfigs[cookie] = config
		}
	}
	return allConfigs
}

/*****************************************************************************
 * NAME: syncWorkingToLocalConfigs
 *
 * DESCRIPTION:
 *     Synchronizes working configurations back to local configurations.
 *     This ensures that policies updated during full sync are properly
 *     reflected in local configs for EndFullSync cleanup logic.
 *****************************************************************************/
func (p *RoutePolicyProcessor) syncWorkingToLocalConfigs() {
	p.logger.Debug("syncing working configurations back to local configurations")

	// Clear existing local configurations
	p.zoneConfigs = make(map[pb.RoutePolicyZone]map[uint32]*RoutePolicyConfig)
	p.cookieToID = make(map[uint32]int)
	p.zoneMaxIDs = make(map[pb.RoutePolicyZone]int)

	// Copy working configurations to local configurations
	for zone, workingZoneMap := range p.workingZoneConfigs {
		if len(workingZoneMap) > 0 {
			p.zoneConfigs[zone] = make(map[uint32]*RoutePolicyConfig)
			for cookie, config := range workingZoneMap {
				// Create a copy of the config
				configCopy := *config
				p.zoneConfigs[zone][cookie] = &configCopy
			}
		}
	}

	// Copy working cookie to ID mapping
	for cookie, id := range p.workingCookieToID {
		p.cookieToID[cookie] = id
	}

	// Copy working zone max IDs
	for zone, maxID := range p.workingZoneMaxIDs {
		p.zoneMaxIDs[zone] = maxID
	}

	p.logger.Debug("working configurations synced to local configurations successfully")
}

/*****************************************************************************
 * NAME: getAllWorkingConfigs
 *
 * DESCRIPTION:
 *     获取所有zone的working配置，按需合并。
 *     用于需要遍历所有策略的场景（使用working配置，用于所有操作）。
 *
 * RETURNS:
 *     map[uint32]*RoutePolicyConfig - 所有策略配置的合并map
 *****************************************************************************/
func (p *RoutePolicyProcessor) getAllWorkingConfigs() map[uint32]*RoutePolicyConfig {
	allConfigs := make(map[uint32]*RoutePolicyConfig)
	for _, zoneMap := range p.workingZoneConfigs {
		for cookie, config := range zoneMap {
			allConfigs[cookie] = config
		}
	}
	return allConfigs
}

/*****************************************************************************
 * NAME: getCurrentPolicyIDByCookie
 *
 * DESCRIPTION:
 *     Gets the current policy ID for a given cookie by querying floweye directly.
 *     This ensures we get the real-time ID, not potentially stale cached data.
 *     Used after ordering operations where policy IDs may have changed.
 *
 * PARAMETERS:
 *     cookie - Policy cookie to search for
 *
 * RETURNS:
 *     int   - Current policy ID
 *     error - Error if policy not found or query fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) getCurrentPolicyIDByCookie(cookie uint32) (int, error) {
	p.logger.Debug("getting current route policy ID by cookie",
		zap.Uint32("cookie", cookie))

	// Use floweye rtpolicy get command with cookie parameter
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "rtpolicy", "get", "cookie="+strconv.FormatUint(uint64(cookie), 10))
	if err != nil {
		return 0, fmt.Errorf("failed to get route policy by cookie: %w", err)
	}

	// Parse the configuration
	config, err := ParseRoutePolicyFromGet(output)
	if err != nil {
		return 0, fmt.Errorf("failed to parse route policy configuration: %w", err)
	}

	p.logger.Debug("successfully retrieved current route policy ID",
		zap.Uint32("cookie", cookie),
		zap.Int("current_id", config.ID))

	return config.ID, nil
}

/*****************************************************************************
 * NAME: getConfigByCookie
 *
 * DESCRIPTION:
 *     按cookie查找策略配置（使用local配置，用于全量同步）。
 *     使用cookieToZone快速定位zone，然后在对应zone中查找。
 *
 * PARAMETERS:
 *     cookie - 策略cookie
 *
 * RETURNS:
 *     *RoutePolicyConfig - 策略配置
 *     bool               - 是否找到
 *****************************************************************************/
func (p *RoutePolicyProcessor) getConfigByCookie(cookie uint32) (*RoutePolicyConfig, bool) {
	if zone, exists := p.cookieToZone[cookie]; exists {
		config, found := p.zoneConfigs[zone][cookie]
		return config, found
	}
	return nil, false
}

/*****************************************************************************
 * NAME: getWorkingConfigByCookie
 *
 * DESCRIPTION:
 *     按cookie查找策略配置（使用working配置，用于所有操作）。
 *     使用workingCookieToZone快速定位zone，然后在对应zone中查找。
 *
 * PARAMETERS:
 *     cookie - 策略cookie
 *
 * RETURNS:
 *     *RoutePolicyConfig - 策略配置
 *     bool               - 是否找到
 *****************************************************************************/
func (p *RoutePolicyProcessor) getWorkingConfigByCookie(cookie uint32) (*RoutePolicyConfig, bool) {
	if zone, exists := p.workingCookieToZone[cookie]; exists {
		config, found := p.workingZoneConfigs[zone][cookie]
		return config, found
	}
	return nil, false
}

/*****************************************************************************
 * NAME: getZoneConfigCount
 *
 * DESCRIPTION:
 *     获取指定zone的策略数量（使用local配置，用于全量同步）。
 *
 * PARAMETERS:
 *     zone - 策略zone
 *
 * RETURNS:
 *     int - 策略数量
 *****************************************************************************/
func (p *RoutePolicyProcessor) getZoneConfigCount(zone pb.RoutePolicyZone) int {
	return len(p.zoneConfigs[zone])
}

/*****************************************************************************
 * NAME: getWorkingZoneConfigCount
 *
 * DESCRIPTION:
 *     获取指定zone的策略数量（使用working配置，用于所有操作）。
 *
 * PARAMETERS:
 *     zone - 策略zone
 *
 * RETURNS:
 *     int - 策略数量
 *****************************************************************************/
func (p *RoutePolicyProcessor) getWorkingZoneConfigCount(zone pb.RoutePolicyZone) int {
	return len(p.workingZoneConfigs[zone])
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a route policy task.
 *     Routes the task to appropriate handler based on task action.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task containing route policy configuration
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get route policy task data
	routePolicyTask := task.GetRoutePolicyTask()
	if routePolicyTask == nil {
		return "Route policy task data is empty", fmt.Errorf("route policy task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "route_policy", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.String("desc", routePolicyTask.GetDesc()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, routePolicyTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, routePolicyTask)
	default:
		err = fmt.Errorf("unknown task action: %v", task.TaskAction)
		result = ""
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) StartFullSync() error {
	p.logger.Info("starting full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	err := p.refreshLocalConfigs()
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up remaining local configurations that were not processed.
 *****************************************************************************/
func (p *RoutePolicyProcessor) EndFullSync() {
	p.logger.Info("ending full synchronization")

	// Create a copy of remaining route policies to avoid concurrent map modification
	remainingPolicies := p.getAllConfigs()

	// Process remaining route policies in local configuration
	// These are policies that were not included in the full sync and should be deleted
	// Keep fullSyncInProgress=true during cleanup so handleDeleteConfig can properly
	// remove items from zone configs
	if len(remainingPolicies) > 0 {
		p.logger.Info("cleaning up remaining route policies",
			zap.Int("count", len(remainingPolicies)))

		for cookie := range remainingPolicies {
			// Create a delete task for this route policy
			deleteTask := &pb.RoutePolicyTask{
				Cookie: cookie,
			}

			// Delete the route policy
			p.logger.Info("deleting route policy",
				zap.Uint32("cookie", cookie))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("failed to delete route policy",
					zap.Uint32("cookie", cookie),
					zap.Error(err))
			}
		}
	}

	// Verify cleanup and set flag to false
	totalRemaining := 0
	for _, zoneMap := range p.zoneConfigs {
		totalRemaining += len(zoneMap)
	}
	if totalRemaining > 0 {
		p.logger.Warn("some route policies not cleaned up",
			zap.Int("count", totalRemaining))
	}

	// Reset state
	p.fullSyncInProgress = false
	p.cookieToID = make(map[uint32]int)
	p.cookieToZone = make(map[uint32]pb.RoutePolicyZone)
	p.workingCookieToID = make(map[uint32]int)
	p.workingCookieToZone = make(map[uint32]pb.RoutePolicyZone)
	p.initializeZoneCaches()

	p.logger.Info("full synchronization ended")
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles NEW_CONFIG and EDIT_CONFIG actions for route policies.
 *     Implements unified logic for both create and update operations.
 *     Includes ordering logic and verification.
 *
 * PARAMETERS:
 *     ctx    - Context for the operation
 *     task   - Route policy task configuration
 *     action - Task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) handleConfigChange(ctx context.Context, task *pb.RoutePolicyTask, action pb.TaskAction) (string, error) {
	// Validate required fields
	if task.GetCookie() == 0 {
		return "Route policy cookie is required", fmt.Errorf("route policy cookie is required")
	}
	// Note: Description is optional, no validation required

	cookie := task.GetCookie()

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		cookieVal := cookie // Copy value to avoid closure capture issues
		defer func() {
			if zone, zoneExists := p.cookieToZone[cookieVal]; zoneExists {
				delete(p.zoneConfigs[zone], cookieVal)
			}
			delete(p.cookieToID, cookieVal)
			delete(p.cookieToZone, cookieVal)
		}()
	}

	p.logger.Info("Processing route policy configuration",
		zap.Uint32("cookie", cookie),
		zap.String("desc", task.GetDesc()),
		zap.String("action", action.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Convert protobuf task to internal data structure (one-time conversion)
	configData, err := ConvertRoutePolicyTaskToConfig(task, p.logger)
	if err != nil {
		return fmt.Sprintf("Failed to convert route policy task to config data: %v", err), err
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Check if route policy exists in working configuration
	_, exists := p.getWorkingConfigByCookie(cookie)

	/*
		// If configurations match, no need to modify (using converted data)
		if exists && CompareRoutePolicyConfig(p.logger, configData, localConfig) {
			p.logger.Info("Route policy configuration already matches, no changes needed",
				zap.Uint32("cookie", cookie))

			return "Route policy configuration already matches, no changes needed", nil
		}
	*/

	// Follow documentation workflow: ID allocation -> create/update -> ordering
	var operation string

	if exists {
		// Update existing route policy (using converted data)
		operation = "update"
		_, err = p.updateRoutePolicyData(configData)
	} else {
		// Create new route policy (using converted data)
		operation = "create"
		_, err = p.createRoutePolicyData(configData)
	}

	if err != nil {
		p.logger.Error("failed to execute route policy operation",
			zap.String("operation", operation),
			zap.Uint32("cookie", cookie),
			zap.Error(err))
		return fmt.Sprintf("Failed to %s route policy: %v", operation, err), err
	}

	// After policy creation/modification, refresh working configs to ensure ordering logic can find the new policy
	if err := p.refreshWorkingConfigs(); err != nil {
		p.logger.Error("failed to refresh working configs after policy creation/modification",
			zap.Uint32("cookie", cookie),
			zap.Error(err))
		return fmt.Sprintf("Route policy %s succeeded but failed to refresh working configs: %v", operation, err), err
	}

	if configData.Zone == pb.RoutePolicyZone_LPM_TIER_T3 {
		// Handle automatic LPM sorting for LPM_TIER_T3 zone when no previous parameter specified
		if err := p.handleLPMSortingData(ctx, configData); err != nil {
			p.logger.Error("failed to handle LPM sorting",
				zap.Uint32("cookie", cookie),
				zap.Error(err))
			return fmt.Sprintf("Route policy %s succeeded but LPM sorting failed: %v", operation, err), err
		}
	} else {
		// Handle ordering based on previous parameter for all other zones
		// All non-LMP zones should have previous parameter set
		if err := p.handleRoutePolicyOrderingData(ctx, configData); err != nil {
			p.logger.Error("failed to handle route policy ordering",
				zap.Uint32("cookie", cookie),
				zap.Error(err))
			return fmt.Sprintf("Route policy %s succeeded but ordering failed: %v", operation, err), err
		}
	}

	// Handle enable/disable operation separately after policy creation/modification
	// Re-get the current policy ID using cookie after potential ordering operations
	currentPolicyID, err := p.getCurrentPolicyIDByCookie(configData.Cookie)
	if err != nil {
		p.logger.Error("failed to get current policy ID for enable/disable",
			zap.Uint32("cookie", cookie),
			zap.Error(err))
		return fmt.Sprintf("Route policy %s succeeded but failed to get current policy ID for enable/disable: %v", operation, err), err
	}

	if err := p.handleRoutePolicyEnableDisable(configData.Cookie, currentPolicyID, configData.Disable); err != nil {
		p.logger.Error("failed to handle route policy enable/disable",
			zap.Uint32("cookie", cookie),
			zap.Bool("disable", configData.Disable),
			zap.Error(err))
		return fmt.Sprintf("Route policy %s succeeded but enable/disable failed: %v", operation, err), err
	}

	// Verify the configuration was applied successfully (using converted data)
	if err := p.verifyRoutePolicyConfigData(ctx, configData); err != nil {
		p.logger.Error("route policy verification failed",
			zap.Uint32("cookie", cookie),
			zap.Error(err))
		return fmt.Sprintf("Route policy %s succeeded but verification failed: %v", operation, err), err
	}

	successMsg := fmt.Sprintf("Route policy %s successful", operation)
	p.logger.Info(successMsg, zap.Uint32("cookie", cookie))
	return successMsg, nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles DELETE_CONFIG action for route policies.
 *     Removes the route policy from the local system and maintains continuous ID ordering.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Route policy task configuration
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) handleDeleteConfig(ctx context.Context, task *pb.RoutePolicyTask) (string, error) {
	cookie := task.GetCookie()

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		cookieVal := cookie // Copy value to avoid closure capture issues
		defer func() {
			if zone, zoneExists := p.cookieToZone[cookieVal]; zoneExists {
				delete(p.zoneConfigs[zone], cookieVal)
			}
			delete(p.cookieToID, cookieVal)
			delete(p.cookieToZone, cookieVal)
		}()
	}

	p.logger.Info("Processing route policy deletion",
		zap.Uint32("cookie", cookie),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Check if route policy exists in working configuration
	workingConfig, exists := p.getWorkingConfigByCookie(cookie)
	if !exists {
		p.logger.Info("Route policy does not exist in working configuration, treating as successful delete",
			zap.Uint32("cookie", cookie))
		return "Route policy does not exist, deletion successful", nil
	}

	// Store deletion information for ordering logic
	deletedPolicyID := workingConfig.ID

	p.logger.Info("deleting route policy",
		zap.Uint32("cookie", cookie),
		zap.Int("policy_id", deletedPolicyID))

	// Delete the route policy
	err := p.deleteRoutePolicy(deletedPolicyID)
	if err != nil {
		return "", fmt.Errorf("failed to delete route policy %d: %w", cookie, err)
	}

	// Handle ordering logic: decrement IDs of all route policies with ID > deleted ID
	if err := p.handleRoutePolicyDeletionOrdering(ctx, deletedPolicyID); err != nil {
		p.logger.Error("failed to handle route policy deletion ordering",
			zap.Uint32("cookie", cookie),
			zap.Int("deleted_id", deletedPolicyID),
			zap.Error(err))
		return fmt.Sprintf("Route policy deletion succeeded but ordering adjustment failed: %v", err), err
	}

	successMsg := "Route policy deletion successful"
	p.logger.Info(successMsg, zap.Uint32("cookie", cookie))
	return successMsg, nil
}

// Note: createRoutePolicy function removed - now using createRoutePolicyData with converted data structure

/*****************************************************************************
 * NAME: createRoutePolicyData
 *
 * DESCRIPTION:
 *     Creates a new route policy using converted data structure.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Implements ID allocation as first step according to documentation.
 *
 * PARAMETERS:
 *     configData - Route policy configuration data (converted from protobuf)
 *
 * RETURNS:
 *     int   - Allocated policy ID
 *     error - Error if creation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) createRoutePolicyData(configData *RoutePolicyConfig) (int, error) {
	// Step 1: ID allocation (first step according to documentation)
	policyID, err := p.allocateRoutePolicyID(configData.Cookie, configData.Zone)
	if err != nil {
		return 0, fmt.Errorf("failed to allocate policy ID: %w", err)
	}

	args := []string{"route", "add"}

	// Build common route policy arguments
	err = p.buildRoutePolicyArgs(&args, configData, policyID)
	if err != nil {
		return 0, fmt.Errorf("failed to build route policy arguments: %w", err)
	}

	// Add ordering if specified (only for create operation)
	// Include previous=0 as it's a valid ordering operation (move to first position)
	if configData.PreviousSet {
		args = append(args, "previous="+strconv.FormatUint(uint64(configData.Previous), 10))
	}

	// Execute the command
	p.logger.Debug("executing route add command with converted data", zap.Strings("args", args))
	_, execErr := utils.ExecuteCommand(p.logger, 30, "floweye", args...)
	if execErr != nil {
		return 0, fmt.Errorf("failed to execute route add command: %w", execErr)
	}

	return policyID, nil
}

/*****************************************************************************
 * NAME: buildRoutePolicyArgs
 *
 * DESCRIPTION:
 *     Builds common floweye route command arguments from route policy configuration.
 *     Used by both create and update operations to ensure consistency.
 *
 * PARAMETERS:
 *     args       - Pointer to command arguments slice to append to
 *     configData - Route policy configuration data
 *     policyID   - Policy ID to use in the command
 *
 * RETURNS:
 *     error - Error if argument building fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) buildRoutePolicyArgs(args *[]string, configData *RoutePolicyConfig, policyID int) error {
	// Add basic configuration - use ID instead of cookie
	*args = append(*args, "id="+strconv.Itoa(policyID))
	*args = append(*args, "cookie="+strconv.FormatUint(uint64(configData.Cookie), 10))

	// Add description with proper quoting for values containing spaces
	if strings.Contains(configData.Desc, " ") {
		*args = append(*args, "desc=\""+configData.Desc+"\"")
	} else {
		*args = append(*args, "desc="+configData.Desc)
	}

	// Add schedule time configuration - always include to ensure proper reset
	*args = append(*args, "schtime="+strconv.Itoa(int(configData.SchTime)))

	// Add source configuration - always include to ensure proper reset
	if configData.Src == "any" || configData.Src == "" {
		*args = append(*args, "src=")
	} else {
		// Quote source address if it contains spaces or special characters
		if strings.Contains(configData.Src, " ") || strings.Contains(configData.Src, ",") {
			*args = append(*args, "src=\""+configData.Src+"\"")
		} else {
			*args = append(*args, "src="+configData.Src)
		}
	}

	if configData.SrcPort == "any" || configData.SrcPort == "" {
		*args = append(*args, "sport=any")
	} else {
		*args = append(*args, "sport="+configData.SrcPort)
	}

	// Add user type configuration - always include to ensure proper reset
	*args = append(*args, "usrtype="+configData.UsrType)

	// Add pool configuration - always include to ensure proper reset
	*args = append(*args, "pool="+strconv.Itoa(int(configData.Pool)))

	// Add destination configuration - always include to ensure proper reset
	if configData.Dst == "any" || configData.Dst == "" {
		*args = append(*args, "dst=")
	} else {
		// Quote destination address if it contains spaces or special characters
		if strings.Contains(configData.Dst, " ") || strings.Contains(configData.Dst, ",") {
			*args = append(*args, "dst=\""+configData.Dst+"\"")
		} else {
			*args = append(*args, "dst="+configData.Dst)
		}
	}

	if configData.DstPort == "any" || configData.DstPort == "" {
		*args = append(*args, "dport=any")
	} else {
		*args = append(*args, "dport="+configData.DstPort)
	}

	// Add protocol configuration - always include to ensure proper reset
	*args = append(*args, "proto="+configData.Proto)

	// Add interface and QoS configuration using converted data
	// Always add inif parameter to ensure proper configuration reset
	if configData.InIf == "any" {
		*args = append(*args, "inif=any")
	} else {
		// For specific interfaces, add "if." prefix as required by floweye
		*args = append(*args, "inif=if."+configData.InIf)
	}

	// Add QoS configuration - always include to ensure proper reset
	*args = append(*args, "wanbw="+strconv.Itoa(int(configData.WanBw)))

	*args = append(*args, "wanbwout="+strconv.Itoa(int(configData.WanBwOut)))

	*args = append(*args, "vlan="+configData.VLAN)

	*args = append(*args, "ttl="+configData.TTL)

	*args = append(*args, "dscp="+configData.DSCP)

	// Add action configuration using converted data
	// Format: action=actiontype-proxy (e.g., action=route-wan)
	actionStr := configData.Action
	if configData.Proxy != "" {
		actionStr = configData.Action + "-" + configData.Proxy
	}
	*args = append(*args, "action="+actionStr)

	// Add action-specific configuration - always include to ensure proper reset
	// For NAT/DNAT actions, nexthop default is _NULL_
	// For route actions, nexthop default is 0.0.0.0
	if configData.NextHop == "" {
		if configData.Action == "nat" || configData.Action == "dnat" || configData.Action == "cgnat" {
			*args = append(*args, "nexthop=_NULL_")
		} else {
			*args = append(*args, "nexthop=0.0.0.0")
		}
	} else {
		*args = append(*args, "nexthop="+configData.NextHop)
	}

	if configData.NewDstIP == "" {
		*args = append(*args, "newdstip=0.0.0.0")
	} else {
		*args = append(*args, "newdstip="+configData.NewDstIP)
	}

	if configData.NatIP == "" {
		*args = append(*args, "natip=")
	} else {
		*args = append(*args, "natip="+configData.NatIP)
	}

	if configData.FullConeNat {
		*args = append(*args, "fullconenat=1")
	} else {
		*args = append(*args, "fullconenat=0")
	}

	if configData.NoSnat {
		*args = append(*args, "nosnat=1")
	} else {
		*args = append(*args, "nosnat=0")
	}

	return nil
}

/*****************************************************************************
 * NAME: updateRoutePolicyData
 *
 * DESCRIPTION:
 *     Updates an existing route policy using converted data structure.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Uses ID allocation to get the correct policy ID.
 *
 * PARAMETERS:
 *     configData - Route policy configuration data (converted from protobuf)
 *
 * RETURNS:
 *     int   - Policy ID
 *     error - Error if update fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) updateRoutePolicyData(configData *RoutePolicyConfig) (int, error) {
	// For update operations, use existing policy's zone or the provided zone
	zone := configData.Zone
	if existingConfig, exists := p.getWorkingConfigByCookie(configData.Cookie); exists {
		zone = existingConfig.Zone
	}

	// Get policy ID (should exist for update operations)
	policyID, err := p.allocateRoutePolicyID(configData.Cookie, zone)
	if err != nil {
		return 0, fmt.Errorf("failed to get policy ID for update: %w", err)
	}

	args := []string{"route", "set"}

	// Build common route policy arguments
	err = p.buildRoutePolicyArgs(&args, configData, policyID)
	if err != nil {
		return 0, fmt.Errorf("failed to build route policy arguments: %w", err)
	}

	// Execute the command
	p.logger.Debug("executing route set command with converted data", zap.Strings("args", args))
	_, execErr := utils.ExecuteCommand(p.logger, 30, "floweye", args...)
	if execErr != nil {
		return 0, fmt.Errorf("failed to execute route set command: %w", execErr)
	}

	return policyID, nil
}

/*****************************************************************************
 * NAME: deleteRoutePolicy
 *
 * DESCRIPTION:
 *     Deletes a route policy using floweye route remove command.
 *     Handles NEXIST errors as successful operations (idempotent delete).
 *
 * PARAMETERS:
 *     policyID - Policy ID to delete
 *
 * RETURNS:
 *     error - Error if deletion fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) deleteRoutePolicy(policyID int) error {
	args := []string{"route", "remove", "id=" + strconv.Itoa(policyID)}

	p.logger.Debug("executing route remove command", zap.Strings("args", args))
	output, err := utils.ExecuteCommand(p.logger, 30, "floweye", args...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Route policy already does not exist, treating as successful delete",
				zap.Int("policy_id", policyID))
			return nil
		}
		return fmt.Errorf("failed to execute route remove command: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: VerifyConfig
 *
 * DESCRIPTION:
 *     Verifies a route policy configuration by querying the local system.
 *     Uses single-object retrieval for performance optimization.
 *
 * PARAMETERS:
 *     cookie - Policy cookie to verify
 *
 * RETURNS:
 *     bool  - True if policy exists and matches expected configuration
 *     error - Error if verification fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) VerifyConfig(cookie uint32) (bool, error) {
	p.logger.Debug("verifying route policy configuration",
		zap.Uint32("cookie", cookie))

	// Use single-object retrieval for performance
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "rtpolicy", "get", "cookie="+strconv.FormatUint(uint64(cookie), 10))
	if err != nil {
		p.logger.Debug("route policy not found during verification",
			zap.Uint32("cookie", cookie),
			zap.Error(err))
		return false, nil
	}

	// Parse the configuration
	config, err := ParseRoutePolicyFromGet(output)
	if err != nil {
		return false, fmt.Errorf("failed to parse route policy config during verification: %w", err)
	}

	// Verify cookie matches
	if config.Cookie != cookie {
		p.logger.Warn("route policy cookie mismatch during verification",
			zap.Uint32("expected_cookie", cookie),
			zap.Uint32("actual_cookie", config.Cookie))
		return false, nil
	}

	p.logger.Debug("route policy verification successful",
		zap.Uint32("cookie", cookie))

	return true, nil
}

/*****************************************************************************
 * NAME: verifyRoutePolicyConfigData
 *
 * DESCRIPTION:
 *     Verifies that a route policy configuration was applied correctly.
 *     Uses converted data structure to eliminate protobuf parsing.
 *     Uses single-object retrieval for performance optimization.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Route policy configuration data (converted from protobuf)
 *
 * RETURNS:
 *     error - Error if verification fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) verifyRoutePolicyConfigData(ctx context.Context, configData *RoutePolicyConfig) error {
	// Use the new verification function that reuses comparison logic
	verified, err := VerifyRoutePolicyConfig(p.logger, configData)
	if err != nil {
		return fmt.Errorf("route policy verification failed: %w", err)
	}

	if !verified {
		return fmt.Errorf("route policy configuration verification failed: configurations do not match")
	}

	p.logger.Debug("route policy configuration verification successful",
		zap.Uint32("cookie", configData.Cookie))
	return nil
}

/*****************************************************************************
 * NAME: handleRoutePolicyEnableDisable
 *
 * DESCRIPTION:
 *     Handles route policy enable/disable operation separately from policy creation/modification.
 *     Uses dedicated floweye route disable/enable commands.
 *
 * PARAMETERS:
 *     cookie   - Policy cookie
 *     policyID - Policy ID
 *     disable  - Whether to disable the policy (true=disable, false=enable)
 *
 * RETURNS:
 *     error - Error if enable/disable operation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) handleRoutePolicyEnableDisable(cookie uint32, policyID int, disable bool) error {
	p.logger.Debug("handling route policy enable/disable",
		zap.Uint32("cookie", cookie),
		zap.Int("policy_id", policyID),
		zap.Bool("disable", disable))

	// Build floweye command for enable/disable operation
	var cmdArgs []string
	if disable {
		cmdArgs = []string{"route", "disable", "id=" + strconv.Itoa(policyID)}
	} else {
		// For enable operation, use route set with disable=0
		cmdArgs = []string{"route", "set", "id=" + strconv.Itoa(policyID), "disable=0"}
	}

	// Execute floweye command
	p.logger.Info("executing floweye enable/disable command for route policy",
		zap.Uint32("cookie", cookie),
		zap.Int("id", policyID),
		zap.Bool("disable", disable),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye enable/disable command for route policy",
			zap.Uint32("cookie", cookie),
			zap.Int("id", policyID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to %s route policy: %w",
			map[bool]string{true: "disable", false: "enable"}[disable], err)
	}

	p.logger.Debug("floweye enable/disable command executed successfully",
		zap.Uint32("cookie", cookie),
		zap.Int("id", policyID),
		zap.String("operation", map[bool]string{true: "disabled", false: "enabled"}[disable]),
		zap.String("output", output))

	return nil
}
