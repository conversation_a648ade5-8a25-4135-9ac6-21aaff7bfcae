/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      flow_control_processor.go
 *
 * DESCRIPTION :    Flow control processor for handling policy groups and policies
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: FlowControlProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_FLOW_CONTROL type tasks.
 *     Handles flow control policy group and policy configuration operations.
 *
 * FIELDS:
 *     logger               - Logger for flow control processor operations
 *     localPolicyGroups    - Cache of local policy group configurations (used for full sync redundant deletion)
 *     localPolicies        - Cache of local policy configurations (by cookie, used for full sync redundant deletion)
 *     fullSyncInProgress   - Flag indicating if full sync is in progress
 *     groupNameToID        - Map of policy group names to IDs
 *     workingPolicyGroups  - Working cache for operations (can be refreshed during full sync)
 *     workingPolicies      - Working cache for operations (can be refreshed during full sync)
 *     workingGroupNameToID - Working map of policy group names to IDs
 *****************************************************************************/
type FlowControlProcessor struct {
	logger               *logger.Logger                // Logger for flow control processor operations
	localPolicyGroups    map[string]*PolicyGroupConfig // Cache of local policy group configurations (used for full sync redundant deletion)
	localPolicies        map[uint32]*PolicyConfig      // Cache of local policy configurations (by cookie, used for full sync redundant deletion)
	fullSyncInProgress   bool                          // Flag indicating if full sync is in progress
	groupNameToID        map[string]int                // Map of policy group names to IDs
	workingPolicyGroups  map[string]*PolicyGroupConfig // Working cache for operations (can be refreshed during full sync)
	workingPolicies      map[uint32]*PolicyConfig      // Working cache for operations (can be refreshed during full sync)
	workingGroupNameToID map[string]int                // Working map of policy group names to IDs
}

/*****************************************************************************
 * NAME: NewFlowControlProcessor
 *
 * DESCRIPTION:
 *     Creates a new flow control processor instance.
 *     Initializes the local configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *FlowControlProcessor - Initialized flow control processor
 *****************************************************************************/
func NewFlowControlProcessor(log *logger.Logger) *FlowControlProcessor {
	processor := &FlowControlProcessor{
		logger:               log.WithModule("flow-control-processor"),
		localPolicyGroups:    make(map[string]*PolicyGroupConfig),
		localPolicies:        make(map[uint32]*PolicyConfig),
		fullSyncInProgress:   false,
		groupNameToID:        make(map[string]int),
		workingPolicyGroups:  make(map[string]*PolicyGroupConfig),
		workingPolicies:      make(map[uint32]*PolicyConfig),
		workingGroupNameToID: make(map[string]int),
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *
 * RETURNS:
 *     pb.TaskType - TASK_FLOW_CONTROL
 *****************************************************************************/
func (p *FlowControlProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_FLOW_CONTROL
}

/*****************************************************************************
 * NAME: fetchFlowControlConfigs
 *
 * DESCRIPTION:
 *     Fetches flow control configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*PolicyGroupConfig - Policy groups by name
 *     map[uint32]*PolicyConfig      - Policies by cookie
 *     map[string]int                - Group name to ID mapping
 *     error                         - Error if fetch fails
 *****************************************************************************/
func (p *FlowControlProcessor) fetchFlowControlConfigs() (map[string]*PolicyGroupConfig, map[uint32]*PolicyConfig, map[string]int, error) {
	policyGroups := make(map[string]*PolicyGroupConfig)
	policies := make(map[uint32]*PolicyConfig)
	groupNameToID := make(map[string]int)

	// Get list of policy groups
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "policygroup2", "list")
	if err != nil {
		p.logger.Error("failed to list policy groups", zap.Error(err))
		return nil, nil, nil, fmt.Errorf("failed to list policy groups: %w", err)
	}

	// Parse policy group names and IDs from list output
	groupIDs := p.parsePolicyGroupIDsFromList(output)

	// Get detailed configuration for each policy group
	for _, groupID := range groupIDs {
		config, err := p.getPolicyGroupConfig(groupID)
		if err != nil {
			p.logger.Error("failed to get policy group configuration",
				zap.Int("group_id", groupID),
				zap.Error(err))
			continue
		}
		policyGroups[config.Name] = config
		groupNameToID[config.Name] = config.ID

		// Get policies for this group
		groupPolicies, err := p.getPoliciesForGroup(groupID)
		if err != nil {
			p.logger.Error("failed to get policies for group",
				zap.Int("group_id", groupID),
				zap.String("group_name", config.Name),
				zap.Error(err))
			continue
		}

		// Add policies to cache with detailed information
		for _, policy := range groupPolicies {
			// Get detailed policy configuration including cookie
			detailedPolicy, err := p.getPolicyDetailedConfig(groupID, policy.ID)
			if err != nil {
				p.logger.Error("failed to get detailed policy configuration",
					zap.Int("group_id", groupID),
					zap.Int("policy_id", policy.ID),
					zap.Error(err))
				continue
			}

			detailedPolicy.GroupID = groupID
			detailedPolicy.GroupName = config.Name
			policies[detailedPolicy.Cookie] = detailedPolicy
		}
	}

	return policyGroups, policies, groupNameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local policy group and policy configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *FlowControlProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local flow control configurations")

	policyGroups, policies, groupNameToID, err := p.fetchFlowControlConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localPolicyGroups = policyGroups
	p.localPolicies = policies
	p.groupNameToID = groupNameToID

	p.logger.Debug("refreshed local flow control configurations",
		zap.Int("policy_groups", len(p.localPolicyGroups)),
		zap.Int("policies", len(p.localPolicies)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working policy group and policy configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *FlowControlProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working flow control configurations")

	policyGroups, policies, groupNameToID, err := p.fetchFlowControlConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingPolicyGroups = policyGroups
	p.workingPolicies = policies
	p.workingGroupNameToID = groupNameToID

	p.logger.Debug("refreshed working flow control configurations",
		zap.Int("policy_groups", len(p.workingPolicyGroups)),
		zap.Int("policies", len(p.workingPolicies)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like ordering, enable/disable, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *FlowControlProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: parsePolicyGroupIDsFromList
 *
 * DESCRIPTION:
 *     Parses policy group IDs from floweye policygroup2 list output.
 *     Extracts the first field (ID) from each line.
 *
 * PARAMETERS:
 *     output - Output from floweye policygroup2 list command
 *
 * RETURNS:
 *     []int - List of policy group IDs
 *****************************************************************************/
func (p *FlowControlProcessor) parsePolicyGroupIDsFromList(output string) []int {
	var groupIDs []int
	lines := strings.Split(strings.TrimSpace(output), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Split by space and get the first field (ID)
		fields := strings.Fields(line)
		if len(fields) > 0 {
			if id, err := strconv.Atoi(fields[0]); err == nil {
				groupIDs = append(groupIDs, id)
			}
		}
	}

	return groupIDs
}

/*****************************************************************************
 * NAME: getPolicyGroupConfig
 *
 * DESCRIPTION:
 *     Gets detailed configuration for a specific policy group.
 *     Uses floweye policygroup2 get command to fetch group details.
 *
 * PARAMETERS:
 *     groupID - Policy group ID to fetch
 *
 * RETURNS:
 *     *PolicyGroupConfig - Policy group configuration
 *     error              - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) getPolicyGroupConfig(groupID int) (*PolicyGroupConfig, error) {
	// Execute floweye command to get policy group details
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "policygroup2", "get", "id="+strconv.Itoa(groupID))
	if err != nil {
		return nil, fmt.Errorf("failed to get policy group basic config: %w", err)
	}

	config, err := ParsePolicyGroupFromGetOutput(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse policy group basic config: %w", err)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: getPoliciesForGroup
 *
 * DESCRIPTION:
 *     Gets all policies for a specific policy group.
 *     Uses floweye newpolicy list command with JSON output.
 *
 * PARAMETERS:
 *     groupID - Policy group ID to fetch policies for
 *
 * RETURNS:
 *     []*PolicyConfig - List of policy configurations
 *     error           - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) getPoliciesForGroup(groupID int) ([]*PolicyConfig, error) {
	// Execute floweye command to get policies for the group
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "newpolicy", "list",
		"group="+strconv.Itoa(groupID), "json=1")
	if err != nil {
		return nil, fmt.Errorf("failed to get policies for group: %w", err)
	}

	// Handle empty output (no policies in group)
	output = strings.TrimSpace(output)
	if output == "" || output == "[]" {
		return []*PolicyConfig{}, nil
	}

	policies, err := ParsePolicyFromJSONOutput(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse policies from JSON: %w", err)
	}

	return policies, nil
}

/*****************************************************************************
 * NAME: getPolicyDetailedConfig
 *
 * DESCRIPTION:
 *     Gets detailed configuration for a specific policy including cookie.
 *     Uses floweye newpolicy get command to fetch complete policy details.
 *
 * PARAMETERS:
 *     groupID  - Policy group ID
 *     policyID - Policy ID to fetch details for
 *
 * RETURNS:
 *     *PolicyConfig - Detailed policy configuration
 *     error         - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) getPolicyDetailedConfig(groupID, policyID int) (*PolicyConfig, error) {
	// Execute floweye command to get detailed policy configuration
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "newpolicy", "get",
		"group="+strconv.Itoa(groupID), "id="+strconv.Itoa(policyID))
	if err != nil {
		return nil, fmt.Errorf("failed to get detailed policy config: %w", err)
	}

	config, err := ParsePolicyFromGetOutput(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse detailed policy config: %w", err)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: getTrafficChannelIDByName
 *
 * DESCRIPTION:
 *     Gets traffic channel ID by name using floweye policy listbwo command.
 *     Returns 0 if channel not found.
 *
 * PARAMETERS:
 *     channelName - Name of the traffic channel
 *
 * RETURNS:
 *     int   - Channel ID (0 if not found)
 *     error - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) getTrafficChannelIDByName(channelName string) (int, error) {
	if channelName == "" {
		return 0, nil
	}

	// Execute floweye command to list traffic channels
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "policy", "listbwo")
	if err != nil {
		return 0, fmt.Errorf("failed to list traffic channels: %w", err)
	}

	// Parse output to find channel ID by name
	// Expected format: id name rate quota qsize outbps dropbps outbytes dropbytes leftbytes
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) >= 2 {
			// Check if the name matches
			if fields[1] == channelName {
				// Parse the ID (first field)
				if id, err := strconv.Atoi(fields[0]); err == nil {
					p.logger.Debug("found traffic channel ID",
						zap.String("channel_name", channelName),
						zap.Int("channel_id", id))
					return id, nil
				}
			}
		}
	}

	p.logger.Warn("traffic channel not found",
		zap.String("channel_name", channelName))
	return 0, fmt.Errorf("traffic channel '%s' not found", channelName)
}

/*****************************************************************************
 * NAME: getTrafficStatIDByName
 *
 * DESCRIPTION:
 *     Gets traffic statistics ID by name using floweye ntmso list command.
 *     Returns 0 if statistics object not found.
 *
 * PARAMETERS:
 *     statName - Name of the traffic statistics object
 *
 * RETURNS:
 *     int   - Statistics ID (0 if not found)
 *     error - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) getTrafficStatIDByName(statName string) (int, error) {
	if statName == "" {
		return 0, nil
	}

	// Execute floweye command to list traffic statistics
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "ntmso", "list")
	if err != nil {
		return 0, fmt.Errorf("failed to list traffic statistics: %w", err)
	}

	// Parse JSON output to find statistics ID by name
	// Expected format: {"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
	configs, err := ParseTrafficStatFromList(output)
	if err != nil {
		return 0, fmt.Errorf("failed to parse traffic statistics list: %w", err)
	}

	for _, config := range configs {
		if config.Name == statName {
			p.logger.Debug("found traffic statistics ID",
				zap.String("stat_name", statName),
				zap.Int("stat_id", config.ID))
			return config.ID, nil
		}
	}

	p.logger.Warn("traffic statistics not found",
		zap.String("stat_name", statName))
	return 0, fmt.Errorf("traffic statistics '%s' not found", statName)
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes local configuration cache and sets sync flag.
 *     Creates default policy groups if they don't exist.
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) StartFullSync() error {
	p.logger.Info("starting full sync for flow control processor")

	p.fullSyncInProgress = true

	// Create default policy groups if they don't exist
	if err := p.createDefaultPolicyGroups(); err != nil {
		p.logger.Error("failed to create default policy groups", zap.Error(err))
		// Don't fail the full sync for default group creation errors
		// Just log the error and continue
	}

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	p.logger.Info("full sync started for flow control processor")
	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up remaining local configurations and resets sync flag.
 *****************************************************************************/
func (p *FlowControlProcessor) EndFullSync() {
	p.logger.Info("ending full sync for flow control processor")

	// Create copies of remaining configurations to avoid modifying maps during iteration
	remainingPolicyGroups := make(map[string]*PolicyGroupConfig)
	for groupName, config := range p.localPolicyGroups {
		remainingPolicyGroups[groupName] = config
	}

	remainingPolicies := make(map[uint32]*PolicyConfig)
	for cookie, config := range p.localPolicies {
		remainingPolicies[cookie] = config
	}

	// Delete remaining policies first (policies depend on groups)
	for cookie := range remainingPolicies {
		deleteTask := &pb.FlowControlPolicyTask{Cookie: cookie}
		_, err := p.handleDeletePolicy(context.Background(), deleteTask)
		if err != nil {
			p.logger.Error("failed to delete remaining policy",
				zap.Uint32("cookie", cookie),
				zap.Error(err))
		}
	}

	// Delete remaining policy groups
	for groupName := range remainingPolicyGroups {
		if groupName == DefaultTrafficMirroringGroup || groupName == DefaultSubscriptionGroup {
			delete(p.localPolicyGroups, groupName)
			continue
		}
		deleteTask := &pb.FlowControlPolicyGroupTask{Name: groupName}
		_, err := p.handleDeletePolicyGroup(context.Background(), deleteTask)
		if err != nil {
			p.logger.Error("failed to delete remaining policy group",
				zap.String("name", groupName),
				zap.Error(err))
		}
	}

	// Verify cleanup
	if len(p.localPolicyGroups) > 0 {
		p.logger.Warn("some policy groups not cleaned up",
			zap.Int("count", len(p.localPolicyGroups)))
	}
	if len(p.localPolicies) > 0 {
		p.logger.Warn("some policies not cleaned up",
			zap.Int("count", len(p.localPolicies)))
	}

	// Reset state
	p.fullSyncInProgress = false
	p.localPolicyGroups = make(map[string]*PolicyGroupConfig)
	p.localPolicies = make(map[uint32]*PolicyConfig)
	p.groupNameToID = make(map[string]int)
	p.workingPolicyGroups = make(map[string]*PolicyGroupConfig)
	p.workingPolicies = make(map[uint32]*PolicyConfig)
	p.workingGroupNameToID = make(map[string]int)

	p.logger.Info("full sync ended for flow control processor")
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a flow control task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *FlowControlProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Validate task payload
	if task.GetFlowControlTask() == nil {
		return "Flow control task payload is nil", fmt.Errorf("flow control task payload is nil")
	}

	flowControlTask := task.GetFlowControlTask()

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "flow_control", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Determine task type and delegate to appropriate handler
	switch taskConfig := flowControlTask.GetTaskConfig().(type) {
	case *pb.FlowControlTask_PolicyGroup:
		result, err = p.processPolicyGroupTask(ctx, taskConfig.PolicyGroup, task.TaskAction)
	case *pb.FlowControlTask_Policy:
		result, err = p.processPolicyTask(ctx, taskConfig.Policy, task.TaskAction)
	default:
		err = fmt.Errorf("unknown flow control task type")
		result = "Unknown flow control task type"
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: processPolicyGroupTask
 *
 * DESCRIPTION:
 *     Processes a policy group task based on its action type.
 *     Handles policy group creation, modification, and deletion.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - Policy group task to process
 *     taskAction - Action to perform (NEW_CONFIG, EDIT_CONFIG, DELETE_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *FlowControlProcessor) processPolicyGroupTask(ctx context.Context, task *pb.FlowControlPolicyGroupTask, taskAction pb.TaskAction) (string, error) {
	switch taskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handlePolicyGroupConfigChange(ctx, task, taskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeletePolicyGroup(ctx, task)
	default:
		return fmt.Sprintf("unsupported policy group task action: %s", taskAction.String()),
			fmt.Errorf("unsupported policy group task action: %s", taskAction.String())
	}
}

/*****************************************************************************
 * NAME: processPolicyTask
 *
 * DESCRIPTION:
 *     Processes a policy task based on its action type.
 *     Handles policy creation, modification, and deletion.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - Policy task to process
 *     taskAction - Action to perform (NEW_CONFIG, EDIT_CONFIG, DELETE_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *FlowControlProcessor) processPolicyTask(ctx context.Context, task *pb.FlowControlPolicyTask, taskAction pb.TaskAction) (string, error) {
	switch taskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handlePolicyConfigChange(ctx, task, taskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeletePolicy(ctx, task)
	default:
		return fmt.Sprintf("unsupported policy task action: %s", taskAction.String()),
			fmt.Errorf("unsupported policy task action: %s", taskAction.String())
	}
}
