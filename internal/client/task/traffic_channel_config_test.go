/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_channel_config_test.go
 *
 * DESCRIPTION :    Unit tests for traffic channel configuration utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        30/05/2025  create
 ******************************************************************************/

package task

import (
	"testing"

	"agent/internal/logger"
	pb "agent/internal/pb"
)

func TestParseTrafficChannelFromGetBwo(t *testing.T) {
	tests := []struct {
		name     string
		output   string
		expected *TrafficChannelConfig
		wantErr  bool
	}{
		{
			name: "valid output",
			output: `id=1
name=channel_1
rate=20000
bytes=0
qsize=1048576
outbps=0
dropbps=0
outbytes=0
dropbytes=0
quota=10000
noquota=0
leftbytes=10485760000`,
			expected: &TrafficChannelConfig{
				Name:       "channel_1",
				Rate:       20000,
				Quota:      10000,
				Priorities: make([]*TrafficChannelPriority, 0),
			},
			wantErr: false,
		},
		{
			name: "zero quota",
			output: `id=2
name=channel_2
rate=50000
quota=0`,
			expected: &TrafficChannelConfig{
				Name:       "channel_2",
				Rate:       50000,
				Quota:      0,
				Priorities: make([]*TrafficChannelPriority, 0),
			},
			wantErr: false,
		},
		{
			name:     "missing name",
			output:   `rate=20000\nquota=0`,
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTrafficChannelFromGetBwo(tt.output)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseTrafficChannelFromGetBwo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if result.Name != tt.expected.Name {
					t.Errorf("ParseTrafficChannelFromGetBwo() name = %v, want %v", result.Name, tt.expected.Name)
				}
				if result.Rate != tt.expected.Rate {
					t.Errorf("ParseTrafficChannelFromGetBwo() rate = %v, want %v", result.Rate, tt.expected.Rate)
				}
				if result.Quota != tt.expected.Quota {
					t.Errorf("ParseTrafficChannelFromGetBwo() quota = %v, want %v", result.Quota, tt.expected.Quota)
				}
			}
		})
	}
}

func TestParseTrafficChannelPrioritiesFromGetHtb(t *testing.T) {
	tests := []struct {
		name     string
		output   string
		expected []*TrafficChannelPriority
		wantErr  bool
	}{
		{
			name: "valid priorities",
			output: `pri gbw  maxrate bytes   outbps  dropbps desc
1   2000    10000   0       0       0       high_priority
2   1000    5000    0       0       0       medium_priority
3   0       0       0       0       0
4   0       0       0       0       0       `,
			expected: []*TrafficChannelPriority{
				{Pri: 1, Gbw: 2000, MaxRate: 10000, Desc: "high_priority"},
				{Pri: 2, Gbw: 1000, MaxRate: 5000, Desc: "medium_priority"},
			},
			wantErr: false,
		},
		{
			name: "no active priorities",
			output: `pri gbw  maxrate bytes   outbps  dropbps desc
1   0       0       0       0       0
2   0       0       0       0       0       `,
			expected: []*TrafficChannelPriority{},
			wantErr:  false,
		},
		{
			name:     "empty output",
			output:   "",
			expected: []*TrafficChannelPriority{},
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTrafficChannelPrioritiesFromGetHtb(tt.output)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseTrafficChannelPrioritiesFromGetHtb() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(result) != len(tt.expected) {
				t.Errorf("ParseTrafficChannelPrioritiesFromGetHtb() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			for i, priority := range result {
				expected := tt.expected[i]
				if priority.Pri != expected.Pri {
					t.Errorf("Priority %d: pri = %v, want %v", i, priority.Pri, expected.Pri)
				}
				if priority.Gbw != expected.Gbw {
					t.Errorf("Priority %d: gbw = %v, want %v", i, priority.Gbw, expected.Gbw)
				}
				if priority.MaxRate != expected.MaxRate {
					t.Errorf("Priority %d: maxrate = %v, want %v", i, priority.MaxRate, expected.MaxRate)
				}
				if priority.Desc != expected.Desc {
					t.Errorf("Priority %d: desc = %v, want %v", i, priority.Desc, expected.Desc)
				}
			}
		})
	}
}

func TestCompareTrafficChannelConfig(t *testing.T) {
	logger := logger.NewLogger(&logger.Config{Level: "debug"})

	tests := []struct {
		name        string
		task        *pb.TrafficChannelTask
		localConfig *TrafficChannelConfig
		expected    bool
	}{
		{
			name: "matching configs",
			task: &pb.TrafficChannelTask{
				Name:  "channel_1",
				Rate:  20000,
				Quota: func() *int32 { v := int32(10000); return &v }(),
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, Maxrate: 10000, Gbw: 2000, Desc: func() *string { v := "high"; return &v }()},
				},
			},
			localConfig: &TrafficChannelConfig{
				Name:  "channel_1",
				Rate:  20000,
				Quota: 10000,
				Priorities: []*TrafficChannelPriority{
					{Pri: 1, MaxRate: 10000, Gbw: 2000, Desc: "high"},
				},
			},
			expected: true,
		},
		{
			name: "different rates",
			task: &pb.TrafficChannelTask{
				Name: "channel_1",
				Rate: 20000,
			},
			localConfig: &TrafficChannelConfig{
				Name: "channel_1",
				Rate: 30000,
			},
			expected: false,
		},
		{
			name: "different priority count",
			task: &pb.TrafficChannelTask{
				Name: "channel_1",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, Maxrate: 10000, Gbw: 2000},
				},
			},
			localConfig: &TrafficChannelConfig{
				Name:       "channel_1",
				Rate:       20000,
				Priorities: []*TrafficChannelPriority{},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompareTrafficChannelConfig(logger, tt.task, tt.localConfig)
			if result != tt.expected {
				t.Errorf("CompareTrafficChannelConfig() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestParseTrafficChannelFromGetBwo_EdgeCases tests edge cases for parsing
func TestParseTrafficChannelFromGetBwo_EdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		output   string
		expected *TrafficChannelConfig
		wantErr  bool
	}{
		{
			name:     "empty output",
			output:   "",
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "whitespace only",
			output:   "   \n\t  \n  ",
			expected: nil,
			wantErr:  true,
		},
		{
			name: "malformed key-value pairs",
			output: `name=channel_1
invalid_line_without_equals
rate=20000
=value_without_key
key_without_value=`,
			expected: &TrafficChannelConfig{
				Name:       "channel_1",
				Rate:       20000,
				Quota:      0,
				Priorities: make([]*TrafficChannelPriority, 0),
			},
			wantErr: false,
		},
		{
			name: "non-numeric values",
			output: `name=channel_1
rate=invalid_number
quota=also_invalid`,
			expected: &TrafficChannelConfig{
				Name:       "channel_1",
				Rate:       0, // Should default to 0 for invalid numbers
				Quota:      0,
				Priorities: make([]*TrafficChannelPriority, 0),
			},
			wantErr: false,
		},
		{
			name: "extra whitespace",
			output: `  name  =  channel_1
  rate  =  20000
  quota  =  10000  `,
			expected: &TrafficChannelConfig{
				Name:       "channel_1",
				Rate:       20000,
				Quota:      10000,
				Priorities: make([]*TrafficChannelPriority, 0),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseTrafficChannelFromGetBwo(tt.output)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseTrafficChannelFromGetBwo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if result.Name != tt.expected.Name {
					t.Errorf("ParseTrafficChannelFromGetBwo() name = %v, want %v", result.Name, tt.expected.Name)
				}
				if result.Rate != tt.expected.Rate {
					t.Errorf("ParseTrafficChannelFromGetBwo() rate = %v, want %v", result.Rate, tt.expected.Rate)
				}
				if result.Quota != tt.expected.Quota {
					t.Errorf("ParseTrafficChannelFromGetBwo() quota = %v, want %v", result.Quota, tt.expected.Quota)
				}
				if result.Priorities == nil {
					t.Errorf("ParseTrafficChannelFromGetBwo() priorities should not be nil")
				}
			}
		})
	}
}
