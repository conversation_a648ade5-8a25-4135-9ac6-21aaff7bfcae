/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      interface_processor.go
 *
 * DESCRIPTION :    Processor for CPE_INTERFACE tasks
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: InterfaceProcessor
 *
 * DESCRIPTION:
 *     Processes CPE_INTERFACE type tasks.
 *     Handles interface configuration operations.
 *     Implements working config management design pattern.
 *
 * FIELDS:
 *     logger             - Logger for interface processor operations
 *     localConfigs       - Cache of local interface configurations (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type InterfaceProcessor struct {
	logger             *logger.Logger              // Logger for interface processor operations
	localConfigs       map[string]*InterfaceConfig // Cache of local interface configurations (used for full sync redundant deletion)
	workingConfigs     map[string]*InterfaceConfig // Working cache for operations (can be refreshed during full sync)
	fullSyncInProgress bool                        // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewInterfaceProcessor
 *
 * DESCRIPTION:
 *     Creates a new interface processor instance.
 *     Initializes the local and working configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *InterfaceProcessor - Initialized interface processor
 *****************************************************************************/
func NewInterfaceProcessor(log *logger.Logger) *InterfaceProcessor {
	processor := &InterfaceProcessor{
		logger:             log.WithModule("interface-processor"),
		localConfigs:       make(map[string]*InterfaceConfig),
		workingConfigs:     make(map[string]*InterfaceConfig),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: fetchInterfaceConfigs
 *
 * DESCRIPTION:
 *     Fetches interface configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*InterfaceConfig - Interface configurations by name
 *     error                       - Error if fetch fails
 *****************************************************************************/
func (p *InterfaceProcessor) fetchInterfaceConfigs() (map[string]*InterfaceConfig, error) {
	return GetLocalInterfaceConfigs(p.logger)
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local interface configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *InterfaceProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local interface configurations")

	configs, err := p.fetchInterfaceConfigs()
	if err != nil {
		return err
	}

	// Update local cache (used for full sync redundant deletion)
	p.localConfigs = configs

	p.logger.Debug("refreshed local interface configurations",
		zap.Int("interfaces", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working interface configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *InterfaceProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working interface configurations")

	configs, err := p.fetchInterfaceConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working cache (used for all operations)
	p.workingConfigs = configs

	p.logger.Debug("refreshed working interface configurations",
		zap.Int("interfaces", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like configuration changes, verification, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *InterfaceProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (p *InterfaceProcessor) StartFullSync() error {
	p.logger.Info("starting full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	err := p.refreshLocalConfigs()
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up any remaining resources.
 *****************************************************************************/
func (p *InterfaceProcessor) EndFullSync() {
	p.logger.Info("ending full synchronization")

	// Process remaining interfaces in local configuration
	// These are interfaces that were not included in the full sync
	// and should be reset to default values
	if len(p.localConfigs) > 0 {
		p.logger.Info("cleaning up remaining interfaces",
			zap.Int("count", len(p.localConfigs)))

		for ifName := range p.localConfigs {
			// Create a delete task for this interface
			deleteTask := &pb.InterfaceTask{
				Name: ifName,
			}

			// Reset the interface to default values
			p.logger.Info("resetting interface to default values",
				zap.String("name", ifName))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("failed to reset interface",
					zap.String("name", ifName),
					zap.Error(err))
			}
		}
	}

	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*InterfaceConfig)
	p.workingConfigs = make(map[string]*InterfaceConfig)
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the type of task this processor can handle.
 *
 * RETURNS:
 *     pb.TaskType - Type of task (TASK_INTERFACE)
 *****************************************************************************/
func (p *InterfaceProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_INTERFACE
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes an interface task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *InterfaceProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get interface task data
	interfaceTask := task.GetInterfaceTask()
	if interfaceTask == nil {
		return "Interface task data is empty", fmt.Errorf("interface task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "interface", configIdentifier, p.logger)

	// Log task start with additional context
	mixModeValue := false
	if interfaceTask.MixMode != nil {
		mixModeValue = *interfaceTask.MixMode
	}

	lagroupValue := uint32(0)
	if interfaceTask.LaGroup != nil {
		lagroupValue = *interfaceTask.LaGroup
	}

	peerValue := ""
	if interfaceTask.Peer != nil {
		peerValue = *interfaceTask.Peer
	}

	taskLogCtx.LogTaskStart(
		zap.String("mode", interfaceTask.Mode.String()),
		zap.String("zone", interfaceTask.Zone.String()),
		zap.Bool("mix_mode", mixModeValue),
		zap.Uint32("lagroup", lagroupValue),
		zap.String("peer", peerValue),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, interfaceTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, interfaceTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles interface configuration changes (both new and edit operations).
 *     Configures a network interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *     Supports bridge configuration and link aggregation features.
 *
 * PARAMETERS:
 *     ctx           - Context for the operation
 *     interfaceTask - Interface task containing configuration details
 *     taskAction    - Task action type (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *InterfaceProcessor) handleConfigChange(ctx context.Context, interfaceTask *pb.InterfaceTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	expectedConfig, err := ConvertInterfaceTaskToConfig(interfaceTask)
	if err != nil {
		p.logger.Error("failed to convert interface task to config",
			zap.String("name", interfaceTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert interface configuration: %v", err), err
	}

	p.logger.Info("Processing interface configuration",
		zap.String("name", expectedConfig.Name),
		zap.Int("mode", expectedConfig.Mode),
		zap.String("zone", expectedConfig.Zone),
		zap.Int("mix_mode", expectedConfig.MixMode),
		zap.Int("lagroup", expectedConfig.Lagroup),
		zap.String("peer", expectedConfig.Peer),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Validate required fields using converted data
	if expectedConfig.Name == "" {
		return "Interface name is required", fmt.Errorf("interface name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := expectedConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}
	// Check if interface exists in working configuration
	workingConfig, exists := p.workingConfigs[expectedConfig.Name]

	// If interface exists and configurations match, no need to modify
	if exists && CompareInterfaceConfig(p.logger, expectedConfig, workingConfig) {
		// Log that configuration already matches
		p.logger.Info("Interface configuration already matches, no changes needed",
			zap.String("name", expectedConfig.Name),
			zap.Int("mode", expectedConfig.Mode),
			zap.String("zone", expectedConfig.Zone),
			zap.Int("mix_mode", expectedConfig.MixMode))

		return "Interface configuration already matches, no changes needed", nil
	}

	// Build floweye command arguments using converted data structure
	cmdArgs, err := BuildInterfaceCommand(expectedConfig)
	if err != nil {
		return fmt.Sprintf("Failed to build interface command: %v", err), err
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command for interface configuration",
		zap.String("name", expectedConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for interface configuration",
			zap.String("name", expectedConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure interface: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", expectedConfig.Name),
		zap.String("output", output))

	// Configure LACP if link aggregation is enabled
	if expectedConfig.Lagroup > 0 && expectedConfig.LacpConfig != nil {
		lacpErr := p.configureLacpData(ctx, expectedConfig)
		if lacpErr != nil {
			p.logger.Error("failed to configure LACP",
				zap.Error(lacpErr))
			return fmt.Sprintf("Failed to configure LACP: %v", lacpErr), lacpErr
		}
	}

	// Refresh working configs to include the newly created/updated interface
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful
	}

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyInterfaceConfig(p.logger, expectedConfig)
	if verifyErr != nil {
		p.logger.Error("failed to verify interface configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify interface configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("interface configuration verification failed")
		return "Interface configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("interface configured successfully", zap.String("output", output))
	return "Interface configuration applied successfully", nil
}

/*****************************************************************************
 * NAME: configureLacpData
 *
 * DESCRIPTION:
 *     Configures LACP settings for link aggregation using unified internal data structure.
 *     Executes the floweye lacp set command with the specified parameters.
 *     Uses converted data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     ctx    - Context for the operation
 *     config - Interface configuration containing LACP settings
 *
 * RETURNS:
 *     error - Error if configuration fails
 *****************************************************************************/
func (p *InterfaceProcessor) configureLacpData(ctx context.Context, config *InterfaceConfig) error {
	if config.Lagroup == 0 || config.LacpConfig == nil {
		return fmt.Errorf("LACP configuration not needed for lagroup=0 or missing LACP config")
	}

	// Build floweye lacp set command using converted data structure
	cmdArgs, err := BuildLacpCommand(config)
	if err != nil {
		return fmt.Errorf("failed to build LACP command: %w", err)
	}

	// Execute floweye lacp command
	p.logger.Info("Executing floweye LACP command",
		zap.Int("lag", config.Lagroup),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye LACP command",
			zap.Int("lag", config.Lagroup),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to configure LACP: %w", err)
	}

	p.logger.Debug("Floweye LACP command executed successfully",
		zap.Int("lag", config.Lagroup),
		zap.String("output", output))
	return nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles interface configuration deletion tasks.
 *     Note: In the current implementation, network interfaces cannot be deleted,
 *     but their configuration can be reset to default values.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx           - Context for the operation
 *     interfaceTask - Interface task containing configuration to delete
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *InterfaceProcessor) handleDeleteConfig(ctx context.Context, interfaceTask *pb.InterfaceTask) (string, error) {
	p.logger.Info("handling delete interface config",
		zap.String("name", interfaceTask.Name),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields
	if interfaceTask.Name == "" {
		return "Interface name is required", fmt.Errorf("interface name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := interfaceTask.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// For physical interfaces, we can't delete them, but we can reset their configuration
	// Set to default values (mode=0, zone=inside, mixmode=0, lagroup=0, peer=none)
	cmdArgs := []string{
		"if", "set",
		"name=" + interfaceTask.Name,
		"mode=0",
		"zone=inside",
		"lagroup=0",
		"mixmode=0",
	}

	// Execute floweye command
	p.logger.Info("resetting interface configuration", zap.Strings("args", cmdArgs))
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to reset interface configuration",
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to reset interface configuration: %v", err), err
	}

	// Verify configuration was reset correctly using default configuration data
	defaultConfig := &InterfaceConfig{
		Name:    interfaceTask.Name,
		Mode:    0,        // Monitor mode
		Zone:    "inside", // Default zone
		MixMode: 0,        // Default mixmode
		Lagroup: 0,        // No aggregation
		Peer:    "none",   // No peer
		LacpConfig: &LacpConfig{
			Lag:     0,
			Enable:  0,
			Timeout: 0,
			Passive: 0,
		},
	}

	success, verifyErr := VerifyInterfaceConfig(p.logger, defaultConfig)
	if verifyErr != nil {
		p.logger.Error("failed to verify interface configuration reset",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify interface configuration reset: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("interface configuration reset verification failed")
		return "Interface configuration reset verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("interface configuration reset successfully", zap.String("output", output))
	return "Interface configuration reset to default values", nil
}
