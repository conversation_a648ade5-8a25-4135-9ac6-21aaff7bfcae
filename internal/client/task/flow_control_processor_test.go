/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      flow_control_processor_test.go
 *
 * DESCRIPTION :    Unit tests for flow control processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"os"
	"strings"
	"testing"

	"agent/internal/logger"
	pb "agent/internal/pb"

	"github.com/stretchr/testify/assert"
)

// setupFlowControlTestLogger creates a test logger for flow control processor tests
func setupFlowControlTestLogger() *logger.Logger {
	config := logger.Config{
		Level:      "debug",
		Format:     "console",
		OutputPath: "stdout",
	}
	log, _ := logger.NewLogger(config)
	return log
}

// containsAny checks if any of the substrings exist in the main string
func containsAnyFlowControl(s string, substrings []string) bool {
	for _, substr := range substrings {
		if strings.Contains(s, substr) {
			return true
		}
	}
	return false
}

// TestFlowControlProcessor_GetTaskType tests the GetTaskType method
func TestFlowControlProcessor_GetTaskType(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_FLOW_CONTROL, taskType)
}

// TestFlowControlProcessor_StartFullSync tests full sync initialization
func TestFlowControlProcessor_StartFullSync(t *testing.T) {
	// Skip if running in CI environment or if floweye command is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Test starting full sync
	err := processor.StartFullSync()

	// In test environment, this might fail due to missing floweye command
	// We'll check if the error is due to missing command and skip if so
	if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
		t.Skip("Skipping test because floweye command is not available")
	}

	// If no error or different error, verify the state
	if err == nil {
		assert.True(t, processor.fullSyncInProgress)
	}
}

// TestFlowControlProcessor_EndFullSync tests full sync cleanup
func TestFlowControlProcessor_EndFullSync(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Set up initial state
	processor.fullSyncInProgress = true
	processor.localPolicyGroups["test_group"] = &PolicyGroupConfig{
		Name: "test_group",
		ID:   1,
	}
	processor.localPolicies[100] = &PolicyConfig{
		Cookie: 100,
		Desc:   "test_policy",
	}

	// Test ending full sync
	processor.EndFullSync()

	// Verify state after cleanup
	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localPolicyGroups)
	assert.Empty(t, processor.localPolicies)
	assert.Empty(t, processor.groupNameToID)
}

// TestFlowControlProcessor_ProcessTask_InvalidPayload tests task processing with invalid payload
func TestFlowControlProcessor_ProcessTask_InvalidPayload(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Test with nil flow control task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_FLOW_CONTROL,
		TaskAction: pb.TaskAction_NEW_CONFIG,
	}

	result, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, result, "Flow control task payload is nil")
}

// TestFlowControlProcessor_ProcessTask_PolicyGroup tests policy group task processing
func TestFlowControlProcessor_ProcessTask_PolicyGroup(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Create a policy group task
	policyGroupTask := &pb.FlowControlPolicyGroupTask{
		Name:      "test_group",
		Disable:   false,
		Stop:      false,
		TimeRange: &pb.TimeSpec{
			StartDay:  1,
			EndDay:    7,
			StartTime: &pb.DailyTime{Hour: 0, Min: 0, Sec: 0},
			EndTime:   &pb.DailyTime{Hour: 23, Min: 59, Sec: 59},
		},
	}

	flowControlTask := &pb.FlowControlTask{
		TaskConfig: &pb.FlowControlTask_PolicyGroup{
			PolicyGroup: policyGroupTask,
		},
	}

	task := &pb.DeviceTask{
		TaskType:        pb.TaskType_TASK_FLOW_CONTROL,
		TaskAction:      pb.TaskAction_NEW_CONFIG,
		FlowControlTask: flowControlTask,
	}

	// This will likely fail in test environment due to missing floweye command
	// but we can test the basic flow
	result, err := processor.ProcessTask(context.Background(), task)

	// Check if error is due to missing floweye command
	if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
		t.Skip("Skipping test because floweye command is not available")
	}

	// If we get here, either the command succeeded or failed for other reasons
	assert.NotEmpty(t, result)
}

// TestFlowControlProcessor_ProcessTask_Policy tests policy task processing
func TestFlowControlProcessor_ProcessTask_Policy(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Create a policy task
	policyTask := &pb.PolicyTask{
		Cookie:    100,
		Desc:      "test_policy",
		GroupName: "test_group",
		Disable:   false,
		Action:    pb.FlowControlAction_FLOW_CONTROL_ACTION_PERMIT,
		ActionAccept: &pb.ActionAcceptConfig{
			Next: false,
		},
	}

	flowControlTask := &pb.FlowControlTask{
		TaskConfig: &pb.FlowControlTask_Policy{
			Policy: policyTask,
		},
	}

	task := &pb.DeviceTask{
		TaskType:        pb.TaskType_TASK_FLOW_CONTROL,
		TaskAction:      pb.TaskAction_NEW_CONFIG,
		FlowControlTask: flowControlTask,
	}

	// This will likely fail in test environment due to missing floweye command
	// but we can test the basic flow
	result, err := processor.ProcessTask(context.Background(), task)

	// Check if error is due to missing floweye command
	if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
		t.Skip("Skipping test because floweye command is not available")
	}

	// If we get here, either the command succeeded or failed for other reasons
	assert.NotEmpty(t, result)
}

// TestFlowControlProcessor_ProcessTask_UnknownTaskType tests unknown task type handling
func TestFlowControlProcessor_ProcessTask_UnknownTaskType(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Create a flow control task with no specific task config
	flowControlTask := &pb.FlowControlTask{}

	task := &pb.DeviceTask{
		TaskType:        pb.TaskType_TASK_FLOW_CONTROL,
		TaskAction:      pb.TaskAction_NEW_CONFIG,
		FlowControlTask: flowControlTask,
	}

	result, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, result, "Unknown flow control task type")
}

// TestComparePolicyGroupConfig tests policy group configuration comparison
func TestComparePolicyGroupConfig(t *testing.T) {
	log := setupFlowControlTestLogger()

	// Test cases for policy group comparison
	testCases := []struct {
		name     string
		task     *pb.PolicyGroupTask
		local    *PolicyGroupConfig
		expected bool
	}{
		{
			name: "matching configurations",
			task: &pb.PolicyGroupTask{
				Name:    "test_group",
				Disable: false,
				Stop:    false,
				TimeRange: &pb.TimeSpec{
					StartDay: 1,
					EndDay:   7,
					StartTime: &pb.DailyTime{Hour: 0, Min: 0, Sec: 0},
					EndTime:   &pb.DailyTime{Hour: 23, Min: 59, Sec: 59},
				},
			},
			local: &PolicyGroupConfig{
				Name:    "test_group",
				Disable: false,
				Stop:    false,
				Schedule: &PolicyGroupSchedule{
					Month:     0,
					StartDay:  1,
					EndDay:    7,
					StartHour: 0,
					StartMin:  0,
					StartSec:  0,
					EndHour:   23,
					EndMin:    59,
					EndSec:    59,
				},
			},
			expected: true,
		},
		{
			name: "different names",
			task: &pb.PolicyGroupTask{
				Name:    "test_group1",
				Disable: false,
				Stop:    false,
			},
			local: &PolicyGroupConfig{
				Name:    "test_group2",
				Disable: false,
				Stop:    false,
			},
			expected: false,
		},
		{
			name: "different disable status",
			task: &pb.PolicyGroupTask{
				Name:    "test_group",
				Disable: true,
				Stop:    false,
			},
			local: &PolicyGroupConfig{
				Name:    "test_group",
				Disable: false,
				Stop:    false,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := ComparePolicyGroupConfig(log, tc.task, tc.local)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestComparePolicyConfig tests policy configuration comparison
func TestComparePolicyConfig(t *testing.T) {
	log := setupFlowControlTestLogger()

	// Test cases for policy comparison
	testCases := []struct {
		name     string
		task     *pb.PolicyTask
		local    *PolicyConfig
		expected bool
	}{
		{
			name: "matching permit configurations",
			task: &pb.PolicyTask{
				Cookie:  100,
				Desc:    "test_policy",
				Disable: false,
				Action:  pb.FlowControlAction_FLOW_CONTROL_ACTION_PERMIT,
				ActionAccept: &pb.ActionAcceptConfig{
					Next: false,
				},
			},
			local: &PolicyConfig{
				Cookie:  100,
				Desc:    "test_policy",
				Disable: false,
				Action:  "permit",
				Next:    false,
			},
			expected: true,
		},
		{
			name: "different cookies",
			task: &pb.PolicyTask{
				Cookie:  100,
				Desc:    "test_policy",
				Disable: false,
			},
			local: &PolicyConfig{
				Cookie:  200,
				Desc:    "test_policy",
				Disable: false,
			},
			expected: false,
		},
		{
			name: "different descriptions",
			task: &pb.PolicyTask{
				Cookie:  100,
				Desc:    "test_policy1",
				Disable: false,
			},
			local: &PolicyConfig{
				Cookie:  100,
				Desc:    "test_policy2",
				Disable: false,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := ComparePolicyConfig(log, tc.task, tc.local)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestFlowControlProcessor_CreateDefaultPolicyGroups tests default policy group creation
func TestFlowControlProcessor_CreateDefaultPolicyGroups(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Initialize local policy groups map
	processor.localPolicyGroups = make(map[string]*PolicyGroupConfig)

	// Test creating default policy groups when none exist
	err := processor.createDefaultPolicyGroups()

	// In test environment, this might fail due to missing floweye command
	if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
		t.Skip("Skipping test because floweye command is not available")
	}

	// If no error, verify the method was called (actual creation would require floweye)
	if err == nil {
		t.Log("Default policy groups creation method executed successfully")
	}
}

// TestFlowControlProcessor_GetGroupNameByID tests the helper method for getting group name by ID
func TestFlowControlProcessor_GetGroupNameByID(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Create test data
	currentGroups := []PolicyGroupOrderInfo{
		{ID: 1, Name: "_traffic_mirroring_group"},
		{ID: 2, Name: "_subscription_group"},
		{ID: 3, Name: "test_group"},
	}

	// Test cases
	testCases := []struct {
		name     string
		groupID  int
		expected string
	}{
		{
			name:     "existing group",
			groupID:  2,
			expected: "_subscription_group",
		},
		{
			name:     "non-existing group",
			groupID:  999,
			expected: "",
		},
		{
			name:     "first group",
			groupID:  1,
			expected: "_traffic_mirroring_group",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := processor.getGroupNameByID(tc.groupID, currentGroups)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestFlowControlProcessor_MovePolicyGroupUsingMoveTo_SpecialPrevious tests special previous values
func TestFlowControlProcessor_MovePolicyGroupUsingMoveTo_SpecialPrevious(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Initialize local policy groups map
	processor.localPolicyGroups = make(map[string]*PolicyGroupConfig)

	// Create test data
	currentGroups := []PolicyGroupOrderInfo{
		{ID: 1, Name: "_traffic_mirroring_group"},
		{ID: 2, Name: "_subscription_group"},
		{ID: 3, Name: "existing_group"},
	}

	// Add existing group to local config
	processor.localPolicyGroups["existing_group"] = &PolicyGroupConfig{
		Name: "existing_group",
		ID:   3,
	}

	// Test cases for special previous values
	testCases := []struct {
		name              string
		currentGroupID    int
		previousGroupName string
		shouldSkip        bool
		description       string
	}{
		{
			name:              "previous=append with existing group",
			currentGroupID:    3,
			previousGroupName: "append",
			shouldSkip:        false,
			description:       "Should keep current position for existing group",
		},
		{
			name:              "previous=append with new group",
			currentGroupID:    4,
			previousGroupName: "append",
			shouldSkip:        false,
			description:       "Should keep at end for new group",
		},
		{
			name:              "previous=null",
			currentGroupID:    4,
			previousGroupName: "null",
			shouldSkip:        true, // Skip because it requires floweye command
			description:       "Should move after _subscription_group",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.shouldSkip {
				t.Skip("Skipping test that requires floweye command")
			}

			err := processor.movePolicyGroupUsingMoveTo(tc.currentGroupID, tc.previousGroupName, false, 0, currentGroups)

			// For append cases, should not return error
			if tc.previousGroupName == "append" {
				assert.NoError(t, err, tc.description)
			}
		})
	}
}

// TestDefaultPolicyGroupConstants tests the default policy group constants
func TestDefaultPolicyGroupConstants(t *testing.T) {
	assert.Equal(t, "_traffic_mirroring_group", DefaultTrafficMirroringGroup)
	assert.Equal(t, "_subscription_group", DefaultSubscriptionGroup)
}

// TestFlowControlProcessor_EnsureDefaultGroupsOrdering tests default groups ordering logic
func TestFlowControlProcessor_EnsureDefaultGroupsOrdering(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Test the ordering logic without actual floweye commands
	// This tests the logic flow and error handling
	err := processor.ensureDefaultGroupsOrdering()

	// In test environment, this will fail due to missing floweye command
	if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
		t.Skip("Skipping test because floweye command is not available")
	}

	// If no error, verify the method was called
	if err == nil {
		t.Log("Default groups ordering method executed successfully")
	}
}

// TestFlowControlProcessor_DefaultGroupCreationOrder tests the creation order logic
func TestFlowControlProcessor_DefaultGroupCreationOrder(t *testing.T) {
	log := setupFlowControlTestLogger()
	processor := NewFlowControlProcessor(log)

	// Initialize local policy groups map
	processor.localPolicyGroups = make(map[string]*PolicyGroupConfig)

	// Test case 1: No default groups exist
	t.Run("no_default_groups_exist", func(t *testing.T) {
		// Both groups should be created
		err := processor.createDefaultPolicyGroups()

		// In test environment, this might fail due to missing floweye command
		if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
			t.Skip("Skipping test because floweye command is not available")
		}
	})

	// Test case 2: Traffic mirroring group already exists
	t.Run("traffic_mirroring_exists", func(t *testing.T) {
		processor.localPolicyGroups[DefaultTrafficMirroringGroup] = &PolicyGroupConfig{
			Name: DefaultTrafficMirroringGroup,
			ID:   1,
		}

		err := processor.createDefaultPolicyGroups()

		// In test environment, this might fail due to missing floweye command
		if err != nil && containsAnyFlowControl(err.Error(), []string{"executable file not found", "command not found", "no such file"}) {
			t.Skip("Skipping test because floweye command is not available")
		}
	})

	// Test case 3: Both groups already exist
	t.Run("both_groups_exist", func(t *testing.T) {
		processor.localPolicyGroups[DefaultTrafficMirroringGroup] = &PolicyGroupConfig{
			Name: DefaultTrafficMirroringGroup,
			ID:   1,
		}
		processor.localPolicyGroups[DefaultSubscriptionGroup] = &PolicyGroupConfig{
			Name: DefaultSubscriptionGroup,
			ID:   2,
		}

		err := processor.createDefaultPolicyGroups()

		// Should not return error when both groups exist
		assert.NoError(t, err)
	})
}
