/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      task_logger.go
 *
 * DESCRIPTION :    Unified logging utilities for TaskProcessor modules
 *
 * AUTHOR :         wei
 *
 * HISTORY :        15/07/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"time"

	"agent/internal/logger"
	pb "agent/internal/pb"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: TaskLogContext
 *
 * DESCRIPTION:
 *     Context structure for unified task logging.
 *     Contains all necessary information for consistent logging across TaskProcessor modules.
 *
 * FIELDS:
 *     ModuleName       - TaskProcessor module identifier
 *     TxID             - Transaction ID from TaskTx
 *     ConfigIdentifier - Configuration identifier (name, cookie, etc.)
 *     TaskType         - Type of task being processed
 *     TaskAction       - Action being performed (NEW_CONFIG, EDIT_CONFIG, DELETE_CONFIG)
 *     StartTime        - Task processing start time
 *     Logger           - Logger instance for the module
 *****************************************************************************/
type TaskLogContext struct {
	ModuleName       string
	TxID             string
	ConfigIdentifier string
	TaskType         pb.TaskType
	TaskAction       pb.TaskAction
	StartTime        time.Time
	Logger           *logger.Logger
}

/*****************************************************************************
 * NAME: TaskResult
 *
 * DESCRIPTION:
 *     Enumeration for task processing results
 *****************************************************************************/
type TaskResult string

const (
	TaskResultSuccess TaskResult = "SUCCESS"
	TaskResultFailed  TaskResult = "FAILED"
)

/*****************************************************************************
 * NAME: NewTaskLogContext
 *
 * DESCRIPTION:
 *     Creates a new TaskLogContext for unified logging.
 *     Extracts transaction ID from context and configuration identifier from task.
 *     Uses the existing logger architecture with module-specific loggers.
 *
 * PARAMETERS:
 *     ctx              - Context containing transaction information
 *     task             - Device task being processed
 *     moduleName       - Name of the TaskProcessor module
 *     configIdentifier - Configuration identifier (name, cookie, etc.)
 *     logger           - Logger instance (should already be module-specific via WithModule)
 *
 * RETURNS:
 *     *TaskLogContext - New task log context
 *****************************************************************************/
func NewTaskLogContext(ctx context.Context, task *pb.DeviceTask, moduleName, configIdentifier string, logger *logger.Logger) *TaskLogContext {
	// Extract transaction ID from context if available
	txID := "unknown"
	if txIDValue := ctx.Value("tx_id"); txIDValue != nil {
		if txIDStr, ok := txIDValue.(string); ok {
			txID = txIDStr
		}
	}

	return &TaskLogContext{
		ModuleName:       moduleName,
		TxID:             txID,
		ConfigIdentifier: configIdentifier,
		TaskType:         task.TaskType,
		TaskAction:       task.TaskAction,
		StartTime:        time.Now(),
		Logger:           logger, // Use the module-specific logger passed from processor
	}
}

/*****************************************************************************
 * NAME: LogTaskStart
 *
 * DESCRIPTION:
 *     Logs the start of task processing with unified format.
 *     Records all essential information for debugging and tracking.
 *
 * PARAMETERS:
 *     additionalFields - Optional additional zap fields for specific context
 *****************************************************************************/
func (tlc *TaskLogContext) LogTaskStart(additionalFields ...zap.Field) {
	fields := []zap.Field{
		zap.String("module", tlc.ModuleName),
		zap.String("txid", tlc.TxID),
		zap.String("config", tlc.ConfigIdentifier),
		zap.String("task_type", tlc.TaskType.String()),
		zap.String("task_action", tlc.TaskAction.String()),
		zap.String("result", "STARTED"),
		zap.Time("timestamp", tlc.StartTime),
	}
	fields = append(fields, additionalFields...)

	tlc.Logger.Info("TaskProcessor | Task started", fields...)
}

/*****************************************************************************
 * NAME: LogTaskEnd
 *
 * DESCRIPTION:
 *     Logs the completion of task processing with unified format.
 *     Records result, duration, and any error information.
 *
 * PARAMETERS:
 *     result           - Task processing result (SUCCESS/FAILED)
 *     err              - Error if task failed (nil for success)
 *     additionalFields - Optional additional zap fields for specific context
 *****************************************************************************/
func (tlc *TaskLogContext) LogTaskEnd(result TaskResult, err error, additionalFields ...zap.Field) {
	endTime := time.Now()
	duration := endTime.Sub(tlc.StartTime)

	fields := []zap.Field{
		zap.String("module", tlc.ModuleName),
		zap.String("txid", tlc.TxID),
		zap.String("config", tlc.ConfigIdentifier),
		zap.String("task_type", tlc.TaskType.String()),
		zap.String("task_action", tlc.TaskAction.String()),
		zap.String("result", string(result)),
		zap.Duration("duration", duration),
		zap.Time("timestamp", endTime),
	}

	if err != nil {
		fields = append(fields, zap.Error(err))
	}
	fields = append(fields, additionalFields...)

	if result == TaskResultSuccess {
		tlc.Logger.Info("TaskProcessor | Task completed", fields...)
	} else {
		tlc.Logger.Error("TaskProcessor | Task failed", fields...)
	}
}

/*****************************************************************************
 * NAME: LogTaskProgress
 *
 * DESCRIPTION:
 *     Logs intermediate progress during task processing.
 *     Useful for tracking long-running operations or debugging.
 *
 * PARAMETERS:
 *     message          - Progress message
 *     additionalFields - Optional additional zap fields for specific context
 *****************************************************************************/
func (tlc *TaskLogContext) LogTaskProgress(message string, additionalFields ...zap.Field) {
	fields := []zap.Field{
		zap.String("module", tlc.ModuleName),
		zap.String("txid", tlc.TxID),
		zap.String("config", tlc.ConfigIdentifier),
		zap.String("message", message),
		zap.Time("timestamp", time.Now()),
	}
	fields = append(fields, additionalFields...)

	tlc.Logger.Debug("TaskProcessor | Progress", fields...)
}

/*****************************************************************************
 * NAME: GetConfigIdentifier
 *
 * DESCRIPTION:
 *     Extracts configuration identifier from different task types.
 *     Provides a unified way to get the primary identifier for logging.
 *
 * PARAMETERS:
 *     task - Device task to extract identifier from
 *
 * RETURNS:
 *     string - Configuration identifier (name, cookie, etc.)
 *****************************************************************************/
func GetConfigIdentifier(task *pb.DeviceTask) string {
	switch task.TaskType {
	case pb.TaskType_TASK_INTERFACE:
		if interfaceTask := task.GetInterfaceTask(); interfaceTask != nil {
			return interfaceTask.GetName()
		}
	case pb.TaskType_TASK_WAN:
		if wanTask := task.GetWanTask(); wanTask != nil {
			return wanTask.GetName()
		}
	case pb.TaskType_TASK_LAN:
		if lanTask := task.GetLanTask(); lanTask != nil {
			return lanTask.GetName()
		}
	case pb.TaskType_TASK_DHCP:
		if dhcpTask := task.GetDhcpTask(); dhcpTask != nil {
			return dhcpTask.GetName()
		}
	case pb.TaskType_TASK_WAN_GROUP:
		if wanGroupTask := task.GetWanGroupTask(); wanGroupTask != nil {
			return wanGroupTask.GetName()
		}
	case pb.TaskType_TASK_USER_GROUP:
		if userGroupTask := task.GetUserGroupTask(); userGroupTask != nil {
			return userGroupTask.GetName()
		}
	case pb.TaskType_TASK_USER:
		if userTask := task.GetUserTask(); userTask != nil {
			return userTask.GetName()
		}
	case pb.TaskType_TASK_IWAN_PROXY:
		if iwanProxyTask := task.GetIwanProxyTask(); iwanProxyTask != nil {
			return iwanProxyTask.GetName()
		}
	case pb.TaskType_TASK_IWAN_SERVICE:
		if iwanServiceTask := task.GetIwanServiceTask(); iwanServiceTask != nil {
			return iwanServiceTask.GetName()
		}
	case pb.TaskType_TASK_IWAN_MAPPING:
		if iwanMappingTask := task.GetIwanMappingTask(); iwanMappingTask != nil {
			return fmt.Sprintf("%s:%d", iwanMappingTask.GetProxy(), iwanMappingTask.GetPort())
		}
	case pb.TaskType_TASK_SR_PROXY:
		if srProxyTask := task.GetSrProxyTask(); srProxyTask != nil {
			return srProxyTask.GetName()
		}
	case pb.TaskType_TASK_IP_GROUP:
		if ipGroupTask := task.GetIpGroupTask(); ipGroupTask != nil {
			return ipGroupTask.GetName()
		}
	case pb.TaskType_TASK_DOMAIN_GROUP:
		if domainGroupTask := task.GetDomainGroupTask(); domainGroupTask != nil {
			return domainGroupTask.GetName()
		}
	case pb.TaskType_TASK_EFFECTIVE_TIME:
		if effectiveTimeTask := task.GetEffectiveTimeTask(); effectiveTimeTask != nil {
			return effectiveTimeTask.GetName()
		}
	case pb.TaskType_TASK_TRAFFIC_CHANNEL:
		if trafficChannelTask := task.GetTrafficChannelTask(); trafficChannelTask != nil {
			return trafficChannelTask.GetName()
		}
	case pb.TaskType_TASK_TRAFFIC_STAT:
		if trafficStatTask := task.GetTrafficStatTask(); trafficStatTask != nil {
			return trafficStatTask.GetName()
		}
	case pb.TaskType_TASK_FLOW_CONTROL:
		if flowControlTask := task.GetFlowControlTask(); flowControlTask != nil {
			// FlowControlTask has oneof task_config, need to check which type
			if policyGroup := flowControlTask.GetPolicyGroup(); policyGroup != nil {
				return fmt.Sprintf("group_%s", policyGroup.GetName())
			}
			if policy := flowControlTask.GetPolicy(); policy != nil {
				return fmt.Sprintf("policy_cookie_%d", policy.GetCookie())
			}
			return "flow_control_unknown"
		}
	case pb.TaskType_TASK_ROUTE_POLICY:
		if routePolicyTask := task.GetRoutePolicyTask(); routePolicyTask != nil {
			return fmt.Sprintf("cookie_%d", routePolicyTask.GetCookie())
		}
	case pb.TaskType_TASK_DNS_POLICY:
		if dnsPolicyTask := task.GetDnsPolicyTask(); dnsPolicyTask != nil {
			return fmt.Sprintf("cookie_%d", dnsPolicyTask.GetCookie())
		}
	case pb.TaskType_TASK_DNS_TRACKING_POLICY:
		if dnsTrackingTask := task.GetDnsTrackingPolicyTask(); dnsTrackingTask != nil {
			return fmt.Sprintf("cookie_%d", dnsTrackingTask.GetCookie())
		}
	}
	return "unknown"
}
