/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      domain_group_processor.go
 *
 * DESCRIPTION :    Domain Group task processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/20/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"os"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

// Override utils.ExecuteCommand for testing
var executeCommand = func(logger *logger.Logger, timeout int, command string, args ...string) (string, error) {
	return utils.ExecuteCommand(logger, timeout, command, args...)
}

/*****************************************************************************
 * NAME: DomainGroupProcessor
 *
 * DESCRIPTION:
 *     Processes tasks of type TASK_DOMAIN_GROUP.
 *     Handles Domain Group configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for the Domain Group processor
 *     localConfigs       - Local Domain Group configuration cache (used for full sync redundant deletion)
 *     localNameToID      - Map of Domain Group names to IDs (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     workingNameToID    - Working map of Domain Group names to IDs
 *     fullSyncInProgress - Flag indicating whether a full sync is in progress
 *****************************************************************************/
type DomainGroupProcessor struct {
	logger             *logger.Logger                // Logger for the Domain Group processor
	localConfigs       map[string]*DomainGroupConfig // Local Domain Group configuration cache (used for full sync redundant deletion)
	localNameToID      map[string]int                // Map of Domain Group names to IDs (used for full sync redundant deletion)
	workingConfigs     map[string]*DomainGroupConfig // Working cache for operations (can be refreshed during full sync)
	workingNameToID    map[string]int                // Working map of Domain Group names to IDs
	fullSyncInProgress bool                          // Flag indicating whether a full sync is in progress
}

/*****************************************************************************
 * NAME: NewDomainGroupProcessor
 *
 * DESCRIPTION:
 *     Creates a new Domain Group processor instance.
 *     Initializes the local configuration cache and working configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *DomainGroupProcessor - Initialized Domain Group processor
 *****************************************************************************/
func NewDomainGroupProcessor(log *logger.Logger) *DomainGroupProcessor {
	processor := &DomainGroupProcessor{
		logger:             log.WithModule("domain-group-processor"),
		localConfigs:       make(map[string]*DomainGroupConfig),
		localNameToID:      make(map[string]int),
		workingConfigs:     make(map[string]*DomainGroupConfig),
		workingNameToID:    make(map[string]int),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the type of task this processor can handle.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - Type of task (TASK_DOMAIN_GROUP)
 *****************************************************************************/
func (p *DomainGroupProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_DOMAIN_GROUP
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a Domain Group task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DomainGroupProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Extract Domain Group task
	domainGroupTask := task.GetDomainGroupTask()
	if domainGroupTask == nil {
		return "Domain Group task data is empty", fmt.Errorf("Domain Group task data is empty")
	}

	// Log task details
	p.logger.Info("Processing Domain Group task",
		zap.String("name", domainGroupTask.GetName()),
		zap.String("action", task.TaskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Execute different processing based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handleConfigChange(ctx, domainGroupTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeleteConfig(ctx, domainGroupTask)
	default:
		return fmt.Sprintf("Unsupported task action: %s", task.TaskAction.String()),
			fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
	}
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles both NEW_CONFIG and EDIT_CONFIG operations for Domain Group tasks.
 *     Creates a new Domain Group if it doesn't exist, or updates an existing one.
 *
 * PARAMETERS:
 *     ctx             - Context for the operation
 *     domainGroupTask - Domain Group task to process
 *     taskAction      - Task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DomainGroupProcessor) handleConfigChange(ctx context.Context, domainGroupTask *pb.DomainGroupTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertDomainGroupTaskToConfig(domainGroupTask)
	if err != nil {
		p.logger.Error("failed to convert domain group task to config data",
			zap.String("name", domainGroupTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert domain group configuration: %v", err), err
	}

	p.logger.Info("Handling Domain Group configuration change",
		zap.String("name", configData.Name),
		zap.String("action", taskAction.String()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields
	if configData.Name == "" {
		return "Domain Group name is required", fmt.Errorf("Domain Group name is required")
	}

	// Register cleanup defer immediately after validation during full sync
	if p.fullSyncInProgress {
		name := configData.Name // Copy value to avoid closure capture reference issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Get Domain Group ID
	groupId, err := GetDomainGroupIdByName(p.logger, configData.Name)
	if err != nil {
		return fmt.Sprintf("Failed to get Domain Group ID: %v", err), err
	}

	// Check if Domain Group exists
	_, exists := p.workingConfigs[configData.Name]

	// If Domain Group doesn't exist, create it
	if !exists || groupId == -1 {
		// Create new Domain Group
		p.logger.Info("Creating new Domain Group", zap.String("name", configData.Name))

		cmdArgs := []string{
			"dns", "addgrp",
			configData.Name,
		}

		// Execute floweye command
		output, err := executeCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to execute floweye command to create Domain Group",
				zap.String("name", configData.Name),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to create Domain Group: %v", err), err
		}

		// Get the new Domain Group ID
		groupId, err = GetDomainGroupIdByName(p.logger, configData.Name)
		if err != nil {
			return fmt.Sprintf("Failed to get new Domain Group ID: %v", err), err
		}

		if groupId == -1 {
			return "Failed to create Domain Group: ID not found after creation",
				fmt.Errorf("ID not found after creation")
		}

		p.logger.Info("Successfully created Domain Group",
			zap.String("name", configData.Name),
			zap.Int("id", groupId))
	} else {
		// Domain Group already exists, always update with full replacement
		// No need to compare configurations since we use full replacement strategy
		p.logger.Info("Domain Group exists, will update members using full replacement",
			zap.String("name", configData.Name),
			zap.Int("id", groupId))
	}

	// Add Domain Group members using converted data
	var content []byte
	var source string

	if len(configData.Domains) > 0 {
		// Convert domain list to content
		content = []byte(strings.Join(configData.Domains, "\n"))
		source = "domain list"
	} else {
		// Empty content for empty group
		content = []byte("")
		source = "empty group"
	}
	// Add members using content
	return p.addMembersFromContent(ctx, configData, groupId, content, source)
}

/*****************************************************************************
 * NAME: addMembersFromContent
 *
 * DESCRIPTION:
 *     Adds Domain Group members from content.
 *     Creates a temporary file and uses the floweye dns loadfile command.
 *
 * PARAMETERS:
 *     ctx             - Context for the operation
 *     domainGroupTask - Domain Group task to process
 *     groupId         - ID of the Domain Group
 *     content         - Content to write to the file
 *     source          - Source of the content (for logging)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DomainGroupProcessor) addMembersFromContent(ctx context.Context, configData *DomainGroupConfig, groupId int, content []byte, source string) (string, error) {
	p.logger.Info("Adding Domain Group members from "+source,
		zap.String("name", configData.Name),
		zap.Int("id", groupId),
		zap.Int("content_size", len(content)))

	// Create temporary file in /tmp with Domain group name as prefix
	tempFileName := fmt.Sprintf("/tmp/%s_domaingroup.txt", configData.Name)
	err := os.WriteFile(tempFileName, content, 0644)
	// Ensure the temporary file is removed when done
	defer os.Remove(tempFileName)
	if err != nil {
		p.logger.Error("Failed to write temporary file", zap.Error(err))
		return fmt.Sprintf("Failed to write temporary file: %v", err), err
	}

	// Use floweye command to load file
	cmdArgs := []string{
		"dns", "loadfile",
		fmt.Sprintf("%d", groupId),
		tempFileName,
	}

	// Execute floweye command
	output, err := executeCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command to load file",
			zap.String("name", configData.Name),
			zap.Int("id", groupId),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to load Domain Group members file: %v", err), err
	}

	// Verify if configuration was applied successfully
	success, verifyErr := VerifyDomainGroupConfig(p.logger, configData, groupId)
	if verifyErr != nil {
		p.logger.Error("Failed to verify Domain Group configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify Domain Group configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("Domain Group configuration verification failed")
		return "Domain Group configuration verification failed", fmt.Errorf("verification failed")
	}

	// Refresh working configs to include the updated configuration
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful
	}

	p.logger.Info("Successfully added Domain Group members from "+source,
		zap.String("name", configData.Name),
		zap.Int("id", groupId))

	return "Successfully added Domain Group members from " + source, nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles DELETE_CONFIG operations for Domain Group tasks.
 *     Deletes an existing Domain Group.
 *
 * PARAMETERS:
 *     ctx             - Context for the operation
 *     domainGroupTask - Domain Group task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DomainGroupProcessor) handleDeleteConfig(ctx context.Context, domainGroupTask *pb.DomainGroupTask) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	configData, err := ConvertDomainGroupTaskToConfig(domainGroupTask)
	if err != nil {
		p.logger.Error("failed to convert domain group task to config data",
			zap.String("name", domainGroupTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert domain group configuration: %v", err), err
	}

	p.logger.Info("Handling Domain Group deletion",
		zap.String("name", configData.Name))

	// Validate required fields
	if configData.Name == "" {
		return "Domain Group name is required", fmt.Errorf("Domain Group name is required")
	}

	// Register cleanup defer immediately after validation during full sync
	if p.fullSyncInProgress {
		name := configData.Name // Copy value to avoid closure capture reference issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Get Domain Group ID
	groupId, err := GetDomainGroupIdByName(p.logger, configData.Name)
	if err != nil {
		return fmt.Sprintf("Failed to get Domain Group ID: %v", err), err
	}

	// Check if Domain Group exists
	_, exists := p.workingConfigs[configData.Name]

	// If Domain Group doesn't exist, no need to delete
	if !exists || groupId == -1 {
		p.logger.Info("Domain Group does not exist, no need to delete",
			zap.String("name", configData.Name))
		return "Domain Group does not exist, no need to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"dns", "rmvgrp",
		fmt.Sprintf("%d", groupId),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete Domain Group",
		zap.String("name", configData.Name),
		zap.Int("id", groupId),
		zap.Strings("args", cmdArgs))

	output, err := executeCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Domain Group already does not exist, treating as successful delete",
				zap.String("name", configData.Name),
				zap.Int("id", groupId))
		} else {
			p.logger.Error("Failed to execute floweye command to delete Domain Group",
				zap.String("name", configData.Name),
				zap.Int("id", groupId),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete Domain Group: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", configData.Name),
		zap.Int("id", groupId),
		zap.String("output", output))

	// Verify if Domain Group was deleted by refreshing working configs
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Error("Failed to refresh configurations after deletion",
			zap.Error(err))
		return "Failed to verify Domain Group deletion", err
	}

	// Check if Domain Group still exists
	if _, exists := p.workingConfigs[configData.Name]; exists {
		p.logger.Error("Domain Group still exists after deletion",
			zap.String("name", configData.Name))
		return "Domain Group still exists after deletion", fmt.Errorf("deletion failed")
	}

	p.logger.Info("Successfully deleted Domain Group",
		zap.String("name", configData.Name))

	return "Successfully deleted Domain Group", nil
}

/*****************************************************************************
 * NAME: fetchDomainGroupConfigs
 *
 * DESCRIPTION:
 *     Fetches Domain Group configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*DomainGroupConfig - Domain Group configs by name
 *     map[string]int                - Domain Group name to ID mapping
 *     error                         - Error if fetch fails
 *****************************************************************************/
func (p *DomainGroupProcessor) fetchDomainGroupConfigs() (map[string]*DomainGroupConfig, map[string]int, error) {
	p.logger.Debug("Fetching Domain Group configurations from floweye")

	configs, err := GetDomainGroupConfigs(p.logger)
	if err != nil {
		p.logger.Error("Failed to get Domain Group configurations", zap.Error(err))
		return nil, nil, err
	}

	// Build name to ID mapping
	nameToID := make(map[string]int)
	for name, config := range configs {
		nameToID[name] = config.ID
	}

	p.logger.Debug("Domain Group configurations fetched",
		zap.Int("count", len(configs)))

	return configs, nameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local Domain Group configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *DomainGroupProcessor) refreshLocalConfigs() error {
	p.logger.Debug("Refreshing local Domain Group configurations")

	configs, nameToID, err := p.fetchDomainGroupConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs
	p.localNameToID = nameToID

	p.logger.Debug("Local Domain Group configurations refreshed",
		zap.Int("count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working Domain Group configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *DomainGroupProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("Refreshing working Domain Group configurations")

	configs, nameToID, err := p.fetchDomainGroupConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs
	p.workingNameToID = nameToID

	p.logger.Debug("Refreshed working Domain Group configurations",
		zap.Int("count", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like creation, update, deletion, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *DomainGroupProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts the full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *DomainGroupProcessor) StartFullSync() error {
	p.logger.Info("Starting Domain Group full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends the full synchronization process.
 *     Deletes any Domain Groups that were not included in the sync.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *DomainGroupProcessor) EndFullSync() {
	p.logger.Info("Ending Domain Group full synchronization")

	// If full sync is not in progress, nothing to do
	if !p.fullSyncInProgress {
		p.logger.Warn("EndFullSync called but full sync is not in progress")
		return
	}

	// Delete any Domain Groups that were not included in the sync
	for name, config := range p.localConfigs {
		p.logger.Info("Deleting Domain Group not included in full sync",
			zap.String("name", name),
			zap.Int("id", config.ID))

		// Build floweye command
		cmdArgs := []string{
			"dns", "rmvgrp",
			fmt.Sprintf("%d", config.ID),
		}

		// Execute floweye command
		output, err := executeCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to execute floweye command to delete Domain Group",
				zap.String("name", name),
				zap.Int("id", config.ID),
				zap.Error(err),
				zap.String("output", output))
			continue
		}

		p.logger.Debug("Floweye command executed successfully",
			zap.String("name", name),
			zap.Int("id", config.ID),
			zap.String("output", output))
	}

	// Reset state and clear all configurations
	p.fullSyncInProgress = false
	p.localConfigs = make(map[string]*DomainGroupConfig)
	p.localNameToID = make(map[string]int)
	p.workingConfigs = make(map[string]*DomainGroupConfig)
	p.workingNameToID = make(map[string]int)
}
