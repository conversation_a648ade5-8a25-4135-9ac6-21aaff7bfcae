/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_policy_processor.go
 *
 * DESCRIPTION :    DNS policy processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        17/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: DnsPolicyProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_DNS_POLICY type tasks.
 *     Handles DNS policy configuration operations including creation,
 *     modification, deletion, and ordering management.
 *     Implements working config management design pattern.
 *
 * FIELDS:
 *     logger               - Logger for DNS policy processor operations
 *     localConfigs         - Cache of local DNS policy configurations (used for full sync redundant deletion)
 *     fullSyncInProgress   - Flag indicating if full sync is in progress
 *     cookieToID           - Map of policy cookies to local IDs (used for full sync redundant deletion)
 *     workingConfigs       - Working cache for operations (can be refreshed during full sync)
 *     workingCookieToID    - Working map of policy cookies to IDs
 *****************************************************************************/
type DnsPolicyProcessor struct {
	logger             *logger.Logger
	localConfigs       map[uint32]*DnsPolicyConfig // Cache of local DNS policy configurations (used for full sync redundant deletion)
	fullSyncInProgress bool                        // Flag for full sync mode
	cookieToID         map[uint32]int              // Map of cookies to local IDs (used for full sync redundant deletion)
	workingConfigs     map[uint32]*DnsPolicyConfig // Working cache for operations (can be refreshed during full sync)
	workingCookieToID  map[uint32]int              // Working map of policy cookies to IDs
}

/*****************************************************************************
 * NAME: NewDnsPolicyProcessor
 *
 * DESCRIPTION:
 *     Creates a new DNS policy processor instance.
 *     Initializes the processor with logger and empty configuration caches.
 *     Implements working config management design pattern.
 *
 * PARAMETERS:
 *     logger - Logger instance for DNS policy operations
 *
 * RETURNS:
 *     *DnsPolicyProcessor - New DNS policy processor instance
 *****************************************************************************/
func NewDnsPolicyProcessor(logger *logger.Logger) *DnsPolicyProcessor {
	return &DnsPolicyProcessor{
		logger:            logger,
		localConfigs:      make(map[uint32]*DnsPolicyConfig),
		cookieToID:        make(map[uint32]int),
		workingConfigs:    make(map[uint32]*DnsPolicyConfig),
		workingCookieToID: make(map[uint32]int),
	}
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type handled by this processor.
 *
 * RETURNS:
 *     pb.TaskType - TASK_DNS_POLICY task type
 *****************************************************************************/
func (p *DnsPolicyProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_DNS_POLICY
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes local configuration cache and sets sync flag.
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) StartFullSync() error {
	p.logger.Info("starting full sync for DNS policy processor")

	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	p.logger.Info("DNS policy full sync started successfully",
		zap.Int("local_policies", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up any remaining local configurations not processed during sync.
 *
 * PARAMETERS:
 *     ctx - Context for the operation
 *
 * RETURNS:
 *     error - Error if cleanup fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) EndFullSync() {
	p.logger.Info("ending full sync for DNS policy processor",
		zap.Int("remaining_policies", len(p.localConfigs)))

	// Create a copy to avoid modifying map during iteration
	remainingPolicies := make(map[uint32]*DnsPolicyConfig)
	for cookie, config := range p.localConfigs {
		remainingPolicies[cookie] = config
	}

	// Process remaining policies (keep fullSyncInProgress = true)
	for cookie := range remainingPolicies {
		deleteTask := &pb.DnsPolicyTask{Cookie: cookie}
		ctx := context.Background()
		_, err := p.handleDeleteConfig(ctx, deleteTask)
		if err != nil {
			p.logger.Error("failed to delete DNS policy during cleanup",
				zap.Uint32("cookie", cookie),
				zap.Error(err))
		}
	}

	// Verify cleanup and set flag to false
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some DNS policies not cleaned up",
			zap.Int("count", len(p.localConfigs)))
	}

	p.fullSyncInProgress = false
	p.localConfigs = make(map[uint32]*DnsPolicyConfig)
	p.cookieToID = make(map[uint32]int)
	p.workingConfigs = make(map[uint32]*DnsPolicyConfig)
	p.workingCookieToID = make(map[uint32]int)

	p.logger.Info("DNS policy full sync ended successfully")
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a DNS policy task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Validate task payload
	if task.GetDnsPolicyTask() == nil {
		return "DNS policy task payload is nil", fmt.Errorf("DNS policy task payload is nil")
	}

	dnsPolicyTask := task.GetDnsPolicyTask()

	// Validate required fields
	if dnsPolicyTask.GetCookie() == 0 {
		return "DNS policy cookie is required", fmt.Errorf("DNS policy cookie is required")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "dns_policy", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Bool("disable", dnsPolicyTask.GetDisable()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Delegate to appropriate handler based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, dnsPolicyTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, dnsPolicyTask)
	default:
		err = fmt.Errorf("unknown task action: %v", task.TaskAction)
		result = "Unknown task action"
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: fetchDnsPolicyConfigs
 *
 * DESCRIPTION:
 *     Fetches DNS policy configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[uint32]*DnsPolicyConfig - DNS policy configurations by cookie
 *     map[uint32]int              - Cookie to ID mapping
 *     error                       - Error if fetch fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) fetchDnsPolicyConfigs() (map[uint32]*DnsPolicyConfig, map[uint32]int, error) {
	configs := make(map[uint32]*DnsPolicyConfig)
	cookieToID := make(map[uint32]int)

	// Get list of all DNS policies
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "dnspolicy", "list", "json=1")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list DNS policies: %w", err)
	}

	// Parse JSON output
	if strings.TrimSpace(output) == "" {
		p.logger.Debug("no DNS policies found")
		return configs, cookieToID, nil
	}

	policies, err := ParseDnsPolicyFromJSONOutput(output)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse DNS policy list: %w", err)
	}

	// Get detailed configuration for each policy to retrieve cookies
	for _, policy := range policies {
		detailedConfig, err := p.getDnsPolicyDetailedConfig(policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed DNS policy config",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		configs[detailedConfig.Cookie] = detailedConfig
		cookieToID[detailedConfig.Cookie] = detailedConfig.ID
	}

	return configs, cookieToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local DNS policy configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local DNS policy configurations")

	configs, cookieToID, err := p.fetchDnsPolicyConfigs()
	if err != nil {
		return err
	}

	p.localConfigs = configs
	p.cookieToID = cookieToID

	p.logger.Debug("refreshed local DNS policy configurations",
		zap.Int("count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working DNS policy configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working DNS policy configurations")

	configs, cookieToID, err := p.fetchDnsPolicyConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	p.workingConfigs = configs
	p.workingCookieToID = cookieToID

	p.logger.Debug("refreshed working DNS policy configurations",
		zap.Int("count", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like ordering, enable/disable, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: getDnsPolicyDetailedConfig
 *
 * DESCRIPTION:
 *     Gets detailed configuration for a specific DNS policy by ID.
 *     Uses floweye dnspolicy get command for single-object retrieval.
 *
 * PARAMETERS:
 *     policyID - Local policy ID
 *
 * RETURNS:
 *     *DnsPolicyConfig - Detailed DNS policy configuration
 *     error            - Error if retrieval fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) getDnsPolicyDetailedConfig(policyID int) (*DnsPolicyConfig, error) {
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "dnspolicy", "get", "id="+strconv.Itoa(policyID))
	if err != nil {
		return nil, fmt.Errorf("failed to get DNS policy details: %w", err)
	}

	config, err := ParseDnsPolicyFromGetOutput(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse DNS policy details: %w", err)
	}

	return config, nil
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles DNS policy creation and modification.
 *     Implements unified logic for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - DNS policy task to process
 *     taskAction - Task action type (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) handleConfigChange(ctx context.Context, task *pb.DnsPolicyTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertDnsPolicyTaskToConfig(task, p.logger)
	if err != nil {
		p.logger.Error("failed to convert DNS policy task to config data",
			zap.String("cookie", fmt.Sprintf("%d", task.GetCookie())),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert DNS policy configuration: %v", err), err
	}

	p.logger.Info("Processing DNS policy configuration change",
		zap.Uint32("cookie", configData.Cookie),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Register cleanup defer after validation (validation is done in ConvertDnsPolicyTaskToConfig)
	if p.fullSyncInProgress {
		cookie := configData.Cookie // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, cookie)
			delete(p.cookieToID, cookie)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if policy exists in working configuration
	_, exists := p.workingConfigs[configData.Cookie]

	/*
		// If configurations match, no need to modify
		if exists && CompareDnsPolicyConfig(p.logger, configData, localConfig) {
			p.logger.Info("DNS policy configuration already matches, no changes needed",
				zap.Uint32("cookie", configData.Cookie))

			return "DNS policy configuration already matches, no changes needed", nil
		}
	*/

	// Step 1: ID allocation (according to documentation)
	policyID, err := p.allocatePolicyID(configData.Cookie)
	if err != nil {
		return fmt.Sprintf("Failed to allocate policy ID: %v", err), err
	}

	// Step 2: Build floweye command using converted data
	operation := "add"
	if exists {
		operation = "set"
	}

	cmdArgs := []string{"dnspolicy", operation, "id=" + strconv.Itoa(policyID)}

	// Add cookie
	cmdArgs = append(cmdArgs, "cookie="+strconv.FormatUint(uint64(configData.Cookie), 10))

	// Note: disable parameter is handled separately after policy creation/modification

	// Add schedule time
	if configData.SchTime != 0 {
		cmdArgs = append(cmdArgs, "schtime="+strconv.Itoa(configData.SchTime))
	} else {
		cmdArgs = append(cmdArgs, "schtime=0")
	}

	// Add matching criteria using converted data
	if err := p.addMatchingCriteriaFromConfig(&cmdArgs, configData); err != nil {
		return fmt.Sprintf("Failed to build matching criteria: %v", err), err
	}

	// Add action configuration using converted data
	if err := p.addActionConfigurationFromConfig(&cmdArgs, configData); err != nil {
		return fmt.Sprintf("Failed to build action configuration: %v", err), err
	}

	// Execute floweye command
	p.logger.Info("executing floweye command for DNS policy",
		zap.String("operation", operation),
		zap.Uint32("cookie", configData.Cookie),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye command",
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to %s DNS policy: %v", operation, err), err
	}

	// Refresh working configs to include the newly created/updated policy
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful
	}

	// Handle ordering logic after creation/update using converted data
	if err := p.handlePolicyOrderingFromConfig(ctx, configData, policyID); err != nil {
		p.logger.Error("failed to handle DNS policy ordering",
			zap.Uint32("cookie", configData.Cookie),
			zap.Error(err))
		return fmt.Sprintf("DNS policy %s succeeded but ordering failed: %v", operation, err), err
	}

	// Refresh working configs to get the current policy ID after ordering (ordering may have changed the ID)
	if err := p.refreshWorkingConfigs(); err != nil {
		p.logger.Error("failed to refresh working configs after ordering",
			zap.Uint32("cookie", configData.Cookie),
			zap.Error(err))
		return fmt.Sprintf("DNS policy %s succeeded but failed to refresh configs: %v", operation, err), err
	}

	// Get the current policy ID from working configs
	currentConfig, exists := p.workingConfigs[configData.Cookie]
	if !exists {
		p.logger.Error("DNS policy not found in working configs after ordering",
			zap.Uint32("cookie", configData.Cookie))
		return fmt.Sprintf("DNS policy %s succeeded but policy not found after ordering", operation),
			fmt.Errorf("policy not found after ordering")
	}
	currentPolicyID := currentConfig.ID

	// Handle enable/disable operation separately after policy creation/modification
	if err := p.handlePolicyEnableDisable(configData.Cookie, configData.Disable, currentPolicyID); err != nil {
		p.logger.Error("failed to handle DNS policy enable/disable",
			zap.Uint32("cookie", configData.Cookie),
			zap.Bool("disable", configData.Disable),
			zap.Error(err))
		return fmt.Sprintf("DNS policy %s succeeded but enable/disable failed: %v", operation, err), err
	}

	// Verify the configuration was applied successfully using converted data
	if err := p.verifyDnsPolicyConfigFromConfig(ctx, configData, currentPolicyID); err != nil {
		p.logger.Error("DNS policy verification failed",
			zap.Uint32("cookie", configData.Cookie),
			zap.Error(err))
		return fmt.Sprintf("DNS policy %s succeeded but verification failed: %v", operation, err), err
	}

	successMsg := fmt.Sprintf("DNS policy %s successful", operation)
	p.logger.Info(successMsg, zap.Uint32("cookie", configData.Cookie))
	return successMsg, nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles DNS policy deletion.
 *     Removes the policy from the local PA system and maintains continuous ID ordering.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - DNS policy task containing the cookie to delete
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) handleDeleteConfig(ctx context.Context, task *pb.DnsPolicyTask) (string, error) {
	// Validate required fields
	if task.GetCookie() == 0 {
		return "DNS policy cookie is required for deletion", fmt.Errorf("DNS policy cookie is required for deletion")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		cookie := task.GetCookie() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, cookie)
			delete(p.cookieToID, cookie)
		}()
	}

	p.logger.Info("Processing DNS policy deletion",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if policy exists in working configs
	workingConfig, exists := p.workingConfigs[task.GetCookie()]
	if !exists {
		p.logger.Info("DNS policy does not exist locally, treating as successful delete",
			zap.Uint32("cookie", task.GetCookie()))

		return "DNS policy does not exist, deletion successful", nil
	}

	// Get current policy ID from floweye (don't rely on cached localConfig ID which may be outdated)
	var currentPolicyID int
	if p.fullSyncInProgress {
		// During full sync, get real-time ID from floweye to avoid stale cache issues
		var err error
		currentPolicyID, err = p.getCurrentPolicyIDByCookie(task.GetCookie())
		if err != nil {
			p.logger.Info("DNS policy does not exist in floweye, treating as successful delete",
				zap.Uint32("cookie", task.GetCookie()),
				zap.Error(err))
			return "DNS policy does not exist, deletion successful", nil
		}
	} else {
		// For incremental updates, use working config ID (should be up-to-date after refresh)
		currentPolicyID = workingConfig.ID
	}

	// Store deletion information for ordering logic
	deletedPolicyID := currentPolicyID

	// Build delete command arguments
	cmdArgs := []string{"dnspolicy", "remove", "id=" + strconv.Itoa(deletedPolicyID)}

	// Execute floweye command
	p.logger.Info("executing floweye command for DNS policy deletion",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Int("id", deletedPolicyID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("DNS policy already does not exist, treating as successful delete",
				zap.Uint32("cookie", task.GetCookie()))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete DNS policy: %v", err), err
		}
	}

	// Handle ordering logic: decrement IDs of all policies with ID > deleted ID
	if err := p.handlePolicyDeletionOrdering(ctx, deletedPolicyID); err != nil {
		p.logger.Error("failed to handle DNS policy deletion ordering",
			zap.Uint32("cookie", task.GetCookie()),
			zap.Int("deleted_id", deletedPolicyID),
			zap.Error(err))
		return fmt.Sprintf("DNS policy deletion succeeded but ordering adjustment failed: %v", err), err
	}

	successMsg := "DNS policy deletion successful"
	p.logger.Info(successMsg, zap.Uint32("cookie", task.GetCookie()))
	return successMsg, nil
}

/*****************************************************************************
 * NAME: allocatePolicyID
 *
 * DESCRIPTION:
 *     Allocates a policy ID for the given cookie.
 *     Uses existing ID if policy exists, otherwise allocates max(ID)+1.
 *
 * PARAMETERS:
 *     cookie - Policy cookie
 *
 * RETURNS:
 *     int   - Allocated policy ID
 *     error - Error if allocation fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) allocatePolicyID(cookie uint32) (int, error) {
	// Check if policy already exists in working configs
	if existingConfig, exists := p.workingConfigs[cookie]; exists {
		p.logger.Debug("DNS policy exists, using existing ID",
			zap.Uint32("cookie", cookie),
			zap.Int("existing_id", existingConfig.ID))
		return existingConfig.ID, nil
	}

	// Policy doesn't exist, allocate new ID using max(ID)+1
	maxID := 0
	for _, config := range p.workingConfigs {
		if config.ID > maxID {
			maxID = config.ID
		}
	}

	newID := maxID + 1
	p.logger.Debug("allocating new DNS policy ID",
		zap.Uint32("cookie", cookie),
		zap.Int("new_id", newID),
		zap.Int("max_existing_id", maxID))

	return newID, nil
}

/*****************************************************************************
 * NAME: handlePolicyEnableDisable
 *
 * DESCRIPTION:
 *     Handles DNS policy enable/disable operation separately from policy creation/modification.
 *     Uses dedicated floweye dnspolicy set command with only disable parameter.
 *
 * PARAMETERS:
 *     cookie   - Policy cookie
 *     disable  - Whether to disable the policy (true=disable, false=enable)
 *     policyID - Policy ID
 *
 * RETURNS:
 *     error - Error if enable/disable operation fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) handlePolicyEnableDisable(cookie uint32, disable bool, policyID int) error {
	p.logger.Debug("handling DNS policy enable/disable",
		zap.Uint32("cookie", cookie),
		zap.Bool("disable", disable),
		zap.Int("policy_id", policyID))

	// Build floweye command for enable/disable operation
	disableValue := "0" // 0 = enable
	if disable {
		disableValue = "1" // 1 = disable
	}

	cmdArgs := []string{"dnspolicy", "set", "id=" + strconv.Itoa(policyID), "disable=" + disableValue}

	// Execute floweye command
	p.logger.Info("executing floweye enable/disable command for DNS policy",
		zap.Uint32("cookie", cookie),
		zap.Int("id", policyID),
		zap.String("disable", disableValue),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye enable/disable command for DNS policy",
			zap.Uint32("cookie", cookie),
			zap.Int("id", policyID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to %s DNS policy: %w",
			map[bool]string{true: "disable", false: "enable"}[disable], err)
	}

	p.logger.Debug("floweye enable/disable command executed successfully",
		zap.Uint32("cookie", cookie),
		zap.Int("id", policyID),
		zap.String("operation", map[bool]string{true: "disabled", false: "enabled"}[disable]),
		zap.String("output", output))

	return nil
}

/*****************************************************************************
 * NAME: handlePolicyOrderingFromConfig
 *
 * DESCRIPTION:
 *     Handles DNS policy ordering using converted configuration data.
 *     Uses internal data structure instead of protobuf access.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Converted DNS policy configuration data
 *     policyID   - Policy ID
 *
 * RETURNS:
 *     error - Error if ordering fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) handlePolicyOrderingFromConfig(ctx context.Context, configData *DnsPolicyConfig, policyID int) error {
	// Use the previous field from converted data
	previousCookie := configData.Previous

	p.logger.Debug("handling DNS policy ordering",
		zap.Uint32("cookie", configData.Cookie),
		zap.Uint32("previous_cookie", previousCookie),
		zap.Int("policy_id", policyID))

	// Determine target position based on previous cookie
	var targetPosition int
	if previousCookie == 0 {
		// Insert at the beginning (position 1)
		targetPosition = 1
	} else if previousCookie == 4294967295 { // -1 as uint32 (math.MaxUint32)
		// Append to the end - find max ID
		maxID := 0
		for _, config := range p.workingConfigs {
			if config.ID > maxID {
				maxID = config.ID
			}
		}
		targetPosition = maxID + 1
	} else {
		// Insert after the specified previous policy
		if previousID, exists := p.workingCookieToID[previousCookie]; exists {
			targetPosition = previousID + 1
		} else {
			p.logger.Warn("previous policy not found, appending to end",
				zap.Uint32("previous_cookie", previousCookie))
			// Append to the end if previous policy not found
			maxID := 0
			for _, config := range p.workingConfigs {
				if config.ID > maxID {
					maxID = config.ID
				}
			}
			targetPosition = maxID + 1
		}
	}

	// If current position matches target, no reordering needed
	if policyID == targetPosition {
		p.logger.Debug("DNS policy already in correct position",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("position", policyID))
		return nil
	}

	// Get current policy list to determine ordering
	policies, err := p.getDnsPolicies()
	if err != nil {
		return fmt.Errorf("failed to get DNS policies for ordering: %w", err)
	}

	// Get detailed configurations including cookies for all policies
	var currentPolicies []DnsPolicyOrderInfo
	for _, policy := range policies {
		detailedPolicy, err := p.getDnsPolicyDetailedConfig(policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed DNS policy config during ordering",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		currentPolicies = append(currentPolicies, DnsPolicyOrderInfo{
			ID:     policy.ID,
			Cookie: detailedPolicy.Cookie,
		})
	}

	// Perform reordering logic with complete policy information
	return p.movePolicyToPosition(policyID, targetPosition, currentPolicies)
}

/*****************************************************************************
 * NAME: verifyDnsPolicyConfigFromConfig
 *
 * DESCRIPTION:
 *     Verifies DNS policy configuration using converted configuration data.
 *     Uses internal data structure and reuses comparison logic.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Converted DNS policy configuration data
 *     policyID   - Policy ID
 *
 * RETURNS:
 *     error - Error if verification fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) verifyDnsPolicyConfigFromConfig(ctx context.Context, configData *DnsPolicyConfig, policyID int) error {
	p.logger.Debug("verifying DNS policy configuration",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("policy_id", policyID))

	// Use the verification function that reuses comparison logic
	success, err := VerifyDnsPolicyConfig(p.logger, configData)
	if err != nil {
		return fmt.Errorf("verification failed: %w", err)
	}

	if !success {
		return fmt.Errorf("DNS policy configuration verification failed")
	}

	p.logger.Debug("DNS policy configuration verified successfully",
		zap.Uint32("cookie", configData.Cookie))

	return nil
}

/*****************************************************************************
 * NAME: getCurrentPolicyIDByCookie
 *
 * DESCRIPTION:
 *     Gets the current DNS policy ID by cookie from floweye.
 *     This ensures we get the most up-to-date ID information during full sync
 *     to avoid issues with stale cache data during policy ordering operations.
 *
 * PARAMETERS:
 *     cookie - Policy cookie to search for
 *
 * RETURNS:
 *     int   - Current policy ID
 *     error - Error if policy not found or operation fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) getCurrentPolicyIDByCookie(cookie uint32) (int, error) {
	// Get all DNS policies
	policies, err := p.getDnsPolicies()
	if err != nil {
		return 0, fmt.Errorf("failed to get DNS policies: %w", err)
	}

	// Find the policy with matching cookie
	for _, policy := range policies {
		detailedPolicy, err := p.getDnsPolicyDetailedConfig(policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed DNS policy config",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		if detailedPolicy.Cookie == cookie {
			p.logger.Debug("found current DNS policy ID by cookie",
				zap.Uint32("cookie", cookie),
				zap.Int("current_id", policy.ID))
			return policy.ID, nil
		}
	}

	return 0, fmt.Errorf("DNS policy with cookie %d not found", cookie)
}
