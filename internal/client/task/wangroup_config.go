/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      wangroup_config.go
 *
 * DESCRIPTION :    WAN Group configuration data structures and helper functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
)

/*****************************************************************************
 * NAME: WanGroupConfig
 *
 * DESCRIPTION:
 *     Represents a WAN group configuration.
 *     Contains group ID, name, type, and member list.
 *
 * FIELDS:
 *     ID      - Group ID (1-128)
 *     Name    - Group name
 *     Type    - Group type (srcdst, spdp, src, srcsport, dst, dstdport, leftbw)
 *     Members - Set of member WANs, stored as map[string]bool for efficient lookup
 *****************************************************************************/
type WanGroupConfig struct {
	ID      int32           // 群组 ID
	Name    string          // 群组名称
	Type    string          // 群组类型
	Members map[string]bool // 成员列表，key 为 WAN 名称
}

/*****************************************************************************
 * NAME: GetAllWanGroups
 *
 * DESCRIPTION:
 *     Retrieves all WAN group configurations from the system.
 *     Executes 'floweye wangroup list' command and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging
 *
 * RETURNS:
 *     map[int32]*WanGroupConfig - Map of WAN group configurations, keyed by ID
 *     error - Error if retrieval fails
 *****************************************************************************/
func GetAllWanGroups(logger *logger.Logger) (map[int32]*WanGroupConfig, error) {
	// 执行 floweye wangroup list 命令
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "wangroup", "list")
	if err != nil {
		logger.Error("Failed to execute floweye wangroup list command",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get WAN group list: %v", err)
	}

	// 解析输出
	configs := make(map[int32]*WanGroupConfig)
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析行格式：<ID> <名称> <成员数量> <类型> <未知字段> <未知字段> [成员列表]
		fields := strings.Fields(line)
		if len(fields) < 6 {
			continue
		}

		id, err := strconv.ParseInt(fields[0], 10, 32)
		if err != nil {
			logger.Warn("Failed to parse WAN group ID",
				zap.String("id", fields[0]),
				zap.Error(err))
			continue
		}

		config := &WanGroupConfig{
			ID:      int32(id),
			Name:    fields[1],
			Type:    fields[3],
			Members: make(map[string]bool),
		}

		// 解析成员列表
		if len(fields) > 6 {
			for _, member := range fields[6:] {
				// 直接添加成员，不需要获取权重
				config.Members[member] = true
			}
		}

		configs[config.ID] = config
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: VerifyWanGroupConfig
 *
 * DESCRIPTION:
 *     Verifies that a WAN group configuration has been successfully applied.
 *     Checks if the group exists with the correct name, type, and members.
 *     Uses unified internal structure for consistent verification logic.
 *
 * PARAMETERS:
 *     logger         - Logger instance for logging
 *     expectedConfig - Expected WAN group configuration (from protobuf conversion)
 *
 * RETURNS:
 *     bool  - True if configuration is verified, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyWanGroupConfig(logger *logger.Logger, expectedConfig *WanGroupConfig) (bool, error) {
	// 执行 floweye wangroup list 命令
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "wangroup", "list")
	if err != nil {
		logger.Error("Failed to execute floweye wangroup list command",
			zap.Error(err),
			zap.String("output", output))
		return false, fmt.Errorf("failed to get WAN group list: %v", err)
	}

	// 解析输出，查找指定 ID 的群组
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 6 {
			continue
		}

		id, err := strconv.ParseInt(fields[0], 10, 32)
		if err != nil {
			continue
		}

		if int32(id) == expectedConfig.ID {
			// 找到了指定 ID 的群组
			// 验证名称和类型
			if fields[1] != expectedConfig.Name {
				logger.Warn("WAN group name mismatch",
					zap.Int32("id", expectedConfig.ID),
					zap.String("expected", expectedConfig.Name),
					zap.String("actual", fields[1]))
				return false, nil
			}

			if fields[3] != expectedConfig.Type {
				logger.Warn("WAN group type mismatch",
					zap.Int32("id", expectedConfig.ID),
					zap.String("expected", expectedConfig.Type),
					zap.String("actual", fields[3]))
				return false, nil
			}

			// 验证成员
			// 获取成员详细信息
			output, err := utils.ExecuteCommand(logger, 10, "floweye", "wangroup", "get", "id="+fmt.Sprintf("%d", expectedConfig.ID), "showproxy=1")
			if err != nil {
				logger.Error("Failed to execute floweye wangroup get command",
					zap.Int32("id", expectedConfig.ID),
					zap.Error(err),
					zap.String("output", output))
				return false, fmt.Errorf("failed to get WAN group member info: %v", err)
			}

			// 解析成员列表
			actualMembers := make(map[string]bool)
			memberLines := strings.Split(output, "\n")
			for _, memberLine := range memberLines {
				memberLine = strings.TrimSpace(memberLine)
				if memberLine == "" {
					continue
				}

				memberFields := strings.Fields(memberLine)
				if len(memberFields) < 5 {
					continue
				}

				proxyName := memberFields[2]
				actualMembers[proxyName] = true
			}

			// Create actual config for comparison
			actualConfig := &WanGroupConfig{
				ID:      expectedConfig.ID,
				Name:    fields[1],
				Type:    fields[3],
				Members: actualMembers,
			}

			// Use CompareWanGroupConfig to verify the configuration
			if !CompareWanGroupConfig(logger, expectedConfig, actualConfig) {
				logger.Error("WAN group configuration verification failed",
					zap.Int32("id", expectedConfig.ID))
				return false, nil
			}

			return true, nil
		}
	}

	// 没有找到指定 ID 的群组
	return false, nil
}

/*****************************************************************************
 * NAME: GetWanGroupTypeString
 *
 * DESCRIPTION:
 *     Converts a WanGroupType enum to its string representation.
 *     Used for constructing floweye commands.
 *
 * PARAMETERS:
 *     groupType - WAN group type enum
 *
 * RETURNS:
 *     string - String representation of the WAN group type
 *****************************************************************************/
func GetWanGroupTypeString(groupType pb.WanGroupType) string {
	switch groupType {
	case pb.WanGroupType_WAN_GROUP_TYPE_SRCDST:
		return "srcdst"
	case pb.WanGroupType_WAN_GROUP_TYPE_SPDP:
		return "spdp"
	case pb.WanGroupType_WAN_GROUP_TYPE_SRC:
		return "src"
	case pb.WanGroupType_WAN_GROUP_TYPE_SRCSPORT:
		return "srcsport"
	case pb.WanGroupType_WAN_GROUP_TYPE_DST:
		return "dst"
	case pb.WanGroupType_WAN_GROUP_TYPE_DSTDPORT:
		return "dstdport"
	case pb.WanGroupType_WAN_GROUP_TYPE_RX_LEFTBW:
		return "leftbw"
	case pb.WanGroupType_WAN_GROUP_TYPE_TX_LEFTBW:
		return "leftbwup"
	case pb.WanGroupType_WAN_GROUP_TYPE_SESSION:
		return "minflow"
	case pb.WanGroupType_WAN_GROUP_TYPE_FAILOVER:
		return "failover"
	default:
		return "srcdst" // 默认使用 srcdst
	}
}

/*****************************************************************************
 * NAME: CompareWanGroupConfig
 *
 * DESCRIPTION:
 *     Compares two WAN group configurations to check if they match.
 *     Checks if name, type, and members match. Uses unified internal structures
 *     for consistent comparison logic.
 *
 * PARAMETERS:
 *     logger        - Logger instance for logging
 *     expectedConfig - Expected WAN group configuration (from protobuf conversion)
 *     localConfig   - Local WAN group configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareWanGroupConfig(logger *logger.Logger, expectedConfig *WanGroupConfig, localConfig *WanGroupConfig) bool {

	if expectedConfig == nil || localConfig == nil {
		logger.Debug("One of the configurations is nil")
		return false
	}

	// 比较名称和类型
	if expectedConfig.Name != localConfig.Name {
		logger.Debug("WAN group name mismatch",
			zap.String("expected", expectedConfig.Name),
			zap.String("actual", localConfig.Name))
		return false
	}

	if expectedConfig.Type != localConfig.Type {
		logger.Debug("WAN group type mismatch",
			zap.String("expected", expectedConfig.Type),
			zap.String("actual", localConfig.Type))
		return false
	}

	// 比较成员列表
	if len(expectedConfig.Members) != len(localConfig.Members) {
		logger.Debug("WAN group member count mismatch",
			zap.Int("expected", len(expectedConfig.Members)),
			zap.Int("actual", len(localConfig.Members)))
		return false
	}

	// 检查所有期望的成员是否存在
	for proxyName := range expectedConfig.Members {
		if _, exists := localConfig.Members[proxyName]; !exists {
			logger.Debug("Expected member not found in local config",
				zap.String("member", proxyName))
			return false
		}
	}

	// 检查本地是否有多余的成员
	for proxyName := range localConfig.Members {
		if _, exists := expectedConfig.Members[proxyName]; !exists {
			logger.Debug("Unexpected member found in local config",
				zap.String("member", proxyName))
			return false
		}
	}

	return true

}

/*****************************************************************************
 * NAME: ConvertWanGroupTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf WanGroupTask message to unified WanGroupConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing WanGroupConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     wanGroupTask - Protobuf WAN group task message to convert
 *
 * RETURNS:
 *     *WanGroupConfig - Converted WAN group configuration structure
 *     error           - Error if conversion fails
 *****************************************************************************/
func ConvertWanGroupTaskToConfig(wanGroupTask *pb.WanGroupTask) (*WanGroupConfig, error) {
	if wanGroupTask == nil {
		return nil, fmt.Errorf("wanGroupTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &WanGroupConfig{
		ID:      wanGroupTask.GetId(),
		Name:    wanGroupTask.GetName(),
		Type:    GetWanGroupTypeString(wanGroupTask.GetType()),
		Members: make(map[string]bool),
	}

	// Convert members list to map for efficient lookup
	for _, member := range wanGroupTask.GetMembers() {
		if member.GetProxyName() != "" {
			config.Members[member.GetProxyName()] = true
		}
	}

	return config, nil
}
