// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : dhcp_config.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"go.uber.org/zap"
)

// DhcpConfig represents a DHCP server configuration.
// It contains DHCP-specific configuration parameters extracted from a LAN configuration.
type DhcpConfig struct {
	Name        string            // The name of the LAN interface.
	DhcpEnable  int               // DHCP server enabled (0: disabled, 1: enabled).
	DhcpPool    string            // The DHCP address pool (start-end format).
	LeaseTtl    int               // The DHCP lease time in seconds.
	Dns0        string            // The primary DNS server.
	Dns1        string            // The secondary DNS server.
	DhcpGateway string            // The DHCP gateway address.
	DhcpMask    string            // The DHCP subnet mask.
	DhcpAcAddr  string            // The DHCP AC address.
	DhcpDomain  string            // The DHCP domain name.
	DhcpOptions map[string]string // A map of DHCP options (option number -> type,value).
}

// NewDhcpConfigFromLan creates a DhcpConfig from a LanConfig by extracting DHCP-related fields.
// This function serves as a converter between the LAN configuration and the DHCP-specific configuration structure.
//
// Parameters:
//   - lanConfig: The LAN configuration containing DHCP settings.
func NewDhcpConfigFromLan(lanConfig *LanConfig) *DhcpConfig {
	if lanConfig == nil {
		return nil
	}

	return &DhcpConfig{
		Name:        lanConfig.Name,
		DhcpEnable:  lanConfig.DhcpEnable,
		DhcpPool:    lanConfig.DhcpPool,
		LeaseTtl:    lanConfig.LeaseTtl,
		Dns0:        lanConfig.Dns0,
		Dns1:        lanConfig.Dns1,
		DhcpGateway: lanConfig.DhcpGateway,
		DhcpMask:    lanConfig.DhcpMask,
		DhcpAcAddr:  lanConfig.DhcpAcAddr,
		DhcpDomain:  lanConfig.DhcpDomain,
		DhcpOptions: lanConfig.DhcpOptions,
	}
}

// ConvertDhcpTaskToConfig converts a DhcpServerTask protobuf message to a DhcpConfig structure.
// This function performs a one-time conversion at the entry point to eliminate
// repeated protobuf parsing throughout the processing pipeline.
// It follows the WAN module pattern for protobuf optimization.
//
// Parameters:
//   - dhcpTask: The DHCP server task protobuf message.
func ConvertDhcpTaskToConfig(dhcpTask *pb.DhcpServerTask) (*DhcpConfig, error) {
	if dhcpTask == nil {
		return nil, fmt.Errorf("dhcpTask is nil")
	}

	config := &DhcpConfig{
		Name:        dhcpTask.GetName(),
		DhcpOptions: make(map[string]string),
	}

	// Convert DHCP enable status (protobuf bool to int).
	if dhcpTask.GetDhcpEnable() {
		config.DhcpEnable = 1
	} else {
		config.DhcpEnable = 0
	}

	// Convert DHCP pool (IpRange to string format).
	if dhcpTask.GetDhcpPool() != nil {
		startIp := utils.GetIpString(dhcpTask.GetDhcpPool().GetStartIp())
		endIp := utils.GetIpString(dhcpTask.GetDhcpPool().GetEndIp())
		if startIp != "" && endIp != "" {
			// Validate IP range - start IP should be less than or equal to end IP.
			startIPParts := strings.Split(startIp, ".")
			endIPParts := strings.Split(endIp, ".")
			if len(startIPParts) == 4 && len(endIPParts) == 4 {
				// Simple validation: compare last octet for same subnet.
				if startIPParts[0] == endIPParts[0] && startIPParts[1] == endIPParts[1] && startIPParts[2] == endIPParts[2] {
					startLastOctet, _ := strconv.Atoi(startIPParts[3])
					endLastOctet, _ := strconv.Atoi(endIPParts[3])
					if startLastOctet > endLastOctet {
						return nil, fmt.Errorf("invalid DHCP pool: start IP %s is greater than end IP %s", startIp, endIp)
					}
				}
			}
			config.DhcpPool = startIp + "-" + endIp
		}
	}

	// Convert lease time.
	config.LeaseTtl = int(dhcpTask.GetLeaseTtl())

	// Validate lease time.
	if config.LeaseTtl < 0 {
		return nil, fmt.Errorf("invalid lease time: %d (must be non-negative)", config.LeaseTtl)
	}

	// Convert DNS servers (optional fields with defaults).
	if dhcpTask.GetDns0() != nil {
		config.Dns0 = utils.GetIpString(dhcpTask.GetDns0())
	} else {
		config.Dns0 = "" // Default empty string for unset DNS.
	}

	if dhcpTask.GetDns1() != nil {
		config.Dns1 = utils.GetIpString(dhcpTask.GetDns1())
	} else {
		config.Dns1 = "" // Default empty string for unset DNS.
	}

	// Convert DHCP gateway (optional field with default).
	if dhcpTask.GetDhcpGateway() != nil {
		config.DhcpGateway = utils.GetIpString(dhcpTask.GetDhcpGateway())
	} else {
		config.DhcpGateway = "0.0.0.0" // Default as per floweye documentation.
	}

	// Convert DHCP mask (optional field with default).
	if dhcpTask.GetDhcpMask() != nil {
		config.DhcpMask = utils.GetIpString(dhcpTask.GetDhcpMask())
	} else {
		config.DhcpMask = "0.0.0.0" // Default as per floweye documentation.
	}

	// Convert DHCP AC address (optional field with default).
	if dhcpTask.GetDhcpAcAddr() != nil {
		config.DhcpAcAddr = utils.GetIpString(dhcpTask.GetDhcpAcAddr())
	} else {
		config.DhcpAcAddr = "0.0.0.0"
	}

	// Convert DHCP domain (optional field with default).
	config.DhcpDomain = dhcpTask.GetDhcpDomain() // GetDhcpDomain() returns string directly for optional string fields.

	// Convert DHCP options (optional field).
	if dhcpTask.GetDhcpOptions() != nil {
		dhcpOptions := dhcpTask.GetDhcpOptions()

		// Convert option 12 (hostname).
		if dhcpOptions.GetOption12() != nil {
			valueType := "str"
			if dhcpOptions.GetOption12().GetValueType() == pb.DhcpOptionValueType_DHCP_OPTION_TYPE_HEX {
				valueType = "hex"
			}
			config.DhcpOptions["12"] = valueType + "," + dhcpOptions.GetOption12().GetValue()
		}

		// Convert option 61 (vendor class ID).
		if dhcpOptions.GetOption61() != nil {
			valueType := "str"
			if dhcpOptions.GetOption61().GetValueType() == pb.DhcpOptionValueType_DHCP_OPTION_TYPE_HEX {
				valueType = "hex"
			}
			config.DhcpOptions["61"] = valueType + "," + dhcpOptions.GetOption61().GetValue()
		}

		// Convert option 60 (client ID).
		if dhcpOptions.GetOption60() != nil {
			valueType := "str"
			if dhcpOptions.GetOption60().GetValueType() == pb.DhcpOptionValueType_DHCP_OPTION_TYPE_HEX {
				valueType = "hex"
			}
			config.DhcpOptions["60"] = valueType + "," + dhcpOptions.GetOption60().GetValue()
		}
	}

	return config, nil
}

// CompareDhcpConfig compares the requested DHCP configuration with the local configuration.
// It determines if the requested configuration matches the existing configuration,
// using the converted internal data structure instead of the protobuf message.
// It returns true if the configurations match, false otherwise.
//
// Parameters:
//   - logger: The logger instance for logging operations.
//   - requestConfig: The converted DHCP configuration from the protobuf task.
//   - localConfig: The local DHCP configuration to compare against.
func CompareDhcpConfig(logger *logger.Logger, requestConfig *DhcpConfig, localConfig *DhcpConfig) bool {
	if requestConfig == nil || localConfig == nil {
		return false
	}

	// Compare DHCP enable status.
	if localConfig.DhcpEnable != requestConfig.DhcpEnable {
		logger.Debug("DHCP enable status mismatch",
			zap.String("lan", requestConfig.Name),
			zap.Int("expected", requestConfig.DhcpEnable),
			zap.Int("actual", localConfig.DhcpEnable))
		return false
	}

	// If DHCP is disabled, no need to check other parameters.
	if requestConfig.DhcpEnable == 0 {
		return true
	}

	// Compare DHCP pool.
	if requestConfig.DhcpPool != "" && requestConfig.DhcpPool != localConfig.DhcpPool {
		logger.Debug("DHCP pool mismatch",
			zap.String("lan", requestConfig.Name),
			zap.String("expected", requestConfig.DhcpPool),
			zap.String("actual", localConfig.DhcpPool))
		return false
	}

	// Compare lease time.
	if requestConfig.LeaseTtl != localConfig.LeaseTtl {
		logger.Debug("DHCP lease time mismatch",
			zap.String("lan", requestConfig.Name),
			zap.Int("expected", requestConfig.LeaseTtl),
			zap.Int("actual", localConfig.LeaseTtl))
		return false
	}

	// Compare DNS servers.
	if requestConfig.Dns0 != "" && requestConfig.Dns0 != localConfig.Dns0 {
		logger.Debug("DHCP primary DNS mismatch",
			zap.String("lan", requestConfig.Name),
			zap.String("expected", requestConfig.Dns0),
			zap.String("actual", localConfig.Dns0))
		return false
	}

	if requestConfig.Dns1 != "" && requestConfig.Dns1 != localConfig.Dns1 {
		logger.Debug("DHCP secondary DNS mismatch",
			zap.String("lan", requestConfig.Name),
			zap.String("expected", requestConfig.Dns1),
			zap.String("actual", localConfig.Dns1))
		return false
	}

	// Skip DHCP gateway and mask validation.
	// These fields are managed by the system and may be automatically set
	// based on LAN configuration, so we don't validate them in tests.

	// Compare DHCP AC address.
	if requestConfig.DhcpAcAddr != "" && requestConfig.DhcpAcAddr != localConfig.DhcpAcAddr {
		logger.Debug("DHCP AC address mismatch",
			zap.String("lan", requestConfig.Name),
			zap.String("expected", requestConfig.DhcpAcAddr),
			zap.String("actual", localConfig.DhcpAcAddr))
		return false
	}

	// Compare DHCP domain.
	if requestConfig.DhcpDomain != "" && requestConfig.DhcpDomain != localConfig.DhcpDomain {
		logger.Debug("DHCP domain mismatch",
			zap.String("lan", requestConfig.Name),
			zap.String("expected", requestConfig.DhcpDomain),
			zap.String("actual", localConfig.DhcpDomain))
		return false
	}

	// Compare DHCP options.
	for optionNum, expectedOption := range requestConfig.DhcpOptions {
		actualOption, exists := localConfig.DhcpOptions[optionNum]
		if !exists || expectedOption != actualOption {
			logger.Debug("DHCP option mismatch",
				zap.String("lan", requestConfig.Name),
				zap.String("option", optionNum),
				zap.String("expected", expectedOption),
				zap.String("actual", actualOption))
			return false
		}
	}

	return true
}

// VerifyConfig verifies that the DHCP configuration was applied correctly by comparing
// the requested configuration with the actual configuration on the system.
// It returns true if the configuration matches, false otherwise, and an error if verification fails.
//
// Parameters:
//   - logger: The logger instance for logging operations.
//   - requestConfig: The converted DHCP configuration from the protobuf task.
func (d *DhcpConfig) VerifyConfig(logger *logger.Logger, requestConfig *DhcpConfig) (bool, error) {
	logger.Info("verifying DHCP configuration", zap.String("name", requestConfig.Name))

	// Get current LAN configuration (which includes DHCP configuration).
	lanConfig, err := GetLanConfig(logger, requestConfig.Name)
	if err != nil {
		return false, err
	}

	// Convert to DhcpConfig for comparison.
	actualConfig := NewDhcpConfigFromLan(lanConfig)
	if actualConfig == nil {
		logger.Error("failed to create DHCP config from LAN config")
		return false, nil
	}

	// Use CompareDhcpConfig to verify the configuration.
	if !CompareDhcpConfig(logger, requestConfig, actualConfig) {
		logger.Error("DHCP configuration verification failed",
			zap.String("name", requestConfig.Name))
		return false, nil
	}

	logger.Info("DHCP configuration verified successfully",
		zap.String("name", requestConfig.Name))
	return true, nil
}

// VerifyDhcpConfig is a convenience function to verify DHCP configuration.
// This function maintains backward compatibility by providing the same interface as the original VerifyDhcpConfig function,
// but now uses the optimized conversion function.
// It returns true if the configuration matches, false otherwise, and an error if verification fails.
//
// Parameters:
//   - logger: The logger instance for logging operations.
//   - dhcpTask: The DHCP task containing the requested configuration.
func VerifyDhcpConfig(logger *logger.Logger, dhcpTask *pb.DhcpServerTask) (bool, error) {
	// Convert protobuf task to internal configuration structure.
	requestConfig, err := ConvertDhcpTaskToConfig(dhcpTask)
	if err != nil {
		logger.Error("failed to convert DHCP task to config",
			zap.String("name", dhcpTask.GetName()),
			zap.Error(err))
		return false, err
	}

	// Get current LAN configuration.
	lanConfig, err := GetLanConfig(logger, requestConfig.Name)
	if err != nil {
		return false, err
	}

	// Convert to DhcpConfig.
	dhcpConfig := NewDhcpConfigFromLan(lanConfig)
	if dhcpConfig == nil {
		logger.Error("failed to create DHCP config from LAN config")
		return false, fmt.Errorf("failed to create DHCP config")
	}

	// Use the VerifyConfig method with converted configuration.
	return dhcpConfig.VerifyConfig(logger, requestConfig)
}