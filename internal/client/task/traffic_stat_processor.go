/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_stat_processor.go
 *
 * DESCRIPTION :    Traffic statistics processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/15/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: TrafficStatProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_TRAFFIC_STAT type tasks.
 *     Handles traffic statistics configuration operations.
 *     Implements working config management design pattern.
 *
 * FIELDS:
 *     logger             - Logger for traffic statistics processor operations
 *     localConfigs       - Cache of local traffic statistics configurations (used for full sync redundant deletion)
 *     localNameToID      - Map of traffic statistics names to IDs (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     workingNameToID    - Working map of traffic statistics names to IDs
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type TrafficStatProcessor struct {
	logger             *logger.Logger                // Logger for traffic statistics processor operations
	localConfigs       map[string]*TrafficStatConfig // Cache of local traffic statistics configurations (used for full sync redundant deletion)
	localNameToID      map[string]int                // Map of traffic statistics names to IDs (used for full sync redundant deletion)
	workingConfigs     map[string]*TrafficStatConfig // Working cache for operations (can be refreshed during full sync)
	workingNameToID    map[string]int                // Working map of traffic statistics names to IDs
	fullSyncInProgress bool                          // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewTrafficStatProcessor
 *
 * DESCRIPTION:
 *     Creates a new traffic statistics processor instance.
 *     Initializes both local and working configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *TrafficStatProcessor - Initialized traffic statistics processor
 *****************************************************************************/
func NewTrafficStatProcessor(log *logger.Logger) *TrafficStatProcessor {
	processor := &TrafficStatProcessor{
		logger:             log.WithModule("traffic-stat-processor"),
		localConfigs:       make(map[string]*TrafficStatConfig),
		localNameToID:      make(map[string]int),
		workingConfigs:     make(map[string]*TrafficStatConfig),
		workingNameToID:    make(map[string]int),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *
 * RETURNS:
 *     pb.TaskType - TASK_TRAFFIC_STAT
 *****************************************************************************/
func (p *TrafficStatProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_TRAFFIC_STAT
}

/*****************************************************************************
 * NAME: fetchTrafficStatConfigs
 *
 * DESCRIPTION:
 *     Fetches traffic statistics configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*TrafficStatConfig - Traffic statistics by name
 *     map[string]int                - Name to ID mapping
 *     error                         - Error if fetch fails
 *****************************************************************************/
func (p *TrafficStatProcessor) fetchTrafficStatConfigs() (map[string]*TrafficStatConfig, map[string]int, error) {
	configs := make(map[string]*TrafficStatConfig)
	nameToID := make(map[string]int)

	// Get list of traffic statistics
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "ntmso", "list")
	if err != nil {
		p.logger.Error("failed to list traffic statistics", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to list traffic statistics: %w", err)
	}

	// Parse traffic statistics from list output
	configList, err := ParseTrafficStatFromList(output)
	if err != nil {
		p.logger.Error("failed to parse traffic statistics list", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to parse traffic statistics list: %w", err)
	}

	// Store configurations in maps using name as key
	for _, config := range configList {
		configs[config.Name] = config
		nameToID[config.Name] = config.ID
	}

	p.logger.Debug("fetched traffic statistics configurations",
		zap.Int("count", len(configs)))

	return configs, nameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local traffic statistics configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *TrafficStatProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local traffic statistics configurations")

	configs, nameToID, err := p.fetchTrafficStatConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs
	p.localNameToID = nameToID

	p.logger.Debug("refreshed local traffic statistics configurations",
		zap.Int("count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working traffic statistics configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *TrafficStatProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working traffic statistics configurations")

	configs, nameToID, err := p.fetchTrafficStatConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs
	p.workingNameToID = nameToID

	p.logger.Debug("refreshed working traffic statistics configurations",
		zap.Int("count", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like create, update, delete, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *TrafficStatProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a traffic statistics task.
 *     Routes the task to appropriate handler based on task action.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task containing traffic statistics configuration
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *TrafficStatProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get traffic statistics task data
	trafficStatTask := task.GetTrafficStatTask()
	if trafficStatTask == nil {
		return "Traffic statistics task data is empty", fmt.Errorf("traffic statistics task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "traffic_stat", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Bool("track_ip", trafficStatTask.GetTrackIp()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, trafficStatTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, trafficStatTask)
	default:
		err = fmt.Errorf("unknown task action: %v", task.TaskAction)
		result = ""
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (p *TrafficStatProcessor) StartFullSync() error {
	p.logger.Info("starting full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	err := p.refreshLocalConfigs()
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up remaining local configurations that were not processed.
 *****************************************************************************/
func (p *TrafficStatProcessor) EndFullSync() {
	p.logger.Info("ending full synchronization")

	// Create a copy of remaining traffic statistics to avoid concurrent map modification
	remainingStats := make(map[string]*TrafficStatConfig)
	for name, config := range p.localConfigs {
		remainingStats[name] = config
	}

	// Process remaining traffic statistics in local configuration
	// These are statistics that were not included in the full sync and should be deleted
	// Keep fullSyncInProgress=true during cleanup so handleDeleteConfig can properly
	// remove items from localConfigs map
	if len(remainingStats) > 0 {
		p.logger.Info("cleaning up remaining traffic statistics",
			zap.Int("count", len(remainingStats)))

		for statName := range remainingStats {
			// Create a delete task for this traffic statistics
			deleteTask := &pb.TrafficStatTask{
				Name: statName,
			}

			// Delete the traffic statistics
			p.logger.Info("deleting traffic statistics",
				zap.String("name", statName))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("failed to delete traffic statistics",
					zap.String("name", statName),
					zap.Error(err))
			}
		}
	}

	// Verify cleanup and set flag to false
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some traffic statistics not cleaned up",
			zap.Int("count", len(p.localConfigs)))
	}

	// Reset state and clear all caches
	p.fullSyncInProgress = false
	p.localConfigs = make(map[string]*TrafficStatConfig)
	p.localNameToID = make(map[string]int)
	p.workingConfigs = make(map[string]*TrafficStatConfig)
	p.workingNameToID = make(map[string]int)

	p.logger.Info("full synchronization ended")
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles traffic statistics configuration changes (create/update).
 *     Implements unified logic for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - Traffic statistics task
 *     taskAction - Task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *TrafficStatProcessor) handleConfigChange(ctx context.Context, task *pb.TrafficStatTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	config, err := ConvertTrafficStatTaskToConfig(task)
	if err != nil {
		p.logger.Error("failed to convert traffic stat task to config",
			zap.String("name", task.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert traffic statistics configuration: %v", err), err
	}

	p.logger.Info("Processing traffic statistics configuration change",
		zap.String("name", config.Name),
		zap.Bool("track_ip", config.TrackIP),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Register cleanup defer after validation (validation is done in ConvertTrafficStatTaskToConfig)
	if p.fullSyncInProgress {
		name := config.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if traffic statistics exists in working configuration
	workingConfig, exists := p.workingConfigs[config.Name]

	/*
		// If configurations match, no need to modify
		if exists && CompareTrafficStatConfig(p.logger, config, workingConfig) {
			p.logger.Info("Traffic statistics configuration already matches, no changes needed",
				zap.String("name", config.Name),
				zap.Bool("track_ip", config.TrackIP))

			return "Traffic statistics configuration already matches, no changes needed", nil
		}
	*/

	// Build floweye command based on whether traffic statistics exists
	var cmdArgs []string
	var cmdName string

	if exists {
		// Update existing traffic statistics using set command
		// Need to get the ID first since set command requires ID
		cmdName = "set"
		cmdArgs = []string{"floweye", "ntmso", "set",
			fmt.Sprintf("id=%d", workingConfig.ID),
			fmt.Sprintf("trackip=%s", boolToIntString(config.TrackIP))}
	} else {
		// Create new traffic statistics using add command
		cmdName = "add"
		cmdArgs = []string{"floweye", "ntmso", "add",
			fmt.Sprintf("name=%s", config.Name),
			fmt.Sprintf("trackip=%s", boolToIntString(config.TrackIP))}
	}

	// Execute floweye command
	p.logger.Info("executing floweye command",
		zap.String("operation", cmdName),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, cmdArgs[0], cmdArgs[1:]...)
	if err != nil {
		p.logger.Error("failed to execute floweye command",
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to %s traffic statistics: %v", cmdName, err), err
	}

	p.logger.Debug("floweye command executed successfully",
		zap.String("name", task.GetName()),
		zap.String("output", output))

	// Refresh working configs after creation/update to get latest state
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful
	}

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyTrafficStatConfig(p.logger, config)
	if verifyErr != nil {
		p.logger.Error("failed to verify traffic statistics configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify traffic statistics configuration: %v", verifyErr),
			fmt.Errorf("failed to verify traffic statistics configuration: %w", verifyErr)
	}

	if !success {
		p.logger.Error("Traffic statistics configuration verification failed")
		return "Traffic statistics configuration verification failed", fmt.Errorf("verification failed")
	}

	return fmt.Sprintf("Traffic statistics %s successfully", cmdName), nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles traffic statistics configuration deletion.
 *     Uses the ID-based remove command for deletion.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Traffic statistics task containing name to delete
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *TrafficStatProcessor) handleDeleteConfig(ctx context.Context, task *pb.TrafficStatTask) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	config, err := ConvertTrafficStatTaskToConfig(task)
	if err != nil {
		p.logger.Error("failed to convert traffic stat task to config for deletion",
			zap.String("name", task.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert traffic statistics configuration: %v", err), err
	}

	p.logger.Info("Processing traffic statistics deletion",
		zap.String("name", config.Name),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Register cleanup defer after validation (validation is done in ConvertTrafficStatTaskToConfig)
	if p.fullSyncInProgress {
		name := config.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if traffic statistics exists in working configuration
	workingConfig, exists := p.workingConfigs[config.Name]
	if !exists {
		p.logger.Info("Traffic statistics does not exist, treating as successful delete",
			zap.String("name", config.Name))

		return "Traffic statistics does not exist, deletion successful", nil
	}

	// Build delete command using ID
	cmdArgs := []string{"floweye", "ntmso", "remove", fmt.Sprintf("id=%d", workingConfig.ID)}

	// Execute floweye command
	p.logger.Info("executing floweye command", zap.Strings("args", cmdArgs))
	output, err := utils.ExecuteCommand(p.logger, 10, cmdArgs[0], cmdArgs[1:]...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Traffic statistics already does not exist, treating as successful delete",
				zap.String("name", config.Name))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete traffic statistics: %v", err), err
		}
	}

	p.logger.Debug("floweye command executed successfully",
		zap.String("name", config.Name),
		zap.String("output", output))

	// Skip post-delete verification for improved performance and reliability

	return "Traffic statistics deleted successfully", nil
}

/*****************************************************************************
 * NAME: boolToIntString
 *
 * DESCRIPTION:
 *     Converts boolean value to integer string for floweye commands.
 *     true becomes "1", false becomes "0".
 *
 * PARAMETERS:
 *     value - Boolean value to convert
 *
 * RETURNS:
 *     string - "1" for true, "0" for false
 *****************************************************************************/
func boolToIntString(value bool) string {
	if value {
		return "1"
	}
	return "0"
}
