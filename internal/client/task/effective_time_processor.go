/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      effective_time_processor.go
 *
 * DESCRIPTION :    Effective time configuration task processor
 *
 * AUTHOR :			wei
 *
 * HISTORY :        04/19/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: EffectiveTimeProcessor
 *
 * DESCRIPTION:
 *     Effective time configuration task processor that implements TaskProcessor interface
 *     Handles creation, modification, and deletion of effective time configurations
 *
 * FIELDS:
 *     logger             - Logger instance
 *     localConfigs       - Local effective time configuration cache (used for full sync redundant deletion)
 *     workingConfigs     - Working effective time configuration cache (used for all operations)
 *     workingNameToID    - Working map of effective time names to IDs
 *     fullSyncInProgress - Whether a full sync is in progress
 *****************************************************************************/
type EffectiveTimeProcessor struct {
	logger             *logger.Logger
	localConfigs       map[int]*EffectiveTimeConfig // Used for full sync redundant deletion
	workingConfigs     map[int]*EffectiveTimeConfig // Used for all operations
	workingNameToID    map[string]int               // Working map of names to IDs
	fullSyncInProgress bool
}

/*****************************************************************************
 * NAME: NewEffectiveTimeProcessor
 *
 * DESCRIPTION:
 *     Creates a new effective time processor instance
 *
 * PARAMETERS:
 *     log      - Logger instance
 *     executor - Command executor (not used, kept for interface compatibility)
 *
 * RETURNS:
 *     *EffectiveTimeProcessor - Newly created processor instance
 *****************************************************************************/
func NewEffectiveTimeProcessor(log *logger.Logger) *EffectiveTimeProcessor {
	processor := &EffectiveTimeProcessor{
		logger:             log.WithModule("effective-time-processor"),
		localConfigs:       make(map[int]*EffectiveTimeConfig),
		workingConfigs:     make(map[int]*EffectiveTimeConfig),
		workingNameToID:    make(map[string]int),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles
 *     Implements TaskProcessor interface
 *
 * RETURNS:
 *     pb.TaskType - Task type (TASK_EFFECTIVE_TIME)
 *****************************************************************************/
func (p *EffectiveTimeProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_EFFECTIVE_TIME
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes effective time configuration tasks
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG operations
 *     Implements TaskProcessor interface
 *
 * PARAMETERS:
 *     ctx  - Operation context
 *     task - Task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get effective time task data
	effectiveTimeTask := task.GetEffectiveTimeTask()
	if effectiveTimeTask == nil {
		return "Effective time task data is empty", fmt.Errorf("effective time task data is empty")
	}

	// Log task details
	p.logger.Info("Processing effective time task",
		zap.Int32("id", effectiveTimeTask.GetId()),
		zap.String("name", effectiveTimeTask.GetName()),
		zap.String("action", task.TaskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Process based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handleConfigChange(ctx, effectiveTimeTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeleteConfig(ctx, effectiveTimeTask)
	default:
		return fmt.Sprintf("Unsupported task action: %s", task.TaskAction.String()),
			fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
	}
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles effective time configuration creation and modification
 *     Determines whether to create or modify based on local state
 *
 * PARAMETERS:
 *     ctx               - Operation context
 *     effectiveTimeTask - Effective time task data
 *     taskAction        - Task action
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) handleConfigChange(
	ctx context.Context,
	effectiveTimeTask *pb.EffectiveTimeTask,
	taskAction pb.TaskAction,
) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertEffectiveTimeTaskToConfig(effectiveTimeTask)
	if err != nil {
		p.logger.Error("failed to convert effective time task to config data",
			zap.String("name", effectiveTimeTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert effective time configuration: %v", err), err
	}

	p.logger.Info("Handling effective time configuration change",
		zap.Int("id", configData.ID),
		zap.String("name", configData.Name),
		zap.String("action", taskAction.String()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields using converted data
	if configData.ID <= 0 {
		return "Effective time ID must be greater than 0", fmt.Errorf("effective time ID must be greater than 0")
	}
	if configData.Name == "" {
		return "Effective time name cannot be empty", fmt.Errorf("effective time name cannot be empty")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		id := configData.ID // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, id)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if effective time exists using working configuration
	workingConfig, exists := p.workingConfigs[configData.ID]

	// If configurations match, no need to modify
	if exists && CompareEffectiveTimeConfig(p.logger, configData, workingConfig) {
		p.logger.Info("Effective time configuration already matches, no changes needed",
			zap.Int("id", configData.ID),
			zap.String("name", configData.Name))

		return "Effective time configuration already matches, no changes needed", nil
	}

	// Format time strings using converted data
	startTimeStr := fmt.Sprintf("%02d:%02d:%02d", configData.StartHour, configData.StartMin, configData.StartSec)
	endTimeStr := fmt.Sprintf("%02d:%02d:%02d", configData.EndHour, configData.EndMin, configData.EndSec)

	var cmdArgs []string
	var actionDesc string

	if !exists {
		// Create new effective time
		actionDesc = "create"
		cmdArgs = []string{
			"rtptime", "add",
			fmt.Sprintf("id=%d", configData.ID),
			fmt.Sprintf("name=%s", configData.Name),
			fmt.Sprintf("startwday=%d", configData.StartWDay),
			fmt.Sprintf("endwday=%d", configData.EndWDay),
			fmt.Sprintf("start=%s", startTimeStr),
			fmt.Sprintf("end=%s", endTimeStr),
		}
	} else {
		// Modify existing effective time
		actionDesc = "modify"
		cmdArgs = []string{
			"rtptime", "set",
			fmt.Sprintf("id=%d", configData.ID),
			fmt.Sprintf("name=%s", configData.Name),
			fmt.Sprintf("startwday=%d", configData.StartWDay),
			fmt.Sprintf("endwday=%d", configData.EndWDay),
			fmt.Sprintf("start=%s", startTimeStr),
			fmt.Sprintf("end=%s", endTimeStr),
		}
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to configure effective time",
		zap.Int("id", configData.ID),
		zap.String("name", configData.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command to configure effective time",
			zap.Int("id", configData.ID),
			zap.String("name", configData.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure effective time: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.Int("id", configData.ID),
		zap.String("name", configData.Name),
		zap.String("output", output))

	// Refresh working configs to include the newly created/updated configuration
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error as the main operation succeeded
	}

	// Verify configuration was successfully applied using converted data
	success, verifyErr := VerifyEffectiveTimeConfig(ctx, p.logger, configData)
	if verifyErr != nil {
		p.logger.Error("Failed to verify effective time configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify effective time configuration: %v", verifyErr), verifyErr
	}

	if !success {
		errMsg := "Effective time configuration verification failed, configuration not correctly applied"
		p.logger.Error(errMsg)
		return errMsg, fmt.Errorf(errMsg)
	}

	return fmt.Sprintf("Successfully %sd effective time [%s]", actionDesc, configData.Name), nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles effective time configuration deletion
 *
 * PARAMETERS:
 *     ctx               - Operation context
 *     effectiveTimeTask - Effective time task data
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) handleDeleteConfig(
	ctx context.Context,
	effectiveTimeTask *pb.EffectiveTimeTask,
) (string, error) {
	p.logger.Info("Handling effective time deletion",
		zap.Int32("id", effectiveTimeTask.GetId()),
		zap.String("name", effectiveTimeTask.GetName()))

	// Validate required fields
	if effectiveTimeTask.GetId() <= 0 {
		return "Effective time ID must be greater than 0", fmt.Errorf("effective time ID must be greater than 0")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		id := int(effectiveTimeTask.GetId()) // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, id)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Get effective time ID
	id := int(effectiveTimeTask.GetId())

	// Check if effective time exists using working configuration
	_, exists := p.workingConfigs[id]
	if !exists {
		p.logger.Info("Effective time does not exist, no need to delete",
			zap.Int("id", id),
			zap.String("name", effectiveTimeTask.GetName()))
		return "Effective time does not exist, no need to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"rtptime", "remove",
		fmt.Sprintf("id=%d", id),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete effective time",
		zap.Int("id", id),
		zap.String("name", effectiveTimeTask.GetName()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Effective time already does not exist, treating as successful delete",
				zap.Int("id", id),
				zap.String("name", effectiveTimeTask.GetName()))
		} else {
			p.logger.Error("Failed to execute floweye command to delete effective time",
				zap.Int("id", id),
				zap.String("name", effectiveTimeTask.GetName()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete effective time: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.Int("id", id),
		zap.String("name", effectiveTimeTask.GetName()),
		zap.String("output", output))

	// Skip post-delete verification for improved performance and reliability

	return fmt.Sprintf("Successfully deleted effective time [%s]", effectiveTimeTask.GetName()), nil
}

/*****************************************************************************
 * NAME: fetchEffectiveTimeConfigs
 *
 * DESCRIPTION:
 *     Fetches effective time configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[int]*EffectiveTimeConfig - Effective time configs by ID
 *     map[string]int               - Name to ID mapping
 *     error                        - Error if fetch fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) fetchEffectiveTimeConfigs() (map[int]*EffectiveTimeConfig, map[string]int, error) {
	// Execute floweye command to list all effective times
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "rtptime", "list")
	if err != nil {
		p.logger.Error("Failed to execute floweye command to list effective time",
			zap.Error(err),
			zap.String("output", output))
		return nil, nil, fmt.Errorf("failed to list effective time: %w", err)
	}

	// Parse output
	configs := make(map[int]*EffectiveTimeConfig)
	nameToID := make(map[string]int)
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 10 {
			continue
		}

		id, err := strconv.Atoi(fields[0])
		if err != nil {
			continue
		}

		name := fields[1]
		startWDay, _ := strconv.Atoi(fields[2])
		startHour, _ := strconv.Atoi(fields[3])
		startMin, _ := strconv.Atoi(fields[4])
		startSec, _ := strconv.Atoi(fields[5])
		endWDay, _ := strconv.Atoi(fields[6])
		endHour, _ := strconv.Atoi(fields[7])
		endMin, _ := strconv.Atoi(fields[8])
		endSec, _ := strconv.Atoi(fields[9])

		config := &EffectiveTimeConfig{
			ID:        id,
			Name:      name,
			StartWDay: startWDay,
			StartHour: startHour,
			StartMin:  startMin,
			StartSec:  startSec,
			EndWDay:   endWDay,
			EndHour:   endHour,
			EndMin:    endMin,
			EndSec:    endSec,
		}

		configs[id] = config
		nameToID[name] = id
	}

	return configs, nameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local effective time configuration cache.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local effective time configurations")

	configs, _, err := p.fetchEffectiveTimeConfigs()
	if err != nil {
		return err
	}

	p.localConfigs = configs
	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working effective time configuration cache.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working effective time configurations")

	configs, nameToID, err := p.fetchEffectiveTimeConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	p.workingConfigs = configs
	p.workingNameToID = nameToID
	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like creation, modification, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts full synchronization
 *     Implements TaskProcessor interface
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *EffectiveTimeProcessor) StartFullSync() error {
	p.logger.Info("Starting effective time full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	configs, err := GetAllEffectiveTimeConfigs(p.logger)
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	p.localConfigs = configs
	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends full synchronization
 *     Deletes effective times that exist locally but were not included in full sync
 *     Implements TaskProcessor interface
 *****************************************************************************/
func (p *EffectiveTimeProcessor) EndFullSync() {
	if !p.fullSyncInProgress {
		p.logger.Warn("Attempted to end a full sync that was not started")
		return
	}

	p.logger.Info("Ending effective time full synchronization, deleting unconfigured effective times",
		zap.Int("remaining_count", len(p.localConfigs)))

	// Create a copy of remaining effective time configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations
	remainingConfigs := make(map[int]*EffectiveTimeConfig)
	for id, config := range p.localConfigs {
		remainingConfigs[id] = config
	}

	// Delete effective times not included in full sync
	for id, config := range remainingConfigs {
		p.logger.Info("Deleting effective time not included in full sync",
			zap.Int("id", id),
			zap.String("name", config.Name))

		// Build floweye command
		cmdArgs := []string{
			"rtptime", "remove",
			fmt.Sprintf("id=%d", id),
		}

		// Execute floweye command
		output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to execute floweye command to delete effective time",
				zap.Int("id", id),
				zap.String("name", config.Name),
				zap.Error(err),
				zap.String("output", output))
			continue
		}

		p.logger.Debug("Floweye command executed successfully",
			zap.Int("id", id),
			zap.String("name", config.Name),
			zap.String("output", output))
	}

	// Verify that all remaining effective times have been cleaned up
	// Note: We don't refresh here since we're directly executing commands
	// The verification will happen in the next refresh cycle
	if len(remainingConfigs) > 0 {
		p.logger.Info("attempted to clean up effective times during full sync",
			zap.Int("attempted_count", len(remainingConfigs)))
	}

	// Clear local configuration cache and working cache, set flag to false
	p.localConfigs = make(map[int]*EffectiveTimeConfig)
	p.workingConfigs = make(map[int]*EffectiveTimeConfig)
	p.workingNameToID = make(map[string]int)
	p.fullSyncInProgress = false
}
