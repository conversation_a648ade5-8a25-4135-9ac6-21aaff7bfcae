/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      domain_group_config.go
 *
 * DESCRIPTION :    Domain Group configuration data structures and helper functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/20/2025  create
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: DomainGroupConfig
 *
 * DESCRIPTION:
 *     Represents a local Domain Group configuration.
 *     Used for caching and comparing Domain Group configurations.
 *
 * FIELDS:
 *     ID       - Domain Group ID
 *     Name     - Domain Group name
 *     Domains  - List of domain members
 *****************************************************************************/
type DomainGroupConfig struct {
	ID      int      // Domain Group ID
	Name    string   // Domain Group name
	Domains []string // List of domain members
}

/*****************************************************************************
 * NAME: GetDomainGroupConfigs
 *
 * DESCRIPTION:
 *     Retrieves all Domain Group configurations from the local system.
 *     Uses the floweye dns listgrp command to get the Domain Group list.
 *     Since we always do a full replacement, we don't need to get detailed configuration.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*DomainGroupConfig - Map of Domain Group name to configuration
 *     error                         - Error if retrieval fails
 *****************************************************************************/
func GetDomainGroupConfigs(logger *logger.Logger) (map[string]*DomainGroupConfig, error) {
	logger.Debug("Retrieving all Domain Group configurations")

	// Execute floweye command to list all Domain Groups
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dns", "listgrp")
	if err != nil {
		logger.Error("Failed to execute floweye command to list Domain Groups", zap.Error(err))
		return nil, fmt.Errorf("failed to list Domain Groups: %w", err)
	}

	// Parse output, each line format: usr <id> <name> <count>
	configs := make(map[string]*DomainGroupConfig)
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line, format: usr <id> <name> <count>
		parts := strings.Fields(line)
		if len(parts) < 4 || parts[0] != "usr" {
			// Skip system-created groups or invalid lines
			continue
		}

		// Parse ID
		id, err := strconv.Atoi(parts[1])
		if err != nil {
			logger.Warn("Failed to parse Domain Group ID", zap.String("line", line), zap.Error(err))
			continue
		}

		// Get name
		name := parts[2]

		// Create config
		config := &DomainGroupConfig{
			ID:      id,
			Name:    name,
			Domains: []string{},
		}

		// Add to map
		configs[name] = config
	}

	logger.Debug("Retrieved Domain Group configurations", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: GetDomainGroupIdByName
 *
 * DESCRIPTION:
 *     Retrieves the ID of a Domain Group by its name.
 *     Uses the floweye dns listgrp command to get the Domain Group list.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the Domain Group
 *
 * RETURNS:
 *     int   - ID of the Domain Group, -1 if not found
 *     error - Error if retrieval fails
 *****************************************************************************/
func GetDomainGroupIdByName(logger *logger.Logger, name string) (int, error) {
	logger.Debug("Retrieving Domain Group ID by name", zap.String("name", name))

	// Execute floweye command to list all Domain Groups
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dns", "listgrp")
	if err != nil {
		logger.Error("Failed to execute floweye command to list Domain Groups", zap.Error(err))
		return -1, fmt.Errorf("failed to list Domain Groups: %w", err)
	}

	// Parse output, each line format: usr <id> <name> <count>
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line, format: usr <id> <name> <count>
		parts := strings.Fields(line)
		if len(parts) < 4 || parts[0] != "usr" {
			// Skip system-created groups or invalid lines
			continue
		}

		// Parse ID
		id, err := strconv.Atoi(parts[1])
		if err != nil {
			logger.Warn("Failed to parse Domain Group ID", zap.String("line", line), zap.Error(err))
			continue
		}

		// Check if name matches
		if parts[2] == name {
			logger.Debug("Found Domain Group ID", zap.String("name", name), zap.Int("id", id))
			return id, nil
		}
	}

	logger.Debug("Domain Group not found", zap.String("name", name))
	return -1, nil
}

/*****************************************************************************
 * NAME: GetDomainGroupNameByID
 *
 * DESCRIPTION:
 *     Retrieves the name of a Domain Group by its ID.
 *     Uses the floweye dns listgrp command to get the Domain Group list.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     id     - ID of the Domain Group
 *
 * RETURNS:
 *     string - Name of the Domain Group, empty string if not found
 *     error  - Error if retrieval fails
 *****************************************************************************/
func GetDomainGroupNameByID(logger *logger.Logger, id int) (string, error) {
	logger.Debug("Retrieving Domain Group name by ID", zap.Int("id", id))

	// Execute floweye command to list all Domain Groups
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dns", "listgrp")
	if err != nil {
		logger.Error("Failed to execute floweye command to list Domain Groups", zap.Error(err))
		return "", fmt.Errorf("failed to list Domain Groups: %w", err)
	}

	// Parse output, each line format: usr <id> <name> <count>
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line, format: usr <id> <name> <count>
		parts := strings.Fields(line)
		if len(parts) < 4 || parts[0] != "usr" {
			// Skip system-created groups or invalid lines
			continue
		}

		// Parse ID
		lineID, err := strconv.Atoi(parts[1])
		if err != nil {
			logger.Warn("Failed to parse Domain Group ID", zap.String("line", line), zap.Error(err))
			continue
		}

		// Check if ID matches
		if lineID == id {
			name := parts[2]
			logger.Debug("Found Domain Group name", zap.Int("id", id), zap.String("name", name))
			return name, nil
		}
	}

	logger.Debug("Domain Group not found", zap.Int("id", id))
	return "", nil
}

/*****************************************************************************
 * NAME: GetDomainGroupConfig
 *
 * DESCRIPTION:
 *     Retrieves the configuration of a Domain Group by its name and ID.
 *     Uses the floweye dns dumpgrp command to get the Domain Group members.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the Domain Group
 *     id     - ID of the Domain Group
 *
 * RETURNS:
 *     *DomainGroupConfig - Domain Group configuration
 *     error              - Error if retrieval fails
 *****************************************************************************/
func GetDomainGroupConfig(logger *logger.Logger, name string, id int) (*DomainGroupConfig, error) {
	logger.Debug("Retrieving Domain Group configuration", zap.String("name", name), zap.Int("id", id))

	// Create config
	config := &DomainGroupConfig{
		ID:      id,
		Name:    name,
		Domains: []string{},
	}

	// Execute floweye command to get Domain Group members
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "dns", "dumpgrp", fmt.Sprintf("%d", id))
	if err != nil {
		logger.Error("Failed to execute floweye command to get Domain Group members",
			zap.String("name", name),
			zap.Int("id", id),
			zap.Error(err))
		return nil, fmt.Errorf("failed to get Domain Group members: %w", err)
	}

	// Parse output, each line is a domain
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Add domain to list
		config.Domains = append(config.Domains, line)
	}

	logger.Debug("Retrieved Domain Group configuration",
		zap.String("name", name),
		zap.Int("id", id),
		zap.Int("domains", len(config.Domains)))
	return config, nil
}

/*****************************************************************************
 * NAME: ConvertDomainGroupTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf DomainGroupTask message to unified DomainGroupConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing DomainGroupConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     domainGroupTask - Protobuf Domain Group task message to convert
 *
 * RETURNS:
 *     *DomainGroupConfig - Converted Domain Group configuration structure
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertDomainGroupTaskToConfig(domainGroupTask *pb.DomainGroupTask) (*DomainGroupConfig, error) {
	if domainGroupTask == nil {
		return nil, fmt.Errorf("domainGroupTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &DomainGroupConfig{
		Name:    domainGroupTask.GetName(),
		ID:      0,          // Will be set later when we get the actual ID
		Domains: []string{}, // Default empty domain list
	}

	// Handle domain sources - prioritize file content over domain list
	if domainGroupTask.GetFileContent() != nil && len(domainGroupTask.GetFileContent()) > 0 {
		// Parse file content into domain list
		lines := strings.Split(string(domainGroupTask.GetFileContent()), "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" {
				config.Domains = append(config.Domains, line)
			}
		}
	} else if len(domainGroupTask.GetDomain()) > 0 {
		// Use domain list directly
		config.Domains = make([]string, len(domainGroupTask.GetDomain()))
		copy(config.Domains, domainGroupTask.GetDomain())
	}
	// If neither file content nor domain list is provided, keep empty domain list

	return config, nil
}

/*****************************************************************************
 * NAME: normalizeDomainForFloweye
 *
 * DESCRIPTION:
 *     Normalizes domain format to match floweye's internal representation.
 *     Based on floweye documentation and actual behavior:
 *     - *.domain.com becomes .domain.com (wildcard prefix converted)
 *     - @domain.com stays @domain.com (prefix matching)
 *     - ^domain.com stays ^domain.com (exact matching)
 *     - domain.com stays domain.com (suffix matching)
 *
 * PARAMETERS:
 *     domain - Original domain string
 *
 * RETURNS:
 *     string - Normalized domain string as floweye would store it
 *****************************************************************************/
func normalizeDomainForFloweye(domain string) string {
	if strings.HasPrefix(domain, "*") {
		// floweye converts *.domain.com to .domain.com
		// Remove the * and keep the rest (which already starts with .)
		return domain[1:]
	}
	// @domain.com and ^domain.com remain unchanged
	// plain domain.com remains unchanged
	return domain
}

/*****************************************************************************
 * NAME: CompareDomainGroupConfig
 *
 * DESCRIPTION:
 *     Compares a requested Domain Group configuration with a local configuration.
 *     Used for verification purposes only. Since Domain Group uses full replacement
 *     strategy, this function is primarily used by VerifyDomainGroupConfig.
 *     Handles floweye's domain format normalization (*.domain.com -> .domain.com).
 *
 * PARAMETERS:
 *     logger      - Logger instance for logging operations
 *     configData  - Requested Domain Group configuration (converted from protobuf)
 *     localConfig - Local Domain Group configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareDomainGroupConfig(logger *logger.Logger, configData *DomainGroupConfig, localConfig *DomainGroupConfig) bool {
	if configData == nil || localConfig == nil {
		logger.Debug("One of the configurations is nil")
		return false
	}

	logger.Debug("Comparing Domain Group configuration",
		zap.String("name", configData.Name))

	// Compare basic fields
	if configData.Name != localConfig.Name {
		logger.Debug("Domain Group name mismatch",
			zap.String("expected", configData.Name),
			zap.String("actual", localConfig.Name))
		return false
	}

	// Compare domain count
	if len(configData.Domains) != len(localConfig.Domains) {
		logger.Debug("Domain Group member count mismatch",
			zap.String("name", configData.Name),
			zap.Int("expected", len(configData.Domains)),
			zap.Int("actual", len(localConfig.Domains)))
		return false
	}

	// Convert expected domains to normalized format and create map for efficient lookup
	expectedDomainsMap := make(map[string]bool)
	for _, domain := range configData.Domains {
		normalizedDomain := normalizeDomainForFloweye(domain)
		expectedDomainsMap[normalizedDomain] = true
	}

	// Check if all local domains exist in expected configuration
	for _, localDomain := range localConfig.Domains {
		if _, exists := expectedDomainsMap[localDomain]; !exists {
			logger.Debug("Local domain not found in expected configuration",
				zap.String("name", configData.Name),
				zap.String("local_domain", localDomain),
				zap.Strings("expected_domains", configData.Domains))
			return false
		}
	}

	logger.Debug("Domain Group configurations match",
		zap.String("name", configData.Name))
	return true
}

/*****************************************************************************
 * NAME: VerifyDomainGroupConfig
 *
 * DESCRIPTION:
 *     Verifies if a Domain Group configuration was applied correctly.
 *     Compares the requested configuration with the current local configuration.
 *
 * PARAMETERS:
 *     logger     - Logger instance for logging operations
 *     configData - Requested Domain Group configuration (converted from protobuf)
 *     groupId    - ID of the Domain Group
 *
 * RETURNS:
 *     bool  - True if verification passes, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyDomainGroupConfig(logger *logger.Logger, configData *DomainGroupConfig, groupId int) (bool, error) {
	logger.Info("Verifying Domain Group configuration",
		zap.String("name", configData.Name),
		zap.Int("id", groupId))

	// Get current Domain Group configuration
	localConfig, err := GetDomainGroupConfig(logger, configData.Name, groupId)
	if err != nil {
		return false, fmt.Errorf("failed to get Domain Group configuration: %w", err)
	}

	// Use CompareDomainGroupConfig to verify the configuration
	if !CompareDomainGroupConfig(logger, configData, localConfig) {
		logger.Error("Domain Group configuration verification failed",
			zap.String("name", configData.Name),
			zap.Int("id", groupId))
		return false, nil
	}

	logger.Info("Domain Group configuration verification passed",
		zap.String("name", configData.Name),
		zap.Int("domains", len(configData.Domains)))
	return true, nil
}
