/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_policy_ordering.go
 *
 * DESCRIPTION :    DNS policy ordering and verification functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        17/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"agent/internal/utils"

	"go.uber.org/zap"
)

// DnsPolicyOrderInfo represents DNS policy ordering information
type DnsPolicyOrderInfo struct {
	ID     int
	Cookie uint32
}

/*****************************************************************************
 * NAME: movePolicyToPosition
 *
 * DESCRIPTION:
 *     Moves a DNS policy to the specified position.
 *     Handles both forward insertion and backward movement.
 *
 * PARAMETERS:
 *     currentID       - Current policy ID
 *     targetPosition  - Target position (-1 for last position)
 *     currentPolicies - List of current policies with ordering info
 *
 * RETURNS:
 *     error - Error if movement fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) movePolicyToPosition(currentID, targetPosition int, currentPolicies []DnsPolicyOrderInfo) error {
	p.logger.Debug("moving DNS policy to position",
		zap.Int("current_id", currentID),
		zap.Int("target_position", targetPosition))

	// Handle move to last position
	if targetPosition == -1 {
		// Find the maximum ID
		maxID := 0
		for _, policy := range currentPolicies {
			if policy.ID > maxID {
				maxID = policy.ID
			}
		}
		targetPosition = maxID
		if currentID == targetPosition {
			p.logger.Debug("DNS policy already at last position",
				zap.Int("current_id", currentID))
			return nil
		}
	}

	// If already at target position, no movement needed
	if currentID == targetPosition {
		p.logger.Debug("DNS policy already at target position",
			zap.Int("current_id", currentID),
			zap.Int("target_position", targetPosition))
		return nil
	}

	// Find the cookie for the current policy
	var currentCookie uint32
	for _, policy := range currentPolicies {
		if policy.ID == currentID {
			currentCookie = policy.Cookie
			break
		}
	}

	// Step 1: Move current policy to temporary ID to avoid conflicts
	const tempID = 65535
	if err := p.setDnsPolicyID(currentID, tempID); err != nil {
		return fmt.Errorf("failed to set temporary ID: %w", err)
	}

	// Step 2: Adjust IDs of other policies based on movement direction
	var adjustErr error
	if currentID > targetPosition {
		// Forward insertion: current ID > target ID
		// Increment IDs in range [target, current-1] by +1 (from large to small)
		adjustErr = p.adjustPolicyIDsForwardInsertion(targetPosition, currentID-1, currentPolicies)
	} else if currentID < targetPosition {
		// Backward movement: current ID < target ID
		// Shift policies at target position and beyond by +1 (to make room for insertion)
		adjustErr = p.adjustPolicyIDsBackwardMovement(targetPosition, currentPolicies)
	}

	if adjustErr != nil {
		// Try to restore original ID if adjustment fails
		p.setDnsPolicyID(tempID, currentID)
		return fmt.Errorf("failed to adjust policy IDs: %w", adjustErr)
	}

	// Step 3: Move policy to final target position
	if err := p.setDnsPolicyID(tempID, targetPosition); err != nil {
		return fmt.Errorf("failed to set final position: %w", err)
	}

	p.logger.Info("DNS policy moved successfully",
		zap.Int("from", currentID),
		zap.Int("to", targetPosition),
		zap.Uint32("cookie", currentCookie))

	return nil
}

/*****************************************************************************
 * NAME: handlePolicyDeletionOrdering
 *
 * DESCRIPTION:
 *     Handles DNS policy ordering after deletion.
 *     Decrements IDs of all policies with ID > deleted ID to maintain continuous ordering.
 *
 * PARAMETERS:
 *     ctx             - Context for the operation
 *     deletedPolicyID - ID of the deleted policy
 *
 * RETURNS:
 *     error - Error if ordering adjustment fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) handlePolicyDeletionOrdering(ctx context.Context, deletedPolicyID int) error {
	p.logger.Debug("handling DNS policy deletion ordering",
		zap.Int("deleted_policy_id", deletedPolicyID))

	// Get current policy list
	policies, err := p.getDnsPolicies()
	if err != nil {
		return fmt.Errorf("failed to get DNS policies for deletion ordering: %w", err)
	}

	// Find policies with ID > deleted ID that need to be decremented
	var policiesToShift []DnsPolicyOrderInfo
	for _, policy := range policies {
		// Get detailed config to get cookie information
		detailedPolicy, err := p.getDnsPolicyDetailedConfig(policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed DNS policy config during ordering",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		if policy.ID > deletedPolicyID {
			policiesToShift = append(policiesToShift, DnsPolicyOrderInfo{
				ID:     policy.ID,
				Cookie: detailedPolicy.Cookie,
			})
		}
	}

	if len(policiesToShift) == 0 {
		p.logger.Debug("no DNS policies need ID adjustment after deletion")
		return nil
	}

	// Shift each policy ID by -1 to maintain continuous ordering
	for _, policy := range policiesToShift {
		newID := policy.ID - 1
		if err := p.setDnsPolicyID(policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift DNS policy ID from %d to %d after deletion: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted DNS policy ID after deletion",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	p.logger.Debug("DNS policy deletion ordering completed",
		zap.Int("deleted_policy_id", deletedPolicyID),
		zap.Int("policies_shifted", len(policiesToShift)))

	return nil
}

/*****************************************************************************
 * NAME: setDnsPolicyID
 *
 * DESCRIPTION:
 *     Sets a DNS policy ID using floweye dnspolicy set command.
 *     According to documentation, only id and newid parameters are required.
 *
 * PARAMETERS:
 *     oldID - Current policy ID
 *     newID - New policy ID
 *
 * RETURNS:
 *     error - Error if setting fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) setDnsPolicyID(oldID, newID int) error {
	cmdArgs := []string{"dnspolicy", "set", "id=" + strconv.Itoa(oldID), "newid=" + strconv.Itoa(newID)}

	p.logger.Debug("setting DNS policy ID",
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to set DNS policy ID",
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to set DNS policy ID: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: getDnsPolicies
 *
 * DESCRIPTION:
 *     Gets list of DNS policies from the local PA system.
 *
 * RETURNS:
 *     []*DnsPolicyConfig - List of DNS policy configurations
 *     error              - Error if retrieval fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) getDnsPolicies() ([]*DnsPolicyConfig, error) {
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "dnspolicy", "list", "json=1")
	if err != nil {
		return nil, fmt.Errorf("failed to list DNS policies: %w", err)
	}

	if strings.TrimSpace(output) == "" {
		return []*DnsPolicyConfig{}, nil
	}

	policies, err := ParseDnsPolicyFromJSONOutput(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse DNS policy list: %w", err)
	}

	return policies, nil
}

/*****************************************************************************
 * NAME: adjustPolicyIDsForwardInsertion
 *
 * DESCRIPTION:
 *     Adjusts policy IDs for forward insertion (current ID > target ID).
 *     Increments IDs in range [targetID, currentIDMinus1] by +1.
 *     Processes from large to small to prevent conflicts.
 *
 * PARAMETERS:
 *     targetID        - Target position ID
 *     currentIDMinus1 - Current ID minus 1 (upper bound)
 *     currentPolicies - List of current policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) adjustPolicyIDsForwardInsertion(targetID, currentIDMinus1 int, currentPolicies []DnsPolicyOrderInfo) error {
	// Find policies that need to be shifted
	var policiesToShift []DnsPolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID && policy.ID <= currentIDMinus1 {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	if len(policiesToShift) == 0 {
		return nil
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	sort.Slice(policiesToShift, func(i, j int) bool {
		return policiesToShift[i].ID > policiesToShift[j].ID
	})

	// Shift each policy ID by +1
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setDnsPolicyID(policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID for forward insertion",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: adjustPolicyIDsBackwardMovement
 *
 * DESCRIPTION:
 *     Adjusts policy IDs for backward movement (current ID < target ID).
 *     Shifts policies at target position and beyond by +1 to make room.
 *     Processes from large to small to prevent conflicts.
 *
 * PARAMETERS:
 *     targetPosition  - Target position
 *     currentPolicies - List of current policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *DnsPolicyProcessor) adjustPolicyIDsBackwardMovement(targetPosition int, currentPolicies []DnsPolicyOrderInfo) error {
	// Find policies that need to be shifted (at target position and beyond)
	var policiesToShift []DnsPolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetPosition {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	if len(policiesToShift) == 0 {
		return nil
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	sort.Slice(policiesToShift, func(i, j int) bool {
		return policiesToShift[i].ID > policiesToShift[j].ID
	})

	// Shift each policy ID by +1 to make room for insertion
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setDnsPolicyID(policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID for backward movement",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}
