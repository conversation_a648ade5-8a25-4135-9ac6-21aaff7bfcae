/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_mapping_processor.go
 *
 * DESCRIPTION :    Implementation of iWAN Mapping task processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        wei     04/18/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
)

/*****************************************************************************
 * NAME: IwanMappingProcessor
 *
 * DESCRIPTION:
 *     Processor for handling iWAN Mapping configuration tasks
 *     Implements the TaskProcessor interface
 *
 * FIELDS:
 *     logger             - Logger for processor operations
 *     localConfigs       - Local configuration cache (used for full sync redundant deletion)
 *     workingConfigs     - Working configuration cache (used for all operations)
 *     fullSyncInProgress - Full synchronization flag
 *****************************************************************************/
type IwanMappingProcessor struct {
	logger             *logger.Logger
	localConfigs       map[IwanMappingKey]IwanMappingConfig // Cache for full sync redundant deletion
	workingConfigs     map[IwanMappingKey]IwanMappingConfig // Working cache for operations (can be refreshed during full sync)
	fullSyncInProgress bool
}

/*****************************************************************************
 * NAME: NewIwanMappingProcessor
 *
 * DESCRIPTION:
 *     Creates a new IwanMappingProcessor instance
 *     Initializes the local configuration cache
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *IwanMappingProcessor - Newly created processor instance
 *****************************************************************************/
func NewIwanMappingProcessor(log *logger.Logger) *IwanMappingProcessor {
	processor := &IwanMappingProcessor{
		logger:             log.WithModule("iwan-mapping-processor"),
		localConfigs:       make(map[IwanMappingKey]IwanMappingConfig),
		workingConfigs:     make(map[IwanMappingKey]IwanMappingConfig),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     返回处理器处理的任务类型
 *
 * RETURNS:
 *     pb.TaskType - 任务类型
 *****************************************************************************/
func (p *IwanMappingProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_IWAN_MAPPING
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     处理iWAN Mapping任务
 *     根据任务动作分发到不同的处理方法
 *
 * RETURNS:
 *     string - 处理结果描述
 *     error - 处理过程中的错误
 *****************************************************************************/
func (p *IwanMappingProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	if task.GetIwanMappingTask() == nil {
		return "", fmt.Errorf("iwan mapping task payload is nil")
	}

	mappingTask := task.GetIwanMappingTask()
	p.logger.Info("Processing iWAN Mapping task",
		zap.String("proxy", mappingTask.GetProxy()),
		zap.Int32("port", mappingTask.GetPort()),
		zap.String("server", mappingTask.GetServer()))

	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handleConfigChange(ctx, mappingTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeleteConfig(ctx, mappingTask)
	default:
		return "", fmt.Errorf("unknown task action: %v", task.TaskAction)
	}
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts full synchronization
 *     Retrieves all local configurations
 *
 * RETURNS:
 *     error - Error during synchronization
 *****************************************************************************/
func (p *IwanMappingProcessor) StartFullSync() error {
	p.fullSyncInProgress = true
	return p.refreshLocalConfigs()
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends full synchronization
 *     Deletes configurations that exist locally but were not included in the full sync
 *****************************************************************************/
func (p *IwanMappingProcessor) EndFullSync() {
	// Create a copy of remaining iWAN Mapping configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations while keeping fullSyncInProgress = true
	remainingMappings := make(map[IwanMappingKey]IwanMappingConfig)
	for key, config := range p.localConfigs {
		remainingMappings[key] = config
	}

	// Delete configurations that exist locally but were not included in the full sync
	// Keep fullSyncInProgress = true during cleanup so handleConfigChange can properly
	// remove items from localConfigs map
	for _, config := range remainingMappings {
		p.logger.Info("Deleting iWAN Mapping not in full sync",
			zap.String("proxy", config.Proxy),
			zap.Int("port", config.Port),
			zap.String("server", config.Server))

		// Create delete task
		deleteTask := &pb.IwanMappingTask{
			Proxy:  config.Proxy,
			Port:   int32(config.Port),
			Server: nil, // nil server means delete
		}

		// Execute delete operation
		_, err := p.handleConfigChange(context.Background(), deleteTask, pb.TaskAction_DELETE_CONFIG)
		if err != nil {
			p.logger.Error("Failed to delete iWAN Mapping during full sync cleanup",
				zap.String("proxy", config.Proxy),
				zap.Int("port", config.Port),
				zap.Error(err))
		}
	}

	// Verify that all remaining iWAN Mappings have been cleaned up
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some iWAN Mappings were not cleaned up during full sync",
			zap.Int("remaining_count", len(p.localConfigs)))

		// Log the remaining mappings for debugging
		for _, config := range p.localConfigs {
			p.logger.Warn("remaining iWAN Mapping after cleanup",
				zap.String("proxy", config.Proxy),
				zap.Int("port", config.Port),
				zap.String("server", config.Server))
		}
	} else {
		p.logger.Info("all remaining iWAN Mappings cleaned up successfully")
	}

	// Now set fullSyncInProgress to false after cleanup is complete
	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[IwanMappingKey]IwanMappingConfig)
	p.workingConfigs = make(map[IwanMappingKey]IwanMappingConfig)
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles new and edit configuration tasks
 *     Converts protobuf message to internal structure at entry point
 *     Builds command arguments using internal structure
 *     Executes configuration command
 *     Verifies if configuration was successfully applied
 *
 * RETURNS:
 *     string - Processing result description
 *     error - Error during processing
 *****************************************************************************/
func (p *IwanMappingProcessor) handleConfigChange(ctx context.Context, mappingTask *pb.IwanMappingTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertIwanMappingTaskToConfig(mappingTask)
	if err != nil {
		p.logger.Error("failed to convert iWAN mapping task to config",
			zap.String("name", mappingTask.GetProxy()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert iWAN mapping configuration: %v", err), err
	}

	// Log operation details using converted data
	p.logger.Info("Handling iWAN Mapping config change",
		zap.String("proxy", configData.Proxy),
		zap.Int("port", configData.Port),
		zap.String("server", configData.Server),
		zap.String("action", taskAction.String()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Register cleanup defer after validation (validation is done in ConvertIwanMappingTaskToConfig)
	if p.fullSyncInProgress {
		key := NewIwanMappingKeyFromConfig(configData) // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, key)
		}()
	}

	// Get configurations for operation (uses working configs)
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Build command arguments using converted data
	cmdArgs := []string{"iwanmap", "set",
		fmt.Sprintf("proxy=%s", configData.Proxy),
		fmt.Sprintf("port=%d", configData.Port)}

	// If server is empty, it indicates a delete operation
	if configData.Server == "" {
		cmdArgs = append(cmdArgs, "server=NULL")
	} else {
		cmdArgs = append(cmdArgs, fmt.Sprintf("server=%s", configData.Server))
	}

	// Execute configuration command using converted data
	p.logger.Info("Executing floweye command for iWAN Mapping configuration",
		zap.String("proxy", configData.Proxy),
		zap.Int("port", configData.Port),
		zap.String("server", configData.Server),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success for delete operations (idempotent delete operation)
		if configData.Server == "" &&
			(strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST")) {
			p.logger.Info("iWAN Mapping already does not exist, treating as successful delete",
				zap.String("proxy", configData.Proxy),
				zap.Int("port", configData.Port))
		} else {
			p.logger.Error("Failed to execute floweye command for iWAN Mapping configuration",
				zap.String("proxy", configData.Proxy),
				zap.Int("port", configData.Port),
				zap.Error(err),
				zap.String("output", output))
			return "", fmt.Errorf("failed to execute command: %w", err)
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("proxy", configData.Proxy),
		zap.Int("port", configData.Port),
		zap.String("output", output))

	// Refresh working configs to include the newly created/updated/deleted mapping
	// This ensures subsequent operations have access to latest configs
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation",
			zap.String("proxy", configData.Proxy),
			zap.Int("port", configData.Port),
			zap.Error(err))
		// Don't return error as the main operation succeeded
	}

	// Skip post-delete verification for delete operations to improve performance
	if configData.Server == "" {
		// For delete operations, skip verification
		p.logger.Debug("Skipping post-delete verification for improved performance")
	} else {
		// For add or edit operations, verify if the configuration matches the expected state
		verified, err := p.VerifyIwanMappingConfig(ctx, configData)
		if err != nil {
			return "", fmt.Errorf("failed to verify config: %w", err)
		}
		if !verified {
			return "", fmt.Errorf("config verification failed")
		}
	}

	if configData.Server == "" {
		return fmt.Sprintf("Successfully deleted iWAN Mapping for proxy=%s, port=%d",
			configData.Proxy, configData.Port), nil
	} else {
		return fmt.Sprintf("Successfully applied iWAN Mapping config for proxy=%s, port=%d, server=%s",
			configData.Proxy, configData.Port, configData.Server), nil
	}
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles delete configuration task
 *     Sets server to NULL and calls handleConfigChange
 *
 * RETURNS:
 *     string - Processing result description
 *     error - Error during processing
 *****************************************************************************/
func (p *IwanMappingProcessor) handleDeleteConfig(ctx context.Context, mappingTask *pb.IwanMappingTask) (string, error) {
	// Log delete operation
	p.logger.Info("Handling iWAN Mapping delete",
		zap.String("proxy", mappingTask.GetProxy()),
		zap.Int32("port", mappingTask.GetPort()))

	// Create a copy of the task with nil server (which will be treated as NULL)
	deleteTask := &pb.IwanMappingTask{
		Proxy:  mappingTask.Proxy,
		Port:   mappingTask.Port,
		Server: nil, // nil server means delete
	}

	// Use handleConfigChange with DELETE action
	return p.handleConfigChange(ctx, deleteTask, pb.TaskAction_DELETE_CONFIG)
}

/*****************************************************************************
 * NAME: VerifyIwanMappingConfig
 *
 * DESCRIPTION:
 *     Verifies if configuration was successfully applied
 *     Uses the VerifyIwanMappingConfig function from iwan_mapping_config.go
 *     Updated to use internal IwanMappingConfig structure and working configs
 *
 * RETURNS:
 *     bool - Whether verification passed
 *     error - Error during verification
 *****************************************************************************/
func (p *IwanMappingProcessor) VerifyIwanMappingConfig(ctx context.Context, configData *IwanMappingConfig) (bool, error) {
	return VerifyIwanMappingConfig(ctx, p.logger, p.workingConfigs, configData)
}

/*****************************************************************************
 * NAME: fetchIwanMappingConfigs
 *
 * DESCRIPTION:
 *     Fetches iWAN Mapping configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[IwanMappingKey]IwanMappingConfig - iWAN Mapping configurations by key
 *     error                                - Error if fetch fails
 *****************************************************************************/
func (p *IwanMappingProcessor) fetchIwanMappingConfigs() (map[IwanMappingKey]IwanMappingConfig, error) {
	p.logger.Debug("Fetching iWAN Mapping configurations from floweye")

	configs, err := GetIwanMappingConfigs(p.logger)
	if err != nil {
		p.logger.Error("Failed to get iWAN Mapping configurations", zap.Error(err))
		return nil, fmt.Errorf("failed to get iWAN Mapping configurations: %w", err)
	}

	p.logger.Debug("iWAN Mapping configurations fetched", zap.Int("count", len(configs)))
	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local iWAN Mapping configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IwanMappingProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local iWAN Mapping configurations")

	configs, err := p.fetchIwanMappingConfigs()
	if err != nil {
		return err
	}

	p.localConfigs = configs
	p.logger.Debug("local iWAN Mapping configurations refreshed", zap.Int("count", len(configs)))
	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working iWAN Mapping configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IwanMappingProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working iWAN Mapping configurations")

	configs, err := p.fetchIwanMappingConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	p.workingConfigs = configs
	p.logger.Debug("working iWAN Mapping configurations refreshed", zap.Int("count", len(configs)))
	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *IwanMappingProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}
