/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      lan_config.go
 *
 * DESCRIPTION :    LAN configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: LanConfig
 *
 * DESCRIPTION:
 *     Represents the configuration of a LAN interface.
 *     Stores all relevant LAN parameters retrieved from the system.
 *
 * FIELDS:
 *     Id          - LAN interface ID
 *     Name        - LAN interface name
 *     Type        - LAN interface type (rtif)
 *     State       - Interface state (0: down, 1: up)
 *     Ifname      - Network interface name
 *     Mtu         - Maximum transmission unit
 *     Addr        - IP address
 *     Mask        - Subnet mask
 *     CloneMac    - MAC address to clone
 *     DhcpEnable  - DHCP server enabled
 *     DhcpPool    - DHCP address pool
 *     LeaseTtl    - DHCP lease time
 *     Dns0        - Primary DNS server
 *     Dns1        - Secondary DNS server
 *     DhcpGateway - DHCP gateway
 *     DhcpMask    - DHCP subnet mask
 *     DhcpAcAddr  - DHCP AC address
 *     DhcpDomain  - DHCP domain name
 *     DhcpOptions - DHCP options
 *****************************************************************************/
type LanConfig struct {
	Id          int               // LAN interface ID
	Name        string            // LAN interface name
	Type        string            // LAN interface type (rtif)
	State       int               // Interface state (0: down, 1: up)
	Ifname      string            // Network interface name
	Mtu         int               // Maximum transmission unit
	Addr        string            // IP address
	Mask        string            // Subnet mask
	CloneMac    string            // MAC address to clone
	DhcpEnable  int               // DHCP server enabled (0: disabled, 1: enabled)
	DhcpPool    string            // DHCP address pool
	LeaseTtl    int               // DHCP lease time
	Dns0        string            // Primary DNS server
	Dns1        string            // Secondary DNS server
	DhcpGateway string            // DHCP gateway
	DhcpMask    string            // DHCP subnet mask
	DhcpAcAddr  string            // DHCP AC address
	DhcpDomain  string            // DHCP domain name
	DhcpOptions map[string]string // DHCP options
}

/*****************************************************************************
 * NAME: GetLocalLanConfigs
 *
 * DESCRIPTION:
 *     Retrieves all local LAN configurations from the system.
 *     Executes the floweye nat listproxy command and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*LanConfig - Map of LAN names to their configurations
 *     error                 - Error if retrieval fails
 *****************************************************************************/
func GetLocalLanConfigs(logger *logger.Logger) (map[string]*LanConfig, error) {
	logger.Info("Starting to retrieve all local LAN configurations")

	// Execute floweye nat listproxy command with JSON output
	// Execute floweye command to list LAN configurations
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "listproxy", "json=1", "type=lan")
	if err != nil {
		logger.Error("failed to execute floweye nat listproxy command",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get LAN list: %v", err)
	}

	// Parse JSON output to extract LAN names only
	lans := make(map[string]*LanConfig)

	// Parse JSON output using unified floweye JSON parser
	lanList, err := utils.ParseFloweyeJSON(output)
	if err != nil {
		logger.Error("failed to parse floweye JSON output",
			zap.String("output", output),
			zap.Error(err))
		return nil, fmt.Errorf("failed to parse JSON output: %v", err)
	}

	// Extract LAN configurations
	for _, lanEntry := range lanList {
		// Only extract name and type for filtering
		if name, ok := lanEntry["name"].(string); ok {
			if lanType, ok := lanEntry["type"].(string); ok && lanType == "rtif" {
				// Create a minimal config with just the name
				lans[name] = &LanConfig{Name: name, DhcpOptions: make(map[string]string)}
			}
		}
	}

	// Log summary of all LANs
	lanNames := make([]string, 0, len(lans))
	for name := range lans {
		lanNames = append(lanNames, name)
	}

	logger.Info("Retrieved all local LAN configurations",
		zap.Int("count", len(lans)),
		zap.Strings("lan_names", lanNames))

	// Get detailed configuration for each LAN using GetLanConfig
	for name := range lans {
		detailedConfig, err := GetLanConfig(logger, name)
		if err != nil {
			logger.Warn("failed to get detailed config for LAN",
				zap.String("lan", name),
				zap.Error(err))
			continue
		}

		// Replace the basic config with the detailed config
		lans[name] = detailedConfig
	}

	// Log detailed configuration for each LAN
	for name, config := range lans {
		logger.Info("LAN configuration details",
			zap.String("name", name),
			zap.String("ifname", config.Ifname),
			zap.Int("mtu", config.Mtu),
			zap.String("addr", config.Addr),
			zap.String("mask", config.Mask),
			zap.String("clone_mac", config.CloneMac),
			zap.Int("dhcp_enable", config.DhcpEnable),
			zap.String("dhcp_pool", config.DhcpPool),
			zap.Int("lease_ttl", config.LeaseTtl),
			zap.String("dns0", config.Dns0),
			zap.String("dns1", config.Dns1),
			zap.String("dhcp_domain", config.DhcpDomain))
	}

	return lans, nil
}

// Helper function to parse a LAN entry from JSON
func parseLanEntry(lanEntry map[string]interface{}) *LanConfig {
	lanConfig := &LanConfig{
		DhcpOptions: make(map[string]string),
	}

	// Extract fields
	if id, ok := lanEntry["id"].(float64); ok {
		lanConfig.Id = int(id)
	}

	if name, ok := lanEntry["name"].(string); ok {
		lanConfig.Name = name
	}

	if lanType, ok := lanEntry["type"].(string); ok {
		lanConfig.Type = lanType
	}

	if state, ok := lanEntry["state"].(float64); ok {
		lanConfig.State = int(state)
	}

	if ifname, ok := lanEntry["if"].(string); ok {
		lanConfig.Ifname = ifname
	}

	if mtu, ok := lanEntry["mtu"].(float64); ok {
		lanConfig.Mtu = int(mtu)
	}

	if addr, ok := lanEntry["ip"].(string); ok {
		lanConfig.Addr = addr
	}

	if mask, ok := lanEntry["mask"].(string); ok {
		lanConfig.Mask = mask
	}

	return lanConfig
}

/*****************************************************************************
 * NAME: GetLanConfig
 *
 * DESCRIPTION:
 *     Retrieves the configuration of a specific LAN interface.
 *     Executes the floweye nat getproxy command and parses the output.
 *
 * PARAMETERS:
 *     logger  - Logger instance for logging operations
 *     lanName - Name of the LAN interface to retrieve configuration for
 *
 * RETURNS:
 *     *LanConfig - LAN configuration structure
 *     error      - Error if retrieval fails
 *****************************************************************************/
func GetLanConfig(logger *logger.Logger, lanName string) (*LanConfig, error) {
	logger.Debug("retrieving configuration for LAN", zap.String("lan", lanName))

	// Execute floweye nat getproxy command
	output, err := utils.ExecuteCommand(logger, 5, "floweye", "nat", "getproxy", lanName)
	if err != nil {
		logger.Error("failed to execute floweye nat getproxy command",
			zap.Error(err),
			zap.String("lan", lanName),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get LAN config: %v", err)
	}

	// Parse output into a map for easier access
	// Note: For dhcp_option, we need to handle multiple entries with the same key
	configMap := make(map[string]string)
	dhcpOptions := make([]string, 0) // Store all dhcp_option values separately
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			// Handle multiple dhcp_option entries
			if key == "dhcp_option" {
				dhcpOptions = append(dhcpOptions, value)
			} else {
				configMap[key] = value
			}
		}
	}

	// Create configuration object with default values
	config := &LanConfig{
		Name:        lanName,
		DhcpOptions: make(map[string]string),
	}

	// Extract values using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "proxyid":
			if val, err := strconv.Atoi(value); err == nil {
				config.Id = val
			}
		case "type":
			config.Type = value
		case "state":
			if val, err := strconv.Atoi(value); err == nil {
				config.State = val
			}
		case "ifname":
			config.Ifname = value
		case "mtu":
			if val, err := strconv.Atoi(value); err == nil {
				config.Mtu = val
			}
		case "addr":
			config.Addr = value
		case "netmask":
			config.Mask = value
		case "clonemac":
			config.CloneMac = value
		}
	}

	// DHCP related fields
	if dhcpEnable, ok := configMap["dhcp_enable"]; ok {
		if val, err := strconv.Atoi(dhcpEnable); err == nil {
			config.DhcpEnable = val
		}
	}

	if dhcpPool, ok := configMap["dhcp_pool"]; ok {
		config.DhcpPool = dhcpPool
	}

	if leaseTtl, ok := configMap["leasettl"]; ok {
		if val, err := strconv.Atoi(leaseTtl); err == nil {
			config.LeaseTtl = val
		}
	}

	if dns0, ok := configMap["dns0"]; ok {
		config.Dns0 = dns0
	}

	if dns1, ok := configMap["dns1"]; ok {
		config.Dns1 = dns1
	}

	if dhcpGateway, ok := configMap["dhcp_gateway"]; ok {
		config.DhcpGateway = dhcpGateway
	}

	if dhcpMask, ok := configMap["dhcp_mask"]; ok {
		config.DhcpMask = dhcpMask
	}

	if dhcpAcAddr, ok := configMap["dhcp_acaddr"]; ok {
		config.DhcpAcAddr = dhcpAcAddr
	}

	if dhcpDomain, ok := configMap["dhcp_domain"]; ok {
		config.DhcpDomain = dhcpDomain
	}

	// DHCP options
	// Parse dhcp_option entries from floweye output format: dhcp_option=<num>,<type>,<value>
	for _, optionValue := range dhcpOptions {
		// Parse the value format: "61,str,MSFT" or "12,str,host-123"
		parts := strings.SplitN(optionValue, ",", 3)
		if len(parts) >= 3 {
			optionNumber := parts[0]
			optionType := parts[1]
			optionVal := parts[2]
			config.DhcpOptions[optionNumber] = optionType + "," + optionVal
		}
	}

	logger.Debug("retrieved LAN configuration",
		zap.String("lan", lanName),
		zap.String("ifname", config.Ifname),
		zap.String("addr", config.Addr),
		zap.Int("dhcp_enable", config.DhcpEnable))
	return config, nil
}

/*****************************************************************************
 * NAME: VerifyLanConfig
 *
 * DESCRIPTION:
 *     Verifies that the LAN configuration was applied correctly.
 *     Compares the requested configuration with the actual configuration on the system.
 *
 * PARAMETERS:
 *     logger     - Logger instance for logging operations
 *     configData - Converted LAN configuration data to verify
 *
 * RETURNS:
 *     bool  - True if configuration matches, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyLanConfig(logger *logger.Logger, configData *LanConfig) (bool, error) {
	logger.Info("verifying LAN configuration", zap.String("name", configData.Name))

	// Get current LAN configuration
	config, err := GetLanConfig(logger, configData.Name)
	if err != nil {
		return false, err
	}

	// Verify basic parameters
	if config.Ifname != configData.Ifname {
		logger.Error("LAN ifname mismatch",
			zap.String("lan", configData.Name),
			zap.String("expected", configData.Ifname),
			zap.String("actual", config.Ifname))
		return false, nil
	}

	if config.Mtu != configData.Mtu {
		logger.Error("LAN MTU mismatch",
			zap.String("lan", configData.Name),
			zap.Int("expected", configData.Mtu),
			zap.Int("actual", config.Mtu))
		return false, nil
	}

	// Verify IP address
	if configData.Addr != "" && configData.Addr != config.Addr {
		logger.Error("LAN IP address mismatch",
			zap.String("lan", configData.Name),
			zap.String("expected", configData.Addr),
			zap.String("actual", config.Addr))
		return false, nil
	}

	// Verify subnet mask
	if configData.Mask != "" && configData.Mask != config.Mask {
		logger.Error("LAN subnet mask mismatch",
			zap.String("lan", configData.Name),
			zap.String("expected", configData.Mask),
			zap.String("actual", config.Mask))
		return false, nil
	}

	// Verify clone MAC
	if configData.CloneMac != config.CloneMac {
		logger.Error("LAN clone MAC mismatch",
			zap.String("lan", configData.Name),
			zap.String("expected", configData.CloneMac),
			zap.String("actual", config.CloneMac))
		return false, nil
	}

	logger.Info("LAN configuration verified successfully",
		zap.String("name", configData.Name))
	return true, nil
}

// VerifyDhcpConfig moved to dhcp_config.go

/*****************************************************************************
 * NAME: CompareLanConfig
 *
 * DESCRIPTION:
 *     Compares the requested configuration with the local configuration.
 *     Determines if the requested configuration matches the existing configuration.
 *
 * PARAMETERS:
 *     logger      - Logger instance for logging operations
 *     configData  - Converted LAN configuration data to compare
 *     localConfig - Local LAN configuration to compare against
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func CompareLanConfig(logger *logger.Logger, configData *LanConfig, localConfig *LanConfig) bool {
	if localConfig == nil || configData == nil {
		return false
	}

	// Compare basic parameters
	if configData.Ifname != localConfig.Ifname {
		return false
	}

	if configData.Mtu != localConfig.Mtu {
		return false
	}

	// Compare IP address
	if configData.Addr != localConfig.Addr {
		return false
	}

	// Compare subnet mask
	if configData.Mask != localConfig.Mask {
		return false
	}

	// Compare clone MAC
	if configData.CloneMac != localConfig.CloneMac {
		return false
	}

	return true
}

// CompareDhcpConfig moved to dhcp_config.go

/*****************************************************************************
 * NAME: ConvertLanTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf LanTask message to a unified LanConfig structure.
 *     This function performs the single conversion point for the entire
 *     processing pipeline, eliminating repeated protobuf parsing.
 *
 * PARAMETERS:
 *     lanTask - Protobuf LAN task message to convert
 *
 * RETURNS:
 *     *LanConfig - Converted LAN configuration structure
 *     error      - Error if conversion fails
 *****************************************************************************/
func ConvertLanTaskToConfig(lanTask *pb.LanTask) (*LanConfig, error) {
	if lanTask == nil {
		return nil, fmt.Errorf("lanTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &LanConfig{
		Name:        lanTask.GetName(),
		Ifname:      lanTask.GetIfname(),
		Mtu:         int(lanTask.GetMtu()),
		DhcpOptions: make(map[string]string),
		// Default values for optional fields
		CloneMac:    "00-00-00-00-00-00", // Default clone MAC
		DhcpEnable:  0,                   // DHCP disabled by default
		LeaseTtl:    86400,               // Default lease time
		Dns0:        "",                  // Empty DNS servers by default
		Dns1:        "",
		DhcpGateway: "0.0.0.0", // Default gateway
		DhcpMask:    "0.0.0.0", // Default DHCP mask
		DhcpAcAddr:  "0.0.0.0", // Default AC address
		DhcpDomain:  "",        // Empty domain by default
		DhcpPool:    "",        // Empty DHCP pool by default
	}

	// Handle IP address fields
	if lanTask.GetAddr() != nil {
		config.Addr = utils.GetIpString(lanTask.GetAddr())
	} else {
		config.Addr = ""
	}

	if lanTask.GetMask() != nil {
		config.Mask = utils.GetIpString(lanTask.GetMask())
	} else {
		config.Mask = ""
	}

	// Handle optional clone MAC
	if lanTask.GetCloneMac() != "" {
		config.CloneMac = lanTask.GetCloneMac()
	}

	return config, nil
}
