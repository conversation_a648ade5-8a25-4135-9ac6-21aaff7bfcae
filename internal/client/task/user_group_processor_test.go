/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_group_processor_test.go
 *
 * DESCRIPTION :    Unit tests for user group processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	pb "agent/internal/pb"
	"context"
	"errors"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test GetTaskType
func TestUserGroupProcessor_GetTaskType(t *testing.T) {
	log := setupUserGroupTestLogger()
	processor := NewUserGroupProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_USER_GROUP, taskType)
}

// Test ProcessTask with nil payload
func TestUserGroupProcessor_ProcessTask_NilPayload(t *testing.T) {
	log := setupUserGroupTestLogger()
	processor := NewUserGroupProcessor(log)

	// Create a task with nil payload
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload:    nil,
	}

	// Call the function
	_, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "user group task data is nil")
}

// Test ProcessTask with unsupported action
func TestUserGroupProcessor_ProcessTask_UnsupportedAction(t *testing.T) {
	log := setupUserGroupTestLogger()
	processor := NewUserGroupProcessor(log)

	// Create a task with an unsupported action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER_GROUP,
		TaskAction: 99, // Invalid action
		Payload: &pb.DeviceTask_UserGroupTask{
			UserGroupTask: &pb.UserGroupTask{
				Id:   100,
				Name: "group1",
				Pid:  1,
			},
		},
	}

	// Call the function
	_, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported task action")
}

// Test ProcessTask with missing required fields
func TestUserGroupProcessor_ProcessTask_MissingRequiredFields(t *testing.T) {
	log := setupUserGroupTestLogger()
	processor := NewUserGroupProcessor(log)

	// Test cases for missing required fields
	testCases := []struct {
		name          string
		userGroupTask *pb.UserGroupTask
		errorMsg      string
	}{
		{
			name: "Missing ID",
			userGroupTask: &pb.UserGroupTask{
				Id:   0, // Invalid ID
				Name: "group1",
				Pid:  1,
			},
			errorMsg: "User group ID must be between 1 and 2063",
		},
		{
			name: "Invalid ID (too large)",
			userGroupTask: &pb.UserGroupTask{
				Id:   3000, // Invalid ID
				Name: "group1",
				Pid:  1,
			},
			errorMsg: "User group ID must be between 1 and 2063",
		},
		{
			name: "Missing Name",
			userGroupTask: &pb.UserGroupTask{
				Id:   100,
				Name: "", // Missing name
				Pid:  1,
			},
			errorMsg: "User group name is required",
		},
		{
			name: "Missing PID",
			userGroupTask: &pb.UserGroupTask{
				Id:   100,
				Name: "group1",
				Pid:  0, // Missing PID
			},
			errorMsg: "User group PID is required",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a task with missing required fields
			task := &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_USER_GROUP,
				TaskAction: pb.TaskAction_NEW_CONFIG,
				Payload: &pb.DeviceTask_UserGroupTask{
					UserGroupTask: tc.userGroupTask,
				},
			}

			// Call the function
			_, err := processor.ProcessTask(context.Background(), task)

			// Verify results
			assert.Error(t, err)
			assert.Contains(t, err.Error(), tc.errorMsg)
		})
	}
}

// Test ProcessTask with NEW_CONFIG action
func TestUserGroupProcessor_ProcessTask_NewConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
			"name=default\n"+
			"pid=0\n"+
			"start=0.0.0.0\n"+
			"end=0.0.0.0\n"+
			"ratein=0\n"+
			"rateout=0\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=0.0.0.0\n"+
			"maxonlinetime=0\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Mock the add command
	mockExecuteCommand.On("Execute", 10, "floweye", mock.Anything).Return(
		"User group added successfully",
		nil,
	)

	// Mock the get command for verification
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
			"name=group1\n"+
			"pid=1\n"+
			"start=*************\n"+
			"end=*************\n"+
			"ratein=1024\n"+
			"rateout=2048\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=*******\n"+
			"maxonlinetime=24\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Create a user group task
	userGroupTask := &pb.UserGroupTask{
		Id:   100,
		Name: "group1",
		Pid:  1,
	}

	// Create a task with NEW_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_UserGroupTask{
			UserGroupTask: userGroupTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User group configuration created successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with EDIT_CONFIG action
func TestUserGroupProcessor_ProcessTask_EditConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0\n"+
			"100 group1 1 default ************* ************* 101 101 1024 2048 0 0 101 ************* 0 reject eth0 0 ::/0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
			"name=default\n"+
			"pid=0\n"+
			"start=0.0.0.0\n"+
			"end=0.0.0.0\n"+
			"ratein=0\n"+
			"rateout=0\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=0.0.0.0\n"+
			"maxonlinetime=0\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
			"name=group1\n"+
			"pid=1\n"+
			"start=*************\n"+
			"end=*************\n"+
			"ratein=1024\n"+
			"rateout=2048\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=*******\n"+
			"maxonlinetime=24\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Mock the set command
	mockExecuteCommand.On("Execute", 10, "floweye", mock.Anything).Return(
		"User group updated successfully",
		nil,
	)

	// Create a user group task with updated values
	userGroupTask := &pb.UserGroupTask{
		Id:   100,
		Name: "group1_updated",
		Pid:  1,
	}

	// Create a task with EDIT_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER_GROUP,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		Payload: &pb.DeviceTask_UserGroupTask{
			UserGroupTask: userGroupTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User group configuration updated successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with DELETE_CONFIG action
func TestUserGroupProcessor_ProcessTask_DeleteConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0\n"+
			"100 group1 1 default ************* ************* 101 101 1024 2048 0 0 101 ************* 0 reject eth0 0 ::/0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
			"name=default\n"+
			"pid=0\n"+
			"start=0.0.0.0\n"+
			"end=0.0.0.0\n"+
			"ratein=0\n"+
			"rateout=0\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=0.0.0.0\n"+
			"maxonlinetime=0\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
			"name=group1\n"+
			"pid=1\n"+
			"start=*************\n"+
			"end=*************\n"+
			"ratein=1024\n"+
			"rateout=2048\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=*******\n"+
			"maxonlinetime=24\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Mock the remove command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "remove", "id=100"}).Return(
		"User group removed successfully",
		nil,
	)

	// Mock refreshLocalConfigs after deletion
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0",
		nil,
	)

	// Create a user group task
	userGroupTask := &pb.UserGroupTask{
		Id: 100,
	}

	// Create a task with DELETE_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER_GROUP,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_UserGroupTask{
			UserGroupTask: userGroupTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User group deleted successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with DELETE_CONFIG action for non-existent user group
func TestUserGroupProcessor_ProcessTask_DeleteConfig_NonExistent(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
			"name=default\n"+
			"pid=0\n"+
			"start=0.0.0.0\n"+
			"end=0.0.0.0\n"+
			"ratein=0\n"+
			"rateout=0\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=0.0.0.0\n"+
			"maxonlinetime=0\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Create a user group task for a non-existent user group
	userGroupTask := &pb.UserGroupTask{
		Id: 100, // Non-existent ID
	}

	// Create a task with DELETE_CONFIG action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_USER_GROUP,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_UserGroupTask{
			UserGroupTask: userGroupTask,
		},
	}

	// Call the function
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "User group not found, nothing to delete")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test StartFullSync
func TestUserGroupProcessor_StartFullSync(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0\n"+
			"100 group1 1 default ************* ************* 101 101 1024 2048 0 0 101 ************* 0 reject eth0 0 ::/0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
			"name=default\n"+
			"pid=0\n"+
			"start=0.0.0.0\n"+
			"end=0.0.0.0\n"+
			"ratein=0\n"+
			"rateout=0\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=0.0.0.0\n"+
			"maxonlinetime=0\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
			"name=group1\n"+
			"pid=1\n"+
			"start=*************\n"+
			"end=*************\n"+
			"ratein=1024\n"+
			"rateout=2048\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=*******\n"+
			"maxonlinetime=24\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Call the function
	err := processor.StartFullSync()

	// Verify results
	assert.NoError(t, err)
	assert.True(t, processor.fullSyncInProgress)
	assert.Equal(t, 2, len(processor.localConfigs))

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test StartFullSync with error
func TestUserGroupProcessor_StartFullSync_Error(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs with error
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"",
		errors.New("command failed"),
	)

	// Call the function
	err := processor.StartFullSync()

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to refresh local configurations")
	assert.False(t, processor.fullSyncInProgress)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test EndFullSync
func TestUserGroupProcessor_EndFullSync(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Set fullSyncInProgress to true
	processor.fullSyncInProgress = true

	// Add some local configs
	processor.localConfigs = map[int32]*UserGroupConfig{
		1: {
			ID:   1,
			Name: "default",
			PID:  0,
		},
		100: {
			ID:   100,
			Name: "group1",
			PID:  1,
		},
	}

	// Mock the remove command for non-default group
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "remove", "id=100"}).Return(
		"User group removed successfully",
		nil,
	)

	// Call the function
	processor.EndFullSync()

	// Verify results
	assert.False(t, processor.fullSyncInProgress)
	assert.Equal(t, 0, len(processor.localConfigs))

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test refreshLocalConfigs
func TestUserGroupProcessor_refreshLocalConfigs(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"1 default 0 - 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0 reject eth0 0 ::/0\n"+
			"100 group1 1 default ************* ************* 101 101 1024 2048 0 0 101 ************* 0 reject eth0 0 ::/0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=1"}).Return(
		"id=1\n"+
			"name=default\n"+
			"pid=0\n"+
			"start=0.0.0.0\n"+
			"end=0.0.0.0\n"+
			"ratein=0\n"+
			"rateout=0\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=0.0.0.0\n"+
			"maxonlinetime=0\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "get", "id=100"}).Return(
		"id=100\n"+
			"name=group1\n"+
			"pid=1\n"+
			"start=*************\n"+
			"end=*************\n"+
			"ratein=1024\n"+
			"rateout=2048\n"+
			"ratein6=0\n"+
			"rateout6=0\n"+
			"dns=*******\n"+
			"maxonlinetime=24\n"+
			"clntepa=reject\n"+
			"prefix=::\n"+
			"pfxlen=0",
		nil,
	)

	// Call the function
	err := processor.refreshLocalConfigs()

	// Verify results
	assert.NoError(t, err)
	assert.Equal(t, 2, len(processor.localConfigs))

	// Verify default group
	defaultGroup, exists := processor.localConfigs[1]
	assert.True(t, exists)
	assert.Equal(t, int32(1), defaultGroup.ID)
	assert.Equal(t, "default", defaultGroup.Name)
	assert.Equal(t, int32(0), defaultGroup.PID)

	// Verify group1
	group1, exists := processor.localConfigs[100]
	assert.True(t, exists)
	assert.Equal(t, int32(100), group1.ID)
	assert.Equal(t, "group1", group1.Name)
	assert.Equal(t, int32(1), group1.PID)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test refreshLocalConfigs with error
func TestUserGroupProcessor_refreshLocalConfigs_Error(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupUserGroupTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	processor := NewUserGroupProcessor(log)

	// Mock refreshLocalConfigs with error
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"pppoeippool", "list"}).Return(
		"",
		errors.New("command failed"),
	)

	// Call the function
	err := processor.refreshLocalConfigs()

	// Verify results
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to list user groups")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}
