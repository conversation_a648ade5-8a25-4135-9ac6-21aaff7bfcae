/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      ip_group_processor.go
 *
 * DESCRIPTION :    IP Group task processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/20/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: IpGroupProcessor
 *
 * DESCRIPTION:
 *     Processes tasks of type TASK_IP_GROUP.
 *     Handles IP Group configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for the IP Group processor
 *     localConfigs       - Local IP Group configuration cache
 *     fullSyncInProgress - Flag indicating whether a full sync is in progress
 *****************************************************************************/
type IpGroupProcessor struct {
	logger             *logger.Logger            // Logger for the IP Group processor
	localConfigs       map[string]*IpGroupConfig // Local IP Group configuration cache
	fullSyncInProgress bool                      // Flag indicating whether a full sync is in progress
}

/*****************************************************************************
 * NAME: NewIpGroupProcessor
 *
 * DESCRIPTION:
 *     Creates a new IP Group processor instance.
 *     Initializes the local configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *IpGroupProcessor - Initialized IP Group processor
 *****************************************************************************/
func NewIpGroupProcessor(log *logger.Logger) *IpGroupProcessor {
	processor := &IpGroupProcessor{
		logger:             log.WithModule("ip-group-processor"),
		localConfigs:       make(map[string]*IpGroupConfig),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type that this processor handles.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - Task type (TASK_IP_GROUP)
 *****************************************************************************/
func (p *IpGroupProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_IP_GROUP
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes IP Group configuration tasks.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG operations.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Operation context
 *     task - Task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IpGroupProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get IP Group task data
	ipGroupTask := task.GetIpGroupTask()
	if ipGroupTask == nil {
		return "IP Group task data is empty", fmt.Errorf("IP Group task data is empty")
	}

	// Log task details
	p.logger.Info("Processing IP Group task",
		zap.String("name", ipGroupTask.GetName()),
		zap.String("action", task.TaskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Execute different processing based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handleConfigChange(ctx, ipGroupTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeleteConfig(ctx, ipGroupTask)
	default:
		return fmt.Sprintf("Unsupported task action: %s", task.TaskAction.String()),
			fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
	}
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles IP Group configuration creation or modification.
 *     Determines whether to create or modify a configuration.
 *
 * PARAMETERS:
 *     ctx         - Operation context
 *     ipGroupTask - IP Group task containing the configuration
 *     taskAction  - Task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if configuration fails
 *****************************************************************************/
func (p *IpGroupProcessor) handleConfigChange(ctx context.Context, ipGroupTask *pb.IpGroupTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified IpGroupConfigData structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	expectedConfig, err := ConvertIpGroupTaskToConfig(ipGroupTask)
	if err != nil {
		p.logger.Error("failed to convert IP Group task to config",
			zap.String("name", ipGroupTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert IP Group configuration: %v", err), err
	}

	p.logger.Info("Handling IP Group configuration change",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.String("action", taskAction.String()),
		zap.Bool("fullSync", p.fullSyncInProgress),
		zap.Bool("useFileMode", expectedConfig.UseFileMode))

	// Validate required fields
	if expectedConfig.IpGroupConfig.Name == "" {
		return "IP Group name is required", fmt.Errorf("IP Group name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := expectedConfig.IpGroupConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Check if local configurations need to be refreshed (incremental update)
	if !p.fullSyncInProgress {
		if err := p.refreshLocalConfigs(); err != nil {
			return fmt.Sprintf("Failed to refresh local configurations: %v", err), err
		}
	}

	// Check if IP Group exists and get ID from local cache
	existingConfig, exists := p.localConfigs[expectedConfig.IpGroupConfig.Name]
	groupId := -1
	if exists {
		groupId = existingConfig.ID
	}

	// If IP Group doesn't exist, create it
	if !exists || groupId == -1 {
		// Create new IP Group
		p.logger.Info("Creating new IP Group", zap.String("name", expectedConfig.IpGroupConfig.Name))

		cmdArgs := []string{
			"table", "add",
			fmt.Sprintf("name=%s", expectedConfig.IpGroupConfig.Name),
		}

		// Execute floweye command
		output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to execute floweye command to create IP Group",
				zap.String("name", expectedConfig.IpGroupConfig.Name),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to create IP Group: %v", err), err
		}

		groupId, err = GetIpGroupIdByName(p.logger, expectedConfig.IpGroupConfig.Name)
		if err != nil || groupId == -1 {
			return "Failed to get new IP Group ID", fmt.Errorf("failed to get new IP Group ID")
		}

		p.logger.Info("Successfully created IP Group",
			zap.String("name", expectedConfig.IpGroupConfig.Name),
			zap.Int("id", groupId))
	} else {
		/*
			// IP Group already exists, check if configuration is the same
			if CompareIpGroupConfig(p.logger, expectedConfig, existingConfig) {
				p.logger.Info("IP Group configuration unchanged, skipping update",
					zap.String("name", expectedConfig.IpGroupConfig.Name))

				return "IP Group configuration unchanged", nil
			}
		*/
		// Clear existing IP Group members
		p.logger.Info("Clearing IP Group members",
			zap.String("name", expectedConfig.IpGroupConfig.Name),
			zap.Int("id", groupId))

		clearOutput, err := utils.ExecuteCommand(p.logger, 10, "floweye", "table", "clear", fmt.Sprintf("%d", groupId))
		if err != nil {
			p.logger.Error("Failed to execute floweye command to clear IP Group members",
				zap.String("name", expectedConfig.IpGroupConfig.Name),
				zap.Int("id", groupId),
				zap.Error(err),
				zap.String("output", clearOutput))
			return fmt.Sprintf("Failed to clear IP Group members: %v", err), err
		}
	}

	// Add IP Group members using converted data
	if len(expectedConfig.IpGroupConfig.Members) > 0 {
		// Add members from converted data
		return p.addMembersFromConfig(ctx, expectedConfig, groupId)
	} else {
		// No members, just create empty IP Group
		p.logger.Info("Creating empty IP Group",
			zap.String("name", expectedConfig.IpGroupConfig.Name),
			zap.Int("id", groupId))

		return "Successfully created empty IP Group", nil
	}
}

/*****************************************************************************
 * NAME: addMembersFromConfig
 *
 * DESCRIPTION:
 *     Adds IP Group members from converted configuration data.
 *     Uses unified IpGroupConfigData structure and supports both file mode
 *     and individual member addition for optimal performance.
 *
 * PARAMETERS:
 *     ctx            - Operation context
 *     expectedConfig - Expected IP Group configuration from protobuf conversion
 *     groupId        - IP Group ID
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if addition fails
 *****************************************************************************/
func (p *IpGroupProcessor) addMembersFromConfig(ctx context.Context, expectedConfig *IpGroupConfigData, groupId int) (string, error) {
	p.logger.Info("Adding IP Group members from converted configuration",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.Int("id", groupId),
		zap.Int("members", len(expectedConfig.IpGroupConfig.Members)),
		zap.Bool("useFileMode", expectedConfig.UseFileMode))

	// Choose optimal method based on configuration
	var result string
	var err error

	if expectedConfig.UseFileMode && expectedConfig.FileContent != nil {
		// Use file mode for batch processing (more efficient for large member lists)
		result, err = p.addMembersFromFile(ctx, expectedConfig, groupId)
	} else {
		// Use individual member addition
		result, err = p.addMembersIndividually(ctx, expectedConfig, groupId)
	}

	if err != nil {
		return result, err
	}

	p.logger.Info("IP Group configuration applied successfully",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.Int("id", groupId))

	return result, nil
}

/*****************************************************************************
 * NAME: addMembersFromFile
 *
 * DESCRIPTION:
 *     Adds IP Group members using file batch processing mode.
 *     Uses floweye table loadfile command for optimal performance.
 *
 * PARAMETERS:
 *     ctx            - Operation context
 *     expectedConfig - Expected IP Group configuration with file content
 *     groupId        - IP Group ID
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if addition fails
 *****************************************************************************/
func (p *IpGroupProcessor) addMembersFromFile(ctx context.Context, expectedConfig *IpGroupConfigData, groupId int) (string, error) {
	p.logger.Info("Adding IP Group members from file content",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.Int("id", groupId),
		zap.Int("content_size", len(expectedConfig.FileContent)))

	// Create temporary file in /tmp with IP group name as prefix
	tempFileName := fmt.Sprintf("/tmp/%s_ipgroup.txt", expectedConfig.IpGroupConfig.Name)
	err := ioutil.WriteFile(tempFileName, expectedConfig.FileContent, 0644)
	// Ensure the temporary file is removed when done
	defer os.Remove(tempFileName)
	if err != nil {
		p.logger.Error("Failed to write temporary file", zap.Error(err))
		return fmt.Sprintf("Failed to write temporary file: %v", err), err
	}

	// Use floweye command to load file
	cmdArgs := []string{
		"table", "loadfile",
		fmt.Sprintf("tid=%d", groupId),
		fmt.Sprintf("file=%s", tempFileName),
		"clear=1", // Overwrite update
	}

	// Execute floweye command
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command to load file",
			zap.String("name", expectedConfig.IpGroupConfig.Name),
			zap.Int("id", groupId),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to load IP Group members file: %v", err), err
	}

	p.logger.Info("Successfully added IP Group members from file content",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.Int("id", groupId))

	return "Successfully added IP Group members from file content", nil
}

/*****************************************************************************
 * NAME: addMembersIndividually
 *
 * DESCRIPTION:
 *     Adds IP Group members one by one using individual addip commands.
 *     Used when file mode is not available or for small member lists.
 *
 * PARAMETERS:
 *     ctx            - Operation context
 *     expectedConfig - Expected IP Group configuration
 *     groupId        - IP Group ID
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if addition fails
 *****************************************************************************/
func (p *IpGroupProcessor) addMembersIndividually(ctx context.Context, expectedConfig *IpGroupConfigData, groupId int) (string, error) {
	p.logger.Info("Adding IP Group members individually",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.Int("id", groupId),
		zap.Int("members", len(expectedConfig.IpGroupConfig.Members)))

	// Add members one by one using converted data
	for _, member := range expectedConfig.IpGroupConfig.Members {
		cmdArgs := []string{
			"table", "addip",
			fmt.Sprintf("id=%d", groupId),
			member.IP,
		}

		if member.Info != "" {
			cmdArgs = append(cmdArgs, fmt.Sprintf("info=%s", member.Info))
		}

		// Execute floweye command
		output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to execute floweye command to add IP Group member",
				zap.String("name", expectedConfig.IpGroupConfig.Name),
				zap.Int("id", groupId),
				zap.String("ip", member.IP),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to add IP Group member: %v", err), err
		}
	}

	p.logger.Info("Successfully added IP Group members individually",
		zap.String("name", expectedConfig.IpGroupConfig.Name),
		zap.Int("id", groupId),
		zap.Int("members", len(expectedConfig.IpGroupConfig.Members)))

	return "Successfully added IP Group members", nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles IP Group configuration deletion.
 *
 * PARAMETERS:
 *     ctx         - Operation context
 *     ipGroupTask - IP Group task containing the configuration to delete
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if deletion fails
 *****************************************************************************/
func (p *IpGroupProcessor) handleDeleteConfig(ctx context.Context, ipGroupTask *pb.IpGroupTask) (string, error) {
	p.logger.Info("Handling IP Group configuration deletion",
		zap.String("name", ipGroupTask.GetName()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields
	if ipGroupTask.GetName() == "" {
		return "IP Group name is required", fmt.Errorf("IP Group name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := ipGroupTask.GetName() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Check if local configurations need to be refreshed (incremental update)
	if !p.fullSyncInProgress {
		if err := p.refreshLocalConfigs(); err != nil {
			return "Failed to refresh local configurations", err
		}
	}

	// Check if IP Group exists and get ID from local cache
	existingConfig, exists := p.localConfigs[ipGroupTask.GetName()]

	// If IP Group doesn't exist, no need to delete
	if !exists {
		p.logger.Info("IP Group does not exist, no need to delete",
			zap.String("name", ipGroupTask.GetName()))
		return "IP Group does not exist, no need to delete", nil
	}

	groupId := existingConfig.ID

	// Build floweye command
	cmdArgs := []string{
		"table", "remove",
		fmt.Sprintf("id=%d", groupId),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete IP Group",
		zap.String("name", ipGroupTask.GetName()),
		zap.Int("id", groupId),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("IP Group already does not exist, treating as successful delete",
				zap.String("name", ipGroupTask.GetName()),
				zap.Int("id", groupId))
		} else {
			p.logger.Error("Failed to execute floweye command to delete IP Group",
				zap.String("name", ipGroupTask.GetName()),
				zap.Int("id", groupId),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete IP Group: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", ipGroupTask.GetName()),
		zap.Int("id", groupId),
		zap.String("output", output))

	p.logger.Info("IP Group deleted successfully",
		zap.String("name", ipGroupTask.GetName()))

	return "IP Group deleted successfully", nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes the local IP Group configuration cache.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IpGroupProcessor) refreshLocalConfigs() error {
	p.logger.Debug("Refreshing local IP Group configurations")

	configs, err := GetIpGroupConfigs(p.logger)
	if err != nil {
		p.logger.Error("Failed to get local IP Group configurations", zap.Error(err))
		return err
	}

	p.localConfigs = configs
	p.logger.Debug("Local IP Group configurations refreshed", zap.Int("count", len(configs)))
	return nil
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts the full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *IpGroupProcessor) StartFullSync() error {
	p.logger.Info("Starting IP Group full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends the full synchronization process.
 *     Deletes configurations that exist locally but not in the synchronization.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *IpGroupProcessor) EndFullSync() {
	p.logger.Info("Ending IP Group full synchronization")

	// If full sync is not in progress, no need to do anything
	if !p.fullSyncInProgress {
		p.logger.Info("Full synchronization not in progress, no action needed")
		return
	}

	// Delete remaining local configurations
	for name, config := range p.localConfigs {
		p.logger.Info("Deleting IP Group not present in full sync", zap.String("name", name), zap.Int("id", config.ID))

		// Build floweye command
		cmdArgs := []string{
			"table", "remove",
			fmt.Sprintf("id=%d", config.ID),
		}

		// Execute floweye command
		output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to delete IP Group during full sync cleanup",
				zap.String("name", name),
				zap.Int("id", config.ID),
				zap.Error(err),
				zap.String("output", output))
			continue
		}

		p.logger.Info("Successfully deleted IP Group during full sync cleanup", zap.String("name", name), zap.Int("id", config.ID))
	}

	// Clear local configurations
	p.localConfigs = make(map[string]*IpGroupConfig)
	p.fullSyncInProgress = false

	p.logger.Info("IP Group full synchronization completed")
}
