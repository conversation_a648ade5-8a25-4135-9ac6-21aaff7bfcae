/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_channel_processor_test.go
 *
 * DESCRIPTION :    Unit tests for traffic channel processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/15/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"os"
	"strings"
	"testing"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTrafficChannelTestLogger creates a test logger for traffic channel tests
func setupTrafficChannelTestLogger() *logger.Logger {
	config := &logger.Config{
		Level:  "debug",
		Format: "text",
	}
	return logger.NewLogger(config)
}

// TestNewTrafficChannelProcessor tests the processor initialization
func TestNewTrafficChannelProcessor(t *testing.T) {
	log := setupTrafficChannelTestLogger()
	processor := NewTrafficChannelProcessor(log)

	assert.NotNil(t, processor)
	assert.NotNil(t, processor.logger)
	assert.NotNil(t, processor.localConfigs)
	assert.False(t, processor.fullSyncInProgress)
	assert.Equal(t, pb.TaskType_TASK_TRAFFIC_CHANNEL, processor.GetTaskType())
}

// TestTrafficChannelProcessor_validatePriorities tests priority validation logic
func TestTrafficChannelProcessor_validatePriorities(t *testing.T) {
	log := setupTrafficChannelTestLogger()
	processor := NewTrafficChannelProcessor(log)

	tests := []struct {
		name        string
		task        *pb.TrafficChannelTask
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid priorities",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: 10000, Gbw: 5000, Desc: func() *string { s := "high"; return &s }()},
					{Pri: 2, MaxRate: 8000, Gbw: 3000, Desc: func() *string { s := "medium"; return &s }()},
				},
			},
			expectError: false,
		},
		{
			name: "too many priorities",
			task: &pb.TrafficChannelTask{
				Name:       "test_channel",
				Rate:       20000,
				Priorities: make([]*pb.TrafficChannelPriority, 17), // More than 16
			},
			expectError: true,
			errorMsg:    "maximum 16 priorities allowed",
		},
		{
			name: "invalid priority range - too low",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 0, MaxRate: 10000, Gbw: 5000}, // Priority 0 is invalid
				},
			},
			expectError: true,
			errorMsg:    "priority must be between 1 and 16",
		},
		{
			name: "invalid priority range - too high",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 17, MaxRate: 10000, Gbw: 5000}, // Priority 17 is invalid
				},
			},
			expectError: true,
			errorMsg:    "priority must be between 1 and 16",
		},
		{
			name: "duplicate priorities",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: 10000, Gbw: 5000},
					{Pri: 1, MaxRate: 8000, Gbw: 3000}, // Duplicate priority 1
				},
			},
			expectError: true,
			errorMsg:    "duplicate priority level",
		},
		{
			name: "negative maxrate",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: -1000, Gbw: 5000}, // Negative maxrate
				},
			},
			expectError: true,
			errorMsg:    "maxrate cannot be negative",
		},
		{
			name: "negative guaranteed bandwidth",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: 10000, Gbw: -5000}, // Negative gbw
				},
			},
			expectError: true,
			errorMsg:    "guaranteed bandwidth cannot be negative",
		},
		{
			name: "gbw exceeds maxrate",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: 5000, Gbw: 10000}, // gbw > maxrate
				},
			},
			expectError: true,
			errorMsg:    "guaranteed bandwidth (10000) cannot exceed maxrate (5000)",
		},
		{
			name: "total gbw exceeds channel bandwidth",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 10000, // Channel bandwidth is 10000
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: 8000, Gbw: 6000},
					{Pri: 2, MaxRate: 8000, Gbw: 5000}, // Total gbw = 11000 > 10000
				},
			},
			expectError: true,
			errorMsg:    "sum of guaranteed bandwidths (11000 kbps) exceeds channel bandwidth (10000 kbits/s)",
		},
		{
			name: "zero maxrate with non-zero gbw is allowed",
			task: &pb.TrafficChannelTask{
				Name: "test_channel",
				Rate: 20000,
				Priorities: []*pb.TrafficChannelPriority{
					{Pri: 1, MaxRate: 0, Gbw: 5000}, // maxrate=0 with gbw>0 is allowed
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Initialize priorities slice for the "too many priorities" test
			if tt.name == "too many priorities" {
				for i := 0; i < 17; i++ {
					tt.task.Priorities[i] = &pb.TrafficChannelPriority{
						Pri:     int32(i + 1),
						MaxRate: 1000,
						Gbw:     500,
					}
				}
			}

			err := processor.validatePriorities(tt.task)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestTrafficChannelProcessor_StartFullSync tests full sync initialization
func TestTrafficChannelProcessor_StartFullSync(t *testing.T) {
	// Skip if running in CI environment or if floweye command is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupTrafficChannelTestLogger()
	processor := NewTrafficChannelProcessor(log)

	// Test starting full sync
	err := processor.StartFullSync()

	// In test environment, this might fail due to missing floweye command
	// We'll check if the error is due to missing command and skip if so
	if err != nil && (containsAny(err.Error(), []string{"executable file not found", "command not found", "no such file"})) {
		t.Skip("Skipping test because floweye command is not available")
	}

	// If no error or different error, verify the state
	if err == nil {
		assert.True(t, processor.fullSyncInProgress)
	}
}

// TestTrafficChannelProcessor_EndFullSync tests full sync cleanup
func TestTrafficChannelProcessor_EndFullSync(t *testing.T) {
	log := setupTrafficChannelTestLogger()
	processor := NewTrafficChannelProcessor(log)

	// Set up initial state
	processor.fullSyncInProgress = true
	processor.localConfigs["test_channel"] = &TrafficChannelConfig{
		Name: "test_channel",
		Rate: 10000,
	}

	// Test ending full sync
	processor.EndFullSync()

	// Verify state after cleanup
	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localConfigs)
}

// TestTrafficChannelProcessor_ProcessTask tests task processing
func TestTrafficChannelProcessor_ProcessTask(t *testing.T) {
	log := setupTrafficChannelTestLogger()
	processor := NewTrafficChannelProcessor(log)

	tests := []struct {
		name        string
		task        *pb.DeviceTask
		expectError bool
	}{
		{
			name: "nil traffic channel task",
			task: &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
				TaskAction: pb.TaskAction_NEW_CONFIG,
				// TrafficChannelTask is nil
			},
			expectError: true,
		},
		{
			name: "unsupported task action",
			task: &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
				TaskAction: pb.TaskAction_UNKNOWN_ACTION,
				TrafficChannelTask: &pb.TrafficChannelTask{
					Name: "test_channel",
					Rate: 10000,
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.ProcessTask(context.Background(), tt.task)

			if tt.expectError {
				assert.Error(t, err)
				assert.NotEmpty(t, result)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// containsAny checks if the string contains any of the substrings
func containsAny(s string, substrings []string) bool {
	for _, substr := range substrings {
		if strings.Contains(s, substr) {
			return true
		}
	}
	return false
}
