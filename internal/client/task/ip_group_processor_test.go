/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      ip_group_processor_test.go
 *
 * DESCRIPTION :    Unit tests for IP Group processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/20/2025  create
 ******************************************************************************/

package task

import (
	pb "agent/internal/pb"
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Helper function to convert string to *string
func ipGroupStringPtr(s string) *string {
	return &s
}

// Test NewIpGroupProcessor
func TestNewIpGroupProcessor(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Mock the list command for refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("", nil)

	// Create a new processor
	processor := NewIpGroupProcessor(log)

	// Verify processor is created
	assert.NotNil(t, processor)
	assert.Equal(t, pb.TaskType_TASK_IP_GROUP, processor.GetTaskType())
	assert.False(t, processor.fullSyncInProgress)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask - NEW_CONFIG
func TestProcessTask_NewConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Create a new processor
	processor := &IpGroupProcessor{
		logger:             log.WithModule("ip-group-processor"),
		localConfigs:       make(map[string]*IpGroupConfig),
		fullSyncInProgress: false,
	}

	// Mock the list command for refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("", nil)

	// Mock the list command for GetIpGroupIdByName
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("", nil)

	// Mock the add command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "add", "name=testgroup"}).Return("", nil)

	// Mock the list command for GetIpGroupIdByName after creation
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("1 testgroup", nil)

	// Mock the get command for VerifyIpGroupConfig
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=testgroup"}).Return("", nil)

	// Create an IP address for the member
	ipAddr := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{
			IpString: "***********",
		},
	}

	// Create a member
	member := &pb.IpGroupMember{
		IpAddr: &pb.IpGroupMember_Ip{
			Ip: ipAddr,
		},
		Info: ipGroupStringPtr("Test IP"),
	}

	// Create an IP Group task
	ipGroupTask := &pb.IpGroupTask{
		Name:    "testgroup",
		Members: []*pb.IpGroupMember{member},
	}

	// Create a task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IP_GROUP,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_IpGroupTask{
			IpGroupTask: ipGroupTask,
		},
	}

	// Mock the addip command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "addip", "id=1", "***********", "info=Test IP"}).Return("", nil)

	// Call the function
	ctx := context.Background()
	result, err := processor.ProcessTask(ctx, task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "Successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask - EDIT_CONFIG
func TestProcessTask_EditConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Create a new processor
	processor := &IpGroupProcessor{
		logger:             log.WithModule("ip-group-processor"),
		localConfigs:       make(map[string]*IpGroupConfig),
		fullSyncInProgress: false,
	}

	// Add an existing config
	processor.localConfigs["testgroup"] = &IpGroupConfig{
		ID:      1,
		Name:    "testgroup",
		Members: []*IpGroupMember{},
	}

	// Mock the list command for refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("1 testgroup", nil)

	// Mock the list command for GetIpGroupIdByName
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("1 testgroup", nil)

	// Mock the clear command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "clear", "1"}).Return("", nil)

	// Mock the get command for VerifyIpGroupConfig
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=testgroup"}).Return("", nil)

	// Create an IP address for the member
	ipAddr := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{
			IpString: "***********",
		},
	}

	// Create a member
	member := &pb.IpGroupMember{
		IpAddr: &pb.IpGroupMember_Ip{
			Ip: ipAddr,
		},
		Info: ipGroupStringPtr("Test IP"),
	}

	// Create an IP Group task
	ipGroupTask := &pb.IpGroupTask{
		Name:    "testgroup",
		Members: []*pb.IpGroupMember{member},
	}

	// Create a task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IP_GROUP,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		Payload: &pb.DeviceTask_IpGroupTask{
			IpGroupTask: ipGroupTask,
		},
	}

	// Mock the addip command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "addip", "id=1", "***********", "info=Test IP"}).Return("", nil)

	// Call the function
	ctx := context.Background()
	result, err := processor.ProcessTask(ctx, task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "Successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask - DELETE_CONFIG
func TestProcessTask_DeleteConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Create a new processor
	processor := &IpGroupProcessor{
		logger:             log.WithModule("ip-group-processor"),
		localConfigs:       make(map[string]*IpGroupConfig),
		fullSyncInProgress: false,
	}

	// Add an existing config
	processor.localConfigs["testgroup"] = &IpGroupConfig{
		ID:      1,
		Name:    "testgroup",
		Members: []*IpGroupMember{},
	}

	// Mock the list command for refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("1 testgroup", nil)

	// Mock the list command for GetIpGroupIdByName
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("1 testgroup", nil)

	// Mock the remove command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "remove", "id=1"}).Return("", nil)

	// Mock the list command for refreshLocalConfigs after deletion
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("", nil)

	// Create an IP Group task
	ipGroupTask := &pb.IpGroupTask{
		Name: "testgroup",
	}

	// Create a task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IP_GROUP,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_IpGroupTask{
			IpGroupTask: ipGroupTask,
		},
	}

	// Call the function
	ctx := context.Background()
	result, err := processor.ProcessTask(ctx, task)

	// Verify results
	assert.NoError(t, err)
	assert.Contains(t, result, "deleted successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test StartFullSync and EndFullSync
func TestFullSync(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Create a new processor
	processor := &IpGroupProcessor{
		logger:             log.WithModule("ip-group-processor"),
		localConfigs:       make(map[string]*IpGroupConfig),
		fullSyncInProgress: false,
	}

	// Add some existing configs
	processor.localConfigs["group1"] = &IpGroupConfig{
		ID:      1,
		Name:    "group1",
		Members: []*IpGroupMember{},
	}
	processor.localConfigs["group2"] = &IpGroupConfig{
		ID:      2,
		Name:    "group2",
		Members: []*IpGroupMember{},
	}

	// Mock the list command for refreshLocalConfigs
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return(
		"1 group1\n"+
		"2 group2",
		nil,
	)

	// Mock the get commands
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group1"}).Return("", nil)
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group2"}).Return("", nil)

	// Start full sync
	err := processor.StartFullSync()
	assert.NoError(t, err)
	assert.True(t, processor.fullSyncInProgress)

	// Process a task during full sync to remove group1 from localConfigs
	ipGroupTask := &pb.IpGroupTask{
		Name: "group1",
	}
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IP_GROUP,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		Payload: &pb.DeviceTask_IpGroupTask{
			IpGroupTask: ipGroupTask,
		},
	}

	// Mock the list command for GetIpGroupIdByName
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return("1 group1", nil)

	// Mock the clear command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "clear", "1"}).Return("", nil)

	// Mock the get command for VerifyIpGroupConfig
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group1"}).Return("", nil)

	// Call ProcessTask to remove group1 from localConfigs
	ctx := context.Background()
	_, err = processor.ProcessTask(ctx, task)
	assert.NoError(t, err)

	// Verify group1 is removed from localConfigs
	_, exists := processor.localConfigs["group1"]
	assert.False(t, exists)

	// Mock the remove command for group2
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "remove", "id=2"}).Return("", nil)

	// End full sync
	processor.EndFullSync()
	assert.False(t, processor.fullSyncInProgress)
	assert.Equal(t, 0, len(processor.localConfigs))

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}
