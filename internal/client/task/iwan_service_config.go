/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_service_config.go
 *
 * DESCRIPTION :    iWAN Service configuration structures and utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/25/2025  create
 *                  05/28/2024  fix JSON parsing logic
 ******************************************************************************/

package task

import (
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: ConvertIwanServiceTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf IwanServiceTask message to unified IwanServiceConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing IwanServiceConfig
 *     structure to eliminate repeated protobuf parsing across comparison, verification,
 *     and command building functions.
 *
 * PARAMETERS:
 *     iwanServiceTask - Protobuf IwanServiceTask message to convert
 *
 * RETURNS:
 *     *IwanServiceConfig - Converted iWAN Service configuration structure
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertIwanServiceTaskToConfig(iwanServiceTask *pb.IwanServiceTask) (*IwanServiceConfig, error) {
	if iwanServiceTask == nil {
		return nil, fmt.Errorf("iwanServiceTask is nil")
	}

	// Initialize with basic fields and defaults
	config := &IwanServiceConfig{
		Name:       iwanServiceTask.GetName(),
		Addr:       "", // Default empty string
		MTU:        0,  // Default 0
		Pool:       0,  // Default 0
		Prefix6Len: 0,  // Default 0 (not in protobuf, system managed)
	}

	// Convert addr field
	if addr := iwanServiceTask.GetAddr(); addr != nil {
		config.Addr = addr.GetIpString()
	}

	// Convert MTU field
	config.MTU = iwanServiceTask.GetMtu()

	// Convert pool field
	config.Pool = iwanServiceTask.GetPool()

	return config, nil
}

/*****************************************************************************
 * NAME: IwanServiceConfig
 *
 * DESCRIPTION:
 *     Represents an iWAN Service configuration.
 *     Contains all the configuration parameters for an iWAN Service.
 *
 * FIELDS:
 *     ID            - iWAN Service proxy ID (required for setiwansvc command)
 *     Name          - iWAN Service name
 *     Addr          - Server gateway
 *     MTU           - Maximum transmission unit
 *     Pool          - Address pool (user group) ID
 *     Prefix6Len    - IPv6 prefix length
 *****************************************************************************/
type IwanServiceConfig struct {
	ID         int32
	Name       string
	Addr       string
	MTU        int32
	Pool       int32
	Prefix6Len int32
}

/*****************************************************************************
 * NAME: GetLocalIwanServiceConfigs
 *
 * DESCRIPTION:
 *     Retrieves all iWAN Service configurations from the system.
 *     First executes floweye command to get all iWAN Service names,
 *     then executes floweye command to get detailed configuration for each name.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *
 * RETURNS:
 *     map[string]*IwanServiceConfig - Map of iWAN Service configurations indexed by name
 *     error - Error if retrieving configurations fails
 *****************************************************************************/
func GetLocalIwanServiceConfigs(logger *logger.Logger) (map[string]*IwanServiceConfig, error) {
	logger.Debug("Getting local iWAN Service configurations")

	// Initialize configurations map
	configs := make(map[string]*IwanServiceConfig)

	// Execute floweye command to get all iWAN Services (JSON format only)
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "listproxy", "json=1", "type=iwansvc")
	if err != nil {
		logger.Error("Failed to execute floweye command to list iWAN Services",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to list iWAN Services: %w", err)
	}

	// Clean up the output
	output = strings.TrimSpace(output)
	if output == "" {
		// No iWAN Services found
		logger.Info("No iWAN Services found")
		return configs, nil
	}

	// Parse JSON output using unified floweye JSON parser
	services, err := utils.ParseFloweyeJSON(output)
	if err != nil {
		logger.Error("Failed to parse floweye JSON output",
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to parse JSON output: %w", err)
	}

	// Extract service names
	var iwanServiceNames []string
	for _, service := range services {
		// Check if this is an iWAN Service
		if serviceType, ok := service["type"].(string); ok && serviceType == "iwansvc" {
			if name, ok := service["name"].(string); ok {
				iwanServiceNames = append(iwanServiceNames, name)
			}
		}
	}

	// Get detailed configuration for each iWAN Service
	for _, name := range iwanServiceNames {
		config, err := GetIwanServiceConfig(logger, name)
		if err != nil {
			logger.Warn("Failed to get iWAN Service configuration",
				zap.String("name", name),
				zap.Error(err))
			continue
		}

		// Add to configurations map
		configs[name] = config
	}

	logger.Info("Retrieved local iWAN Service configurations",
		zap.Int("count", len(configs)))

	return configs, nil
}

/*****************************************************************************
 * NAME: GetIwanServiceConfig
 *
 * DESCRIPTION:
 *     Retrieves a specific iWAN Service configuration from the system.
 *     Executes floweye command to get the iWAN Service and parses the output.
 *
 * PARAMETERS:
 *     logger - Logger instance for logging operations
 *     name   - Name of the iWAN Service to retrieve
 *
 * RETURNS:
 *     *IwanServiceConfig - iWAN Service configuration
 *     error - Error if retrieving configuration fails
 *****************************************************************************/
func GetIwanServiceConfig(logger *logger.Logger, name string) (*IwanServiceConfig, error) {
	logger.Debug("Getting iWAN Service configuration", zap.String("name", name))

	// Execute floweye command to get iWAN Service (key-value format only)
	output, err := utils.ExecuteCommand(logger, 10, "floweye", "nat", "getproxy", "name="+name)
	if err != nil {
		logger.Error("Failed to execute floweye command to get iWAN Service",
			zap.String("name", name),
			zap.Error(err),
			zap.String("output", output))
		return nil, fmt.Errorf("failed to get iWAN Service: %w", err)
	}

	// Check if output contains "NEXIST" which indicates the service doesn't exist
	if strings.Contains(output, "NEXIST") {
		logger.Error("iWAN Service not found",
			zap.String("name", name))
		return nil, fmt.Errorf("iWAN Service not found")
	}

	// Parse key-value output
	return parseKeyValueOutput(logger, name, output)
}

// parseKeyValueOutput parses key-value output from floweye command
func parseKeyValueOutput(logger *logger.Logger, name string, output string) (*IwanServiceConfig, error) {
	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Check if iWAN Service exists
	iwanServiceName, ok := configMap["name"]
	if !ok {
		logger.Error("iWAN Service name not found in configuration")
		return nil, fmt.Errorf("iWAN Service not found")
	}

	// Check if type is iwansvc
	serviceType, ok := configMap["type"]
	if !ok || serviceType != "iwansvc" {
		logger.Error("Invalid service type",
			zap.String("name", name),
			zap.String("type", serviceType))
		return nil, fmt.Errorf("invalid service type: %s", serviceType)
	}

	// Parse proxy ID
	proxyID := int32(0)
	if proxyIDStr, ok := configMap["proxyid"]; ok {
		proxyIDVal, err := strconv.ParseInt(proxyIDStr, 10, 32)
		if err == nil {
			proxyID = int32(proxyIDVal)
		}
	}

	// Parse MTU
	mtu := int32(0)
	if mtuStr, ok := configMap["mtu"]; ok {
		mtuVal, err := strconv.ParseInt(mtuStr, 10, 32)
		if err == nil {
			mtu = int32(mtuVal)
		}
	}

	// Parse pool ID
	pool := int32(0)
	if poolStr, ok := configMap["pool"]; ok {
		poolVal, err := strconv.ParseInt(poolStr, 10, 32)
		if err == nil {
			pool = int32(poolVal)
		}
	}

	// Radius server ID is no longer used

	// Parse IPv6 prefix length
	prefix6len := int32(0)
	if prefix6lenStr, ok := configMap["ipv6_prefixlen"]; ok {
		prefix6lenVal, err := strconv.ParseInt(prefix6lenStr, 10, 32)
		if err == nil {
			prefix6len = int32(prefix6lenVal)
		}
	}

	// Create iWAN Service config
	config := &IwanServiceConfig{
		ID:         proxyID,
		Name:       iwanServiceName,
		Addr:       configMap["addr"],
		MTU:        mtu,
		Pool:       pool,
		Prefix6Len: prefix6len,
	}

	logger.Debug("Retrieved iWAN Service configuration from key-value",
		zap.String("name", name),
		zap.String("addr", config.Addr))

	return config, nil
}

/*****************************************************************************
 * NAME: CompareIwanServiceConfig
 *
 * DESCRIPTION:
 *     Compares a requested iWAN Service configuration with an existing one.
 *     Used to determine if a configuration change is needed.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger           - Logger instance for logging operations
 *     requestedConfig  - Requested iWAN Service configuration (converted from protobuf)
 *     existingConfig   - Existing iWAN Service configuration
 *
 * RETURNS:
 *     bool - True if configurations are the same, false otherwise
 *****************************************************************************/
func CompareIwanServiceConfig(logger *logger.Logger, requestedConfig *IwanServiceConfig, existingConfig *IwanServiceConfig) bool {
	// Compare name
	if requestedConfig.Name != existingConfig.Name {
		logger.Debug("iWAN Service name mismatch",
			zap.String("requested", requestedConfig.Name),
			zap.String("existing", existingConfig.Name))
		return false
	}

	// Compare addr
	if requestedConfig.Addr != existingConfig.Addr {
		logger.Debug("iWAN Service addr mismatch",
			zap.String("requested", requestedConfig.Addr),
			zap.String("existing", existingConfig.Addr))
		return false
	}

	// Compare MTU
	if requestedConfig.MTU != existingConfig.MTU {
		logger.Debug("iWAN Service MTU mismatch",
			zap.Int32("requested", requestedConfig.MTU),
			zap.Int32("existing", existingConfig.MTU))
		return false
	}

	// Compare pool
	if requestedConfig.Pool != existingConfig.Pool {
		logger.Debug("iWAN Service pool mismatch",
			zap.Int32("requested", requestedConfig.Pool),
			zap.Int32("existing", existingConfig.Pool))
		return false
	}

	// Note: Prefix6Len is not compared because it's not part of the IwanServiceTask

	logger.Debug("iWAN Service configuration is the same")
	return true
}

/*****************************************************************************
 * NAME: VerifyIwanServiceConfig
 *
 * DESCRIPTION:
 *     Verifies that an iWAN Service configuration has been applied correctly.
 *     Gets the current configuration and compares it with the requested one.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *
 * PARAMETERS:
 *     logger          - Logger instance for logging operations
 *     requestedConfig - Requested iWAN Service configuration (converted from protobuf)
 *
 * RETURNS:
 *     bool  - True if verification passed, false otherwise
 *     error - Error if verification fails
 *****************************************************************************/
func VerifyIwanServiceConfig(logger *logger.Logger, requestedConfig *IwanServiceConfig) (bool, error) {
	// Get current configuration
	config, err := GetIwanServiceConfig(logger, requestedConfig.Name)
	if err != nil {
		return false, err
	}

	// Compare configurations
	return CompareIwanServiceConfig(logger, requestedConfig, config), nil
}
