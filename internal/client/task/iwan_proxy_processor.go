/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_proxy_processor.go
 *
 * DESCRIPTION :    iWAN Proxy processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: IwanProxyProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_IWAN_PROXY type tasks.
 *     Handles iWAN Proxy configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for iWAN Proxy processor operations
 *     localConfigs       - Cache of local iWAN Proxy configurations (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type IwanProxyProcessor struct {
	logger             *logger.Logger              // Logger for iWAN Proxy processor operations
	localConfigs       map[string]*IwanProxyConfig // Cache of local iWAN Proxy configurations (used for full sync redundant deletion)
	workingConfigs     map[string]*IwanProxyConfig // Working cache for operations (can be refreshed during full sync)
	fullSyncInProgress bool                        // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewIwanProxyProcessor
 *
 * DESCRIPTION:
 *     Creates a new iWAN Proxy processor instance.
 *     Initializes the local configuration cache and working configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *IwanProxyProcessor - Initialized iWAN Proxy processor
 *****************************************************************************/
func NewIwanProxyProcessor(log *logger.Logger) *IwanProxyProcessor {
	processor := &IwanProxyProcessor{
		logger:             log.WithModule("iwan-proxy-processor"),
		localConfigs:       make(map[string]*IwanProxyConfig),
		workingConfigs:     make(map[string]*IwanProxyConfig),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - The task type (TASK_IWAN_PROXY)
 *****************************************************************************/
func (p *IwanProxyProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_IWAN_PROXY
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes an iWAN Proxy configuration task.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - The task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IwanProxyProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get iWAN Proxy task data
	iwanTask := task.GetIwanProxyTask()
	if iwanTask == nil {
		return "iWAN Proxy task data is empty", fmt.Errorf("iWAN Proxy task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "iwan_proxy", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.String("ifname", iwanTask.GetIfname()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, iwanTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, iwanTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles creating or updating an iWAN Proxy configuration.
 *     Used for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx      - Context for the operation
 *     iwanTask - iWAN Proxy task data
 *     taskAction - The task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IwanProxyProcessor) handleConfigChange(ctx context.Context, iwanTask *pb.IwanProxyTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified IwanProxyConfig structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	expectedConfig, err := ConvertIwanProxyTaskToConfig(iwanTask)
	if err != nil {
		p.logger.Error("failed to convert iWAN Proxy task to config",
			zap.String("name", iwanTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert iWAN Proxy configuration: %v", err), err
	}

	// Validate required fields using converted data structure
	if expectedConfig.Name == "" {
		return "iWAN Proxy name is required", fmt.Errorf("iWAN Proxy name is required")
	}

	if expectedConfig.Ifname == "" {
		return "iWAN Proxy ifname is required", fmt.Errorf("iWAN Proxy ifname is required")
	}

	if expectedConfig.MTU < 500 || expectedConfig.MTU > 4700 {
		return "iWAN Proxy MTU must be between 500 and 4700", fmt.Errorf("iWAN Proxy MTU must be between 500 and 4700")
	}

	if expectedConfig.SvrAddr == "" {
		return "iWAN Proxy server address is required", fmt.Errorf("iWAN Proxy server address is required")
	}

	if expectedConfig.SvrPort <= 0 || expectedConfig.SvrPort > 65535 {
		return "iWAN Proxy server port must be between 1 and 65535", fmt.Errorf("iWAN Proxy server port must be between 1 and 65535")
	}

	if expectedConfig.Username == "" {
		return "iWAN Proxy username is required", fmt.Errorf("iWAN Proxy username is required")
	}

	if expectedConfig.Password == "" {
		return "iWAN Proxy password is required", fmt.Errorf("iWAN Proxy password is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := expectedConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if iWAN Proxy exists
	_, exists := p.workingConfigs[expectedConfig.Name]

	/*
		// If iWAN Proxy exists, check if configuration is the same using converted data structure
		if exists && CompareIwanProxyConfig(p.logger, expectedConfig, existingConfig) {
			p.logger.Info("iWAN Proxy configuration is unchanged, skipping update",
				zap.String("name", expectedConfig.Name))

			// Remove from local configs if in full sync mode
			if p.fullSyncInProgress {
				delete(p.localConfigs, expectedConfig.Name)
			}

			return "iWAN Proxy configuration is unchanged", nil
		}
	*/

	// Build command arguments using converted data structure
	cmdArgs, err := p.buildIwanProxyCommand(expectedConfig, exists)
	if err != nil {
		return fmt.Sprintf("Failed to build iWAN Proxy command: %v", err), err
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command for iWAN Proxy configuration",
		zap.String("name", expectedConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for iWAN Proxy configuration",
			zap.String("name", expectedConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure iWAN Proxy: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", expectedConfig.Name),
		zap.String("output", output))

	// Refresh working configs to include the newly created/updated configuration
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful
	}

	// Verify configuration was applied correctly using converted data structure
	success, verifyErr := VerifyIwanProxyConfig(p.logger, expectedConfig)
	if verifyErr != nil {
		p.logger.Error("Failed to verify iWAN Proxy configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify iWAN Proxy configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("iWAN Proxy configuration verification failed")
		return "iWAN Proxy configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("iWAN Proxy configuration applied successfully",
		zap.String("name", expectedConfig.Name))

	if exists {
		return "iWAN Proxy configuration updated successfully", nil
	}
	return "iWAN Proxy configuration created successfully", nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles deleting an iWAN Proxy configuration.
 *
 * PARAMETERS:
 *     ctx      - Context for the operation
 *     iwanTask - iWAN Proxy task data
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IwanProxyProcessor) handleDeleteConfig(ctx context.Context, iwanTask *pb.IwanProxyTask) (string, error) {
	// Validate required fields
	if iwanTask.GetName() == "" {
		return "iWAN Proxy name is required", fmt.Errorf("iWAN Proxy name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := iwanTask.GetName() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if iWAN Proxy exists in working configuration
	_, exists := p.workingConfigs[iwanTask.GetName()]

	// If iWAN Proxy doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("iWAN Proxy not found in local configuration, nothing to delete",
			zap.String("name", iwanTask.GetName()))
		return "iWAN Proxy not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"nat", "rmvproxy", iwanTask.GetName(),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete iWAN Proxy",
		zap.String("name", iwanTask.GetName()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("iWAN Proxy already does not exist, treating as successful delete",
				zap.String("name", iwanTask.GetName()))
		} else {
			p.logger.Error("Failed to execute floweye command to delete iWAN Proxy",
				zap.String("name", iwanTask.GetName()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete iWAN Proxy: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", iwanTask.GetName()),
		zap.String("output", output))

	// Skip post-delete verification for improved performance and reliability

	p.logger.Info("iWAN Proxy deleted successfully",
		zap.String("name", iwanTask.GetName()))

	return "iWAN Proxy deleted successfully", nil
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *IwanProxyProcessor) StartFullSync() error {
	p.logger.Info("Starting full sync for iWAN Proxy")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Deletes any local configurations that were not included in the sync.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *IwanProxyProcessor) EndFullSync() {
	p.logger.Info("Ending full sync for iWAN Proxy")

	// Create a copy of remaining iWAN Proxy configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations while keeping fullSyncInProgress = true
	remainingProxies := make(map[string]*IwanProxyConfig)
	for name, config := range p.localConfigs {
		remainingProxies[name] = config
	}

	// Delete any remaining local configurations
	// Keep fullSyncInProgress = true during cleanup so handleDeleteConfig can properly
	// remove items from localConfigs map
	if len(remainingProxies) > 0 {
		p.logger.Info("Cleaning up iWAN Proxy not included in full sync",
			zap.Int("count", len(remainingProxies)))

		for name := range remainingProxies {
			// Create a delete task for this iWAN Proxy
			deleteTask := &pb.IwanProxyTask{
				Name: name,
			}

			// Delete the iWAN Proxy using handleDeleteConfig for consistency
			p.logger.Info("Deleting iWAN Proxy not included in full sync",
				zap.String("name", name))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("Failed to delete iWAN Proxy during cleanup",
					zap.String("name", name),
					zap.Error(err))
			}
		}
	}

	// Verify that all remaining iWAN Proxies have been cleaned up
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some iWAN Proxies were not cleaned up during full sync",
			zap.Int("remaining_count", len(p.localConfigs)))

		// Log the remaining proxies for debugging
		for name := range p.localConfigs {
			p.logger.Warn("remaining iWAN Proxy after cleanup",
				zap.String("name", name))
		}
	} else {
		p.logger.Info("all remaining iWAN Proxies cleaned up successfully")
	}

	// Now set fullSyncInProgress to false after cleanup is complete
	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*IwanProxyConfig)
	p.workingConfigs = make(map[string]*IwanProxyConfig)
}

/*****************************************************************************
 * NAME: fetchIwanProxyConfigs
 *
 * DESCRIPTION:
 *     Fetches iWAN Proxy configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*IwanProxyConfig - iWAN Proxy configurations by name
 *     error                       - Error if fetch fails
 *****************************************************************************/
func (p *IwanProxyProcessor) fetchIwanProxyConfigs() (map[string]*IwanProxyConfig, error) {
	// Get local iWAN Proxy configurations using existing function
	configs, err := GetLocalIwanProxyConfigs(p.logger)
	if err != nil {
		return nil, err
	}
	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local iWAN Proxy configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IwanProxyProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local iWAN Proxy configurations")

	configs, err := p.fetchIwanProxyConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs

	p.logger.Debug("refreshed local iWAN Proxy configurations",
		zap.Int("configs", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working iWAN Proxy configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IwanProxyProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working iWAN Proxy configurations")

	configs, err := p.fetchIwanProxyConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs

	p.logger.Debug("refreshed working iWAN Proxy configurations",
		zap.Int("configs", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like create, update, delete, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *IwanProxyProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: buildIwanProxyCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for iWAN Proxy configuration.
 *     Uses converted data structure to eliminate repeated protobuf parsing.
 *
 * PARAMETERS:
 *     config - iWAN Proxy configuration (converted from protobuf)
 *     exists - Whether the iWAN Proxy already exists
 *
 * RETURNS:
 *     []string - Command arguments for floweye
 *     error    - Error if building command fails
 *****************************************************************************/
func (p *IwanProxyProcessor) buildIwanProxyCommand(config *IwanProxyConfig, exists bool) ([]string, error) {
	var cmdArgs []string

	if exists {
		// Update existing iWAN Proxy (setiwan requires both name and newname parameters)
		cmdArgs = append(cmdArgs, "nat", "setiwan", "name="+config.Name, "newname="+config.Name)
	} else {
		// Create new iWAN Proxy
		cmdArgs = append(cmdArgs, "nat", "addiwan", "name="+config.Name)
	}

	// Add parameters in the exact order as shown in floweye documentation
	// Example: floweye nat addiwan name=iwan2 ifname=2 mtu=1420 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 ipv6=0 svraddr=************** svrport=8000 username=admin password=123456 encrypt=0 srid=0 dnspxy=0 link=0 ifname2=1

	cmdArgs = append(cmdArgs, "ifname="+config.Ifname)
	cmdArgs = append(cmdArgs, "mtu="+strconv.Itoa(int(config.MTU)))
	cmdArgs = append(cmdArgs, "ping_disable=0")
	cmdArgs = append(cmdArgs, "pingip="+config.PingIP)
	cmdArgs = append(cmdArgs, "pingip2="+config.PingIP2)
	cmdArgs = append(cmdArgs, "maxdelay="+strconv.Itoa(int(config.MaxDelay)))
	cmdArgs = append(cmdArgs, "ipv6=0")
	cmdArgs = append(cmdArgs, "svraddr="+config.SvrAddr)
	cmdArgs = append(cmdArgs, "svrport="+strconv.Itoa(int(config.SvrPort)))
	cmdArgs = append(cmdArgs, "username="+config.Username)
	cmdArgs = append(cmdArgs, "password="+config.Password)

	// Add encrypt parameter
	if config.Encrypt {
		cmdArgs = append(cmdArgs, "encrypt=1")
	} else {
		cmdArgs = append(cmdArgs, "encrypt=0")
	}

	// Add srid parameter (can be empty according to documentation example)
	cmdArgs = append(cmdArgs, "srid=")

	// Add dnspxy parameter
	if config.DnsPxy {
		cmdArgs = append(cmdArgs, "dnspxy=1")
	} else {
		cmdArgs = append(cmdArgs, "dnspxy=")
	}

	// Add link parameter
	cmdArgs = append(cmdArgs, "link="+strconv.Itoa(int(config.Link)))

	if config.Ifname2 != "" {
		cmdArgs = append(cmdArgs, "ifname2="+config.Ifname2)
	} else {
		cmdArgs = append(cmdArgs, "ifname2="+config.Ifname)
	}

	return cmdArgs, nil
}
