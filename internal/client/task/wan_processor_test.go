/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      wan_processor_test.go
 *
 * DESCRIPTION :    Tests for WAN processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Mock logger for testing
func setupWanTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level: "DEBUG",
		Outputs: []logger.Output{
			{
				Type: logger.TypeConsole,
			},
		},
	}
	log, _ := logger.NewLogger(logConfig)
	return log
}

func TestWanProcessor_GetTaskType(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_WAN, taskType)
}

func TestWanProcessor_ProcessTask_NilTask(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		// No WAN task data
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "wan task data is nil")
}

func TestWanProcessor_ProcessTask_UnsupportedAction(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	// Create a task with an unsupported action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN,
		TaskAction: 99, // Invalid action
		Payload: &pb.DeviceTask_WanTask{
			WanTask: &pb.WanTask{
				Name:   "wan1",
				Ifname: "eth0",
				Mtu:    1500,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported task action")
}

func TestWanProcessor_ProcessTask_MissingName(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	// Create a task with missing name
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanTask{
			WanTask: &pb.WanTask{
				// Name is missing
				Ifname: "eth0",
				Mtu:    1500,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "wan name is required")
}

func TestWanProcessor_ProcessTask_MissingIfname(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	// Create a task with missing interface name
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanTask{
			WanTask: &pb.WanTask{
				Name: "wan1",
				// Ifname is missing
				Mtu: 1500,
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "interface name is required")
}

func TestWanProcessor_ProcessTask_InvalidMtu(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	// Create a task with invalid MTU
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanTask{
			WanTask: &pb.WanTask{
				Name:   "wan1",
				Ifname: "eth0",
				Mtu:    0, // Invalid MTU
			},
		},
	}

	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "mtu must be greater than 0")
}

func TestWanProcessor_ProcessTask_MissingWanType(t *testing.T) {
	log := setupWanTestLogger()
	processor := NewWanProcessor(log)

	// Create a task with missing WAN type
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_WAN,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_WanTask{
			WanTask: &pb.WanTask{
				Name:   "wan1",
				Ifname: "eth0",
				Mtu:    1500,
				// No WAN type specified
			},
		},
	}

	// 由于环境中可能没有floweye命令，我们只检查错误是否发生，而不检查具体的错误消息
	_, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	// 如果环境中有floweye命令，则可以取消下面的注释
	// assert.Contains(t, err.Error(), "wan type is required")
}
