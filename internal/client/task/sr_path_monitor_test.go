/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      sr_path_monitor_test.go
 *
 * DESCRIPTION :    Unit tests for SR Path monitoring system
 *
 * AUTHOR :         wei
 *
 * HISTORY :        06/19/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 创建测试用的 logger
func createTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	testLogger, _ := logger.NewLogger(logConfig)
	return testLogger
}

// 测试用的模拟函数
type mockPathChecker struct {
	mutex       sync.RWMutex
	pathStatus  map[string]map[string]bool // srName -> pathKey -> isActive
	checkCalls  int
	setCalls    []setPathCall
}

type setPathCall struct {
	srName string
	path   SRPath
}

func newMockPathChecker() *mockPathChecker {
	return &mockPathChecker{
		pathStatus: make(map[string]map[string]bool),
		setCalls:   make([]setPathCall, 0),
	}
}

func (m *mockPathChecker) setPathStatus(srName string, path SRPath, isActive bool) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pathKey := pathToKey(path)
	if m.pathStatus[srName] == nil {
		m.pathStatus[srName] = make(map[string]bool)
	}
	m.pathStatus[srName][pathKey] = isActive
}

func (m *mockPathChecker) isPathActive(srName string, path SRPath) bool {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.checkCalls++
	pathKey := pathToKey(path)
	if srPaths, exists := m.pathStatus[srName]; exists {
		if status, exists := srPaths[pathKey]; exists {
			return status
		}
	}
	return false // 默认返回不活跃
}

func (m *mockPathChecker) setPath(srName string, path SRPath) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.setCalls = append(m.setCalls, setPathCall{
		srName: srName,
		path:   path,
	})
	return nil
}

func (m *mockPathChecker) getCheckCalls() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.checkCalls
}

func (m *mockPathChecker) getSetCalls() []setPathCall {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	result := make([]setPathCall, len(m.setCalls))
	copy(result, m.setCalls)
	return result
}

// 辅助函数：将路径转换为字符串键
func pathToKey(path SRPath) string {
	if len(path.Links) == 0 {
		return "empty"
	}
	result := ""
	for i, link := range path.Links {
		if i > 0 {
			result += ","
		}
		result += string(rune(link + '0'))
	}
	return result
}

func TestSRPathMonitor_Register(t *testing.T) {
	testLogger := createTestLogger()
	mockChecker := newMockPathChecker()

	config := &SRMonitorConfig{
		CheckInterval:  100 * time.Millisecond,
		MaxFailCount:   3,
		IsPathActiveFn: mockChecker.isPathActive,
		SetPathFn:      mockChecker.setPath,
	}

	monitor := NewSRPathMonitor(testLogger, config)

	// 测试正常注册
	paths := []SRPath{
		{Links: []int32{443, 999}},
		{Links: []int32{444, 1000}},
	}

	err := monitor.Register("sr1", paths)
	assert.NoError(t, err)

	// 验证状态
	status, err := monitor.GetStatus("sr1")
	require.NoError(t, err)
	assert.Equal(t, "sr1", status.Name)
	assert.Equal(t, 2, len(status.Paths))
	assert.Equal(t, 0, status.ActivePath)
	assert.Equal(t, 0, status.FailCount)

	// 测试重复注册（幂等性）
	newPaths := []SRPath{
		{Links: []int32{555, 666}},
	}
	err = monitor.Register("sr1", newPaths)
	assert.NoError(t, err)

	// 验证配置被更新
	status, err = monitor.GetStatus("sr1")
	require.NoError(t, err)
	assert.Equal(t, 1, len(status.Paths))
	assert.Equal(t, []int32{555, 666}, status.Paths[0].Links)
}

func TestSRPathMonitor_RegisterValidation(t *testing.T) {
	testLogger := createTestLogger()
	mockChecker := newMockPathChecker()

	config := &SRMonitorConfig{
		CheckInterval:  100 * time.Millisecond,
		MaxFailCount:   3,
		IsPathActiveFn: mockChecker.isPathActive,
		SetPathFn:      mockChecker.setPath,
	}

	monitor := NewSRPathMonitor(testLogger, config)

	// 测试空名称
	err := monitor.Register("", []SRPath{{Links: []int32{443}}})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "SR name cannot be empty")

	// 测试空路径列表
	err = monitor.Register("sr1", []SRPath{})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "at least one path must be provided")
}

func TestSRPathMonitor_Unregister(t *testing.T) {
	testLogger := createTestLogger()
	mockChecker := newMockPathChecker()

	config := &SRMonitorConfig{
		CheckInterval:  100 * time.Millisecond,
		MaxFailCount:   3,
		IsPathActiveFn: mockChecker.isPathActive,
		SetPathFn:      mockChecker.setPath,
	}

	monitor := NewSRPathMonitor(testLogger, config)

	// 先注册
	paths := []SRPath{{Links: []int32{443, 999}}}
	err := monitor.Register("sr1", paths)
	require.NoError(t, err)

	// 验证存在
	_, err = monitor.GetStatus("sr1")
	assert.NoError(t, err)

	// 注销
	err = monitor.Unregister("sr1")
	assert.NoError(t, err)

	// 验证不存在
	_, err = monitor.GetStatus("sr1")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "SR instance not found")

	// 测试注销不存在的实例（幂等性）
	err = monitor.Unregister("nonexistent")
	assert.NoError(t, err)
}

func TestSRPathMonitor_StartStop(t *testing.T) {
	testLogger := createTestLogger()
	mockChecker := newMockPathChecker()

	config := &SRMonitorConfig{
		CheckInterval:  50 * time.Millisecond,
		MaxFailCount:   3,
		IsPathActiveFn: mockChecker.isPathActive,
		SetPathFn:      mockChecker.setPath,
	}

	monitor := NewSRPathMonitor(testLogger, config)

	ctx := context.Background()

	// 启动监控
	err := monitor.Start(ctx)
	assert.NoError(t, err)

	// 测试重复启动
	err = monitor.Start(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already running")

	// 停止监控
	err = monitor.Stop()
	assert.NoError(t, err)

	// 测试重复停止（幂等性）
	err = monitor.Stop()
	assert.NoError(t, err)
}

func TestSRPathMonitor_PathSwitching(t *testing.T) {
	testLogger := createTestLogger()
	mockChecker := newMockPathChecker()

	config := &SRMonitorConfig{
		CheckInterval:  50 * time.Millisecond,
		MaxFailCount:   2, // 设置较小的失败次数以便测试
		IsPathActiveFn: mockChecker.isPathActive,
		SetPathFn:      mockChecker.setPath,
	}

	monitor := NewSRPathMonitor(testLogger, config)

	// 注册两个路径
	paths := []SRPath{
		{Links: []int32{443, 999}},  // path 0
		{Links: []int32{444, 1000}}, // path 1
	}

	err := monitor.Register("sr1", paths)
	require.NoError(t, err)

	// 设置第一个路径为不活跃，第二个路径为活跃
	mockChecker.setPathStatus("sr1", paths[0], false)
	mockChecker.setPathStatus("sr1", paths[1], true)

	ctx := context.Background()
	err = monitor.Start(ctx)
	require.NoError(t, err)
	defer monitor.Stop()

	// 等待足够的时间让监控器检查并切换路径
	time.Sleep(200 * time.Millisecond)

	// 验证路径已切换
	status, err := monitor.GetStatus("sr1")
	require.NoError(t, err)
	assert.Equal(t, 1, status.ActivePath, "Should have switched to path 1")

	// 验证设置路径被调用
	setCalls := mockChecker.getSetCalls()
	assert.Greater(t, len(setCalls), 0, "SetPath should have been called")

	if len(setCalls) > 0 {
		lastCall := setCalls[len(setCalls)-1]
		assert.Equal(t, "sr1", lastCall.srName)
		assert.Equal(t, paths[1].Links, lastCall.path.Links)
	}
}

func TestSRPathMonitor_PathRecovery(t *testing.T) {
	testLogger := createTestLogger()
	mockChecker := newMockPathChecker()

	config := &SRMonitorConfig{
		CheckInterval:  50 * time.Millisecond,
		MaxFailCount:   3,
		IsPathActiveFn: mockChecker.isPathActive,
		SetPathFn:      mockChecker.setPath,
	}

	monitor := NewSRPathMonitor(testLogger, config)

	// 注册一个路径
	paths := []SRPath{
		{Links: []int32{443, 999}},
	}

	err := monitor.Register("sr1", paths)
	require.NoError(t, err)

	// 先设置路径为不活跃
	mockChecker.setPathStatus("sr1", paths[0], false)

	ctx := context.Background()
	err = monitor.Start(ctx)
	require.NoError(t, err)
	defer monitor.Stop()

	// 等待一段时间让失败计数增加
	time.Sleep(100 * time.Millisecond)

	// 检查失败计数
	status, err := monitor.GetStatus("sr1")
	require.NoError(t, err)
	assert.Greater(t, status.FailCount, 0, "Fail count should have increased")

	// 设置路径为活跃（恢复）
	mockChecker.setPathStatus("sr1", paths[0], true)

	// 等待恢复
	time.Sleep(100 * time.Millisecond)

	// 验证失败计数被重置
	status, err = monitor.GetStatus("sr1")
	require.NoError(t, err)
	assert.Equal(t, 0, status.FailCount, "Fail count should be reset after recovery")
}
