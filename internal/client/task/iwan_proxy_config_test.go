/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_proxy_config_test.go
 *
 * DESCRIPTION :    Unit tests for iWAN Proxy configuration functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Setup test logger
func setupIwanProxyTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level: "DEBUG",
		Outputs: []logger.Output{
			{
				Type: logger.TypeConsole,
			},
		},
	}
	log, _ := logger.NewLogger(logConfig)
	return log
}

// Test GetLocalIwanProxyConfigs
func TestGetLocalIwanProxyConfigs(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIwanProxyTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Mock the list command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"iwan", "list"}).Return(
		"iwan1 eth0 1500 *********** 8080 user1 pass1 1 segment1 ******* ******* 100 1\n"+
		"iwan2 eth1 1400 *********** 8081 user2 pass2 0 segment2 ******* ******* 200 0",
		nil,
	)

	// Mock the get command for each iWAN Proxy
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"iwan", "get", "name=iwan1"}).Return(
		"name=iwan1\n"+
		"ifname=eth0\n"+
		"mtu=1500\n"+
		"svraddr=***********\n"+
		"svrport=8080\n"+
		"username=user1\n"+
		"password=pass1\n"+
		"encrypt=1\n"+
		"link=segment1\n"+
		"pingip=*******\n"+
		"pingip2=*******\n"+
		"maxdelay=100\n"+
		"dnspxy=1",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"iwan", "get", "name=iwan2"}).Return(
		"name=iwan2\n"+
		"ifname=eth1\n"+
		"mtu=1400\n"+
		"svraddr=***********\n"+
		"svrport=8081\n"+
		"username=user2\n"+
		"password=pass2\n"+
		"encrypt=0\n"+
		"link=segment2\n"+
		"pingip=*******\n"+
		"pingip2=*******\n"+
		"maxdelay=200\n"+
		"dnspxy=0",
		nil,
	)

	// Skip if running in CI environment or if floweye command is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	// Call the function
	configs, err := GetLocalIwanProxyConfigs(log)

	// Check if error is due to missing floweye command
	if err != nil && strings.Contains(err.Error(), "executable file not found") {
		t.Skip("Skipping test because floweye command is not available")
	}

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, configs)
	assert.Equal(t, 2, len(configs))

	// Verify iwan1
	iwan1, exists := configs["iwan1"]
	assert.True(t, exists)
	assert.Equal(t, "iwan1", iwan1.Name)
	assert.Equal(t, "eth0", iwan1.Ifname)
	assert.Equal(t, int32(1500), iwan1.MTU)
	assert.Equal(t, "***********", iwan1.SvrAddr)
	assert.Equal(t, int32(8080), iwan1.SvrPort)
	assert.Equal(t, "user1", iwan1.Username)
	assert.Equal(t, "pass1", iwan1.Password)
	assert.True(t, iwan1.Encrypt)
	assert.Equal(t, "segment1", iwan1.Link)
	assert.Equal(t, "*******", iwan1.PingIP)
	assert.Equal(t, "*******", iwan1.PingIP2)
	assert.Equal(t, int32(100), iwan1.MaxDelay)
	assert.True(t, iwan1.DnsPxy)

	// Verify iwan2
	iwan2, exists := configs["iwan2"]
	assert.True(t, exists)
	assert.Equal(t, "iwan2", iwan2.Name)
	assert.Equal(t, "eth1", iwan2.Ifname)
	assert.Equal(t, int32(1400), iwan2.MTU)
	assert.Equal(t, "***********", iwan2.SvrAddr)
	assert.Equal(t, int32(8081), iwan2.SvrPort)
	assert.Equal(t, "user2", iwan2.Username)
	assert.Equal(t, "pass2", iwan2.Password)
	assert.False(t, iwan2.Encrypt)
	assert.Equal(t, "segment2", iwan2.Link)
	assert.Equal(t, "*******", iwan2.PingIP)
	assert.Equal(t, "*******", iwan2.PingIP2)
	assert.Equal(t, int32(200), iwan2.MaxDelay)
	assert.False(t, iwan2.DnsPxy)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test GetIwanProxyConfig
func TestGetIwanProxyConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIwanProxyTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Mock the get command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"iwan", "get", "name=iwan1"}).Return(
		"name=iwan1\n"+
		"ifname=eth0\n"+
		"mtu=1500\n"+
		"svraddr=***********\n"+
		"svrport=8080\n"+
		"username=user1\n"+
		"password=pass1\n"+
		"encrypt=1\n"+
		"link=segment1\n"+
		"pingip=*******\n"+
		"pingip2=*******\n"+
		"maxdelay=100\n"+
		"dnspxy=1",
		nil,
	)

	// Skip if running in CI environment or if floweye command is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	// Call the function
	config, err := GetIwanProxyConfig(log, "iwan1")

	// Check if error is due to missing floweye command
	if err != nil && strings.Contains(err.Error(), "executable file not found") {
		t.Skip("Skipping test because floweye command is not available")
	}

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, "iwan1", config.Name)
	assert.Equal(t, "eth0", config.Ifname)
	assert.Equal(t, int32(1500), config.MTU)
	assert.Equal(t, "***********", config.SvrAddr)
	assert.Equal(t, int32(8080), config.SvrPort)
	assert.Equal(t, "user1", config.Username)
	assert.Equal(t, "pass1", config.Password)
	assert.True(t, config.Encrypt)
	assert.Equal(t, "segment1", config.Link)
	assert.Equal(t, "*******", config.PingIP)
	assert.Equal(t, "*******", config.PingIP2)
	assert.Equal(t, int32(100), config.MaxDelay)
	assert.True(t, config.DnsPxy)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test VerifyIwanProxyConfig
func TestVerifyIwanProxyConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIwanProxyTestLogger()
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Mock the get command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"iwan", "get", "name=iwan1"}).Return(
		"name=iwan1\n"+
		"ifname=eth0\n"+
		"mtu=1500\n"+
		"svraddr=***********\n"+
		"svrport=8080\n"+
		"username=user1\n"+
		"password=pass1\n"+
		"encrypt=1\n"+
		"link=segment1\n"+
		"pingip=*******\n"+
		"pingip2=*******\n"+
		"maxdelay=100\n"+
		"dnspxy=1",
		nil,
	)

	// Create an iWAN Proxy task
	iwanTask := &pb.IwanProxyTask{
		Name:     "iwan1",
		Ifname:   "eth0",
		Mtu:      1500,
		SvrAddr:  "***********",
		SvrPort:  8080,
		Username: "user1",
		Password: "pass1",
	}

	// Convert to config structure
	expectedConfig, err := ConvertIwanProxyTaskToConfig(iwanTask)
	assert.NoError(t, err)

	// Skip if running in CI environment or if floweye command is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	// Call the function
	success, err := VerifyIwanProxyConfig(log, expectedConfig)

	// Check if error is due to missing floweye command
	if err != nil && strings.Contains(err.Error(), "executable file not found") {
		t.Skip("Skipping test because floweye command is not available")
	}

	// Verify results
	assert.NoError(t, err)
	assert.True(t, success)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test CompareIwanProxyConfig
func TestCompareIwanProxyConfig(t *testing.T) {
	log := setupIwanProxyTestLogger()

	// Create an iWAN Proxy task
	iwanTask := &pb.IwanProxyTask{
		Name:     "iwan1",
		Ifname:   "eth0",
		Mtu:      1500,
		SvrAddr:  "***********",
		SvrPort:  8080,
		Username: "user1",
		Password: "pass1",
	}

	// Convert to config structure
	expectedConfig, err := ConvertIwanProxyTaskToConfig(iwanTask)
	assert.NoError(t, err)

	// Create a matching local config
	localConfig := &IwanProxyConfig{
		Name:     "iwan1",
		Ifname:   "eth0",
		MTU:      1500,
		SvrAddr:  "***********",
		SvrPort:  8080,
		Username: "user1",
		Password: "pass1",
	}

	// Test matching configs
	result := CompareIwanProxyConfig(log, expectedConfig, localConfig)
	assert.True(t, result)

	// Test non-matching configs
	localConfig.Ifname = "eth1"
	result = CompareIwanProxyConfig(log, expectedConfig, localConfig)
	assert.False(t, result)
}

// Test CompareIwanProxyConfig with all fields
func TestCompareIwanProxyConfig_AllFields(t *testing.T) {
	log := setupIwanProxyTestLogger()

	// Create IP addresses for testing
	pingIp := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{IpString: "*******"},
	}
	pingIp2 := &pb.IpAddress{
		Ip: &pb.IpAddress_IpString{IpString: "*******"},
	}

	// Create heartbeat config
	maxDelay := int32(100)
	heartbeat := &pb.HeartbeatConfig{
		PingIp:   pingIp,
		PingIp2:  pingIp2,
		MaxDelay: &maxDelay,
	}

	// Create an iWAN Proxy task with all fields
	iwanTask := &pb.IwanProxyTask{
		Name:      "iwan1",
		Ifname:    "eth0",
		Mtu:       1500,
		SvrAddr:   "***********",
		SvrPort:   8080,
		Username:  "user1",
		Password:  "pass1",
		Encrypt:   true,
		Link:      1,
		Heartbeat: heartbeat,
		DnsPxy:    true,
	}

	// Convert to config structure
	expectedConfig, err := ConvertIwanProxyTaskToConfig(iwanTask)
	assert.NoError(t, err)

	// Create a matching local config
	localConfig := &IwanProxyConfig{
		Name:     "iwan1",
		Ifname:   "eth0",
		MTU:      1500,
		SvrAddr:  "***********",
		SvrPort:  8080,
		Username: "user1",
		Password: "pass1",
		Encrypt:  true,
		Link:     1,
		PingIP:   "*******",
		PingIP2:  "*******",
		MaxDelay: 100,
		DnsPxy:   true,
	}

	// Test matching configs
	result := CompareIwanProxyConfig(log, expectedConfig, localConfig)
	assert.True(t, result)

	// Test non-matching configs - change each field one by one
	tests := []struct {
		name     string
		modifyFn func()
	}{
		{
			name: "Different Name",
			modifyFn: func() {
				localConfig.Name = "iwan2"
			},
		},
		{
			name: "Different Ifname",
			modifyFn: func() {
				localConfig.Ifname = "eth1"
			},
		},
		{
			name: "Different MTU",
			modifyFn: func() {
				localConfig.MTU = 1400
			},
		},
		{
			name: "Different SvrAddr",
			modifyFn: func() {
				localConfig.SvrAddr = "***********"
			},
		},
		{
			name: "Different SvrPort",
			modifyFn: func() {
				localConfig.SvrPort = 8081
			},
		},
		{
			name: "Different Username",
			modifyFn: func() {
				localConfig.Username = "user2"
			},
		},
		{
			name: "Different Password",
			modifyFn: func() {
				localConfig.Password = "pass2"
			},
		},
		{
			name: "Different Encrypt",
			modifyFn: func() {
				localConfig.Encrypt = false
			},
		},
		{
			name: "Different Link",
			modifyFn: func() {
				localConfig.Link = 2
			},
		},
		{
			name: "Different PingIP",
			modifyFn: func() {
				localConfig.PingIP = "*******"
			},
		},
		{
			name: "Different PingIP2",
			modifyFn: func() {
				localConfig.PingIP2 = "*******"
			},
		},
		{
			name: "Different MaxDelay",
			modifyFn: func() {
				localConfig.MaxDelay = 200
			},
		},
		{
			name: "Different DnsPxy",
			modifyFn: func() {
				localConfig.DnsPxy = false
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Reset config
			localConfig = &IwanProxyConfig{
				Name:     "iwan1",
				Ifname:   "eth0",
				MTU:      1500,
				SvrAddr:  "***********",
				SvrPort:  8080,
				Username: "user1",
				Password: "pass1",
				Encrypt:  true,
				Link:     1,
				PingIP:   "*******",
				PingIP2:  "*******",
				MaxDelay: 100,
				DnsPxy:   true,
			}

			// Modify one field
			test.modifyFn()

			// Test non-matching configs
			result := CompareIwanProxyConfig(log, expectedConfig, localConfig)
			assert.False(t, result)
		})
	}
}

// Test ConvertIwanProxyTaskToConfig
func TestConvertIwanProxyTaskToConfig(t *testing.T) {
	// Test basic conversion
	t.Run("Basic conversion", func(t *testing.T) {
		iwanTask := &pb.IwanProxyTask{
			Name:     "iwan1",
			Ifname:   "eth0",
			Ifname2:  "eth1",
			Mtu:      1500,
			SvrAddr:  "***********",
			SvrPort:  8080,
			Username: "user1",
			Password: "pass1",
			Encrypt:  true,
			Link:     123,
			DnsPxy:   true,
		}

		config, err := ConvertIwanProxyTaskToConfig(iwanTask)

		assert.NoError(t, err)
		assert.NotNil(t, config)
		assert.Equal(t, "iwan1", config.Name)
		assert.Equal(t, "eth0", config.Ifname)
		assert.Equal(t, "eth1", config.Ifname2)
		assert.Equal(t, int32(1500), config.MTU)
		assert.Equal(t, "***********", config.SvrAddr)
		assert.Equal(t, int32(8080), config.SvrPort)
		assert.Equal(t, "user1", config.Username)
		assert.Equal(t, "pass1", config.Password)
		assert.True(t, config.Encrypt)
		assert.Equal(t, int32(123), config.Link)
		assert.True(t, config.DnsPxy)
		// Default values
		assert.Equal(t, "0.0.0.0", config.PingIP)
		assert.Equal(t, "0.0.0.0", config.PingIP2)
		assert.Equal(t, int32(0), config.MaxDelay)
	})

	// Test with heartbeat configuration
	t.Run("With heartbeat configuration", func(t *testing.T) {
		pingIp := &pb.IpAddress{
			Ip: &pb.IpAddress_IpString{IpString: "*******"},
		}
		pingIp2 := &pb.IpAddress{
			Ip: &pb.IpAddress_IpString{IpString: "*******"},
		}
		heartbeat := &pb.HeartbeatConfig{
			PingIp:   pingIp,
			PingIp2:  pingIp2,
			MaxDelay: 100,
		}

		iwanTask := &pb.IwanProxyTask{
			Name:      "iwan1",
			Ifname:    "eth0",
			Mtu:       1500,
			SvrAddr:   "***********",
			SvrPort:   8080,
			Username:  "user1",
			Password:  "pass1",
			Heartbeat: heartbeat,
		}

		config, err := ConvertIwanProxyTaskToConfig(iwanTask)

		assert.NoError(t, err)
		assert.NotNil(t, config)
		assert.Equal(t, "*******", config.PingIP)
		assert.Equal(t, "*******", config.PingIP2)
		assert.Equal(t, int32(100), config.MaxDelay)
	})

	// Test nil input
	t.Run("Nil input", func(t *testing.T) {
		config, err := ConvertIwanProxyTaskToConfig(nil)

		assert.Error(t, err)
		assert.Nil(t, config)
		assert.Contains(t, err.Error(), "iwanProxyTask is nil")
	})
}
