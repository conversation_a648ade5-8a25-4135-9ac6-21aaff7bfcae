// Copyright (c) 2025 UniSASE Tech Co., Ltd.
// All Rights Reserved.
//
// This source code is the property of UniSASE Tech Co., Ltd. and is intended for
// use only by authorized employees or contractors of the company. Unauthorized
// copying, modification, distribution, or use of this code, in whole or in part,
// is strictly prohibited.
//
// This file is part of the UniSASE software system and may be covered by one or
// more patents or patent applications owned or licensed by the company.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
// -----------------------------------------------------------------------------
// FILE NAME : dhcp_processor.go
//
// AUTHOR : wei
// -----------------------------------------------------------------------------

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
)

// DhcpProcessor processes CPE_DHCP_SERVER type tasks, handling DHCP server configuration operations.
type DhcpProcessor struct {
	logger             *logger.Logger        // Logger for DHCP processor operations.
	localConfigs       map[string]*LanConfig // Cache of local LAN configurations (used for full sync redundant deletion).
	workingConfigs     map[string]*LanConfig // Working cache for operations (can be refreshed during full sync).
	fullSyncInProgress bool                  // Flag indicating if full sync is in progress.
}

// NewDhcpProcessor creates a new DHCP processor instance and initializes the local configuration cache.
// It returns an initialized DHCP processor.
//
// Parameters:
//   - log: The logger instance for processor operations.
func NewDhcpProcessor(log *logger.Logger) *DhcpProcessor {
	processor := &DhcpProcessor{
		logger:             log.WithModule("dhcp-processor"),
		localConfigs:       make(map[string]*LanConfig),
		workingConfigs:     make(map[string]*LanConfig),
		fullSyncInProgress: false,
	}

	return processor
}

// fetchDhcpConfigs fetches DHCP configurations from floweye.
// This is the common logic used by both local and working config refresh.
// It returns a map of LAN configurations by name, or an error if the fetch fails.
func (p *DhcpProcessor) fetchDhcpConfigs() (map[string]*LanConfig, error) {
	lanConfigs, err := GetLocalLanConfigs(p.logger)
	if err != nil {
		p.logger.Error("failed to fetch LAN configurations", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch LAN configurations: %w", err)
	}

	// Convert LAN configs to DHCP configs and store all LAN configs for DHCP processing.
	dhcpConfigs := make(map[string]*LanConfig)
	for name, lanConfig := range lanConfigs {
		// Store all LAN configs for DHCP processing (both enabled and disabled).
		dhcpConfigs[name] = lanConfig
	}

	p.logger.Debug("fetched DHCP configurations",
		zap.Int("count", len(dhcpConfigs)))
	return dhcpConfigs, nil
}

// refreshLocalConfigs refreshes the local DHCP configuration cache.
// This is used only for full sync redundant deletion.
// It returns an error if the refresh fails.
func (p *DhcpProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local DHCP configurations")

	configs, err := p.fetchDhcpConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for local cache: %w", err)
	}

	p.localConfigs = configs
	p.logger.Info("refreshed local DHCP configurations",
		zap.Int("count", len(configs)))
	return nil
}

// refreshWorkingConfigs refreshes working DHCP configurations.
// This is the primary cache used for all operations and can be refreshed independently during full sync.
// It returns an error if the refresh fails.
func (p *DhcpProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working DHCP configurations")

	configs, err := p.fetchDhcpConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	p.workingConfigs = configs
	p.logger.Debug("refreshed working DHCP configurations",
		zap.Int("count", len(configs)))
	return nil
}

// getConfigsForOperation gets configurations for operations like configuration changes, verification, etc.
// It always uses workingConfigs which can be refreshed independently.
// This simplifies the logic - working configs are the primary cache for all operations.
// It returns an error if getting configs fails.
func (p *DhcpProcessor) getConfigsForOperation() error {
	// Always use working configs for operations.
	// This simplifies logic and ensures consistency.
	return p.refreshWorkingConfigs()
}

// StartFullSync starts a full synchronization process by refreshing the local configuration cache.
// It returns an error if the start fails.
func (p *DhcpProcessor) StartFullSync() error {
	p.logger.Info("starting full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations.
	err := p.refreshLocalConfigs()
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

// EndFullSync ends a full synchronization process.
// It disables DHCP on LANs that were not included in the full sync.
func (p *DhcpProcessor) EndFullSync() {
	p.logger.Info("ending full synchronization")

	// Process remaining LANs in local configuration.
	// These are LANs that were not included in the full sync.
	// For DHCP, we don't delete the LAN, but disable DHCP on it.
	if len(p.localConfigs) > 0 {
		p.logger.Info("disabling DHCP on remaining LANs",
			zap.Int("count", len(p.localConfigs)))

		for lanName, lanConfig := range p.localConfigs {
			// Only process LANs with DHCP enabled.
			if lanConfig.DhcpEnable == 1 {
				// Create a DHCP task to disable DHCP.
				dhcpTask := &pb.DhcpServerTask{
					Name:       lanName,
					DhcpEnable: false,
				}

				// Disable DHCP on the LAN.
				p.logger.Info("disabling DHCP on LAN",
					zap.String("name", lanName))

				_, err := p.handleEditConfig(context.Background(), dhcpTask)
				if err != nil {
					p.logger.Error("failed to disable DHCP on LAN",
						zap.String("name", lanName),
						zap.Error(err))
				}
			}
		}
	}

	// Reset state.
	p.fullSyncInProgress = false
	p.localConfigs = make(map[string]*LanConfig)
	p.workingConfigs = make(map[string]*LanConfig)
}

// GetTaskType returns the type of task this processor can handle.
func (p *DhcpProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_DHCP
}

// ProcessTask processes a DHCP server task based on its action type,
// delegating to specific handlers for different task actions.
// It returns a description of the operation result and an error if processing fails.
//
// Parameters:
//   - ctx: The context for the operation.
//   - task: The device task to process.
func (p *DhcpProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get DHCP server task data.
	dhcpTask := task.GetDhcpTask()
	if dhcpTask == nil {
		return "DHCP server task data is empty", fmt.Errorf("dhcp server task data is nil")
	}

	// Create unified task log context.
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "dhcp", configIdentifier, p.logger)

	// Extract task details for logging.
	startIp := ""
	endIp := ""
	if dhcpTask.DhcpPool != nil {
		if dhcpTask.DhcpPool.StartIp != nil {
			startIp = utils.GetIpString(dhcpTask.DhcpPool.StartIp)
		}
		if dhcpTask.DhcpPool.EndIp != nil {
			endIp = utils.GetIpString(dhcpTask.DhcpPool.EndIp)
		}
	}

	dns0 := ""
	if dhcpTask.Dns0 != nil {
		dns0 = utils.GetIpString(dhcpTask.Dns0)
	}

	dns1 := ""
	if dhcpTask.Dns1 != nil {
		dns1 = utils.GetIpString(dhcpTask.Dns1)
	}

	dhcpDomain := ""
	if dhcpTask.DhcpDomain != nil {
		dhcpDomain = *dhcpTask.DhcpDomain
	}

	// Log task start with additional context.
	taskLogCtx.LogTaskStart(
		zap.Bool("dhcp_enable", dhcpTask.DhcpEnable),
		zap.String("start_ip", startIp),
		zap.String("end_ip", endIp),
		zap.Int32("lease_ttl", dhcpTask.LeaseTtl),
		zap.String("dns0", dns0),
		zap.String("dns1", dns1),
		zap.String("domain", dhcpDomain),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action.
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, dhcpTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, dhcpTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion.
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

// handleConfigChange handles both new and edit DHCP server configuration tasks.
// It configures or updates a DHCP server on a LAN interface using the floweye command
// and implements the configuration consistency mechanism for both full synchronization and incremental updates.
// It returns a success message or an error if the operation fails.
//
// Parameters:
//   - ctx: The context for the operation.
//   - dhcpTask: The DHCP server task containing configuration details.
//   - taskAction: The original task action (NEW_CONFIG or EDIT_CONFIG).
func (p *DhcpProcessor) handleConfigChange(ctx context.Context, dhcpTask *pb.DhcpServerTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point.
	// This is the single conversion point for the entire processing pipeline.
	dhcpConfig, err := ConvertDhcpTaskToConfig(dhcpTask)
	if err != nil {
		p.logger.Error("failed to convert DHCP task to config",
			zap.String("name", dhcpTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert DHCP configuration: %v", err), err
	}

	p.logger.Info("Processing DHCP server configuration",
		zap.String("name", dhcpConfig.Name),
		zap.Int("dhcp_enable", dhcpConfig.DhcpEnable),
		zap.String("dhcp_pool", dhcpConfig.DhcpPool),
		zap.Int("lease_ttl", dhcpConfig.LeaseTtl),
		zap.String("dns0", dhcpConfig.Dns0),
		zap.String("dns1", dhcpConfig.Dns1),
		zap.String("dhcp_gateway", dhcpConfig.DhcpGateway),
		zap.String("dhcp_mask", dhcpConfig.DhcpMask),
		zap.String("dhcp_acaddr", dhcpConfig.DhcpAcAddr),
		zap.String("domain", dhcpConfig.DhcpDomain),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Validate required fields.
	if dhcpConfig.Name == "" {
		return "LAN name is required", fmt.Errorf("lan name is required")
	}

	// Register cleanup defer after validation.
	if p.fullSyncInProgress {
		name := dhcpConfig.Name // Copy value to avoid closure capture issues.
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation.
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// For comparison purposes, try to get existing DHCP configuration.
	// This is optional - if it doesn't exist, we'll just proceed with the configuration.
	// var localDhcpConfig *DhcpConfig
	// if localConfig, exists := p.localConfigs[dhcpConfig.Name]; exists {
	//	localDhcpConfig = NewDhcpConfigFromLan(localConfig)
	// }

	/*
		// If configurations match, no need to modify
		if localDhcpConfig != nil && CompareDhcpConfig(p.logger, dhcpConfig, localDhcpConfig) {
			p.logger.Info("DHCP configuration already matches, no changes needed",
				zap.String("name", dhcpConfig.Name))

			// Remove from local configs if in full sync mode
			if p.fullSyncInProgress {
				delete(p.localConfigs, dhcpConfig.Name)
			}

			return "DHCP configuration already matches, no changes needed", nil
		}
	*/

	// Build floweye command using converted configuration.
	cmdArgs, err := p.buildDhcpCommand(dhcpConfig)
	if err != nil {
		p.logger.Error("failed to build DHCP command",
			zap.String("name", dhcpConfig.Name),
			zap.Error(err))
		return fmt.Sprintf("Failed to build DHCP command: %v", err), err
	}

	// Execute floweye command.
	p.logger.Info("Executing floweye command for DHCP configuration",
		zap.String("name", dhcpConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for DHCP configuration",
			zap.String("name", dhcpConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure DHCP: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", dhcpConfig.Name),
		zap.String("output", output))

	// Refresh configurations after operation to get latest state.
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful.
	}

	// Verify configuration was applied correctly using converted configuration.
	success, verifyErr := p.verifyDhcpConfig(dhcpConfig)
	if verifyErr != nil {
		p.logger.Error("failed to verify DHCP configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify DHCP configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("DHCP configuration verification failed")
		return "DHCP configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("DHCP configured successfully",
		zap.String("name", dhcpConfig.Name))
	return "DHCP configuration modified successfully", nil
}

// handleNewConfig handles new DHCP server configuration tasks.
// It configures a DHCP server on a LAN interface using the floweye command
// and implements the configuration consistency mechanism for both full synchronization and incremental updates.
// This method is kept for backward compatibility and now delegates to the unified handleConfigChange method.
// It returns a success message or an error if the operation fails.
//
// Parameters:
//   - ctx: The context for the operation.
//   - dhcpTask: The DHCP server task containing configuration details.
func (p *DhcpProcessor) handleNewConfig(ctx context.Context, dhcpTask *pb.DhcpServerTask) (string, error) {
	return p.handleConfigChange(ctx, dhcpTask, pb.TaskAction_NEW_CONFIG)
}

// handleEditConfig handles DHCP server configuration modification tasks.
// It updates a DHCP server on a LAN interface using the floweye command
// and implements the configuration consistency mechanism for both full synchronization and incremental updates.
// This method is kept for backward compatibility and now delegates to the unified handleConfigChange method.
// It returns a success message or an error if the operation fails.
//
// Parameters:
//   - ctx: The context for the operation.
//   - dhcpTask: The DHCP server task containing configuration changes.
func (p *DhcpProcessor) handleEditConfig(ctx context.Context, dhcpTask *pb.DhcpServerTask) (string, error) {
	return p.handleConfigChange(ctx, dhcpTask, pb.TaskAction_EDIT_CONFIG)
}

// handleDeleteConfig handles DHCP server configuration deletion tasks.
// It disables DHCP on a LAN interface using the floweye command
// and implements the configuration consistency mechanism for both full synchronization and incremental updates.
// It returns a success message or an error if the operation fails.
//
// Parameters:
//   - ctx: The context for the operation.
//   - dhcpTask: The DHCP server task containing the configuration to delete.
func (p *DhcpProcessor) handleDeleteConfig(ctx context.Context, dhcpTask *pb.DhcpServerTask) (string, error) {
	p.logger.Info("handling delete DHCP server config",
		zap.String("name", dhcpTask.Name),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields.
	if dhcpTask.Name == "" {
		return "LAN name is required", fmt.Errorf("lan name is required")
	}

	// Register cleanup defer after validation.
	if p.fullSyncInProgress {
		name := dhcpTask.Name // Copy value to avoid closure capture issues.
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation.
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if DHCP configuration exists and is enabled using working configs.
	workingConfig, exists := p.workingConfigs[dhcpTask.Name]
	if exists && workingConfig.DhcpEnable == 0 {
		p.logger.Info("DHCP already disabled, nothing to do",
			zap.String("name", dhcpTask.Name))
		return "DHCP already disabled, nothing to do", nil
	}

	// If no local config exists, we'll still try to disable DHCP.
	// This ensures idempotent behavior - the command will succeed or fail naturally.

	// Build floweye command to disable DHCP.
	cmdArgs := []string{
		"nat", "setrtif",
		"name=" + dhcpTask.Name,
		"dhcp_enable=0",
	}

	// Execute floweye command.
	p.logger.Info("Executing floweye command to disable DHCP",
		zap.String("name", dhcpTask.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command to disable DHCP",
			zap.String("name", dhcpTask.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to disable DHCP: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", dhcpTask.Name),
		zap.String("output", output))

	// Verify DHCP was disabled.
	verifyTask := &pb.DhcpServerTask{
		Name:       dhcpTask.Name,
		DhcpEnable: false,
	}

	success, verifyErr := VerifyDhcpConfig(p.logger, verifyTask)
	if verifyErr != nil {
		p.logger.Error("failed to verify DHCP configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify DHCP configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("DHCP disable verification failed")
		return "DHCP disable verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("DHCP disabled successfully", zap.String("output", output))
	return "DHCP configuration deleted successfully", nil
}

// buildDhcpCommand builds floweye command arguments for DHCP configuration using the converted
// internal data structure instead of the protobuf message.
// It returns the command arguments array or an error if building fails.
//
// Parameters:
//   - dhcpConfig: The converted DHCP configuration structure.
func (p *DhcpProcessor) buildDhcpCommand(dhcpConfig *DhcpConfig) ([]string, error) {
	cmdArgs := []string{
		"nat", "setrtif",
		"name=" + dhcpConfig.Name,
	}

	// Add DHCP enable flag.
	cmdArgs = append(cmdArgs, "dhcp_enable="+strconv.Itoa(dhcpConfig.DhcpEnable))

	// Add DHCP pool if DHCP is enabled.
	if dhcpConfig.DhcpEnable == 1 {
		// Add DHCP pool (start IP - end IP).
		if dhcpConfig.DhcpPool != "" {
			cmdArgs = append(cmdArgs, "dhcp_pool="+dhcpConfig.DhcpPool)
		} else {
			return nil, fmt.Errorf("dhcp pool is required when DHCP is enabled")
		}

		// Add lease TTL.
		if dhcpConfig.LeaseTtl > 0 {
			cmdArgs = append(cmdArgs, "leasettl="+strconv.Itoa(dhcpConfig.LeaseTtl))
		} else {
			cmdArgs = append(cmdArgs, "leasettl=86400")
		}

		// Add DNS servers.
		if dhcpConfig.Dns0 != "" {
			cmdArgs = append(cmdArgs, "dns0="+dhcpConfig.Dns0)
		} else {
			cmdArgs = append(cmdArgs, "dns0=")
		}

		if dhcpConfig.Dns1 != "" {
			cmdArgs = append(cmdArgs, "dns1="+dhcpConfig.Dns1)
		} else {
			cmdArgs = append(cmdArgs, "dns1=")
		}

		// Add domain.
		if dhcpConfig.DhcpDomain != "" {
			cmdArgs = append(cmdArgs, "dhcp_domain="+dhcpConfig.DhcpDomain)
		} else {
			cmdArgs = append(cmdArgs, "dhcp_domain=")
		}

		// Add DHCP gateway - if not specified or is "0.0.0.0", get from LAN configuration.
		if dhcpConfig.DhcpGateway != "" && dhcpConfig.DhcpGateway != "0.0.0.0" {
			cmdArgs = append(cmdArgs, "dhcp_gateway="+dhcpConfig.DhcpGateway)
		} else {
			// Get gateway from LAN configuration.
			lanConfig, err := GetLanConfig(p.logger, dhcpConfig.Name)
			if err == nil && lanConfig.Addr != "" {
				cmdArgs = append(cmdArgs, "dhcp_gateway="+lanConfig.Addr)
			} else {
				cmdArgs = append(cmdArgs, "dhcp_gateway=0.0.0.0")
			}
		}

		// Add DHCP mask - if not specified or is "0.0.0.0", get from LAN configuration.
		if dhcpConfig.DhcpMask != "" && dhcpConfig.DhcpMask != "0.0.0.0" {
			cmdArgs = append(cmdArgs, "dhcp_mask="+dhcpConfig.DhcpMask)
		} else {
			// Get mask from LAN configuration.
			lanConfig, err := GetLanConfig(p.logger, dhcpConfig.Name)
			if err == nil && lanConfig.Mask != "" {
				cmdArgs = append(cmdArgs, "dhcp_mask="+lanConfig.Mask)
			} else {
				cmdArgs = append(cmdArgs, "dhcp_mask=0.0.0.0")
			}
		}

		// Add DHCP AC address.
		if dhcpConfig.DhcpAcAddr != "" {
			cmdArgs = append(cmdArgs, "dhcp_acaddr="+dhcpConfig.DhcpAcAddr)
		} else {
			cmdArgs = append(cmdArgs, "dhcp_acaddr=0.0.0.0")
		}

		// Add DHCP options.
		if len(dhcpConfig.DhcpOptions) > 0 {
			for optionNum, optionValue := range dhcpConfig.DhcpOptions {
				cmdArgs = append(cmdArgs, "dhcp_option="+optionNum+","+optionValue)
			}
		} else {
			cmdArgs = append(cmdArgs, "dhcp_option=12,NULL, dhcp_option=61,NULL, dhcp_option=60,NULL,")
		}
	}

	return cmdArgs, nil
}

// verifyDhcpConfig verifies DHCP configuration using the converted internal data structure.
// It returns true if verification succeeds, false otherwise, and an error if verification fails.
//
// Parameters:
//   - dhcpConfig: The converted DHCP configuration structure.
func (p *DhcpProcessor) verifyDhcpConfig(dhcpConfig *DhcpConfig) (bool, error) {
	// Get current LAN configuration.
	lanConfig, err := GetLanConfig(p.logger, dhcpConfig.Name)
	if err != nil {
		return false, err
	}

	// Convert to DhcpConfig.
	actualConfig := NewDhcpConfigFromLan(lanConfig)
	if actualConfig == nil {
		p.logger.Error("failed to create DHCP config from LAN config")
		return false, fmt.Errorf("failed to create DHCP config")
	}

	// Use CompareDhcpConfig to verify the configuration.
	return CompareDhcpConfig(p.logger, dhcpConfig, actualConfig), nil
}
