/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_tracking_policy_processor_test.go
 *
 * DESCRIPTION :    Unit tests for DNS tracking policy processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        17/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"testing"

	"agent/internal/logger"
	pb "agent/internal/pb"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// setupDnsTrackingTestLogger creates a test logger for DNS tracking policy tests
func setupDnsTrackingTestLogger() *logger.Logger {
	config := zap.NewDevelopmentConfig()
	config.Level = zap.NewAtomicLevelAt(zap.DebugLevel)
	zapLogger, _ := config.Build()
	return &logger.Logger{Logger: zapLogger}
}

// mockExecuteCommandDnsTracking is a mock function for testing
func mockExecuteCommandDnsTracking(logger *logger.Logger, timeout int, command string, args ...string) (string, error) {
	// Mock successful execution
	return "success", nil
}

// Test NewDnsTrackingPolicyProcessor
func TestNewDnsTrackingPolicyProcessor(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	assert.NotNil(t, processor)
	assert.Equal(t, pb.TaskType_TASK_DNS_TRACKING_POLICY, processor.GetTaskType())
	assert.False(t, processor.fullSyncInProgress)
	assert.NotNil(t, processor.localConfigs)
	assert.Empty(t, processor.localConfigs)
	assert.NotNil(t, processor.cookieToID)
	assert.Empty(t, processor.cookieToID)
}

// Test GetTaskType
func TestDnsTrackingPolicyProcessor_GetTaskType(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_DNS_TRACKING_POLICY, taskType)
}

// Test ProcessTask with nil payload
func TestDnsTrackingPolicyProcessor_ProcessTask_NilPayload(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DNS_TRACKING_POLICY,
		TaskAction: pb.TaskAction_NEW_CONFIG,
	}

	desc, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, desc, "DNS tracking policy task payload is nil")
}

// Test ProcessTask with unsupported action
func TestDnsTrackingPolicyProcessor_ProcessTask_UnsupportedAction(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DNS_TRACKING_POLICY,
		TaskAction: 999, // Invalid action
		Payload: &pb.DeviceTask_DnsTrackingPolicyTask{
			DnsTrackingPolicyTask: &pb.DnsTrackingPolicyTask{
				Cookie: 12345,
			},
		},
	}

	desc, err := processor.ProcessTask(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, desc, "Unsupported task action")
}

// Test StartFullSync
func TestDnsTrackingPolicyProcessor_StartFullSync(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	// Mock the execute command function
	originalExecuteCommand := executeCommandDnsTracking
	executeCommandDnsTracking = mockExecuteCommandDnsTracking
	defer func() {
		executeCommandDnsTracking = originalExecuteCommand
	}()

	err := processor.StartFullSync()
	// This will fail because we don't have a real floweye environment
	// but we can test that the function is called correctly
	assert.Error(t, err) // Expected to fail in test environment
	assert.True(t, processor.fullSyncInProgress)
}

// Test EndFullSync
func TestDnsTrackingPolicyProcessor_EndFullSync(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	// Set up initial state
	processor.fullSyncInProgress = true
	processor.localConfigs[12345] = &DnsTrackingPolicyConfig{
		ID:     1,
		Cookie: 12345,
	}

	// Mock the execute command function
	originalExecuteCommand := executeCommandDnsTracking
	executeCommandDnsTracking = mockExecuteCommandDnsTracking
	defer func() {
		executeCommandDnsTracking = originalExecuteCommand
	}()

	processor.EndFullSync()

	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localConfigs)
	assert.Empty(t, processor.cookieToID)
}

// Test allocateNewPolicyID
func TestDnsTrackingPolicyProcessor_allocateNewPolicyID(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	// Test with empty configs
	id := processor.allocateNewPolicyID()
	assert.Equal(t, 1, id)

	// Test with existing configs
	processor.localConfigs[12345] = &DnsTrackingPolicyConfig{ID: 5, Cookie: 12345}
	processor.localConfigs[12346] = &DnsTrackingPolicyConfig{ID: 3, Cookie: 12346}
	processor.localConfigs[12347] = &DnsTrackingPolicyConfig{ID: 8, Cookie: 12347}

	id = processor.allocateNewPolicyID()
	assert.Equal(t, 9, id)
}

// Test resolveDomainGroupNameToID
func TestDnsTrackingPolicyProcessor_resolveDomainGroupNameToID(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	// This test will fail in the test environment because it requires
	// actual floweye commands, but we can test the function structure
	_, err := processor.resolveDomainGroupNameToID("test_domain_group")
	assert.Error(t, err) // Expected to fail in test environment
}

// Test handlePolicyOrdering
func TestDnsTrackingPolicyProcessor_handlePolicyOrdering(t *testing.T) {
	log := setupDnsTrackingTestLogger()
	processor := NewDnsTrackingPolicyProcessor(log)

	// Test ordering with different previous values using config data
	previous0 := uint32(0)
	configData1 := &DnsTrackingPolicyConfig{
		Cookie:   12345,
		Previous: &previous0,
	}
	err := processor.handlePolicyOrdering(configData1, 1)
	assert.NoError(t, err) // Basic ordering logic should not fail

	previousEnd := uint32(0xFFFFFFFF)
	configData2 := &DnsTrackingPolicyConfig{
		Cookie:   12345,
		Previous: &previousEnd,
	}
	err = processor.handlePolicyOrdering(configData2, 1)
	assert.NoError(t, err) // Append to end should not fail

	previousSpecific := uint32(12344)
	configData3 := &DnsTrackingPolicyConfig{
		Cookie:   12345,
		Previous: &previousSpecific,
	}
	err = processor.handlePolicyOrdering(configData3, 1)
	assert.NoError(t, err) // Insert after specific policy should not fail

	// Test with nil previous (no ordering needed)
	configData4 := &DnsTrackingPolicyConfig{
		Cookie:   12345,
		Previous: nil,
	}
	err = processor.handlePolicyOrdering(configData4, 1)
	assert.NoError(t, err) // No ordering should not fail
}

// Test CompareDnsTrackingPolicyConfig
func TestCompareDnsTrackingPolicyConfig(t *testing.T) {
	log := setupDnsTrackingTestLogger()

	// Create test task and convert to config data
	trackHost := true
	cacheTtl := uint32(300)
	desc := "test policy"
	task := &pb.DnsTrackingPolicyTask{
		Cookie:      12345,
		Disable:     false,
		DomainGroup: []string{"test_domain"},
		Pxy:         "wan",
		BackupPxy:   "wan1",
		TrackHost:   &trackHost,
		CacheTtl:    &cacheTtl,
		Desc:        &desc,
	}

	// Convert task to config data
	configData, err := ConvertDnsTrackingPolicyTaskToConfig(task)
	assert.NoError(t, err)
	assert.NotNil(t, configData)

	// Create matching local config
	localConfig := &DnsTrackingPolicyConfig{
		Cookie:    12345,
		Enable:    true, // Note: Enable is opposite of Disable
		DnsName:   "test_domain",
		Pxy:       "wan",
		BkupPxy:   "wan1",
		TrackHost: true,
		CacheTTL:  300,
		Desc:      "test policy",
	}

	// Test matching configurations using converted data
	result := CompareDnsTrackingPolicyConfig(log, configData, localConfig)
	assert.True(t, result)

	// Test mismatched configurations
	localConfig.Pxy = "different_proxy"
	result = CompareDnsTrackingPolicyConfig(log, configData, localConfig)
	assert.False(t, result)
}

// Test ConvertDnsTrackingPolicyTaskToConfig
func TestConvertDnsTrackingPolicyTaskToConfig(t *testing.T) {
	// Test with nil task
	config, err := ConvertDnsTrackingPolicyTaskToConfig(nil)
	assert.Error(t, err)
	assert.Nil(t, config)
	assert.Contains(t, err.Error(), "dnsTrackingPolicyTask is nil")

	// Test with complete task
	trackHost := true
	cacheTtl := uint32(300)
	desc := "test policy"
	previous := uint32(12344)
	task := &pb.DnsTrackingPolicyTask{
		Cookie:      12345,
		Previous:    &previous,
		Disable:     false,
		DomainGroup: []string{"test_domain", "another_domain"},
		Pxy:         "wan",
		BackupPxy:   "wan1",
		TrackHost:   &trackHost,
		CacheTtl:    &cacheTtl,
		Desc:        &desc,
	}

	config, err = ConvertDnsTrackingPolicyTaskToConfig(task)
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, uint32(12345), config.Cookie)
	assert.Equal(t, uint32(12344), *config.Previous)
	assert.True(t, config.Enable) // Disable=false -> Enable=true
	assert.Equal(t, "test_domain", config.DnsName) // First domain group
	assert.Equal(t, "wan", config.Pxy)
	assert.Equal(t, "wan1", config.BkupPxy)
	assert.True(t, config.TrackHost)
	assert.Equal(t, 300, config.CacheTTL)
	assert.Equal(t, "test policy", config.Desc)

	// Test with minimal task (only required fields)
	minimalTask := &pb.DnsTrackingPolicyTask{
		Cookie:      12346,
		Disable:     true,
		DomainGroup: []string{"minimal_domain"},
		Pxy:         "wan2",
		BackupPxy:   "wan3",
	}

	config, err = ConvertDnsTrackingPolicyTaskToConfig(minimalTask)
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, uint32(12346), config.Cookie)
	assert.Nil(t, config.Previous) // Not specified
	assert.False(t, config.Enable) // Disable=true -> Enable=false
	assert.Equal(t, "minimal_domain", config.DnsName)
	assert.Equal(t, "wan2", config.Pxy)
	assert.Equal(t, "wan3", config.BkupPxy)
	assert.False(t, config.TrackHost) // Default value
	assert.Equal(t, 0, config.CacheTTL) // Default value
	assert.Equal(t, "", config.Desc) // Default value
	assert.Equal(t, "", config.DnsAddr) // Default value
}

// Test ParseDnsTrackingPolicyFromJSON
func TestParseDnsTrackingPolicyFromJSON(t *testing.T) {
	jsonStr := `{"polno":100,"enable":1,"dnsid":1,"dnsname":"domain","pxy":"wan","bkuppxy":"wan1","active":0,"trackhost":1,"cachettl":0,"desc":"test","dnscnt":0,"dnsreqs":0,"dnshits":0,"dnsaddr":"*******"}`

	config, err := ParseDnsTrackingPolicyFromJSON(jsonStr)
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, 100, config.ID)
	assert.True(t, config.Enable)
	assert.Equal(t, 1, config.DnsID)
	assert.Equal(t, "domain", config.DnsName)
	assert.Equal(t, "wan", config.Pxy)
	assert.Equal(t, "wan1", config.BkupPxy)
	assert.True(t, config.TrackHost)
	assert.Equal(t, 0, config.CacheTTL)
	assert.Equal(t, "test", config.Desc)
	assert.Equal(t, "*******", config.DnsAddr)
}

// Test ParseDnsTrackingPolicyFromKeyValue
func TestParseDnsTrackingPolicyFromKeyValue(t *testing.T) {
	output := `id=100
cookie=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=wan
bkuppxy=wan1
dnsaddr=*******
desc=test policy`

	config, err := ParseDnsTrackingPolicyFromKeyValue(output)
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, 100, config.ID)
	assert.Equal(t, uint32(11100), config.Cookie)
	assert.True(t, config.Enable)
	assert.True(t, config.TrackHost)
	assert.Equal(t, 0, config.CacheTTL)
	assert.Equal(t, 1, config.DnsID)
	assert.Equal(t, "domain", config.DnsName)
	assert.Equal(t, "wan", config.Pxy)
	assert.Equal(t, "wan1", config.BkupPxy)
	assert.Equal(t, "*******", config.DnsAddr)
	assert.Equal(t, "test policy", config.Desc)
}
