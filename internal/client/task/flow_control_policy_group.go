/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      flow_control_policy_group.go
 *
 * DESCRIPTION :    Policy group handlers for flow control processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: handlePolicyGroupConfigChange
 *
 * DESCRIPTION:
 *     Handles policy group configuration changes (create/update).
 *     Implements unified logic for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - Policy group task to process
 *     taskAction - Action type (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyGroupConfigChange(ctx context.Context, task *pb.FlowControlPolicyGroupTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertPolicyGroupTaskToConfig(task)
	if err != nil {
		p.logger.Error("failed to convert policy group task to config",
			zap.String("name", task.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert policy group configuration: %v", err), err
	}

	// Validate required fields
	if configData.Name == "" {
		return "Policy group name is required", fmt.Errorf("policy group name is required")
	}

	// Register cleanup defer immediately after validation during full sync
	if p.fullSyncInProgress {
		name := configData.Name // Copy value to avoid closure capture reference issues
		defer func() {
			delete(p.localPolicyGroups, name)
		}()
	}

	p.logger.Info("Processing policy group configuration",
		zap.String("name", configData.Name),
		zap.Bool("disable", configData.Disable),
		zap.Bool("stop", configData.Stop),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get configurations for operation (uses working configs during full sync)
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Check if policy group exists in working configuration
	_, exists := p.workingPolicyGroups[configData.Name]

	/*
		// If configurations match, no need to modify
		if exists && ComparePolicyGroupConfig(p.logger, configData, localConfig) {
			p.logger.Info("Policy group configuration already matches, no changes needed",
				zap.String("name", configData.Name))

			return "Policy group configuration already matches, no changes needed", nil
		}
	*/

	// Build floweye command arguments
	var cmdArgs []string
	var operation string
	var groupID int

	if exists {
		// Update existing policy group - get existing ID
		groupID = p.groupNameToID[configData.Name]
		operation = "update"
		cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(groupID)}
	} else {
		// Create new policy group - ID will be auto-assigned by floweye
		operation = "create"
		cmdArgs = []string{"policygroup2", "add"}
		// groupID will be determined after creation
	}

	// Build command arguments using converted data
	if err := p.buildPolicyGroupCommand(&cmdArgs, configData); err != nil {
		return fmt.Sprintf("Failed to build policy group command: %v", err), err
	}

	// Execute floweye command
	p.logger.Info("executing floweye command for policy group",
		zap.String("operation", operation),
		zap.String("name", configData.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye command",
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to %s policy group: %v", operation, err), err
	}

	// For new policy group creation, get the assigned ID immediately
	if operation == "create" {
		if err := p.updatePolicyGroupIDAfterCreation(configData.Name); err != nil {
			p.logger.Error("failed to get policy group ID after creation",
				zap.String("name", configData.Name),
				zap.Error(err))
			return fmt.Sprintf("Policy group created but failed to get ID: %v", err), err
		}
	}

	// Refresh working configs to include the newly created/updated policy group
	// This ensures both ordering and enable/disable operations have access to latest configs
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Error("failed to refresh configs after creation/update",
			zap.String("name", configData.Name),
			zap.Error(err))
		return fmt.Sprintf("Policy group %s succeeded but failed to refresh configs: %v", operation, err), err
	}

	// Handle ordering logic after creation/update
	if configData.Previous != "append" {
		if err := p.handlePolicyGroupOrderingWithConfig(ctx, configData); err != nil {
			p.logger.Error("failed to handle policy group ordering",
				zap.String("name", configData.Name),
				zap.Error(err))
			return fmt.Sprintf("Policy group %s succeeded but ordering failed: %v", operation, err), err
		}
	}

	// Handle enable/disable operation separately after policy group creation/modification
	if err := p.handlePolicyGroupEnableDisable(configData.Name, configData.Disable); err != nil {
		p.logger.Error("failed to handle policy group enable/disable",
			zap.String("name", configData.Name),
			zap.Bool("disable", configData.Disable),
			zap.Error(err))
		return fmt.Sprintf("Policy group %s succeeded but enable/disable failed: %v", operation, err), err
	}

	// Verify the configuration was applied successfully
	if err := p.verifyPolicyGroupConfigWithData(ctx, configData); err != nil {
		p.logger.Error("policy group verification failed",
			zap.String("name", configData.Name),
			zap.Error(err))
		return fmt.Sprintf("Policy group %s succeeded but verification failed: %v", operation, err), err
	}

	successMsg := fmt.Sprintf("Policy group %s successful", operation)
	p.logger.Info(successMsg, zap.String("name", configData.Name))
	return successMsg, nil
}

/*****************************************************************************
 * NAME: handleDeletePolicyGroup
 *
 * DESCRIPTION:
 *     Handles policy group deletion.
 *     Removes the policy group from the local PA system and maintains continuous ID ordering.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Policy group task containing the name to delete
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) handleDeletePolicyGroup(ctx context.Context, task *pb.FlowControlPolicyGroupTask) (string, error) {
	// Validate required fields
	if task.GetName() == "" {
		return "Policy group name is required for deletion", fmt.Errorf("policy group name is required for deletion")
	}

	// Register cleanup defer immediately after validation during full sync
	if p.fullSyncInProgress {
		name := task.GetName() // Copy value to avoid closure capture reference issues
		defer func() {
			delete(p.localPolicyGroups, name)
			delete(p.groupNameToID, name)
		}()
	}

	p.logger.Info("Processing policy group deletion",
		zap.String("name", task.GetName()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get configurations for operation (uses working configs during full sync)
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations for operation: %v", err), err
	}

	// Check if policy group exists in working configuration
	workingConfig, exists := p.workingPolicyGroups[task.GetName()]
	if !exists {
		p.logger.Info("Policy group does not exist, treating as successful delete",
			zap.String("name", task.GetName()))

		return "Policy group does not exist, deletion successful", nil
	}

	// Store deletion information for ordering logic
	deletedGroupID := workingConfig.ID

	// Build delete command arguments
	cmdArgs := []string{"policygroup2", "remove", "id=" + strconv.Itoa(deletedGroupID)}

	// Execute floweye command
	p.logger.Info("executing floweye command for policy group deletion",
		zap.String("name", task.GetName()),
		zap.Int("id", deletedGroupID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Policy group already does not exist, treating as successful delete",
				zap.String("name", task.GetName()))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete policy group: %v", err), err
		}
	}

	// Handle ordering logic: decrement IDs of all policy groups with ID > deleted ID
	if err := p.handlePolicyGroupDeletionOrdering(ctx, deletedGroupID); err != nil {
		p.logger.Error("failed to handle policy group deletion ordering",
			zap.String("name", task.GetName()),
			zap.Int("deleted_id", deletedGroupID),
			zap.Error(err))
		return fmt.Sprintf("Policy group deletion succeeded but ordering adjustment failed: %v", err), err
	}

	successMsg := "Policy group deletion successful"
	p.logger.Info(successMsg, zap.String("name", task.GetName()))
	return successMsg, nil
}

/*****************************************************************************
 * NAME: handlePolicyGroupDeletionOrdering
 *
 * DESCRIPTION:
 *     Handles policy group ordering after deletion.
 *     According to floweye design, IDs are automatically maintained after deletion.
 *     This function is kept for compatibility but no longer performs manual ID adjustment.
 *
 * PARAMETERS:
 *     ctx             - Context for the operation
 *     deletedGroupID  - ID of the deleted policy group
 *
 * RETURNS:
 *     error - Error if ordering adjustment fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyGroupDeletionOrdering(ctx context.Context, deletedGroupID int) error {
	p.logger.Debug("handling policy group deletion ordering",
		zap.Int("deleted_group_id", deletedGroupID))

	// According to floweye documentation and design, IDs are automatically maintained
	// after deletion operations. Manual ID adjustment is not necessary and may cause issues.
	p.logger.Info("floweye automatically maintains ID continuity after deletion, no manual adjustment needed",
		zap.Int("deleted_group_id", deletedGroupID))

	return nil
}

/*****************************************************************************
 * NAME: movePolicyGroupUsingMoveTo
 *
 * DESCRIPTION:
 *     Moves a policy group using floweye moveto command according to documentation.
 *     This is the correct and simplified approach based on floweye documentation.
 *
 * PARAMETERS:
 *     currentGroupID     - ID of the group to move
 *     previousGroupName  - Name of the previous group (for positioning)
 *     previousGroupFound - Whether the previous group was found
 *     previousGroupID    - ID of the previous group (if found)
 *     currentGroups      - Current list of policy groups
 *
 * RETURNS:
 *     error - Error if move operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) movePolicyGroupUsingMoveTo(currentGroupID int, previousGroupName string, previousGroupFound bool, previousGroupID int, currentGroups []PolicyGroupOrderInfo) error {
	var cmdArgs []string

	// Handle previous="null" or previous="_subscription_group" - move after _subscription_group
	if previousGroupName == "null" || previousGroupName == "_subscription_group" {
		// Find _subscription_group ID
		var subscriptionGroupID int = -1
		for _, group := range currentGroups {
			if group.Name == DefaultSubscriptionGroup {
				subscriptionGroupID = group.ID
				break
			}
		}

		if subscriptionGroupID == -1 {
			// _subscription_group not found, move to first position as fallback
			p.logger.Warn("_subscription_group not found for previous=null, moving to first position")
			// Find the first group (smallest ID) to move before it
			var firstGroupID int = -1
			for _, group := range currentGroups {
				if group.ID != currentGroupID && (firstGroupID == -1 || group.ID < firstGroupID) {
					firstGroupID = group.ID
				}
			}

			if firstGroupID == -1 {
				// Only one group exists, no need to move
				p.logger.Debug("only one policy group exists, no ordering needed",
					zap.Int("current_id", currentGroupID))
				return nil
			} else {
				// Move before the first group (which puts it at first position)
				cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(currentGroupID), "moveto=" + strconv.Itoa(firstGroupID)}
				p.logger.Info("moving policy group to first position",
					zap.Int("current_id", currentGroupID),
					zap.Int("before_group_id", firstGroupID))
			}
		} else {
			// Move after _subscription_group
			// Find the group that comes after _subscription_group in the list order
			var nextGroupID int = -1
			subscriptionGroupFound := false
			for _, group := range currentGroups {
				if subscriptionGroupFound && group.ID != currentGroupID {
					// This is the first group after _subscription_group in the list
					nextGroupID = group.ID
					break
				}
				if group.ID == subscriptionGroupID {
					subscriptionGroupFound = true
				}
			}

			if nextGroupID == -1 {
				// No group after _subscription_group, move to last position
				cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(currentGroupID), "moveto=-1"}
				p.logger.Info("moving policy group to last position (after _subscription_group)",
					zap.Int("current_id", currentGroupID),
					zap.Int("subscription_group_id", subscriptionGroupID))
			} else {
				// Move before the next group (which puts it after _subscription_group)
				cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(currentGroupID), "moveto=" + strconv.Itoa(nextGroupID)}
				p.logger.Info("moving policy group after _subscription_group",
					zap.Int("current_id", currentGroupID),
					zap.Int("subscription_group_id", subscriptionGroupID),
					zap.Int("before_group_id", nextGroupID))
			}

			// Execute the move command
			p.logger.Debug("executing moveto command for previous=null",
				zap.Strings("args", cmdArgs))

			output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
			if err != nil {
				p.logger.Error("failed to execute moveto command for previous=null",
					zap.Error(err),
					zap.String("output", output))
				return fmt.Errorf("failed to move policy group after _subscription_group: %w", err)
			}

			p.logger.Info("successfully moved policy group after _subscription_group",
				zap.Int("current_id", currentGroupID))
			return nil
		}
	}

	// If previous group is empty, move to first position
	if !previousGroupFound {
		// Previous group not found, move to last position
		cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(currentGroupID), "moveto=-1"}
		p.logger.Info("moving policy group to last position (previous not found)",
			zap.Int("current_id", currentGroupID),
			zap.String("previous_group", previousGroupName))
	} else {
		// Use the previously found previousGroupID
		// (already found in the loop above)

		// Check if current group is already immediately after the previous group
		isAlreadyInCorrectPosition := false
		for i, group := range currentGroups {
			if group.ID == previousGroupID {
				// Found the previous group, check if current group is the next one
				if i+1 < len(currentGroups) && currentGroups[i+1].ID == currentGroupID {
					isAlreadyInCorrectPosition = true
					p.logger.Debug("policy group is already in correct position after previous group",
						zap.Int("current_id", currentGroupID),
						zap.Int("previous_id", previousGroupID))
					break
				}
			}
		}

		// If already in correct position, no need to move
		if isAlreadyInCorrectPosition {
			p.logger.Info("policy group already positioned correctly after previous group, skipping move",
				zap.Int("current_id", currentGroupID),
				zap.Int("previous_id", previousGroupID))
			return nil
		}

		// Find the group that comes after the previous group
		var nextGroupID int = -1
		for _, group := range currentGroups {
			if group.ID > previousGroupID && group.ID != currentGroupID && (nextGroupID == -1 || group.ID < nextGroupID) {
				nextGroupID = group.ID
			}
		}

		if nextGroupID == -1 {
			// No group after previous, move to last position
			cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(currentGroupID), "moveto=-1"}
			p.logger.Info("moving policy group to last position (after previous)",
				zap.Int("current_id", currentGroupID),
				zap.Int("previous_id", previousGroupID))
		} else {
			// Move before the next group (which puts it after previous)
			cmdArgs = []string{"policygroup2", "set", "id=" + strconv.Itoa(currentGroupID), "moveto=" + strconv.Itoa(nextGroupID)}
			p.logger.Info("moving policy group after previous",
				zap.Int("current_id", currentGroupID),
				zap.Int("previous_id", previousGroupID),
				zap.Int("before_group_id", nextGroupID))
		}
	}

	// Execute the moveto command
	p.logger.Debug("executing moveto command",
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		return fmt.Errorf("failed to move policy group using moveto: %w, output: %s", err, output)
	}

	p.logger.Info("successfully moved policy group using moveto",
		zap.Int("group_id", currentGroupID))

	return nil
}

/*****************************************************************************
 * NAME: parsePolicyGroupListForOrdering
 *
 * DESCRIPTION:
 *     Parses policy group list output for ordering operations.
 *     Extracts ID, name, and position information.
 *
 * PARAMETERS:
 *     output - Output from floweye policygroup2 list command
 *
 * RETURNS:
 *     []PolicyGroupOrderInfo - List of policy group ordering information
 *     error                   - Error if parsing fails
 *****************************************************************************/
func (p *FlowControlProcessor) parsePolicyGroupListForOrdering(output string) ([]PolicyGroupOrderInfo, error) {
	var groups []PolicyGroupOrderInfo
	lines := strings.Split(strings.TrimSpace(output), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Split by space and get the first two fields (ID and name)
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			if id, err := strconv.Atoi(fields[0]); err == nil {
				groups = append(groups, PolicyGroupOrderInfo{
					ID:   id,
					Name: fields[1],
				})
			}
		}
	}

	return groups, nil
}

/*****************************************************************************
 * NAME: movePolicyGroupToPosition
 *
 * DESCRIPTION:
 *     DEPRECATED: This function uses complex ID manipulation logic that is not
 *     compatible with floweye's moveto command design. Use movePolicyGroupUsingMoveTo instead.
 *
 * PARAMETERS:
 *     groupID        - ID of the group to move
 *     targetPosition - Target position (1-based, -1 for last)
 *     currentGroups  - Current list of policy groups
 *
 * RETURNS:
 *     error - Error if move operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) movePolicyGroupToPosition(groupID, targetPosition int, currentGroups []PolicyGroupOrderInfo) error {
	p.logger.Info("moving policy group to position",
		zap.Int("group_id", groupID),
		zap.Int("target_position", targetPosition),
		zap.Int("total_groups", len(currentGroups)))

	// Handle special case: move to last position
	if targetPosition == -1 {
		maxID := 0
		for _, group := range currentGroups {
			if group.ID > maxID {
				maxID = group.ID
			}
		}
		targetPosition = maxID + 1
	}

	// If already in correct position, no need to move
	if groupID == targetPosition {
		p.logger.Debug("policy group already in correct position",
			zap.Int("group_id", groupID),
			zap.Int("target_position", targetPosition))
		return nil
	}

	// Try using moveto command first (if supported)
	if err := p.tryMoveToCommand(groupID, targetPosition, currentGroups); err == nil {
		return nil
	}

	return nil
}

/*****************************************************************************
 * NAME: tryMoveToCommand
 *
 * DESCRIPTION:
 *     Attempts to use floweye policygroup2 moveto command for positioning.
 *
 * PARAMETERS:
 *     groupID        - ID of the group to move
 *     targetPosition - Target position
 *     currentGroups  - Current list of policy groups
 *
 * RETURNS:
 *     error - Error if moveto command fails or is not supported
 *****************************************************************************/
func (p *FlowControlProcessor) tryMoveToCommand(groupID, targetPosition int, currentGroups []PolicyGroupOrderInfo) error {
	// If moving to last position, try moveto=-1
	if targetPosition > len(currentGroups) {
		cmdArgs := []string{"policygroup2", "set", "id=" + strconv.Itoa(groupID), "moveto=-1"}

		p.logger.Debug("trying moveto command for last position",
			zap.Int("group_id", groupID),
			zap.Strings("args", cmdArgs))

		output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Debug("moveto command failed, will use ID manipulation",
				zap.Error(err),
				zap.String("output", output))
			return err
		}
		return nil
	}

	// Find the group at target position to use as moveto reference
	var targetGroupID int
	for _, group := range currentGroups {
		if group.ID == targetPosition {
			targetGroupID = group.ID
			break
		}
	}

	if targetGroupID == 0 {
		return fmt.Errorf("no group found at target position %d", targetPosition)
	}

	cmdArgs := []string{"policygroup2", "set", "id=" + strconv.Itoa(groupID), "moveto=" + strconv.Itoa(targetGroupID)}

	p.logger.Debug("trying moveto command",
		zap.Int("group_id", groupID),
		zap.Int("target_group_id", targetGroupID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Debug("moveto command failed, will use ID manipulation",
			zap.Error(err),
			zap.String("output", output))
		return err
	}

	return nil
}

// PolicyGroupOrderInfo represents policy group ordering information
type PolicyGroupOrderInfo struct {
	ID   int
	Name string
}

// Default policy group names
const (
	DefaultTrafficMirroringGroup = "_traffic_mirroring_group"
	DefaultSubscriptionGroup     = "_subscription_group"
)

/*****************************************************************************
 * NAME: createDefaultPolicyGroups
 *
 * DESCRIPTION:
 *     Creates default policy groups if they don't exist in local configuration.
 *     Creates two default groups:
 *     1. _traffic_mirroring_group - for port mirroring functionality
 *     2. _subscription_group - for subscription rate limiting functionality
 *
 * RETURNS:
 *     error - Error if creation fails
 *****************************************************************************/
func (p *FlowControlProcessor) createDefaultPolicyGroups() error {
	p.logger.Info("checking and creating default policy groups")

	// Check if default groups exist in local configuration
	_, trafficMirroringExists := p.localPolicyGroups[DefaultTrafficMirroringGroup]
	_, subscriptionExists := p.localPolicyGroups[DefaultSubscriptionGroup]

	// Create _traffic_mirroring_group if it doesn't exist
	// Use empty previous to ensure it goes to the first position
	if !trafficMirroringExists {
		if err := p.createDefaultPolicyGroup(DefaultTrafficMirroringGroup, ""); err != nil {
			return fmt.Errorf("failed to create %s: %w", DefaultTrafficMirroringGroup, err)
		}
		p.logger.Info("created default policy group", zap.String("name", DefaultTrafficMirroringGroup))
	}

	// Create _subscription_group if it doesn't exist
	// Set previous to _traffic_mirroring_group to ensure it comes after the first default group
	if !subscriptionExists {
		if err := p.createDefaultPolicyGroup(DefaultSubscriptionGroup, DefaultTrafficMirroringGroup); err != nil {
			return fmt.Errorf("failed to create %s: %w", DefaultSubscriptionGroup, err)
		}
		p.logger.Info("created default policy group", zap.String("name", DefaultSubscriptionGroup))
	}

	// After creating both groups, ensure they are in the correct order at the top
	// This handles the case where other groups might already exist
	if !trafficMirroringExists || !subscriptionExists {
		if err := p.ensureDefaultGroupsOrdering(); err != nil {
			p.logger.Warn("failed to ensure default groups ordering", zap.Error(err))
			// Don't fail the entire operation for ordering issues
		}
	}

	return nil
}

/*****************************************************************************
 * NAME: createDefaultPolicyGroup
 *
 * DESCRIPTION:
 *     Creates a single default policy group with predefined settings.
 *     Default settings:
 *     - Schedule: Monday to Sunday (1-7), 00:00:00-23:59:59
 *     - Status: enabled (disable=false)
 *     - Match mode: continue matching (stop=false)
 *
 * PARAMETERS:
 *     name     - Name of the default policy group
 *     previous - Previous group name for ordering (empty for first position)
 *
 * RETURNS:
 *     error - Error if creation fails
 *****************************************************************************/
func (p *FlowControlProcessor) createDefaultPolicyGroup(name, previous string) error {
	// Create default time specification: Monday to Sunday, 00:00:00-23:59:59
	timeRange := &pb.TimeSpec{
		StartDay: 1, // Monday
		EndDay:   7, // Sunday
		StartTime: &pb.DailyTime{
			Hour: 0,
			Min:  0,
			Sec:  0,
		},
		EndTime: &pb.DailyTime{
			Hour: 23,
			Min:  59,
			Sec:  59,
		},
	}

	// Create default policy group task
	task := &pb.FlowControlPolicyGroupTask{
		Name:      name,
		TimeRange: timeRange,
		Disable:   false, // enabled by default
		Stop:      false, // continue matching by default
	}

	// Set previous reference if provided
	if previous != "" {
		task.Previous = &previous
	}

	// Create the policy group using existing logic
	_, err := p.handlePolicyGroupConfigChange(context.Background(), task, pb.TaskAction_NEW_CONFIG)
	if err != nil {
		return fmt.Errorf("failed to create default policy group %s: %w", name, err)
	}

	return nil
}

/*****************************************************************************
 * NAME: ensureDefaultGroupsOrdering
 *
 * DESCRIPTION:
 *     Ensures that default policy groups are ordered correctly at the top.
 *     This method is called after creating default groups to handle cases
 *     where other groups might already exist in the system.
 *
 * RETURNS:
 *     error - Error if ordering adjustment fails
 *****************************************************************************/
func (p *FlowControlProcessor) ensureDefaultGroupsOrdering() error {
	p.logger.Info("ensuring default policy groups are ordered correctly")

	// Get current policy group list from working configs
	// This method is called during default group creation, so we need fresh data
	if err := p.refreshWorkingConfigs(); err != nil {
		return fmt.Errorf("failed to refresh working configs for default groups ordering: %w", err)
	}

	var currentGroups []PolicyGroupOrderInfo
	for name, config := range p.workingPolicyGroups {
		currentGroups = append(currentGroups, PolicyGroupOrderInfo{
			ID:   config.ID,
			Name: name,
		})
	}

	// Find default groups
	var trafficMirroringID, subscriptionID int = -1, -1
	var minNonDefaultID int = -1

	for _, group := range currentGroups {
		switch group.Name {
		case DefaultTrafficMirroringGroup:
			trafficMirroringID = group.ID
		case DefaultSubscriptionGroup:
			subscriptionID = group.ID
		default:
			// Find the smallest ID among non-default groups
			if minNonDefaultID == -1 || group.ID < minNonDefaultID {
				minNonDefaultID = group.ID
			}
		}
	}

	// If both default groups exist, ensure they are at the top
	if trafficMirroringID != -1 && subscriptionID != -1 {
		// Check if _traffic_mirroring_group is already at position 1
		if trafficMirroringID != 1 && minNonDefaultID != -1 && trafficMirroringID > minNonDefaultID {
			// Move _traffic_mirroring_group to first position
			cmdArgs := []string{"policygroup2", "set", "id=" + strconv.Itoa(trafficMirroringID), "moveto=" + strconv.Itoa(minNonDefaultID)}
			p.logger.Info("moving _traffic_mirroring_group to first position",
				zap.Int("current_id", trafficMirroringID),
				zap.Int("before_id", minNonDefaultID))

			if _, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...); err != nil {
				p.logger.Warn("failed to move _traffic_mirroring_group to first position", zap.Error(err))
			}
		}

		// Check if _subscription_group is at position 2 (after _traffic_mirroring_group)
		if subscriptionID != 2 && subscriptionID != trafficMirroringID+1 {
			// Move _subscription_group to second position (after _traffic_mirroring_group)
			// Find the group that should come after _subscription_group
			var nextGroupID int = -1
			for _, group := range currentGroups {
				if group.Name != DefaultTrafficMirroringGroup && group.Name != DefaultSubscriptionGroup {
					if nextGroupID == -1 || group.ID < nextGroupID {
						nextGroupID = group.ID
					}
				}
			}

			if nextGroupID != -1 {
				cmdArgs := []string{"policygroup2", "set", "id=" + strconv.Itoa(subscriptionID), "moveto=" + strconv.Itoa(nextGroupID)}
				p.logger.Info("moving _subscription_group to second position",
					zap.Int("current_id", subscriptionID),
					zap.Int("before_id", nextGroupID))

				if _, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...); err != nil {
					p.logger.Warn("failed to move _subscription_group to second position", zap.Error(err))
				}
			}
		}
	}

	p.logger.Info("default policy groups ordering check completed")
	return nil
}

/*****************************************************************************
 * NAME: getGroupNameByID
 *
 * DESCRIPTION:
 *     Gets the group name by its ID from the current groups list.
 *
 * PARAMETERS:
 *     groupID       - ID of the group to find
 *     currentGroups - List of current policy groups
 *
 * RETURNS:
 *     string - Group name if found, empty string otherwise
 *****************************************************************************/
func (p *FlowControlProcessor) getGroupNameByID(groupID int, currentGroups []PolicyGroupOrderInfo) string {
	for _, group := range currentGroups {
		if group.ID == groupID {
			return group.Name
		}
	}
	return ""
}

/*****************************************************************************
 * NAME: handlePolicyGroupEnableDisable
 *
 * DESCRIPTION:
 *     Handles flow control policy group enable/disable operation separately from group creation/modification.
 *     Uses dedicated floweye policygroup2 set command with only disable parameter.
 *
 * PARAMETERS:
 *     groupName - Policy group name
 *     disable   - Whether to disable the group (true=disable, false=enable)
 *
 * RETURNS:
 *     error - Error if enable/disable operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyGroupEnableDisable(groupName string, disable bool) error {
	p.logger.Debug("handling flow control policy group enable/disable",
		zap.String("group_name", groupName),
		zap.Bool("disable", disable))

	// Get group ID from working configs
	groupID, exists := p.workingGroupNameToID[groupName]
	if !exists {
		return fmt.Errorf("policy group '%s' not found in working cache", groupName)
	}

	// Build floweye command for enable/disable operation
	disableValue := "0" // 0 = enable
	if disable {
		disableValue = "1" // 1 = disable
	}

	cmdArgs := []string{"policygroup2", "set", "id=" + strconv.Itoa(groupID), "disable=" + disableValue}

	// Execute floweye command
	p.logger.Info("executing floweye enable/disable command for flow control policy group",
		zap.String("group_name", groupName),
		zap.Int("group_id", groupID),
		zap.String("disable", disableValue),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye enable/disable command for flow control policy group",
			zap.String("group_name", groupName),
			zap.Int("group_id", groupID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to %s flow control policy group: %w",
			map[bool]string{true: "disable", false: "enable"}[disable], err)
	}

	p.logger.Debug("floweye enable/disable command executed successfully",
		zap.String("group_name", groupName),
		zap.Int("group_id", groupID),
		zap.String("operation", map[bool]string{true: "disabled", false: "enabled"}[disable]),
		zap.String("output", output))

	return nil
}

/*****************************************************************************
 * NAME: buildPolicyGroupCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for policy group using converted data.
 *     Uses internal PolicyGroupConfig structure instead of protobuf access.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - Converted policy group configuration data
 *
 * RETURNS:
 *     error - Error if building command fails
 *****************************************************************************/
func (p *FlowControlProcessor) buildPolicyGroupCommand(cmdArgs *[]string, configData *PolicyGroupConfig) error {
	// Add common arguments
	*cmdArgs = append(*cmdArgs, "name="+configData.Name)

	// Add time configuration using converted data
	if configData.Schedule != nil {
		// Add month (always 0 for weekly schedule)
		*cmdArgs = append(*cmdArgs, "month=0")

		// Add day range
		*cmdArgs = append(*cmdArgs, fmt.Sprintf("startday=%d", configData.Schedule.StartDay))
		*cmdArgs = append(*cmdArgs, fmt.Sprintf("endday=%d", configData.Schedule.EndDay))

		// Format time strings
		startTimeStr := fmt.Sprintf("%d:%d:%d",
			configData.Schedule.StartHour, configData.Schedule.StartMin, configData.Schedule.StartSec)
		*cmdArgs = append(*cmdArgs, "start="+startTimeStr)

		endTimeStr := fmt.Sprintf("%d:%d:%d",
			configData.Schedule.EndHour, configData.Schedule.EndMin, configData.Schedule.EndSec)
		*cmdArgs = append(*cmdArgs, "end="+endTimeStr)
	} else {
		// Default values if no schedule specified
		*cmdArgs = append(*cmdArgs, "month=0", "startday=1", "endday=7")
		*cmdArgs = append(*cmdArgs, "start=0:0:0", "end=23:59:59")
	}

	// Add stop configuration
	stopValue := "0"
	if configData.Stop {
		stopValue = "1"
	}
	*cmdArgs = append(*cmdArgs, "stop="+stopValue)

	// Note: disable parameter is handled separately after policy group creation/modification

	// Add default values for required fields
	*cmdArgs = append(*cmdArgs, "inip=", "outip=", "ifname=NULL", "ifbps=0")

	return nil
}

/*****************************************************************************
 * NAME: handlePolicyGroupOrderingWithConfig
 *
 * DESCRIPTION:
 *     Handles policy group ordering using converted configuration data.
 *     Uses internal PolicyGroupConfig structure instead of protobuf access.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Converted policy group configuration data
 *
 * RETURNS:
 *     error - Error if ordering fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyGroupOrderingWithConfig(ctx context.Context, configData *PolicyGroupConfig) error {
	// If no previous reference, no ordering needed
	if configData.Previous == "" {
		p.logger.Debug("no previous reference for policy group, skipping ordering",
			zap.String("name", configData.Name))
		return nil
	}

	previousGroupName := configData.Previous

	// Get current policy group list from working configs for ordering
	// During full sync, working configs are refreshed independently
	// During incremental updates, working configs are synced with local configs
	var currentGroups []PolicyGroupOrderInfo
	for name, config := range p.workingPolicyGroups {
		currentGroups = append(currentGroups, PolicyGroupOrderInfo{
			ID:   config.ID,
			Name: name,
		})
	}

	// Find the current group and previous group IDs
	var currentGroupID, previousGroupID int
	var currentGroupFound, previousGroupFound bool

	for _, group := range currentGroups {
		if group.Name == configData.Name {
			currentGroupID = group.ID
			currentGroupFound = true
		}
		if group.Name == previousGroupName {
			previousGroupID = group.ID
			previousGroupFound = true
		}
	}

	if !currentGroupFound {
		return fmt.Errorf("current policy group '%s' not found in list", configData.Name)
	}

	// Use simplified moveto-based ordering
	return p.movePolicyGroupUsingMoveTo(currentGroupID, previousGroupName, previousGroupFound, previousGroupID, currentGroups)
}

/*****************************************************************************
 * NAME: updatePolicyGroupIDAfterCreation
 *
 * DESCRIPTION:
 *     Updates the local cache with the newly created policy group ID.
 *     Uses floweye policygroup2 list to get the assigned ID immediately after creation.
 *
 * PARAMETERS:
 *     groupName - Name of the newly created policy group
 *
 * RETURNS:
 *     error - Error if ID retrieval fails
 *****************************************************************************/
func (p *FlowControlProcessor) updatePolicyGroupIDAfterCreation(groupName string) error {
	p.logger.Debug("updating policy group ID after creation",
		zap.String("group_name", groupName))

	// Get current policy group list to find the new ID
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "policygroup2", "list")
	if err != nil {
		return fmt.Errorf("failed to list policy groups after creation: %w", err)
	}

	// Parse the list to find the group ID by name
	groupIDs := p.parsePolicyGroupIDsFromList(output)
	var newGroupID int = -1

	for _, groupID := range groupIDs {
		// Get group config to check name
		tempConfig, err := p.getPolicyGroupConfig(groupID)
		if err != nil {
			p.logger.Warn("failed to get policy group config during ID update",
				zap.Int("group_id", groupID),
				zap.Error(err))
			continue
		}

		if tempConfig.Name == groupName {
			newGroupID = groupID
			break
		}
	}

	if newGroupID == -1 {
		return fmt.Errorf("newly created policy group '%s' not found in list", groupName)
	}

	// Update local cache
	p.groupNameToID[groupName] = newGroupID

	p.logger.Info("successfully updated policy group ID after creation",
		zap.String("group_name", groupName),
		zap.Int("group_id", newGroupID))

	return nil
}

/*****************************************************************************
 * NAME: verifyPolicyGroupConfigWithData
 *
 * DESCRIPTION:
 *     Verifies that a policy group configuration was applied correctly using converted data.
 *     Uses single-object retrieval for performance optimization.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Converted policy group configuration data to verify
 *
 * RETURNS:
 *     error - Error if verification fails
 *****************************************************************************/
func (p *FlowControlProcessor) verifyPolicyGroupConfigWithData(ctx context.Context, configData *PolicyGroupConfig) error {

	// Find the policy group ID by name using floweye list command
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "policygroup2", "list")
	if err != nil {
		return fmt.Errorf("failed to list policy groups for verification: %w", err)
	}

	// Parse the list to find the group ID by name
	groupIDs := p.parsePolicyGroupIDsFromList(output)
	var targetGroupID int = -1

	for _, groupID := range groupIDs {
		// Get group config to check name
		tempConfig, err := p.getPolicyGroupConfig(groupID)
		if err != nil {
			p.logger.Warn("failed to get policy group config during verification",
				zap.Int("group_id", groupID),
				zap.Error(err))
			continue
		}

		if tempConfig.Name == configData.Name {
			targetGroupID = groupID
			break
		}
	}

	if targetGroupID == -1 {
		return fmt.Errorf("policy group %s not found after creation/update", configData.Name)
	}

	// Get the actual configuration using single-object retrieval
	actualConfig, err := p.getPolicyGroupConfig(targetGroupID)
	if err != nil {
		return fmt.Errorf("failed to get policy group for verification: %w", err)
	}

	// Compare configurations using converted data
	if !ComparePolicyGroupConfig(p.logger, configData, actualConfig) {
		return fmt.Errorf("policy group configuration verification failed: configurations do not match")
	}

	p.logger.Debug("policy group configuration verification successful",
		zap.String("name", configData.Name))
	return nil
}
