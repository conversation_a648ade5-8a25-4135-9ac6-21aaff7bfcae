/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      route_policy_ordering.go
 *
 * DESCRIPTION :    Route policy ordering logic implementation
 *                  包含路由策略排序相关的所有函数，支持前向插入和后向移动
 *
 * AUTHOR :         wei
 *
 * HISTORY :        05/06/2025  create
 ******************************************************************************/

package task

import (
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
)

// RoutePolicyOrderInfo represents route policy ordering information
type RoutePolicyOrderInfo struct {
	ID     int
	Cookie uint32
}

/*****************************************************************************
 * NAME: parseRoutePolicyListForOrdering
 *
 * DESCRIPTION:
 *     Parses route policy list output for ordering operations.
 *     Extracts ID, cookie, and position information.
 *
 * PARAMETERS:
 *     p      - Route policy processor instance
 *     output - Output from floweye route list json=1 command
 *
 * RETURNS:
 *     []RoutePolicyOrderInfo - List of route policy ordering information
 *     error                   - Error if parsing fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) parseRoutePolicyListForOrdering(output string) ([]RoutePolicyOrderInfo, error) {
	var policies []RoutePolicyOrderInfo

	// Parse JSON output
	configs, err := ParseRoutePolicyFromList(output)
	if err != nil {
		return nil, fmt.Errorf("failed to parse route policy list: %w", err)
	}

	// Convert to ordering info
	for _, config := range configs {
		policies = append(policies, RoutePolicyOrderInfo{
			ID:     config.ID,
			Cookie: config.Cookie,
		})
	}

	return policies, nil
}

/*****************************************************************************
 * NAME: moveRoutePolicyToPositionBidirectional
 *
 * DESCRIPTION:
 *     Moves a route policy to the specified position with bidirectional support.
 *     Implements both forward insertion and backward movement.
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     currentPolicyID - ID of the policy to move
 *     targetPosition  - Target position (1-based)
 *     currentPolicies - Current list of route policies
 *
 * RETURNS:
 *     error - Error if move operation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) moveRoutePolicyToPositionBidirectional(currentPolicyID, targetPosition int, currentPolicies []RoutePolicyOrderInfo) error {
	p.logger.Info("moving route policy with bidirectional support",
		zap.Int("current_id", currentPolicyID),
		zap.Int("target_position", targetPosition),
		zap.Int("total_policies", len(currentPolicies)))

	// Implement the ID adjustment logic:
	// 1. Set current policy ID to temporary ID (find unused ID in valid range)
	// 2. Adjust other policy IDs based on direction
	// 3. Set current policy to target position

	// Find an unused temporary ID in the valid range (1-65535)
	tempID := p.findUnusedTemporaryID(currentPolicies)
	if tempID == 0 {
		return fmt.Errorf("no available temporary ID found for policy movement")
	}

	// Step 1: Move current policy to temporary ID
	if err := p.setRoutePolicyID(currentPolicyID, tempID); err != nil {
		return fmt.Errorf("failed to set route policy to temporary ID: %w", err)
	}

	// Step 2: Adjust other policy IDs based on direction
	if currentPolicyID > targetPosition {
		// Forward insertion: current ID > target ID
		// Increment IDs in range [target, current-1] by +1 (from large to small)
		if err := p.adjustRoutePolicyIDsForwardInsertion(targetPosition, currentPolicyID-1, currentPolicies); err != nil {
			// Try to restore original ID if adjustment fails
			p.setRoutePolicyID(tempID, currentPolicyID)
			return fmt.Errorf("failed to adjust route policy IDs for forward insertion: %w", err)
		}
	} else if currentPolicyID < targetPosition {
		// Backward movement: current ID < target ID
		// Shift policies at target position and beyond by +1 (to make room for insertion)
		if err := p.adjustRoutePolicyIDsBackwardMovement(targetPosition, targetPosition, currentPolicies); err != nil {
			// Try to restore original ID if adjustment fails
			p.setRoutePolicyID(tempID, currentPolicyID)
			return fmt.Errorf("failed to adjust route policy IDs for backward movement: %w", err)
		}
	}

	// Step 3: Set current policy to target position
	if err := p.setRoutePolicyID(tempID, targetPosition); err != nil {
		return fmt.Errorf("failed to set route policy to target position: %w", err)
	}

	p.logger.Info("successfully moved route policy with bidirectional support",
		zap.Int("old_id", currentPolicyID),
		zap.Int("new_id", targetPosition))

	return nil
}

/*****************************************************************************
 * NAME: findUnusedTemporaryID
 *
 * DESCRIPTION:
 *     Finds an unused temporary ID in the valid range (1-65535) for policy movement.
 *     Searches from high values down to avoid conflicts with normal policy IDs.
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     currentPolicies - Current list of route policies
 *
 * RETURNS:
 *     int - Unused temporary ID (0 if none found)
 *****************************************************************************/
func (p *RoutePolicyProcessor) findUnusedTemporaryID(currentPolicies []RoutePolicyOrderInfo) int {
	// Create a map of used IDs for fast lookup
	usedIDs := make(map[int]bool)
	for _, policy := range currentPolicies {
		usedIDs[policy.ID] = true
	}

	// Search from high values down to avoid conflicts with normal policy IDs
	// Start from 65535 and work down
	for tempID := 65535; tempID >= 60000; tempID-- {
		if !usedIDs[tempID] {
			p.logger.Debug("found unused temporary ID",
				zap.Int("temp_id", tempID))
			return tempID
		}
	}

	// If no high-value ID is available, search in a different range
	for tempID := 59999; tempID >= 55000; tempID-- {
		if !usedIDs[tempID] {
			p.logger.Debug("found unused temporary ID in fallback range",
				zap.Int("temp_id", tempID))
			return tempID
		}
	}

	p.logger.Error("no unused temporary ID found in valid ranges")
	return 0
}

/*****************************************************************************
 * NAME: setRoutePolicyID
 *
 * DESCRIPTION:
 *     Sets a route policy's ID using floweye route set command.
 *
 * PARAMETERS:
 *     p     - Route policy processor instance
 *     oldID - Current route policy ID
 *     newID - New route policy ID
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) setRoutePolicyID(oldID, newID int) error {
	cmdArgs := []string{"route", "set", "id=" + strconv.Itoa(oldID), "newid=" + strconv.Itoa(newID)}

	p.logger.Debug("setting route policy ID",
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		return fmt.Errorf("failed to set route policy ID from %d to %d: %w, output: %s", oldID, newID, err, output)
	}

	return nil
}

/*****************************************************************************
 * NAME: adjustRoutePolicyIDsForwardInsertion
 *
 * DESCRIPTION:
 *     Adjusts route policy IDs for forward insertion (current ID > target ID).
 *     Increments IDs in range [targetID, currentID-1] by +1 (from large to small).
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     targetID        - Target position ID
 *     currentIDMinus1 - Current ID minus 1 (end of range)
 *     currentPolicies - Current list of route policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) adjustRoutePolicyIDsForwardInsertion(targetID, currentIDMinus1 int, currentPolicies []RoutePolicyOrderInfo) error {
	p.logger.Debug("adjusting route policy IDs for forward insertion",
		zap.Int("target_id", targetID),
		zap.Int("current_id_minus_1", currentIDMinus1))

	// Find policies in range [targetID, currentID-1] that need to be shifted
	var policiesToShift []RoutePolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID && policy.ID <= currentIDMinus1 {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID < policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by +1
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setRoutePolicyID(policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift route policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted route policy ID for forward insertion",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: adjustRoutePolicyIDsBackwardMovement
 *
 * DESCRIPTION:
 *     Adjusts route policy IDs for backward movement (current ID < target ID).
 *     Decrements IDs in range [currentID+1, targetID] by -1 (from small to large).
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     currentIDPlus1  - Current ID plus 1 (start of range)
 *     targetID        - Target position ID
 *     currentPolicies - Current list of route policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) adjustRoutePolicyIDsBackwardMovement(targetID, targetID2 int, currentPolicies []RoutePolicyOrderInfo) error {
	p.logger.Debug("adjusting route policy IDs for backward movement",
		zap.Int("target_id", targetID))

	// Find policies at target position and beyond that need to be shifted to make room
	var policiesToShift []RoutePolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID < policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by +1 to make room for insertion
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setRoutePolicyID(policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift route policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted route policy ID for backward movement",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: handleRoutePolicyDeletionOrdering
 *
 * DESCRIPTION:
 *     Handles route policy ordering after deletion.
 *     Decrements IDs of all route policies with ID > deleted ID to maintain continuous ordering.
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     ctx             - Context for the operation
 *     deletedPolicyID - ID of the deleted route policy
 *
 * RETURNS:
 *     error - Error if ordering adjustment fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) handleRoutePolicyDeletionOrdering(ctx context.Context, deletedPolicyID int) error {
	p.logger.Debug("handling route policy deletion ordering",
		zap.Int("deleted_policy_id", deletedPolicyID))

	// Get current route policy list
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "route", "list", "json=1")
	if err != nil {
		return fmt.Errorf("failed to get route policy list for ordering adjustment: %w", err)
	}

	// Parse current route policies and their order
	currentPolicies, err := p.parseRoutePolicyListForOrdering(output)
	if err != nil {
		return fmt.Errorf("failed to parse route policy list for ordering adjustment: %w", err)
	}

	// Find route policies with ID > deleted ID that need to be decremented
	var policiesToShift []RoutePolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID > deletedPolicyID {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in ascending order (adjust from small to large to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID > policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each route policy ID by -1 to maintain continuous ordering
	for _, policy := range policiesToShift {
		newID := policy.ID - 1
		if err := p.setRoutePolicyID(policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift route policy ID from %d to %d after deletion: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted route policy ID after deletion",
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	p.logger.Debug("route policy deletion ordering completed",
		zap.Int("deleted_policy_id", deletedPolicyID),
		zap.Int("policies_shifted", len(policiesToShift)))

	return nil
}

/*****************************************************************************
 * NAME: allocateRoutePolicyID
 *
 * DESCRIPTION:
 *     Allocates ID for route policy based on zone priority ranges.
 *     If policy exists (by cookie), returns existing ID.
 *     If policy doesn't exist, allocates new ID within zone's priority range.
 *
 * PARAMETERS:
 *     p      - Route policy processor instance
 *     cookie - Policy cookie to check
 *     zone   - Policy zone for priority range allocation
 *
 * RETURNS:
 *     int   - Allocated policy ID
 *     error - Error if allocation fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) allocateRoutePolicyID(cookie uint32, zone pb.RoutePolicyZone) (int, error) {
	// Check if policy already exists by cookie (use working config for operations)
	if existingConfig, exists := p.getWorkingConfigByCookie(cookie); exists {
		p.logger.Debug("policy exists, using existing ID",
			zap.Uint32("cookie", cookie),
			zap.Int("existing_id", existingConfig.ID),
			zap.String("zone", zone.String()))
		return existingConfig.ID, nil
	}

	// Policy doesn't exist, allocate new ID within zone's priority range
	minPriority, maxPriority := GetZonePriorityRange(zone)

	// Collect all used IDs in this zone (use working config for operations)
	usedIDs := make(map[int]bool)
	if zoneMap, exists := p.workingZoneConfigs[zone]; exists {
		for _, config := range zoneMap {
			usedIDs[config.ID] = true
		}
	}

	// Find the first available ID from the maximum value downward
	for candidateID := maxPriority; candidateID >= minPriority; candidateID-- {
		if !usedIDs[candidateID] {
			p.logger.Debug("allocating new policy ID within zone (from max downward)",
				zap.Uint32("cookie", cookie),
				zap.String("zone", zone.String()),
				zap.Int("new_id", candidateID),
				zap.Int("min_priority", minPriority),
				zap.Int("max_priority", maxPriority))

			return candidateID, nil
		}
	}

	// No available ID found in the zone
	return 0, fmt.Errorf("zone %s has reached maximum capacity (max priority: %d)", zone.String(), maxPriority)
}

/*****************************************************************************
 * NAME: moveRoutePolicyToZonePosition
 *
 * DESCRIPTION:
 *     将路由策略移动到指定zone内的指定位置。
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     currentPolicyID - 当前策略ID
 *     zone           - 目标zone
 *     position       - 目标位置（-1表示最后位置）
 *     currentPolicies - 当前策略列表
 *
 * RETURNS:
 *     error - 错误信息
 *****************************************************************************/
func (p *RoutePolicyProcessor) moveRoutePolicyToZonePosition(currentPolicyID int, zone pb.RoutePolicyZone, position int, currentPolicies []RoutePolicyOrderInfo) error {
	minPriority, maxPriority := GetZonePriorityRange(zone)

	var targetPosition int
	if position == -1 {
		// 移动到zone的最后位置 - 找到zone中所有策略的最大ID，然后移动到比它更大的位置
		maxIDInZone := minPriority - 1 // 如果zone中没有策略，使用zone的最小位置
		for _, policy := range currentPolicies {
			policyZone := GetZoneFromPriority(policy.ID)
			if policyZone == zone && policy.ID != currentPolicyID {
				if policy.ID > maxIDInZone {
					maxIDInZone = policy.ID
				}
			}
		}

		// 目标位置是zone中最大ID + 1，但不能超过zone的最大范围
		targetPosition = maxIDInZone + 1
		if targetPosition < minPriority {
			targetPosition = minPriority
		}
		if targetPosition > maxPriority {
			targetPosition = maxPriority
		}
	} else {
		// 移动到zone内的指定位置
		targetPosition = minPriority + position - 1
		if targetPosition > maxPriority {
			return fmt.Errorf("target position %d exceeds zone %s maximum priority %d", targetPosition, zone.String(), maxPriority)
		}
	}

	p.logger.Debug("moving route policy to zone position",
		zap.Int("current_id", currentPolicyID),
		zap.String("zone", zone.String()),
		zap.Int("target_position", targetPosition),
		zap.Int("min_priority", minPriority),
		zap.Int("max_priority", maxPriority))

	// 使用现有的移动逻辑
	return p.moveRoutePolicyToPositionBidirectional(currentPolicyID, targetPosition, currentPolicies)
}

/*****************************************************************************
 * NAME: moveRoutePolicyToPositionBidirectionalWithZone
 *
 * DESCRIPTION:
 *     在指定zone内双向移动路由策略到目标位置。
 *     支持向前插入和向后移动，确保zone内排序正确。
 *
 * PARAMETERS:
 *     p               - Route policy processor instance
 *     currentPolicyID - 当前策略ID
 *     targetPosition  - 目标位置
 *     zone           - 策略所在zone
 *     currentPolicies - 当前策略列表
 *
 * RETURNS:
 *     error - 错误信息
 *****************************************************************************/
func (p *RoutePolicyProcessor) moveRoutePolicyToPositionBidirectionalWithZone(currentPolicyID int, targetPosition int, zone pb.RoutePolicyZone, currentPolicies []RoutePolicyOrderInfo) error {
	minPriority, maxPriority := GetZonePriorityRange(zone)

	// 验证目标位置在zone范围内
	if targetPosition < minPriority || targetPosition > maxPriority {
		return fmt.Errorf("target position %d is outside zone %s range [%d, %d]", targetPosition, zone.String(), minPriority, maxPriority)
	}

	p.logger.Debug("moving route policy within zone",
		zap.Int("current_id", currentPolicyID),
		zap.Int("target_position", targetPosition),
		zap.String("zone", zone.String()),
		zap.Int("min_priority", minPriority),
		zap.Int("max_priority", maxPriority))

	// 过滤出同zone内的策略
	zonePolicies := make([]RoutePolicyOrderInfo, 0)
	for _, policy := range currentPolicies {
		policyZone := GetZoneFromPriority(policy.ID)
		if policyZone == zone {
			zonePolicies = append(zonePolicies, policy)
		}
	}

	// 使用现有的双向移动逻辑，但只在zone内操作
	return p.moveRoutePolicyToPositionBidirectional(currentPolicyID, targetPosition, zonePolicies)
}

/*****************************************************************************
 * NAME: handleRoutePolicyOrderingData
 *
 * DESCRIPTION:
 *     Handles route policy ordering using converted data structure.
 *     Uses unified internal data structure to eliminate protobuf parsing.
 *     Implements the ordering logic similar to flow control policies.
 *
 * PARAMETERS:
 *     p          - Route policy processor instance
 *     ctx        - Context for the operation
 *     configData - Route policy configuration data (converted from protobuf)
 *
 * RETURNS:
 *     error - Error if ordering fails
 *****************************************************************************/
func (p *RoutePolicyProcessor) handleRoutePolicyOrderingData(ctx context.Context, configData *RoutePolicyConfig) error {
	// During full sync, we cannot refresh localConfigs as it's used for redundant config deletion
	// We will use the current localConfigs state and query floweye directly when needed

	// Special case: previous = 0 means move to first position in specified zone
	if configData.Previous == 0 {
		p.logger.Debug("previous is 0, moving route policy to first position in specified zone",
			zap.Uint32("cookie", configData.Cookie),
			zap.String("specified_zone", configData.Zone.String()),
			zap.Bool("full_sync_in_progress", p.fullSyncInProgress))

		// Get current policy ID using the specified zone from config
		currentPolicyID, err := p.allocateRoutePolicyID(configData.Cookie, configData.Zone)
		if err != nil {
			return fmt.Errorf("failed to get current policy ID: %w", err)
		}

		// Use the zone specified in the configuration, not derived from ID
		targetZone := configData.Zone
		p.logger.Debug("using specified zone for first position move",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("policy_id", currentPolicyID),
			zap.String("target_zone", targetZone.String()))

		// Use working cache for current policies (for all operations)
		var currentPolicies []RoutePolicyOrderInfo
		for _, zoneMap := range p.workingZoneConfigs {
			for _, config := range zoneMap {
				currentPolicies = append(currentPolicies, RoutePolicyOrderInfo{
					ID:     config.ID,
					Cookie: config.Cookie,
				})
			}
		}

		// Find the minimum ID in the target zone (excluding current policy)
		minPriority, _ := GetZonePriorityRange(targetZone)
		minIDInZone := -1
		for _, policy := range currentPolicies {
			policyZone := GetZoneFromPriority(policy.ID)
			if policyZone == targetZone && policy.ID != currentPolicyID {
				if minIDInZone == -1 || policy.ID < minIDInZone {
					minIDInZone = policy.ID
				}
			}
		}

		var targetPosition int
		if minIDInZone == -1 {
			// No other policies in zone, use zone minimum
			targetPosition = minPriority
		} else {
			// Move to position before the current minimum
			targetPosition = minIDInZone - 1
			if targetPosition < minPriority {
				targetPosition = minPriority
			}
		}

		p.logger.Debug("calculated target position for first position move",
			zap.Int("current_id", currentPolicyID),
			zap.Int("min_id_in_zone", minIDInZone),
			zap.Int("target_position", targetPosition),
			zap.String("target_zone", targetZone.String()))

		// Move to first position within the specified zone
		return p.moveRoutePolicyToPositionBidirectionalWithZone(currentPolicyID, targetPosition, targetZone, currentPolicies)
	}

	// Special case: previous = 4294967295 (uint32 max) means move to last position (-1 in signed)
	if configData.Previous == 4294967295 {
		p.logger.Debug("previous is 4294967295 (uint32 max), moving route policy to last position in zone",
			zap.Uint32("cookie", configData.Cookie),
			zap.String("zone", configData.Zone.String()),
			zap.Bool("full_sync_in_progress", p.fullSyncInProgress))

		// Get current policy ID
		currentPolicyID, err := p.allocateRoutePolicyID(configData.Cookie, configData.Zone)
		if err != nil {
			return fmt.Errorf("failed to get current policy ID: %w", err)
		}

		// Use working cache for current policies
		var currentPolicies []RoutePolicyOrderInfo
		for _, zoneMap := range p.workingZoneConfigs {
			for _, config := range zoneMap {
				currentPolicies = append(currentPolicies, RoutePolicyOrderInfo{
					ID:     config.ID,
					Cookie: config.Cookie,
				})
			}
		}

		// Move to last position within the zone
		return p.moveRoutePolicyToZonePosition(currentPolicyID, configData.Zone, -1, currentPolicies)
	}

	// Determine current policy's zone using converted data
	currentZone := configData.Zone
	previousCookie := configData.Previous

	// Use working cache instead of re-querying floweye to avoid timing issues
	// Working configs are used for all operations and can be refreshed independently
	var currentPolicies []RoutePolicyOrderInfo
	for _, zoneMap := range p.workingZoneConfigs {
		for _, config := range zoneMap {
			currentPolicies = append(currentPolicies, RoutePolicyOrderInfo{
				ID:     config.ID,
				Cookie: config.Cookie,
			})
		}
	}

	// Find the current policy and previous policy IDs, and validate zone constraints
	var currentPolicyID, previousPolicyID int
	var currentPolicyFound, previousPolicyFound bool
	var previousZone pb.RoutePolicyZone

	for _, policy := range currentPolicies {
		if policy.Cookie == configData.Cookie {
			currentPolicyID = policy.ID
			currentPolicyFound = true
		}
		if policy.Cookie == previousCookie {
			previousPolicyID = policy.ID
			previousPolicyFound = true
			previousZone = GetZoneFromPriority(policy.ID)
		}
	}

	if !currentPolicyFound {
		return fmt.Errorf("current route policy with cookie %d not found in list", configData.Cookie)
	}

	if !previousPolicyFound {
		p.logger.Warn("previous route policy not found, treating as first position in zone",
			zap.Uint32("previous_cookie", previousCookie),
			zap.Uint32("current_cookie", configData.Cookie),
			zap.String("zone", currentZone.String()))
		// Move to first position within the zone
		return p.moveRoutePolicyToZonePosition(currentPolicyID, currentZone, 1, currentPolicies)
	}

	// Validate that previous policy is in the same zone or a compatible zone
	if previousZone != currentZone {
		p.logger.Warn("previous policy is in different zone, adjusting ordering within current zone",
			zap.Uint32("previous_cookie", previousCookie),
			zap.String("previous_zone", previousZone.String()),
			zap.Uint32("current_cookie", configData.Cookie),
			zap.String("current_zone", currentZone.String()))
		// Move to first position within the current zone
		return p.moveRoutePolicyToZonePosition(currentPolicyID, currentZone, 1, currentPolicies)
	}

	// Calculate target position (previous ID + 1) - same logic as flow control policy
	targetPosition := previousPolicyID + 1

	p.logger.Debug("moving route policy to position after previous policy",
		zap.Uint32("current_cookie", configData.Cookie),
		zap.Int("current_id", currentPolicyID),
		zap.Uint32("previous_cookie", previousCookie),
		zap.Int("previous_id", previousPolicyID),
		zap.Int("target_position", targetPosition),
		zap.String("zone", currentZone.String()))

	// If already in correct position, no need to move
	if currentPolicyID == targetPosition {
		p.logger.Debug("route policy already in correct position",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("current_id", currentPolicyID),
			zap.Int("target_position", targetPosition),
			zap.String("zone", currentZone.String()))
		return nil
	}

	// Move to target position with zone-aware bidirectional support
	return p.moveRoutePolicyToPositionBidirectionalWithZone(currentPolicyID, targetPosition, currentZone, currentPolicies)
}
