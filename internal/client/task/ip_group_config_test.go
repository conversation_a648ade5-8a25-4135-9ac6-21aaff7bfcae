/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      ip_group_config_test.go
 *
 * DESCRIPTION :    Unit tests for IP Group configuration functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/20/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Helper function to convert string slice to interface slice
func StringSliceToInterfaceSlice(slice []string) []interface{} {
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}

// Mock for utils.ExecuteCommand
type IpGroupMockExecuteCommand struct {
	mock.Mock
}

func (m *IpGroupMockExecuteCommand) Execute(timeout int, command string, args ...string) (string, error) {
	var mockArgs mock.Arguments
	if len(args) > 0 {
		// Pass arguments one by one, not as a slice
		mockArgs = m.Called(append([]interface{}{timeout, command}, StringSliceToInterfaceSlice(args)...)...)
	} else {
		mockArgs = m.Called(timeout, command)
	}
	return mockArgs.String(0), mockArgs.Error(1)
}

// Setup test logger
func setupIpGroupTestLogger() *logger.Logger {
	logConfig := logger.LogConfig{
		Level: "DEBUG",
		Outputs: []logger.Output{
			{
				Type: logger.TypeConsole,
			},
		},
	}
	log, _ := logger.NewLogger(logConfig)
	return log
}

// Use a different method to handle mock
var currentIpGroupMock *IpGroupMockExecuteCommand

// Setup mock for ExecuteCommand
func setupIpGroupMockExecuteCommand() (*IpGroupMockExecuteCommand, func()) {
	mockExecuteCommand := new(IpGroupMockExecuteCommand)

	// Set current mock
	currentIpGroupMock = mockExecuteCommand

	cleanup := func() {
		// Clear current mock after test
		currentIpGroupMock = nil
	}

	return mockExecuteCommand, cleanup
}

// Test GetIpGroupConfigs
func TestGetIpGroupConfigs(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Mock the list command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return(
		"1 group1\n"+
			"2 group2",
		nil,
	)

	// Mock the get command for each IP group
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group1"}).Return(
		"*********** Note for IP 1\n"+
			"*********** Note for IP 2",
		nil,
	)

	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group2"}).Return(
		"******** Server 1\n"+
			"******** Server 2\n"+
			"******** Server 3",
		nil,
	)

	// Call the function
	configs, err := GetIpGroupConfigs(log)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, configs)
	assert.Equal(t, 2, len(configs))

	// Verify group1
	group1, exists := configs["group1"]
	assert.True(t, exists)
	assert.Equal(t, 1, group1.ID)
	assert.Equal(t, "group1", group1.Name)
	assert.Equal(t, 2, len(group1.Members))
	assert.Equal(t, "***********", group1.Members[0].IP)
	assert.Equal(t, "Note for IP 1", group1.Members[0].Info)
	assert.Equal(t, "***********", group1.Members[1].IP)
	assert.Equal(t, "Note for IP 2", group1.Members[1].Info)

	// Verify group2
	group2, exists := configs["group2"]
	assert.True(t, exists)
	assert.Equal(t, 2, group2.ID)
	assert.Equal(t, "group2", group2.Name)
	assert.Equal(t, 3, len(group2.Members))
	assert.Equal(t, "********", group2.Members[0].IP)
	assert.Equal(t, "Server 1", group2.Members[0].Info)
	assert.Equal(t, "********", group2.Members[1].IP)
	assert.Equal(t, "Server 2", group2.Members[1].Info)
	assert.Equal(t, "********", group2.Members[2].IP)
	assert.Equal(t, "Server 3", group2.Members[2].Info)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test GetIpGroupConfig
func TestGetIpGroupConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Mock the get command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group1"}).Return(
		"*********** Note for IP 1\n"+
			"*********** Note for IP 2",
		nil,
	)

	// Call the function
	config, err := GetIpGroupConfig(log, "group1", 1)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, 1, config.ID)
	assert.Equal(t, "group1", config.Name)
	assert.Equal(t, 2, len(config.Members))
	assert.Equal(t, "***********", config.Members[0].IP)
	assert.Equal(t, "Note for IP 1", config.Members[0].Info)
	assert.Equal(t, "***********", config.Members[1].IP)
	assert.Equal(t, "Note for IP 2", config.Members[1].Info)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test GetIpGroupIdByName
func TestGetIpGroupIdByName(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Mock the list command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "list"}).Return(
		"1 group1\n"+
			"2 group2",
		nil,
	)

	// Call the function for existing group
	id, err := GetIpGroupIdByName(log, "group1")

	// Verify results
	assert.NoError(t, err)
	assert.Equal(t, 1, id)

	// Call the function for non-existing group
	id, err = GetIpGroupIdByName(log, "nonexistent")

	// Verify results
	assert.NoError(t, err)
	assert.Equal(t, -1, id)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test CompareIpGroupConfig
func TestCompareIpGroupConfig(t *testing.T) {
	log := setupIpGroupTestLogger()

	// Create an IP Group task
	ipGroupTask := &pb.IpGroupTask{
		Name: "group1",
	}

	// Convert to config data
	expectedConfig, err := ConvertIpGroupTaskToConfig(ipGroupTask)
	assert.NoError(t, err)

	// Create a local config
	localConfig := &IpGroupConfig{
		ID:      1,
		Name:    "group1",
		Members: []*IpGroupMember{},
	}

	// Test comparison - should return true for matching configs
	result := CompareIpGroupConfig(log, expectedConfig, localConfig)
	assert.True(t, result)

	// Test with different names
	localConfig.Name = "different_name"
	result = CompareIpGroupConfig(log, expectedConfig, localConfig)
	assert.False(t, result)
}

// Test VerifyIpGroupConfig
func TestVerifyIpGroupConfig(t *testing.T) {
	// Skip if running in CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test in CI environment")
	}

	log := setupIpGroupTestLogger()
	mockExecuteCommand, cleanup := setupIpGroupMockExecuteCommand()
	defer cleanup()

	// Mock the get command
	mockExecuteCommand.On("Execute", 10, "floweye", []string{"table", "get", "name=group1"}).Return(
		"*********** Note for IP 1\n"+
			"*********** Note for IP 2",
		nil,
	)

	// Create an IP Group task
	ipGroupTask := &pb.IpGroupTask{
		Name: "group1",
		Members: []*pb.IpGroupMember{
			{
				Ip:   &pb.IpAddr{Ip: "***********"},
				Info: "Note for IP 1",
			},
			{
				Ip:   &pb.IpAddr{Ip: "***********"},
				Info: "Note for IP 2",
			},
		},
	}

	// Convert to config data
	expectedConfig, err := ConvertIpGroupTaskToConfig(ipGroupTask)
	assert.NoError(t, err)

	// Call the function
	ctx := context.Background()
	success, err := VerifyIpGroupConfig(ctx, log, expectedConfig, 1)

	// Verify results - should return true if the group matches
	assert.NoError(t, err)
	assert.True(t, success)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}
