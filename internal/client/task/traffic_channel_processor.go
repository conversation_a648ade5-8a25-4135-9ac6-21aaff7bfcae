/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_channel_processor.go
 *
 * DESCRIPTION :    Traffic channel task processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        30/05/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: TrafficChannelProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_TRAFFIC_CHANNEL type tasks.
 *     Handles traffic channel configuration operations.
 *
 * FIELDS:
 *     logger             - Logger for traffic channel processor operations
 *     localConfigs       - Cache of local traffic channel configurations (used for full sync redundant deletion)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *****************************************************************************/
type TrafficChannelProcessor struct {
	logger             *logger.Logger                   // Logger for traffic channel processor operations
	localConfigs       map[string]*TrafficChannelConfig // Cache of local traffic channel configurations (used for full sync redundant deletion)
	fullSyncInProgress bool                             // Flag indicating if full sync is in progress
	workingConfigs     map[string]*TrafficChannelConfig // Working cache for operations (can be refreshed during full sync)
}

/*****************************************************************************
 * NAME: NewTrafficChannelProcessor
 *
 * DESCRIPTION:
 *     Creates a new traffic channel processor instance.
 *     Initializes the local configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *TrafficChannelProcessor - Initialized traffic channel processor
 *****************************************************************************/
func NewTrafficChannelProcessor(log *logger.Logger) *TrafficChannelProcessor {
	processor := &TrafficChannelProcessor{
		logger:             log.WithModule("traffic-channel-processor"),
		localConfigs:       make(map[string]*TrafficChannelConfig),
		fullSyncInProgress: false,
		workingConfigs:     make(map[string]*TrafficChannelConfig),
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *
 * RETURNS:
 *     pb.TaskType - TASK_TRAFFIC_CHANNEL
 *****************************************************************************/
func (p *TrafficChannelProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_TRAFFIC_CHANNEL
}

/*****************************************************************************
 * NAME: fetchTrafficChannelConfigs
 *
 * DESCRIPTION:
 *     Fetches traffic channel configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*TrafficChannelConfig - Traffic channels by name
 *     error                            - Error if fetch fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) fetchTrafficChannelConfigs() (map[string]*TrafficChannelConfig, error) {
	configs := make(map[string]*TrafficChannelConfig)

	// Get list of traffic channels
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", "policy", "listbwo")
	if err != nil {
		p.logger.Error("failed to list traffic channels", zap.Error(err))
		return nil, fmt.Errorf("failed to list traffic channels: %w", err)
	}

	// Parse channel names from list output
	channelNames := p.parseChannelNamesFromList(output)

	// Get detailed configuration for each channel
	for _, name := range channelNames {
		config, err := GetTrafficChannelConfig(p.logger, name)
		if err != nil {
			p.logger.Error("failed to get channel configuration",
				zap.String("name", name),
				zap.Error(err))
			continue
		}
		configs[name] = config
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local traffic channel configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local traffic channel configurations")

	configs, err := p.fetchTrafficChannelConfigs()
	if err != nil {
		return err
	}

	// Update local configs (used for full sync redundant deletion)
	p.localConfigs = configs

	p.logger.Debug("refreshed local traffic channel configurations",
		zap.Int("count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working traffic channel configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working traffic channel configurations")

	configs, err := p.fetchTrafficChannelConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working configs (used for all operations)
	p.workingConfigs = configs

	p.logger.Debug("refreshed working traffic channel configurations",
		zap.Int("count", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like create, update, delete, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: parseChannelNamesFromList
 *
 * DESCRIPTION:
 *     Parses traffic channel names from floweye policy listbwo output.
 *     Expected format: id name rate quota qsize outbps dropbps outbytes dropbytes leftbytes
 *
 * PARAMETERS:
 *     output - Output from floweye policy listbwo command
 *
 * RETURNS:
 *     []string - List of channel names
 *****************************************************************************/
func (p *TrafficChannelProcessor) parseChannelNamesFromList(output string) []string {
	var names []string

	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Parse line format: id name rate quota qsize outbps dropbps outbytes dropbytes leftbytes
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			// Second field is the channel name
			names = append(names, fields[1])
		}
	}

	return names
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes local configuration cache and sets sync flag.
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) StartFullSync() error {
	p.logger.Info("starting full sync for traffic channel processor")

	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	p.logger.Info("full sync started for traffic channel processor",
		zap.Int("local_configs_count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up remaining local configurations and resets sync flag.
 *****************************************************************************/
func (p *TrafficChannelProcessor) EndFullSync() {
	p.logger.Info("ending full sync for traffic channel processor")

	// Create a copy of remaining channel configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations while keeping fullSyncInProgress = true
	remainingChannels := make(map[string]*TrafficChannelConfig)
	for channelName, config := range p.localConfigs {
		remainingChannels[channelName] = config
	}

	// Process remaining traffic channels in local configuration
	// These are channels that were not included in the full sync and should be deleted
	// Keep fullSyncInProgress = true during cleanup so handleDeleteConfig can properly
	// remove items from localConfigs map
	if len(remainingChannels) > 0 {
		p.logger.Info("cleaning up remaining traffic channels",
			zap.Int("count", len(remainingChannels)))

		for channelName := range remainingChannels {
			// Create a delete task for this channel
			deleteTask := &pb.TrafficChannelTask{
				Name: channelName,
			}

			// Delete the channel
			p.logger.Info("deleting traffic channel",
				zap.String("name", channelName))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("failed to delete traffic channel",
					zap.String("name", channelName),
					zap.Error(err))
			}
		}
	}

	// Verify that all remaining channels have been cleaned up
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some traffic channels were not cleaned up during full sync",
			zap.Int("remaining_count", len(p.localConfigs)))

		// Log the remaining channels for debugging
		for channelName := range p.localConfigs {
			p.logger.Warn("remaining traffic channel after cleanup",
				zap.String("name", channelName))
		}
	} else {
		p.logger.Info("all remaining traffic channels cleaned up successfully")
	}

	// Now set fullSyncInProgress to false after cleanup is complete
	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*TrafficChannelConfig)
	p.workingConfigs = make(map[string]*TrafficChannelConfig)
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a traffic channel task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get traffic channel task data
	trafficChannelTask := task.GetTrafficChannelTask()
	if trafficChannelTask == nil {
		return "Traffic channel task data is empty", fmt.Errorf("traffic channel task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "traffic_channel", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Int32("rate", trafficChannelTask.GetRate()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, trafficChannelTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, trafficChannelTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles both new and edit traffic channel configuration tasks.
 *     Configures or updates a traffic channel using floweye commands.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - Traffic channel task containing configuration details
 *     taskAction - The original task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) handleConfigChange(ctx context.Context, task *pb.TrafficChannelTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified TrafficChannelConfig structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	expectedConfig, err := ConvertTrafficChannelTaskToConfig(task)
	if err != nil {
		p.logger.Error("failed to convert traffic channel task to config",
			zap.String("name", task.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert traffic channel configuration: %v", err), err
	}

	// Validate required fields using converted structure
	if expectedConfig.Name == "" {
		return "Traffic channel name is required", fmt.Errorf("traffic channel name is required")
	}

	if expectedConfig.Rate <= 0 || expectedConfig.Rate > 16000000 {
		return "Traffic channel rate must be between 1 and 16000000 kbits/s",
			fmt.Errorf("invalid rate: %d", expectedConfig.Rate)
	}

	// Validate priorities using converted structure
	if err := p.validatePriorities(expectedConfig); err != nil {
		return fmt.Sprintf("Priority validation failed: %v", err), err
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := expectedConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Log detailed information about the traffic channel configuration
	p.logger.Info("Processing traffic channel configuration",
		zap.String("name", expectedConfig.Name),
		zap.Int("rate", expectedConfig.Rate),
		zap.Int("quota", expectedConfig.Quota),
		zap.Int("priorities_count", len(expectedConfig.Priorities)),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if traffic channel exists in working configuration
	_, exists := p.workingConfigs[expectedConfig.Name]

	/*
		// If configurations match, no need to modify
		if exists && CompareTrafficChannelConfig(p.logger, expectedConfig, localConfig) {
			p.logger.Info("Traffic channel configuration already matches, no changes needed",
				zap.String("name", expectedConfig.Name),
				zap.Int("rate", expectedConfig.Rate))

			return "Traffic channel configuration already matches, no changes needed", nil
		}
	*/

	// Configure basic traffic channel using converted structure
	if err := p.configureBasicChannel(expectedConfig, exists); err != nil {
		return fmt.Sprintf("Failed to configure basic channel: %v", err), err
	}

	// Refresh working configs after basic channel creation/update to get latest state
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after basic channel operation", zap.Error(err))
		// Don't return error as main operation succeeded
	}

	// Configure priorities using converted structure
	if err := p.configurePriorities(expectedConfig); err != nil {
		return fmt.Sprintf("Failed to configure priorities: %v", err), err
	}

	// Verify configuration was applied correctly using converted structure
	success, verifyErr := VerifyTrafficChannelConfig(p.logger, expectedConfig)
	if verifyErr != nil {
		p.logger.Error("failed to verify traffic channel configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify traffic channel configuration: %v", verifyErr),
			fmt.Errorf("failed to verify traffic channel configuration: %w", verifyErr)
	}

	if !success {
		p.logger.Error("Traffic channel configuration verification failed")
		return "Traffic channel configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("Traffic channel configuration completed successfully",
		zap.String("name", expectedConfig.Name))

	return "Traffic channel configuration completed successfully", nil
}

/*****************************************************************************
 * NAME: validatePriorities
 *
 * DESCRIPTION:
 *     Validates traffic channel priority configurations.
 *     Checks priority ranges, bandwidth constraints, and business rules.
 *
 * PARAMETERS:
 *     config - Traffic channel configuration to validate (converted from protobuf)
 *
 * RETURNS:
 *     error - Error if validation fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) validatePriorities(config *TrafficChannelConfig) error {
	if len(config.Priorities) > 16 {
		return fmt.Errorf("maximum 16 priorities allowed, got %d", len(config.Priorities))
	}

	totalGbw := 0
	priorityMap := make(map[int]bool)

	for _, priority := range config.Priorities {
		// Check priority range
		if priority.Pri < 1 || priority.Pri > 16 {
			return fmt.Errorf("priority must be between 1 and 16, got %d", priority.Pri)
		}

		// Check for duplicate priorities
		if priorityMap[priority.Pri] {
			return fmt.Errorf("duplicate priority level: %d", priority.Pri)
		}
		priorityMap[priority.Pri] = true

		// Check bandwidth values
		if priority.MaxRate < 0 {
			return fmt.Errorf("maxrate cannot be negative: %d", priority.MaxRate)
		}

		if priority.Gbw < 0 {
			return fmt.Errorf("guaranteed bandwidth cannot be negative: %d", priority.Gbw)
		}

		if priority.Gbw > priority.MaxRate && priority.MaxRate > 0 {
			return fmt.Errorf("guaranteed bandwidth (%d) cannot exceed maxrate (%d) for priority %d",
				priority.Gbw, priority.MaxRate, priority.Pri)
		}

		totalGbw += priority.Gbw
	}

	// Check that sum of guaranteed bandwidths doesn't exceed channel bandwidth
	if totalGbw > config.Rate {
		return fmt.Errorf("sum of guaranteed bandwidths (%d kbps) exceeds channel bandwidth (%d kbits/s)",
			totalGbw, config.Rate)
	}

	return nil
}

/*****************************************************************************
 * NAME: configureBasicChannel
 *
 * DESCRIPTION:
 *     Configures basic traffic channel properties (name, rate, quota).
 *     Uses floweye policy addbwo or setbwo commands.
 *
 * PARAMETERS:
 *     config - Traffic channel configuration (converted from protobuf)
 *     exists - Whether the channel already exists
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) configureBasicChannel(config *TrafficChannelConfig, exists bool) error {
	var cmdArgs []string
	if exists {
		// Update existing channel
		cmdArgs = []string{
			"policy", "setbwo",
			"name=" + config.Name,
			"rate=" + strconv.Itoa(config.Rate),
			"quota=" + strconv.Itoa(config.Quota),
		}
	} else {
		// Create new channel
		cmdArgs = []string{
			"policy", "addbwo",
			"name=" + config.Name,
			"rate=" + strconv.Itoa(config.Rate),
			"quota=" + strconv.Itoa(config.Quota),
		}
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command for basic channel configuration",
		zap.String("name", config.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for basic channel configuration",
			zap.String("name", config.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to configure basic channel: %w", err)
	}

	p.logger.Debug("Basic channel configuration command executed successfully",
		zap.String("name", config.Name),
		zap.String("output", output))

	return nil
}

/*****************************************************************************
 * NAME: configurePriorities
 *
 * DESCRIPTION:
 *     Configures traffic channel priority settings.
 *     First clears all existing priorities, then sets new ones.
 *     Uses floweye policy sethtb commands for each priority.
 *
 * PARAMETERS:
 *     config - Traffic channel configuration (converted from protobuf)
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) configurePriorities(config *TrafficChannelConfig) error {
	// First, clear all existing priorities (1-16) to ensure clean state
	p.logger.Debug("Clearing all existing priorities",
		zap.String("name", config.Name))

	for pri := 1; pri <= 16; pri++ {
		clearArgs := []string{
			"policy", "sethtb",
			"name=" + config.Name,
			"pri=" + strconv.Itoa(pri),
			"maxrate=0",
			"gbw=0",
			"desc=",
		}

		// Execute clear command (ignore errors as some priorities might not exist)
		_, err := utils.ExecuteCommand(p.logger, 10, "floweye", clearArgs...)
		if err != nil {
			p.logger.Debug("Failed to clear priority (expected for non-existent priorities)",
				zap.String("name", config.Name),
				zap.Int("priority", pri),
				zap.Error(err))
		}
	}

	// If no priorities to configure, we're done
	if len(config.Priorities) == 0 {
		p.logger.Debug("No priorities to configure after clearing",
			zap.String("name", config.Name))
		return nil
	}

	// Now set the new priorities
	for _, priority := range config.Priorities {
		cmdArgs := []string{
			"policy", "sethtb",
			"name=" + config.Name,
			"pri=" + strconv.Itoa(priority.Pri),
			"maxrate=" + strconv.Itoa(priority.MaxRate),
			"gbw=" + strconv.Itoa(priority.Gbw),
			"desc=" + priority.Desc,
		}

		// Execute floweye command
		p.logger.Info("Executing floweye command for priority configuration",
			zap.String("name", config.Name),
			zap.Int("priority", priority.Pri),
			zap.Strings("args", cmdArgs))

		output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
		if err != nil {
			p.logger.Error("Failed to execute floweye command for priority configuration",
				zap.String("name", config.Name),
				zap.Int("priority", priority.Pri),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Errorf("failed to configure priority %d: %w", priority.Pri, err)
		}

		p.logger.Debug("Priority configuration command executed successfully",
			zap.String("name", config.Name),
			zap.Int("priority", priority.Pri),
			zap.String("output", output))
	}

	return nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles traffic channel configuration deletion tasks.
 *     Deletes a traffic channel using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Traffic channel task containing configuration to delete
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *TrafficChannelProcessor) handleDeleteConfig(ctx context.Context, task *pb.TrafficChannelTask) (string, error) {
	p.logger.Info("handling delete traffic channel config",
		zap.String("name", task.GetName()),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields
	if task.GetName() == "" {
		return "Traffic channel name is required", fmt.Errorf("traffic channel name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := task.GetName() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if traffic channel exists in working configuration
	_, exists := p.workingConfigs[task.GetName()]

	// If traffic channel doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("Traffic channel not found in local configuration, nothing to delete",
			zap.String("name", task.GetName()))
		return "Traffic channel not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"policy", "rmvbwo", "name=" + task.GetName(),
	}

	// Execute floweye command
	p.logger.Info("executing floweye command", zap.Strings("args", cmdArgs))
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Traffic channel already does not exist, treating as successful delete",
				zap.String("name", task.GetName()))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete traffic channel: %v", err), err
		}
	}

	// Skip post-delete verification for improved performance and reliability

	p.logger.Info("Traffic channel deleted successfully", zap.String("output", output))
	return "Traffic channel configuration deleted successfully", nil
}
