/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      flow_control_policy.go
 *
 * DESCRIPTION :    Policy handlers for flow control processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/06/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: handlePolicyConfigChange
 *
 * DESCRIPTION:
 *     Handles policy configuration changes (create/update).
 *     Implements unified logic for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     task       - Policy task to process
 *     taskAction - Action type (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyConfigChange(ctx context.Context, task *pb.FlowControlPolicyTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertPolicyTaskToConfig(task, p.logger)
	if err != nil {
		p.logger.Error("failed to convert policy task to config",
			zap.Uint32("cookie", task.GetCookie()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert policy configuration: %v", err), err
	}

	p.logger.Info("Processing policy configuration",
		zap.Uint32("cookie", configData.Cookie),
		zap.String("desc", configData.Desc),
		zap.String("group_name", configData.GroupName),
		zap.Bool("disable", configData.Disable),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Register cleanup defer after validation (validation is done in ConvertPolicyTaskToConfig)
	if p.fullSyncInProgress {
		cookie := configData.Cookie // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localPolicies, cookie)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Step 1: Confirm policy group exists (according to documentation)
	groupID, exists := p.workingGroupNameToID[configData.GroupName]
	if !exists {
		return fmt.Sprintf("Policy group '%s' does not exist", configData.GroupName),
			fmt.Errorf("policy group '%s' does not exist", configData.GroupName)
	}

	// Check if policy exists in working configuration
	_, exists = p.workingPolicies[configData.Cookie]

	/*
		// If configurations match, no need to modify
		if exists && ComparePolicyConfig(p.logger, configData, localConfig) {
			p.logger.Info("Policy configuration already matches, no changes needed",
				zap.Uint32("cookie", configData.Cookie))

			return "Policy configuration already matches, no changes needed", nil
		}
	*/

	// Step 2: ID allocation (according to documentation)
	policyID, err := p.allocatePolicyID(groupID, configData.Cookie)
	if err != nil {
		return fmt.Sprintf("Failed to allocate policy ID: %v", err), err
	}

	// Build floweye command arguments
	var cmdArgs []string
	var operation string

	if exists {
		// Update existing policy
		operation = "update"
		cmdArgs = []string{"newpolicy", "set", "group=" + strconv.Itoa(groupID), "id=" + strconv.Itoa(policyID)}
	} else {
		// Create new policy - use allocated ID
		operation = "create"
		cmdArgs = []string{"newpolicy", "add", "group=" + strconv.Itoa(groupID), "id=" + strconv.Itoa(policyID)}
	}

	// Build command arguments using converted data
	if err := p.buildPolicyCommand(&cmdArgs, configData); err != nil {
		return fmt.Sprintf("Failed to build policy command: %v", err), err
	}

	// Note: Ordering is handled separately after creation/update
	// The floweye commands do not support direct previous=xxx parameter

	// Execute floweye command
	p.logger.Info("executing floweye command for policy",
		zap.String("operation", operation),
		zap.Uint32("cookie", configData.Cookie),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye command",
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to %s policy: %v", operation, err), err
	}

	// Refresh configurations after creation/update to include the new/updated policy
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after policy operation",
			zap.Uint32("cookie", configData.Cookie),
			zap.Error(err))
		// Don't return error as the main operation succeeded
	}

	// Handle ordering logic after creation/update
	if configData.Previous != ^uint32(0) {
		if err := p.handlePolicyOrderingWithConfig(ctx, configData, groupID); err != nil {
			p.logger.Error("failed to handle policy ordering",
				zap.Uint32("cookie", configData.Cookie),
				zap.Error(err))
			return fmt.Sprintf("Policy %s succeeded but ordering failed: %v", operation, err), err
		}
	}

	// Handle enable/disable operation separately after policy creation/modification
	if err := p.handlePolicyEnableDisable(configData.Cookie, configData.Disable, groupID, policyID); err != nil {
		p.logger.Error("failed to handle policy enable/disable",
			zap.Uint32("cookie", configData.Cookie),
			zap.Bool("disable", configData.Disable),
			zap.Error(err))
		return fmt.Sprintf("Policy %s succeeded but enable/disable failed: %v", operation, err), err
	}

	// Verify the configuration was applied successfully
	if err := p.verifyPolicyConfigWithData(ctx, configData, groupID); err != nil {
		p.logger.Error("policy verification failed",
			zap.Uint32("cookie", configData.Cookie),
			zap.Error(err))
		return fmt.Sprintf("Policy %s succeeded but verification failed: %v", operation, err), err
	}

	successMsg := fmt.Sprintf("Policy %s successful", operation)
	p.logger.Info(successMsg, zap.Uint32("cookie", configData.Cookie))
	return successMsg, nil
}

// Note: buildAddressString function removed - now using BuildAddressSelectorsString from common_protobuf_utils.go

// Note: resolveIPGroupNameToID and resolveDomainGroupNameToID functions removed -
// now using DefaultGroupResolver from common_protobuf_utils.go

/*****************************************************************************
 * NAME: handleDeletePolicy
 *
 * DESCRIPTION:
 *     Handles policy deletion.
 *     Removes the policy from the local PA system and maintains continuous ID ordering.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Policy task containing the cookie to delete
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) handleDeletePolicy(ctx context.Context, task *pb.FlowControlPolicyTask) (string, error) {
	// Validate required fields
	if task.GetCookie() == 0 {
		return "Policy cookie is required for deletion", fmt.Errorf("policy cookie is required for deletion")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		cookie := task.GetCookie() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localPolicies, cookie)
		}()
	}

	p.logger.Info("Processing policy deletion",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if policy exists in working configuration
	localConfig, exists := p.workingPolicies[task.GetCookie()]
	if !exists {
		p.logger.Info("Policy does not exist locally, treating as successful delete",
			zap.Uint32("cookie", task.GetCookie()))

		return "Policy does not exist, deletion successful", nil
	}

	groupID := localConfig.GroupID
	var currentPolicyID int
	if p.fullSyncInProgress {
		// Get current policy ID from floweye (don't rely on cached localConfig ID which may be outdated)
		var err error
		currentPolicyID, err = p.getCurrentPolicyIDByCookie(groupID, task.GetCookie())
		if err != nil {
			p.logger.Info("Policy does not exist in floweye, treating as successful delete",
				zap.Uint32("cookie", task.GetCookie()),
				zap.Error(err))
			return "Policy does not exist, deletion successful", nil
		}
	} else {
		currentPolicyID = localConfig.ID
	}

	// Store deletion information for ordering logic
	deletedPolicyID := currentPolicyID

	// Build delete command arguments
	cmdArgs := []string{"newpolicy", "remove",
		"group=" + strconv.Itoa(groupID),
		"id=" + strconv.Itoa(deletedPolicyID)}

	// Execute floweye command
	p.logger.Info("executing floweye command for policy deletion",
		zap.Uint32("cookie", task.GetCookie()),
		zap.Int("id", deletedPolicyID),
		zap.Int("group_id", groupID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("Policy already does not exist, treating as successful delete",
				zap.Uint32("cookie", task.GetCookie()))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete policy: %v", err), err
		}
	}

	// Handle ordering logic: decrement IDs of all policies with ID > deleted ID
	if err := p.handlePolicyDeletionOrdering(ctx, groupID, deletedPolicyID); err != nil {
		p.logger.Error("failed to handle policy deletion ordering",
			zap.Uint32("cookie", task.GetCookie()),
			zap.Int("deleted_id", deletedPolicyID),
			zap.Error(err))
		return fmt.Sprintf("Policy deletion succeeded but ordering adjustment failed: %v", err), err
	}

	successMsg := "Policy deletion successful"
	p.logger.Info(successMsg, zap.Uint32("cookie", task.GetCookie()))
	return successMsg, nil
}

/*****************************************************************************
 * NAME: movePolicyToPositionBidirectional
 *
 * DESCRIPTION:
 *     Moves a policy to the specified position with bidirectional support.
 *     Implements both forward insertion and backward movement as described in documentation.
 *
 * PARAMETERS:
 *     groupID        - Policy group ID
 *     currentPolicyID - ID of the policy to move
 *     targetPosition - Target position (1-based)
 *     currentPolicies - Current list of policies
 *
 * RETURNS:
 *     error - Error if move operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) movePolicyToPositionBidirectional(groupID, currentPolicyID, targetPosition int, currentPolicies []PolicyOrderInfo) error {
	p.logger.Info("moving policy with bidirectional support",
		zap.Int("group_id", groupID),
		zap.Int("current_id", currentPolicyID),
		zap.Int("target_position", targetPosition),
		zap.Int("total_policies", len(currentPolicies)))

	// Implement the ID adjustment logic as described in documentation:
	// 1. Set current policy ID to temporary ID (65535)
	// 2. Adjust other policy IDs based on direction
	// 3. Set current policy to target position

	const tempID = 65535

	// Step 1: Move current policy to temporary ID
	if err := p.setFlowControlPolicyID(groupID, currentPolicyID, tempID); err != nil {
		return fmt.Errorf("failed to set policy to temporary ID: %w", err)
	}

	// Step 2: Adjust other policy IDs based on direction
	if currentPolicyID > targetPosition {
		// Forward insertion: current ID > target ID
		// Increment IDs in range [target, current-1] by +1 (from large to small)
		if err := p.adjustPolicyIDsForwardInsertion(groupID, targetPosition, currentPolicyID-1, currentPolicies); err != nil {
			// Try to restore original ID if adjustment fails
			p.setFlowControlPolicyID(groupID, tempID, currentPolicyID)
			return fmt.Errorf("failed to adjust policy IDs for forward insertion: %w", err)
		}
	} else if currentPolicyID < targetPosition {
		// Backward movement: current ID < target ID
		// Only shift policies at target position and beyond by +1 (to make room for insertion)
		if err := p.adjustPolicyIDsBackwardMovement(groupID, targetPosition, targetPosition, currentPolicies); err != nil {
			// Try to restore original ID if adjustment fails
			p.setFlowControlPolicyID(groupID, tempID, currentPolicyID)
			return fmt.Errorf("failed to adjust policy IDs for backward movement: %w", err)
		}
	}

	// Step 3: Set current policy to target position
	if err := p.setFlowControlPolicyID(groupID, tempID, targetPosition); err != nil {
		return fmt.Errorf("failed to set policy to target position: %w", err)
	}

	p.logger.Info("successfully moved policy with bidirectional support",
		zap.Int("group_id", groupID),
		zap.Int("old_id", currentPolicyID),
		zap.Int("new_id", targetPosition))

	return nil
}

/*****************************************************************************
 * NAME: movePolicyToPosition
 *
 * DESCRIPTION:
 *     Legacy function for moving policy to position (single direction only).
 *     Kept for backward compatibility but redirects to bidirectional version.
 *
 * PARAMETERS:
 *     groupID        - Policy group ID
 *     policyID       - ID of the policy to move
 *     targetPosition - Target position (1-based, -1 for last)
 *     currentPolicies - Current list of policies
 *
 * RETURNS:
 *     error - Error if move operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) movePolicyToPosition(groupID, policyID, targetPosition int, currentPolicies []PolicyOrderInfo) error {
	// Handle special case: move to last position
	if targetPosition == -1 {
		maxID := 0
		for _, policy := range currentPolicies {
			if policy.ID > maxID {
				maxID = policy.ID
			}
		}
		targetPosition = maxID + 1
	}

	// Redirect to bidirectional version
	return p.movePolicyToPositionBidirectional(groupID, policyID, targetPosition, currentPolicies)
}

/*****************************************************************************
 * NAME: setFlowControlPolicyID
 *
 * DESCRIPTION:
 *     Sets a policy's ID using floweye newpolicy set command with complete configuration.
 *     According to floweye requirements, when modifying policy ID, all policy configuration
 *     parameters must be specified, not just the basic ID parameters.
 *
 * PARAMETERS:
 *     groupID - Policy group ID
 *     oldID   - Current policy ID
 *     newID   - New policy ID
 *
 * RETURNS:
 *     error - Error if operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) setFlowControlPolicyID(groupID, oldID, newID int) error {
	p.logger.Debug("setting policy ID with complete configuration",
		zap.Int("group_id", groupID),
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID))

	// Step 1: Get the complete configuration of the policy
	policyConfig, err := p.getPolicyDetailedConfig(groupID, oldID)
	if err != nil {
		return fmt.Errorf("failed to get policy configuration for ID change: %w", err)
	}

	// Step 2: Build floweye command with complete configuration
	cmdArgs := []string{"newpolicy", "set",
		"group=" + strconv.Itoa(groupID),
		"id=" + strconv.Itoa(oldID),
		"newid=" + strconv.Itoa(newID)}

	// Step 3: Add all policy configuration parameters using existing buildPolicyCommand function
	if err := p.buildPolicyCommand(&cmdArgs, policyConfig); err != nil {
		return fmt.Errorf("failed to build complete policy command for ID change: %w", err)
	}

	p.logger.Debug("setting policy ID with complete configuration",
		zap.Int("group_id", groupID),
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID),
		zap.Uint32("cookie", policyConfig.Cookie),
		zap.String("desc", policyConfig.Desc),
		zap.Strings("args", cmdArgs))

	// Step 4: Execute floweye command
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		return fmt.Errorf("failed to set policy ID from %d to %d with complete configuration: %w, output: %s", oldID, newID, err, output)
	}

	p.logger.Debug("policy ID set successfully with complete configuration",
		zap.Int("group_id", groupID),
		zap.Int("old_id", oldID),
		zap.Int("new_id", newID),
		zap.Uint32("cookie", policyConfig.Cookie))

	return nil
}

/*****************************************************************************
 * NAME: adjustPolicyIDsForwardInsertion
 *
 * DESCRIPTION:
 *     Adjusts policy IDs for forward insertion (current ID > target ID).
 *     Increments IDs in range [targetID, currentID-1] by +1 (from large to small).
 *
 * PARAMETERS:
 *     groupID         - Policy group ID
 *     targetID        - Target position ID
 *     currentIDMinus1 - Current ID minus 1 (end of range)
 *     currentPolicies - Current list of policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *FlowControlProcessor) adjustPolicyIDsForwardInsertion(groupID, targetID, currentIDMinus1 int, currentPolicies []PolicyOrderInfo) error {
	p.logger.Debug("adjusting policy IDs for forward insertion",
		zap.Int("group_id", groupID),
		zap.Int("target_id", targetID),
		zap.Int("current_id_minus_1", currentIDMinus1))

	// Find policies in range [targetID, currentID-1] that need to be shifted
	var policiesToShift []PolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID && policy.ID <= currentIDMinus1 {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID < policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by +1
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setFlowControlPolicyID(groupID, policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID for forward insertion",
			zap.Int("group_id", groupID),
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: adjustPolicyIDsBackwardMovement
 *
 * DESCRIPTION:
 *     Adjusts policy IDs for backward movement (current ID < target ID).
 *     Decrements IDs in range [currentID+1, targetID] by -1 (from small to large).
 *
 * PARAMETERS:
 *     groupID        - Policy group ID
 *     currentIDPlus1 - Current ID plus 1 (start of range)
 *     targetID       - Target position ID
 *     currentPolicies - Current list of policies
 *
 * RETURNS:
 *     error - Error if adjustment fails
 *****************************************************************************/
func (p *FlowControlProcessor) adjustPolicyIDsBackwardMovement(groupID, targetID, targetID2 int, currentPolicies []PolicyOrderInfo) error {
	p.logger.Debug("adjusting policy IDs for backward movement",
		zap.Int("group_id", groupID),
		zap.Int("target_id", targetID))

	// Find policies at target position and beyond that need to be shifted to make room
	var policiesToShift []PolicyOrderInfo
	for _, policy := range currentPolicies {
		if policy.ID >= targetID {
			policiesToShift = append(policiesToShift, policy)
		}
	}

	// Sort by ID in descending order (adjust from large to small to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID < policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by +1 to make room for insertion
	for _, policy := range policiesToShift {
		newID := policy.ID + 1
		if err := p.setFlowControlPolicyID(groupID, policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID for backward movement",
			zap.Int("group_id", groupID),
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	return nil
}

/*****************************************************************************
 * NAME: handlePolicyDeletionOrdering
 *
 * DESCRIPTION:
 *     Handles policy ordering after deletion.
 *     Decrements IDs of all policies with ID > deleted ID to maintain continuous ordering.
 *
 * PARAMETERS:
 *     ctx           - Context for the operation
 *     groupID       - Policy group ID
 *     deletedPolicyID - ID of the deleted policy
 *
 * RETURNS:
 *     error - Error if ordering adjustment fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyDeletionOrdering(ctx context.Context, groupID, deletedPolicyID int) error {
	p.logger.Debug("handling policy deletion ordering",
		zap.Int("group_id", groupID),
		zap.Int("deleted_policy_id", deletedPolicyID))

	// Get current policies in the group
	policies, err := p.getPoliciesForGroup(groupID)
	if err != nil {
		return fmt.Errorf("failed to get policies for ordering adjustment: %w", err)
	}

	// Find policies with ID > deleted ID that need to be decremented
	var policiesToShift []PolicyOrderInfo
	for _, policy := range policies {
		// Get detailed config to get cookie information
		detailedPolicy, err := p.getPolicyDetailedConfig(groupID, policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed policy config during ordering",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		if policy.ID > deletedPolicyID {
			policiesToShift = append(policiesToShift, PolicyOrderInfo{
				ID:     policy.ID,
				Cookie: detailedPolicy.Cookie,
			})
		}
	}

	// Sort by ID in ascending order (adjust from small to large to prevent conflicts)
	for i := 0; i < len(policiesToShift)-1; i++ {
		for j := i + 1; j < len(policiesToShift); j++ {
			if policiesToShift[i].ID > policiesToShift[j].ID {
				policiesToShift[i], policiesToShift[j] = policiesToShift[j], policiesToShift[i]
			}
		}
	}

	// Shift each policy ID by -1 to maintain continuous ordering
	for _, policy := range policiesToShift {
		newID := policy.ID - 1
		if err := p.setFlowControlPolicyID(groupID, policy.ID, newID); err != nil {
			return fmt.Errorf("failed to shift policy ID from %d to %d after deletion: %w", policy.ID, newID, err)
		}

		p.logger.Debug("shifted policy ID after deletion",
			zap.Int("group_id", groupID),
			zap.Uint32("cookie", policy.Cookie),
			zap.Int("old_id", policy.ID),
			zap.Int("new_id", newID))
	}

	p.logger.Debug("policy deletion ordering completed",
		zap.Int("group_id", groupID),
		zap.Int("deleted_policy_id", deletedPolicyID),
		zap.Int("policies_shifted", len(policiesToShift)))

	return nil
}

/*****************************************************************************
 * NAME: allocatePolicyID
 *
 * DESCRIPTION:
 *     Allocates ID for policy according to floweye documentation.
 *     If policy exists (by cookie in group), returns existing ID.
 *     If policy doesn't exist, returns max(ID)+1 in the group.
 *
 * PARAMETERS:
 *     groupID - Policy group ID
 *     cookie  - Policy cookie to check
 *
 * RETURNS:
 *     int   - Allocated policy ID
 *     error - Error if allocation fails
 *****************************************************************************/
func (p *FlowControlProcessor) allocatePolicyID(groupID int, cookie uint32) (int, error) {
	// Check if policy already exists by cookie in the group
	if existingConfig, exists := p.workingPolicies[cookie]; exists {
		p.logger.Debug("policy exists, using existing ID",
			zap.Int("group_id", groupID),
			zap.Uint32("cookie", cookie),
			zap.Int("existing_id", existingConfig.ID))
		return existingConfig.ID, nil
	}

	// Policy doesn't exist, allocate new ID using max(ID)+1 in the group
	maxID := 0
	for _, config := range p.workingPolicies {
		if config.GroupID == groupID && config.ID > maxID {
			maxID = config.ID
		}
	}

	newID := maxID + 1
	p.logger.Debug("allocating new policy ID",
		zap.Int("group_id", groupID),
		zap.Uint32("cookie", cookie),
		zap.Int("new_id", newID),
		zap.Int("max_existing_id", maxID))

	return newID, nil
}

// PolicyOrderInfo represents policy ordering information
type PolicyOrderInfo struct {
	ID     int
	Cookie uint32
}

/*****************************************************************************
 * NAME: handlePolicyEnableDisable
 *
 * DESCRIPTION:
 *     Handles flow control policy enable/disable operation separately from policy creation/modification.
 *     Uses dedicated floweye newpolicy config command with enable/disable parameter.
 *
 * PARAMETERS:
 *     cookie   - Policy cookie
 *     disable  - Whether to disable the policy (true=disable, false=enable)
 *     groupID  - Policy group ID
 *     policyID - Policy ID
 *
 * RETURNS:
 *     error - Error if enable/disable operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyEnableDisable(cookie uint32, disable bool, groupID, policyID int) error {
	p.logger.Debug("handling flow control policy enable/disable",
		zap.Uint32("cookie", cookie),
		zap.Bool("disable", disable),
		zap.Int("group_id", groupID),
		zap.Int("policy_id", policyID))

	// Build floweye command for enable/disable operation
	// According to floweye documentation, use newpolicy config command
	var cmdArgs []string
	if disable {
		cmdArgs = []string{"newpolicy", "config",
			"group=" + strconv.Itoa(groupID),
			"id=" + strconv.Itoa(policyID),
			"disable=1"}
	} else {
		cmdArgs = []string{"newpolicy", "config",
			"group=" + strconv.Itoa(groupID),
			"id=" + strconv.Itoa(policyID),
			"enable=1"}
	}

	// Execute floweye command
	p.logger.Info("executing floweye enable/disable command for flow control policy",
		zap.Uint32("cookie", cookie),
		zap.Int("group_id", groupID),
		zap.Int("id", policyID),
		zap.Bool("disable", disable),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("failed to execute floweye enable/disable command for flow control policy",
			zap.Uint32("cookie", cookie),
			zap.Int("group_id", groupID),
			zap.Int("id", policyID),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Errorf("failed to %s flow control policy: %w",
			map[bool]string{true: "disable", false: "enable"}[disable], err)
	}

	p.logger.Debug("floweye enable/disable command executed successfully",
		zap.Uint32("cookie", cookie),
		zap.Int("group_id", groupID),
		zap.Int("id", policyID),
		zap.String("operation", map[bool]string{true: "disabled", false: "enabled"}[disable]),
		zap.String("output", output))

	return nil
}

/*****************************************************************************
 * NAME: buildPolicyCommand
 *
 * DESCRIPTION:
 *     Builds floweye command arguments for policy using converted data.
 *     Uses internal PolicyConfig structure instead of protobuf access.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - Converted policy configuration data
 *
 * RETURNS:
 *     error - Error if building command fails
 *****************************************************************************/
func (p *FlowControlProcessor) buildPolicyCommand(cmdArgs *[]string, configData *PolicyConfig) error {
	// Add basic policy configuration
	*cmdArgs = append(*cmdArgs, "cookie="+strconv.Itoa(int(configData.Cookie)))
	*cmdArgs = append(*cmdArgs, "desc="+configData.Desc)

	// Note: disable parameter is handled separately after policy creation/modification

	// Add matching criteria using converted data
	if err := p.addMatchingCriteriaFromConfig(cmdArgs, configData); err != nil {
		return fmt.Errorf("failed to build matching criteria: %w", err)
	}

	// Add action configuration using converted data
	if err := p.addActionConfigurationFromConfig(cmdArgs, configData); err != nil {
		return fmt.Errorf("failed to build action configuration: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: addMatchingCriteriaFromConfig
 *
 * DESCRIPTION:
 *     Adds matching criteria arguments to the floweye command using converted data.
 *     Uses internal PolicyConfig structure instead of protobuf access.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - Converted policy configuration data
 *
 * RETURNS:
 *     error - Error if building criteria fails
 *****************************************************************************/
func (p *FlowControlProcessor) addMatchingCriteriaFromConfig(cmdArgs *[]string, configData *PolicyConfig) error {
	// Add internal IP specifications
	// Convert floweye internal format (ip,32,***********) to command format (,***********,)
	if configData.InIP != "" {
		inIPCmd := ConvertIPFormatForCommand(configData.InIP)
		*cmdArgs = append(*cmdArgs, "inip="+inIPCmd)
	} else {
		*cmdArgs = append(*cmdArgs, "inip=")
	}

	// Add internal port specifications
	if configData.InPort != "" {
		*cmdArgs = append(*cmdArgs, "inport="+configData.InPort)
	} else {
		*cmdArgs = append(*cmdArgs, "inport=0")
	}

	// Add external IP specifications
	// Convert floweye internal format (ip,32,0.0.0.0) to command format (,0.0.0.0,)
	if configData.OutIP != "" {
		outIPCmd := ConvertIPFormatForCommand(configData.OutIP)
		*cmdArgs = append(*cmdArgs, "outip="+outIPCmd)
	} else {
		*cmdArgs = append(*cmdArgs, "outip=")
	}

	// Add external port specifications
	if configData.OutPort != "" {
		*cmdArgs = append(*cmdArgs, "outport="+configData.OutPort)
	} else {
		*cmdArgs = append(*cmdArgs, "outport=0")
	}

	// Add application protocol - combine AppID and Protocol
	appValue := "any"
	if configData.AppID != "" && configData.Protocol != "" {
		appValue = configData.AppID + "." + configData.Protocol
	} else if configData.AppID != "" {
		appValue = configData.AppID + ".any"
	} else if configData.Protocol != "" {
		appValue = "any." + configData.Protocol
	}
	*cmdArgs = append(*cmdArgs, "app="+appValue)

	// Add interface specifications
	*cmdArgs = append(*cmdArgs, "bridge="+configData.Interface.Bridge)
	*cmdArgs = append(*cmdArgs, "dir="+configData.Interface.Dir)
	*cmdArgs = append(*cmdArgs, "ifname="+configData.Interface.IfName)
	*cmdArgs = append(*cmdArgs, "inif="+configData.Interface.InIf)
	*cmdArgs = append(*cmdArgs, "vlan="+configData.Interface.VLAN)

	return nil
}

/*****************************************************************************
 * NAME: addActionConfigurationFromConfig
 *
 * DESCRIPTION:
 *     Adds action configuration arguments to the floweye command using converted data.
 *     Uses internal PolicyConfig structure instead of protobuf access.
 *
 * PARAMETERS:
 *     cmdArgs    - Pointer to command arguments slice
 *     configData - Converted policy configuration data
 *
 * RETURNS:
 *     error - Error if building action configuration fails
 *****************************************************************************/
func (p *FlowControlProcessor) addActionConfigurationFromConfig(cmdArgs *[]string, configData *PolicyConfig) error {
	// Add action type
	switch configData.Action {
	case "permit":
		*cmdArgs = append(*cmdArgs, "action=permit")

		// Add next (continue matching) configuration
		nextValue := "0"
		if configData.Next {
			nextValue = "1" // next=true means continue matching (next=1)
		}
		*cmdArgs = append(*cmdArgs, "next="+nextValue)

		// Add IP rate limiting
		*cmdArgs = append(*cmdArgs, "iprate="+strconv.Itoa(configData.IPRate))

		// Add TOS/DSCP modification
		*cmdArgs = append(*cmdArgs, "tos="+strconv.Itoa(configData.TOS))

		// Add traffic statistics object - convert name to ID
		// Treat "0" as empty value since floweye returns "0" for unset SOID
		if configData.SOID != "" && configData.SOID != "0" {
			soidID, err := p.getTrafficStatIDByName(configData.SOID)
			if err != nil {
				return fmt.Errorf("failed to get traffic statistics ID for '%s': %w", configData.SOID, err)
			}
			*cmdArgs = append(*cmdArgs, "soid="+strconv.Itoa(soidID))
		} else {
			*cmdArgs = append(*cmdArgs, "soid=0")
		}

		// Add default values for channel-specific fields
		*cmdArgs = append(*cmdArgs, "pri=0")

	case "channel":
		// Channel action - convert channel name to ID
		channelID, err := p.getTrafficChannelIDByName(configData.Channel)
		if err != nil {
			return fmt.Errorf("failed to get traffic channel ID for '%s': %w", configData.Channel, err)
		}
		*cmdArgs = append(*cmdArgs, "action="+strconv.Itoa(channelID))

		// Add next (continue matching) configuration
		nextValue := "0"
		if configData.Next {
			nextValue = "1" // next=true means continue matching (next=1)
		}
		*cmdArgs = append(*cmdArgs, "next="+nextValue)

		// Add channel priority
		*cmdArgs = append(*cmdArgs, "pri="+strconv.Itoa(configData.Pri))

		// Add IP rate limiting
		*cmdArgs = append(*cmdArgs, "iprate="+strconv.Itoa(configData.IPRate))

		// Add traffic statistics object - convert name to ID
		// Treat "0" as empty value since floweye returns "0" for unset SOID
		if configData.SOID != "" && configData.SOID != "0" {
			soidID, err := p.getTrafficStatIDByName(configData.SOID)
			if err != nil {
				return fmt.Errorf("failed to get traffic statistics ID for '%s': %w", configData.SOID, err)
			}
			*cmdArgs = append(*cmdArgs, "soid="+strconv.Itoa(soidID))
		} else {
			*cmdArgs = append(*cmdArgs, "soid=0")
		}

		// Add default TOS
		*cmdArgs = append(*cmdArgs, "tos=0")

	case "deny":
		*cmdArgs = append(*cmdArgs, "action=deny")
		// Add default values for deny action
		*cmdArgs = append(*cmdArgs, "next=1", "iprate=0", "tos=0", "soid=0", "pri=0")

	default:
		return fmt.Errorf("unsupported action type: %s", configData.Action)
	}

	// Add common default values
	*cmdArgs = append(*cmdArgs, "natip=0", "appnot=0", "hasms=0", "qqcnt=0", "ttl=0-255", "pktno=0")

	return nil
}

/*****************************************************************************
 * NAME: getCurrentPolicyIDByCookie
 *
 * DESCRIPTION:
 *     Gets the current policy ID by cookie from floweye.
 *     This ensures we get the most up-to-date ID information.
 *
 * PARAMETERS:
 *     groupID - Policy group ID
 *     cookie  - Policy cookie to search for
 *
 * RETURNS:
 *     int   - Current policy ID
 *     error - Error if policy not found or operation fails
 *****************************************************************************/
func (p *FlowControlProcessor) getCurrentPolicyIDByCookie(groupID int, cookie uint32) (int, error) {
	// Get all policies in the group
	policies, err := p.getPoliciesForGroup(groupID)
	if err != nil {
		return 0, fmt.Errorf("failed to get policies for group: %w", err)
	}

	// Find the policy with matching cookie
	for _, policy := range policies {
		detailedPolicy, err := p.getPolicyDetailedConfig(groupID, policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed policy config",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		if detailedPolicy.Cookie == cookie {
			p.logger.Debug("found current policy ID by cookie",
				zap.Uint32("cookie", cookie),
				zap.Int("group_id", groupID),
				zap.Int("current_id", policy.ID))
			return policy.ID, nil
		}
	}

	return 0, fmt.Errorf("policy with cookie %d not found in group %d", cookie, groupID)
}

/*****************************************************************************
 * NAME: handlePolicyOrderingWithConfig
 *
 * DESCRIPTION:
 *     Handles policy ordering using converted configuration data.
 *     Uses internal PolicyConfig structure instead of protobuf access.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Converted policy configuration data
 *     groupID    - Policy group ID
 *
 * RETURNS:
 *     error - Error if ordering fails
 *****************************************************************************/
func (p *FlowControlProcessor) handlePolicyOrderingWithConfig(ctx context.Context, configData *PolicyConfig, groupID int) error {
	// If no previous reference, no ordering needed
	// ^uint32(0) means append to end for new policies only
	if configData.Previous == ^uint32(0) {
		p.logger.Debug("handling append to end operation",
			zap.Uint32("cookie", configData.Cookie))

		// Get current policy list for the group to determine current position
		policies, err := p.getPoliciesForGroup(groupID)
		if err != nil {
			return fmt.Errorf("failed to get policies for last position move: %w", err)
		}

		// Get detailed configurations including cookies for all policies
		var currentPolicies []PolicyOrderInfo
		for _, policy := range policies {
			detailedPolicy, err := p.getPolicyDetailedConfig(groupID, policy.ID)
			if err != nil {
				p.logger.Warn("failed to get detailed policy config during last position move",
					zap.Int("policy_id", policy.ID),
					zap.Error(err))
				continue
			}

			currentPolicies = append(currentPolicies, PolicyOrderInfo{
				ID:     policy.ID,
				Cookie: detailedPolicy.Cookie,
			})
		}

		// Find current policy ID
		currentPolicyID := -1
		for _, policy := range currentPolicies {
			if policy.Cookie == configData.Cookie {
				currentPolicyID = policy.ID
				break
			}
		}

		if currentPolicyID == -1 {
			// Policy doesn't exist yet, it will be created with the allocated ID
			// No ordering needed as it will be appended naturally
			p.logger.Debug("policy doesn't exist yet, will be appended to end",
				zap.Uint32("cookie", configData.Cookie))
			return nil
		}

		// Find the maximum ID to determine if policy is already at the end
		maxID := 0
		for _, policy := range currentPolicies {
			if policy.ID > maxID {
				maxID = policy.ID
			}
		}

		// Policy already exists, ^uint32(0) is only for new policies (append to end)
		// For existing policies, ^uint32(0) means no reordering needed
		p.logger.Debug("policy already exists, ^uint32(0) is for new policies only",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("existing_id", currentPolicyID))
		return nil
	}

	// Handle normal ordering logic (not move to last)
	previousCookie := configData.Previous

	// Get current policy list for the group to determine ordering
	policies, err := p.getPoliciesForGroup(groupID)
	if err != nil {
		return fmt.Errorf("failed to get policies for ordering: %w", err)
	}

	// Get detailed configurations including cookies for all policies
	var currentPolicies []PolicyOrderInfo
	for _, policy := range policies {
		detailedPolicy, err := p.getPolicyDetailedConfig(groupID, policy.ID)
		if err != nil {
			p.logger.Warn("failed to get detailed policy config during ordering",
				zap.Int("policy_id", policy.ID),
				zap.Error(err))
			continue
		}

		currentPolicies = append(currentPolicies, PolicyOrderInfo{
			ID:     policy.ID,
			Cookie: detailedPolicy.Cookie,
		})
	}

	// Find the current policy and previous policy IDs
	var currentPolicyID, previousPolicyID int
	var currentPolicyFound, previousPolicyFound bool

	for _, policy := range currentPolicies {
		if policy.Cookie == configData.Cookie {
			currentPolicyID = policy.ID
			currentPolicyFound = true
		}
		if policy.Cookie == previousCookie {
			previousPolicyID = policy.ID
			previousPolicyFound = true
		}
	}

	if !currentPolicyFound {
		return fmt.Errorf("current policy with cookie %d not found in group %d", configData.Cookie, groupID)
	}

	if !previousPolicyFound {
		// Special case: previous_cookie = 0 means move to first position
		if previousCookie == 0 {
			p.logger.Debug("previous cookie is 0, moving to first position",
				zap.Uint32("current_cookie", configData.Cookie))
			// Move to first position (ID = 1)
			return p.movePolicyToPositionBidirectional(groupID, currentPolicyID, 1, currentPolicies)
		} else {
			p.logger.Warn("previous policy not found, treating as last position",
				zap.Uint32("previous_cookie", previousCookie),
				zap.Uint32("current_cookie", configData.Cookie))
			// Move to last position
			return p.movePolicyToPosition(groupID, currentPolicyID, -1, currentPolicies)
		}
	}

	// Calculate target position (previous ID + 1)
	targetPosition := previousPolicyID + 1

	// If already in correct position, no need to move
	if currentPolicyID == targetPosition {
		p.logger.Debug("policy already in correct position",
			zap.Uint32("cookie", configData.Cookie),
			zap.Int("current_id", currentPolicyID),
			zap.Int("target_position", targetPosition))
		return nil
	}

	// Move to target position with bidirectional support
	return p.movePolicyToPositionBidirectional(groupID, currentPolicyID, targetPosition, currentPolicies)
}

/*****************************************************************************
 * NAME: verifyPolicyConfigWithData
 *
 * DESCRIPTION:
 *     Verifies that a policy configuration was applied correctly using converted data.
 *     Uses single-object retrieval for performance optimization.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     configData - Converted policy configuration data to verify
 *     groupID    - Policy group ID for the policy
 *
 * RETURNS:
 *     error - Error if verification fails
 *****************************************************************************/
func (p *FlowControlProcessor) verifyPolicyConfigWithData(ctx context.Context, configData *PolicyConfig, groupID int) error {
	// Use direct cookie-based retrieval for verification
	cmdArgs := []string{"newpolicy", "get", "group=" + strconv.Itoa(groupID), "cookie=" + strconv.Itoa(int(configData.Cookie))}

	p.logger.Debug("verifying policy configuration using cookie",
		zap.Uint32("cookie", configData.Cookie),
		zap.Int("group_id", groupID),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		return fmt.Errorf("failed to get policy for verification: %w", err)
	}

	// Parse the actual configuration from floweye output
	actualConfig, err := ParsePolicyFromGetOutput(output)
	if err != nil {
		return fmt.Errorf("failed to parse policy configuration for verification: %w", err)
	}

	// Set group information for comparison
	actualConfig.GroupID = groupID
	actualConfig.GroupName = configData.GroupName

	// Compare configurations using converted data
	if !ComparePolicyConfig(p.logger, configData, actualConfig) {
		return fmt.Errorf("policy configuration verification failed: configurations do not match")
	}

	p.logger.Debug("policy configuration verification successful",
		zap.Uint32("cookie", configData.Cookie))
	return nil
}
