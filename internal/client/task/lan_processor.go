/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      lan_processor.go
 *
 * DESCRIPTION :    Processor for CPE_LAN tasks
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: LanProcessor
 *
 * DESCRIPTION:
 *     Processes CPE_LAN type tasks.
 *     Handles LAN configuration operations.
 *
 * FIELDS:
 *     logger             - Logger for LAN processor operations
 *     localConfigs       - Cache of local LAN configurations (used for full sync redundant deletion)
 *     localNameToID      - Map of LAN names to IDs (used for full sync redundant deletion)
 *     workingConfigs     - Working cache for operations (can be refreshed during full sync)
 *     workingNameToID    - Working map of LAN names to IDs
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type LanProcessor struct {
	logger             *logger.Logger        // Logger for LAN processor operations
	localConfigs       map[string]*LanConfig // Cache of local LAN configurations (used for full sync redundant deletion)
	localNameToID      map[string]int        // Map of LAN names to IDs (used for full sync redundant deletion)
	workingConfigs     map[string]*LanConfig // Working cache for operations (can be refreshed during full sync)
	workingNameToID    map[string]int        // Working map of LAN names to IDs
	fullSyncInProgress bool                  // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewLanProcessor
 *
 * DESCRIPTION:
 *     Creates a new LAN processor instance.
 *     Initializes the local configuration cache and working configuration cache.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *LanProcessor - Initialized LAN processor
 *****************************************************************************/
func NewLanProcessor(log *logger.Logger) *LanProcessor {
	processor := &LanProcessor{
		logger:             log.WithModule("lan-processor"),
		localConfigs:       make(map[string]*LanConfig),
		localNameToID:      make(map[string]int),
		workingConfigs:     make(map[string]*LanConfig),
		workingNameToID:    make(map[string]int),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: fetchLANConfigs
 *
 * DESCRIPTION:
 *     Fetches LAN configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*LanConfig - LAN configurations by name
 *     map[string]int        - LAN name to ID mapping
 *     error                 - Error if fetch fails
 *****************************************************************************/
func (p *LanProcessor) fetchLANConfigs() (map[string]*LanConfig, map[string]int, error) {
	configs := make(map[string]*LanConfig)
	nameToID := make(map[string]int)

	// Get LAN configurations using existing GetLocalLanConfigs function
	lanConfigs, err := GetLocalLanConfigs(p.logger)
	if err != nil {
		p.logger.Error("failed to fetch LAN configurations", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to fetch LAN configurations: %w", err)
	}

	// Build name to ID mapping
	for name, config := range lanConfigs {
		configs[name] = config
		nameToID[name] = config.Id
	}

	p.logger.Debug("fetched LAN configurations",
		zap.Int("count", len(configs)))

	return configs, nameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local LAN configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *LanProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local LAN configurations")

	configs, nameToID, err := p.fetchLANConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs
	p.localNameToID = nameToID

	p.logger.Debug("refreshed local LAN configurations",
		zap.Int("count", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working LAN configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *LanProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working LAN configurations")

	configs, nameToID, err := p.fetchLANConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs
	p.workingNameToID = nameToID

	p.logger.Debug("refreshed working LAN configurations",
		zap.Int("count", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like creation, modification, deletion, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *LanProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *
 * RETURNS:
 *     error - Error if start fails
 *****************************************************************************/
func (p *LanProcessor) StartFullSync() error {
	p.logger.Info("starting full synchronization")
	p.fullSyncInProgress = true

	// Refresh local configurations
	err := p.refreshLocalConfigs()
	if err != nil {
		p.fullSyncInProgress = false
		return err
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Cleans up any remaining resources.
 *****************************************************************************/
func (p *LanProcessor) EndFullSync() {
	p.logger.Info("ending full synchronization")

	// Create a copy of remaining LAN configurations to avoid modifying map during iteration
	// This copy will be used for cleanup operations while keeping fullSyncInProgress = true
	remainingLans := make(map[string]*LanConfig)
	for lanName, config := range p.localConfigs {
		remainingLans[lanName] = config
	}

	// Process remaining LANs in local configuration
	// These are LANs that were not included in the full sync and should be deleted
	// Keep fullSyncInProgress = true during cleanup so handleDeleteConfig can properly
	// remove items from localConfigs map
	if len(remainingLans) > 0 {
		p.logger.Info("cleaning up remaining LANs",
			zap.Int("count", len(remainingLans)))

		for lanName := range remainingLans {
			// Create a delete task for this LAN
			deleteTask := &pb.LanTask{
				Name: lanName,
			}

			// Delete the LAN
			p.logger.Info("deleting LAN",
				zap.String("name", lanName))

			_, err := p.handleDeleteConfig(context.Background(), deleteTask)
			if err != nil {
				p.logger.Error("failed to delete LAN",
					zap.String("name", lanName),
					zap.Error(err))
			}
		}
	}

	// Verify that all remaining LANs have been cleaned up
	if len(p.localConfigs) > 0 {
		p.logger.Warn("some LANs were not cleaned up during full sync",
			zap.Int("remaining_count", len(p.localConfigs)))

		// Log the remaining LANs for debugging
		for lanName := range p.localConfigs {
			p.logger.Warn("remaining LAN after cleanup",
				zap.String("name", lanName))
		}
	} else {
		p.logger.Info("all remaining LANs cleaned up successfully")
	}

	// Now set fullSyncInProgress to false after cleanup is complete
	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*LanConfig)
	p.localNameToID = make(map[string]int)
	p.workingConfigs = make(map[string]*LanConfig)
	p.workingNameToID = make(map[string]int)
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the type of task this processor can handle.
 *
 * RETURNS:
 *     pb.TaskType - Type of task (TASK_LAN)
 *****************************************************************************/
func (p *LanProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_LAN
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a LAN task based on its action type.
 *     Delegates to specific handlers for different task actions.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *LanProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get LAN task data
	lanTask := task.GetLanTask()
	if lanTask == nil {
		return "LAN task data is empty", fmt.Errorf("lan task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "lan", configIdentifier, p.logger)

	// Convert protobuf to internal structure for detailed logging
	configData, err := ConvertLanTaskToConfig(lanTask)
	if err != nil {
		// Log task start with basic protobuf fields as fallback
		taskLogCtx.LogTaskStart(
			zap.String("ifname", lanTask.GetIfname()),
			zap.Int32("mtu", lanTask.GetMtu()),
			zap.Bool("full_sync", p.fullSyncInProgress),
			zap.String("conversion_error", err.Error()))
	} else {
		// Log task start with detailed config data
		taskLogCtx.LogTaskStart(
			zap.String("ifname", configData.Ifname),
			zap.Int("mtu", configData.Mtu),
			zap.String("ip", configData.Addr),
			zap.String("mask", configData.Mask),
			zap.String("clone_mac", configData.CloneMac),
			zap.Bool("full_sync", p.fullSyncInProgress))
	}

	var result string
	var taskErr error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, taskErr = p.handleConfigChange(ctx, lanTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, taskErr = p.handleDeleteConfig(ctx, lanTask)
	default:
		taskErr = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if taskErr != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, taskErr)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, taskErr
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles both new and edit LAN configuration tasks.
 *     Configures or updates a LAN interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx        - Context for the operation
 *     lanTask    - LAN task containing configuration details
 *     taskAction - The original task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *LanProcessor) handleConfigChange(ctx context.Context, lanTask *pb.LanTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified LanConfig structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	configData, err := ConvertLanTaskToConfig(lanTask)
	if err != nil {
		p.logger.Error("failed to convert LAN task to config",
			zap.String("name", lanTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert LAN configuration: %v", err), err
	}

	p.logger.Info("Processing LAN configuration",
		zap.String("name", configData.Name),
		zap.String("ifname", configData.Ifname),
		zap.Int("mtu", configData.Mtu),
		zap.String("ip", configData.Addr),
		zap.String("mask", configData.Mask),
		zap.String("clone_mac", configData.CloneMac),
		zap.String("action", taskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Validate required fields using converted data
	if configData.Name == "" {
		return "LAN name is required", fmt.Errorf("lan name is required")
	}

	if configData.Ifname == "" {
		return "Interface name is required", fmt.Errorf("interface name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := configData.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if LAN exists in working configuration
	_, exists := p.workingConfigs[configData.Name]

	/*
		// If configurations match, no need to modify
		if exists && CompareLanConfig(p.logger, configData, localConfig) {
			p.logger.Info("LAN configuration already matches, no changes needed",
				zap.String("name", configData.Name),
				zap.String("ifname", configData.Ifname),
				zap.Int("mtu", configData.Mtu))

			// Remove from local configs if in full sync mode
			if p.fullSyncInProgress {
				delete(p.localConfigs, configData.Name)
			}

			return "LAN configuration already matches, no changes needed", nil
		}
	*/

	// Validate required fields at the beginning
	if configData.Addr == "" {
		return "IP address is required", fmt.Errorf("ip address is required")
	}
	if configData.Mask == "" {
		return "Subnet mask is required", fmt.Errorf("subnet mask is required")
	}

	// Build floweye command based on whether LAN exists using converted data
	var cmdArgs []string

	if !exists {
		// LAN doesn't exist, create new LAN
		cmdArgs = []string{
			"nat", "addrtif",
			"name=" + configData.Name,
		}
	} else {
		// LAN exists, update existing LAN
		cmdArgs = []string{
			"nat", "setrtif",
			"name=" + configData.Name,
			"newname=" + configData.Name,
		}
	}

	// Add common parameters for both create and update operations
	cmdArgs = append(cmdArgs, "ifname="+configData.Ifname)
	cmdArgs = append(cmdArgs, "mtu="+fmt.Sprintf("%d", configData.Mtu))
	cmdArgs = append(cmdArgs, "addr="+configData.Addr)
	cmdArgs = append(cmdArgs, "mask="+configData.Mask)
	cmdArgs = append(cmdArgs, "clonemac="+configData.CloneMac)

	// Add ping parameters (not in protobuf, using defaults)
	cmdArgs = append(cmdArgs, "ping_disable=0")
	cmdArgs = append(cmdArgs, "pingip=0.0.0.0")
	cmdArgs = append(cmdArgs, "pingip2=0.0.0.0")
	cmdArgs = append(cmdArgs, "maxdelay=0")
	// Add VLAN parameter (not in protobuf, using default)
	cmdArgs = append(cmdArgs, "vlan=0")

	// Add standby parameter (not in protobuf, using default)
	cmdArgs = append(cmdArgs, "standby=NULL")

	// Execute floweye command
	p.logger.Info("Executing floweye command for LAN configuration",
		zap.String("name", configData.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for LAN configuration",
			zap.String("name", configData.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure LAN: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", configData.Name),
		zap.String("output", output))

	// Refresh working configs after configuration change to get latest state
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation was successful
	}

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyLanConfig(p.logger, configData)
	if verifyErr != nil {
		p.logger.Error("failed to verify LAN configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify LAN configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("LAN configuration verification failed")
		return "LAN configuration verification failed", fmt.Errorf("verification failed")
	}

	// Return appropriate success message based on whether LAN existed
	if exists {
		p.logger.Info("LAN configured successfully",
			zap.String("name", configData.Name),
			zap.String("ifname", configData.Ifname),
			zap.Int("mtu", configData.Mtu))
		return "LAN configuration modified successfully", nil
	} else {
		p.logger.Info("LAN configured successfully",
			zap.String("name", configData.Name),
			zap.String("ifname", configData.Ifname),
			zap.Int("mtu", configData.Mtu))
		return "LAN configuration created successfully", nil
	}
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles LAN configuration deletion tasks.
 *     Deletes a LAN interface using the floweye command.
 *     Implements the configuration consistency mechanism for both
 *     full synchronization and incremental updates.
 *
 * PARAMETERS:
 *     ctx     - Context for the operation
 *     lanTask - LAN task containing configuration to delete
 *
 * RETURNS:
 *     string - Success message
 *     error  - Error if operation fails
 *****************************************************************************/
func (p *LanProcessor) handleDeleteConfig(ctx context.Context, lanTask *pb.LanTask) (string, error) {
	p.logger.Info("handling delete LAN config",
		zap.String("name", lanTask.Name),
		zap.Bool("fullSync", p.fullSyncInProgress))

	// Validate required fields
	if lanTask.Name == "" {
		return "LAN name is required", fmt.Errorf("lan name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := lanTask.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if LAN exists in working configuration
	_, exists := p.workingConfigs[lanTask.Name]

	// If LAN doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("LAN not found in working configuration, nothing to delete",
			zap.String("name", lanTask.Name))
		return "LAN not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"nat", "rmvproxy", lanTask.Name,
	}

	// Execute floweye command
	p.logger.Info("executing floweye command", zap.Strings("args", cmdArgs))
	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("LAN already does not exist, treating as successful delete",
				zap.String("name", lanTask.Name))
		} else {
			p.logger.Error("failed to execute floweye command",
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete LAN: %v", err), err
		}
	}

	// Skip post-delete verification for improved performance and reliability

	p.logger.Info("LAN deleted successfully", zap.String("output", output))
	return "LAN configuration deleted successfully", nil
}
