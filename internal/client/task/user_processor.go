/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      user_processor.go
 *
 * DESCRIPTION :    User processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/18/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: UserProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_USER type tasks.
 *     Handles user configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for user processor operations
 *     localConfigs       - Cache of local user configurations (used only for full sync redundant deletion)
 *     workingConfigs     - Cache of working user configurations (used for all operations)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type UserProcessor struct {
	logger *logger.Logger // Logger for user processor operations

	// 全量同步专用配置 (用于冗余删除)
	localConfigs map[string]*UserConfig // Cache of local user configurations (used only for full sync redundant deletion)

	// 操作专用工作配置 (用于所有操作)
	workingConfigs map[string]*UserConfig // Cache of working user configurations (used for all operations)

	fullSyncInProgress bool // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewUserProcessor
 *
 * DESCRIPTION:
 *     Creates a new user processor instance.
 *     Initializes both local and working configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *UserProcessor - Initialized user processor
 *****************************************************************************/
func NewUserProcessor(log *logger.Logger) *UserProcessor {
	processor := &UserProcessor{
		logger:             log.WithModule("user-processor"),
		localConfigs:       make(map[string]*UserConfig),
		workingConfigs:     make(map[string]*UserConfig),
		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - The task type (TASK_USER)
 *****************************************************************************/
func (p *UserProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_USER
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a user configuration task.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - The task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *UserProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get user task data
	userTask := task.GetUserTask()
	if userTask == nil {
		return "User task data is empty", fmt.Errorf("user task data is nil")
	}

	// Create unified task log context
	configIdentifier := GetConfigIdentifier(task)
	taskLogCtx := NewTaskLogContext(ctx, task, "user", configIdentifier, p.logger)

	// Log task start with additional context
	taskLogCtx.LogTaskStart(
		zap.Int32("pool_id", userTask.GetPoolId()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	var result string
	var err error

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		result, err = p.handleConfigChange(ctx, userTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		result, err = p.handleDeleteConfig(ctx, userTask)
	default:
		err = fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
		result = fmt.Sprintf("unsupported task action: %s", task.TaskAction.String())
	}

	// Log task completion
	if err != nil {
		taskLogCtx.LogTaskEnd(TaskResultFailed, err)
	} else {
		taskLogCtx.LogTaskEnd(TaskResultSuccess, nil)
	}

	return result, err
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles creating or updating a user configuration.
 *     Used for both NEW_CONFIG and EDIT_CONFIG actions.
 *     Also handles enabling or disabling the user based on the enable flag.
 *
 * PARAMETERS:
 *     ctx      - Context for the operation
 *     userTask - User task data
 *     taskAction - The task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *UserProcessor) handleConfigChange(ctx context.Context, userTask *pb.UserTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	userConfig, err := ConvertUserTaskToConfig(userTask)
	if err != nil {
		p.logger.Error("failed to convert user task to config",
			zap.String("name", userTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert user configuration: %v", err), err
	}

	// Validate required fields using converted data
	if userConfig.Name == "" {
		return "User name is required", fmt.Errorf("user name is required")
	}

	if userConfig.PoolID <= 0 {
		return "User pool ID must be greater than 0", fmt.Errorf("user pool ID must be greater than 0")
	}

	if userConfig.Password == "" {
		return "User password is required", fmt.Errorf("user password is required")
	}

	if userConfig.StartDate == "" {
		return "User start date is required", fmt.Errorf("user start date is required")
	}

	if userConfig.ExpireDate == "" {
		return "User expire date is required", fmt.Errorf("user expire date is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := userConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if user exists
	_, exists := p.workingConfigs[userConfig.Name]

	/*
		// If user exists, check if configuration is the same
		if exists {
			existingConfig := p.localConfigs[userConfig.Name]
			if CompareUserConfig(p.logger, userConfig, existingConfig) {
				p.logger.Info("User configuration is unchanged, skipping update",
					zap.String("name", userConfig.Name))

				// Still need to handle enable/disable state
				if userConfig.Enabled != existingConfig.Enabled {
					if userConfig.Enabled {
						if err := EnableUser(p.logger, userConfig.Name); err != nil {
							return fmt.Sprintf("Failed to enable user: %v", err), err
						}
					} else {
						if err := DisableUser(p.logger, userConfig.Name); err != nil {
							return fmt.Sprintf("Failed to disable user: %v", err), err
						}
					}
				}

				return "User configuration is unchanged", nil
			}
		}

	*/

	// Build command arguments using converted data
	// Follow floweye documentation parameter order: poolid name start expire maxonline password bindmac bindip outvlan phone card cname other
	var cmdArgs []string
	if exists {
		// Update existing user
		cmdArgs = append(cmdArgs, "pppoeacct", "set")
	} else {
		// Create new user
		cmdArgs = append(cmdArgs, "pppoeacct", "add")
	}

	// Add parameters in the correct order according to floweye documentation
	cmdArgs = append(cmdArgs, "poolid="+strconv.Itoa(int(userConfig.PoolID)))
	cmdArgs = append(cmdArgs, "name="+userConfig.Name)
	cmdArgs = append(cmdArgs, "start="+userConfig.StartDate)
	cmdArgs = append(cmdArgs, "expire="+userConfig.ExpireDate)
	cmdArgs = append(cmdArgs, "maxonline="+strconv.Itoa(int(userConfig.MaxOnline)))
	cmdArgs = append(cmdArgs, "password="+userConfig.Password)
	cmdArgs = append(cmdArgs, "bindmac="+userConfig.BindMAC)
	cmdArgs = append(cmdArgs, "bindip="+userConfig.BindIP)
	cmdArgs = append(cmdArgs, "outvlan="+strconv.Itoa(int(userConfig.OutVLAN)))
	cmdArgs = append(cmdArgs, "phone="+userConfig.Phone)
	cmdArgs = append(cmdArgs, "card="+userConfig.Card)
	cmdArgs = append(cmdArgs, "cname="+userConfig.CName)
	cmdArgs = append(cmdArgs, "other="+userConfig.Other)

	// Execute floweye command using converted data
	p.logger.Info("Executing floweye command for user configuration",
		zap.String("name", userConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for user configuration",
			zap.String("name", userConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure user: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", userConfig.Name),
		zap.String("output", output))

	// Refresh configurations to include newly created/updated user
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error, because main operation already succeeded
	}

	// Handle enable/disable state using converted data
	if userConfig.Enabled {
		if err := EnableUser(p.logger, userConfig.Name); err != nil {
			p.logger.Error("Failed to enable user",
				zap.String("name", userConfig.Name),
				zap.Error(err))
			return fmt.Sprintf("User configuration applied but failed to enable user: %v", err), err
		}
	} else {
		if err := DisableUser(p.logger, userConfig.Name); err != nil {
			p.logger.Error("Failed to disable user",
				zap.String("name", userConfig.Name),
				zap.Error(err))
			return fmt.Sprintf("User configuration applied but failed to disable user: %v", err), err
		}
	}

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyUserConfig(p.logger, userConfig)
	if verifyErr != nil {
		p.logger.Error("Failed to verify user configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify user configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("User configuration verification failed")
		return "User configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("User configuration applied successfully",
		zap.String("name", userConfig.Name))

	if exists {
		return "User configuration updated successfully", nil
	}
	return "User configuration created successfully", nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles deleting a user configuration.
 *
 * PARAMETERS:
 *     ctx      - Context for the operation
 *     userTask - User task data
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *UserProcessor) handleDeleteConfig(ctx context.Context, userTask *pb.UserTask) (string, error) {
	// Validate required fields
	if userTask.GetName() == "" {
		return "User name is required", fmt.Errorf("user name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := userTask.GetName() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if user exists in working configuration
	_, exists := p.workingConfigs[userTask.GetName()]

	// If user doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("User not found in local configuration, nothing to delete",
			zap.String("name", userTask.GetName()))
		return "User not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"pppoeacct", "remove", "name=" + userTask.GetName(),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete user",
		zap.String("name", userTask.GetName()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("User already does not exist, treating as successful delete",
				zap.String("name", userTask.GetName()))
		} else {
			p.logger.Error("Failed to execute floweye command to delete user",
				zap.String("name", userTask.GetName()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete user: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", userTask.GetName()),
		zap.String("output", output))

	p.logger.Info("User deleted successfully",
		zap.String("name", userTask.GetName()))

	return "User deleted successfully", nil
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *UserProcessor) StartFullSync() error {
	p.logger.Info("Starting full sync for users")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Deletes any local configurations that were not included in the sync.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *UserProcessor) EndFullSync() {
	p.logger.Info("Ending full sync for users")

	// Delete any remaining local configurations
	if p.fullSyncInProgress {
		p.logger.Info("Cleaning up users not included in full sync",
			zap.Int("count", len(p.localConfigs)))

		for name, config := range p.localConfigs {
			// Delete user
			p.logger.Info("Deleting user not included in full sync",
				zap.String("name", name),
				zap.Int32("pool_id", config.PoolID))

			cmdArgs := []string{
				"pppoeacct", "remove", "name=" + name,
			}

			output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
			if err != nil {
				p.logger.Error("Failed to delete user during cleanup",
					zap.String("name", name),
					zap.Error(err),
					zap.String("output", output))
			}
		}
	}

	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*UserConfig)
	p.workingConfigs = make(map[string]*UserConfig)
}

/*****************************************************************************
 * NAME: fetchUserConfigs
 *
 * DESCRIPTION:
 *     Fetches user configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*UserConfig - User configurations by name
 *     error                  - Error if fetch fails
 *****************************************************************************/
func (p *UserProcessor) fetchUserConfigs() (map[string]*UserConfig, error) {
	p.logger.Debug("Fetching user configurations from floweye")

	// Use existing GetLocalUserConfigs logic
	configs, err := GetLocalUserConfigs(p.logger)
	if err != nil {
		p.logger.Error("failed to fetch user configurations", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch user configurations: %w", err)
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local user configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *UserProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local user configurations")

	configs, err := p.fetchUserConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs

	p.logger.Debug("refreshed local user configurations",
		zap.Int("users", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working user configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *UserProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working user configurations")

	configs, err := p.fetchUserConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs

	p.logger.Debug("refreshed working user configurations",
		zap.Int("users", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like enable/disable, verification, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *UserProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}
