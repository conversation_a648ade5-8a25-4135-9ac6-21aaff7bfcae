/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      flow_control_config.go
 *
 * DESCRIPTION :    Flow control configuration data structures and parsing functions
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/06/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: PolicyGroupSchedule
 *
 * DESCRIPTION:
 *     Represents policy group scheduling configuration.
 *     Contains month, day range, and time range settings.
 *
 * FIELDS:
 *     Month     - Scheduling month (0=weekly, 1-12=specific month)
 *     StartDay  - Start day
 *     EndDay    - End day
 *     StartHour - Start hour (0-23)
 *     StartMin  - Start minute (0-59)
 *     StartSec  - Start second (0-59)
 *     EndHour   - End hour (0-23)
 *     EndMin    - End minute (0-59)
 *     EndSec    - End second (0-59)
 *****************************************************************************/
type PolicyGroupSchedule struct {
	Month     int // Scheduling month (0=weekly, 1-12=specific month)
	StartDay  int // Start day
	EndDay    int // End day
	StartHour int // Start hour (0-23)
	StartMin  int // Start minute (0-59)
	StartSec  int // Start second (0-59)
	EndHour   int // End hour (0-23)
	EndMin    int // End minute (0-59)
	EndSec    int // End second (0-59)
}

/*****************************************************************************
 * NAME: PolicyGroupConfig
 *
 * DESCRIPTION:
 *     Represents a policy group configuration.
 *     Contains group name, scheduling, enable/disable status, and ordering.
 *
 * FIELDS:
 *     ID       - Policy group ID (local identifier)
 *     Name     - Policy group name (unique identifier)
 *     Schedule - Scheduling configuration
 *     Disable  - Whether the group is disabled
 *     Stop     - Whether to stop matching after this group
 *     Active   - Whether the group is currently active
 *****************************************************************************/
type PolicyGroupConfig struct {
	ID       int                  // Policy group ID (local identifier)
	Name     string               // Policy group name (unique identifier)
	Schedule *PolicyGroupSchedule // Scheduling configuration
	Disable  bool                 // Whether the group is disabled
	Stop     bool                 // Whether to stop matching after this group
	Active   bool                 // Whether the group is currently active
	Previous string               // Previous group name for ordering (empty = first position)
}

/*****************************************************************************
 * NAME: PolicyConfig
 *
 * DESCRIPTION:
 *     Represents a policy configuration.
 *     Contains policy details including matching criteria and actions.
 *
 * FIELDS:
 *     ID        - Policy ID (local identifier)
 *     Cookie    - Policy cookie (unique identifier)
 *     GroupID   - Policy group ID this policy belongs to
 *     GroupName - Policy group name this policy belongs to
 *     Desc      - Policy description
 *     Disable   - Whether the policy is disabled
 *     InIP      - Internal IP specifications
 *     InPort    - Internal port specifications
 *     OutIP     - External IP specifications
 *     OutPort   - External port specifications
 *     App       - Application protocol specification
 *     Interface - Interface specifications
 *     Action    - Policy action (permit/deny/channel)
 *     Next      - Whether to continue matching after this policy
 *     IPRate    - IP rate limiting (kbits/s)
 *     TOS       - DSCP modification value
 *     SOID      - Traffic statistics object name
 *     Channel   - Traffic channel name (for channel action)
 *     Pri       - Channel priority (for channel action)
 *****************************************************************************/
type PolicyConfig struct {
	ID        int    // Policy ID (local identifier)
	Cookie    uint32 // Policy cookie (unique identifier)
	GroupID   int    // Policy group ID this policy belongs to
	GroupName string // Policy group name this policy belongs to
	Desc      string // Policy description
	Disable   bool   // Whether the policy is disabled
	Previous  uint32 // Previous policy cookie for ordering (0 = first position)

	// Matching criteria
	InIP      string // Internal IP specifications
	InPort    string // Internal port specifications
	OutIP     string // External IP specifications
	OutPort   string // External port specifications
	AppID     string // Application ID (e.g., "httpgroup", "any")
	Protocol  string // Protocol type (e.g., "tcp", "udp", "any")
	Interface struct {
		Bridge string // Bridge/line name
		Dir    string // Direction (both/in/out)
		IfName string // First packet interface
		InIf   string // Source interface
		VLAN   string // VLAN ID
	}

	// Action configuration
	Action  string // Policy action (permit/deny/channel)
	Next    bool   // Whether to continue matching after this policy
	IPRate  int    // IP rate limiting (kbits/s)
	TOS     int    // DSCP modification value
	SOID    string // Traffic statistics object name
	Channel string // Traffic channel name (for channel action)
	Pri     int    // Channel priority (for channel action)
}

/*****************************************************************************
 * NAME: ParsePolicyGroupFromGetOutput
 *
 * DESCRIPTION:
 *     Parses policy group configuration from floweye policygroup2 get output.
 *     Extracts group information including scheduling and status.
 *
 * PARAMETERS:
 *     output - Output from floweye policygroup2 get command
 *
 * RETURNS:
 *     *PolicyGroupConfig - Parsed policy group configuration
 *     error              - Error if parsing fails
 *****************************************************************************/
func ParsePolicyGroupFromGetOutput(output string) (*PolicyGroupConfig, error) {
	config := &PolicyGroupConfig{
		Schedule: &PolicyGroupSchedule{},
	}

	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Extract values using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "id":
			if id, err := strconv.Atoi(value); err == nil {
				config.ID = id
			}
		case "name":
			config.Name = value
		case "month":
			if month, err := strconv.Atoi(value); err == nil {
				config.Schedule.Month = month
			}
		case "startday":
			if startDay, err := strconv.Atoi(value); err == nil {
				config.Schedule.StartDay = startDay
			}
		case "endday":
			if endDay, err := strconv.Atoi(value); err == nil {
				config.Schedule.EndDay = endDay
			}
		case "starthour":
			if startHour, err := strconv.Atoi(value); err == nil {
				config.Schedule.StartHour = startHour
			}
		case "startmin":
			if startMin, err := strconv.Atoi(value); err == nil {
				config.Schedule.StartMin = startMin
			}
		case "startsec":
			if startSec, err := strconv.Atoi(value); err == nil {
				config.Schedule.StartSec = startSec
			}
		case "endhour":
			if endHour, err := strconv.Atoi(value); err == nil {
				config.Schedule.EndHour = endHour
			}
		case "endmin":
			if endMin, err := strconv.Atoi(value); err == nil {
				config.Schedule.EndMin = endMin
			}
		case "endsec":
			if endSec, err := strconv.Atoi(value); err == nil {
				config.Schedule.EndSec = endSec
			}
		case "disable":
			config.Disable = value == "1"
		case "stop":
			config.Stop = value == "1"
		case "active":
			config.Active = value == "1"
		}
	}

	if config.Name == "" {
		return nil, fmt.Errorf("failed to parse policy group name from output")
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ParsePolicyFromJSONOutput
 *
 * DESCRIPTION:
 *     Parses policy configuration from floweye newpolicy list JSON output.
 *     Extracts policy information including matching criteria and actions.
 *
 * PARAMETERS:
 *     jsonData - JSON data from floweye newpolicy list command
 *
 * RETURNS:
 *     []*PolicyConfig - List of parsed policy configurations
 *     error           - Error if parsing fails
 *****************************************************************************/
func ParsePolicyFromJSONOutput(jsonData string) ([]*PolicyConfig, error) {
	var policies []map[string]interface{}

	// Parse JSON output using unified floweye JSON parser
	policies, err := utils.ParseFloweyeJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse floweye JSON output: %w", err)
	}

	var configs []*PolicyConfig
	for _, policyData := range policies {
		config := &PolicyConfig{}

		// Parse basic fields
		if id, ok := policyData["id"].(float64); ok {
			config.ID = int(id)
		}
		if desc, ok := policyData["desc"].(string); ok {
			config.Desc = desc
		}
		if disable, ok := policyData["disable"].(float64); ok {
			config.Disable = int(disable) == 1
		}

		// Parse matching criteria
		if inip, ok := policyData["inip"].(string); ok {
			config.InIP = inip
		}
		if inport, ok := policyData["inport"].(string); ok {
			config.InPort = inport
		}
		if outip, ok := policyData["outip"].(string); ok {
			config.OutIP = outip
		}
		if outport, ok := policyData["outport"].(string); ok {
			config.OutPort = outport
		}
		if appname, ok := policyData["appname"].(string); ok {
			// Parse appname in format "appid.protocol"
			parts := strings.Split(appname, ".")
			if len(parts) >= 2 {
				config.AppID = parts[0]
				config.Protocol = parts[1]
			} else {
				config.AppID = appname
				config.Protocol = "any"
			}
		}

		// Parse interface fields
		if bridge, ok := policyData["bridge"].(float64); ok && bridge != 0 {
			config.Interface.Bridge = fmt.Sprintf("%.0f", bridge)
		}
		if dir, ok := policyData["dir"].(string); ok {
			config.Interface.Dir = dir
		}
		if ifname, ok := policyData["fistif"].(string); ok {
			config.Interface.IfName = ifname
		}
		if inif, ok := policyData["inif"].(string); ok {
			config.Interface.InIf = inif
		}
		if vlan, ok := policyData["vlan"].(string); ok {
			config.Interface.VLAN = vlan
		}

		// Parse action fields
		if action, ok := policyData["action"].(string); ok {
			config.Action = action
		}
		if stop, ok := policyData["stop"].(float64); ok {
			config.Next = int(stop) == 1 // stop=1 means continue (next=true)
		}
		if iprate, ok := policyData["iprate"].(string); ok {
			if rate, err := strconv.Atoi(iprate); err == nil {
				config.IPRate = rate
			}
		}
		if tos, ok := policyData["tos"].(float64); ok {
			config.TOS = int(tos)
		}
		if soname, ok := policyData["soname"].(string); ok && soname != "NULL" {
			config.SOID = soname
		}
		if pri, ok := policyData["pri"].(float64); ok {
			config.Pri = int(pri)
		}

		configs = append(configs, config)
	}

	return configs, nil
}

/*****************************************************************************
 * NAME: ParsePolicyFromGetOutput
 *
 * DESCRIPTION:
 *     Parses policy configuration from floweye newpolicy get output.
 *     Extracts detailed policy information including cookie from key=value format.
 *
 * PARAMETERS:
 *     output - Output from floweye newpolicy get command
 *
 * RETURNS:
 *     *PolicyConfig - Parsed policy configuration
 *     error         - Error if parsing fails
 *****************************************************************************/
func ParsePolicyFromGetOutput(output string) (*PolicyConfig, error) {
	config := &PolicyConfig{}

	// Parse output using unified helper function
	configMap := ParseKeyValueOutput(output)

	// Extract values using switch for better performance and readability
	for key, value := range configMap {
		switch key {
		case "id", "polno":
			if val, err := strconv.Atoi(value); err == nil {
				config.ID = val
			}
		case "cookie":
			if val, err := strconv.ParseUint(value, 10, 32); err == nil {
				config.Cookie = uint32(val)
			}
		case "desc":
			config.Desc = value
		case "disable":
			config.Disable = value == "1"
		case "inip":
			config.InIP = value
		case "inport":
			config.InPort = value
		case "outip":
			config.OutIP = value
		case "outport":
			config.OutPort = value
		case "appid":
			// Store app ID separately
			config.AppID = value
		case "proto":
			// Store protocol separately
			config.Protocol = value
		case "appname":
			// Skip appname field - we use appid and proto instead
			continue
		case "bridge":
			config.Interface.Bridge = value
		case "dir":
			config.Interface.Dir = value
		case "fistif", "ifname":
			config.Interface.IfName = value
		case "inif":
			config.Interface.InIf = value
		case "vlan":
			config.Interface.VLAN = value
		case "action":
			// Handle action field - floweye returns channel name for channel actions
			if value == "permit" || value == "deny" {
				config.Action = value
			} else {
				// For any other value, treat as channel action
				config.Action = "channel"
				config.Channel = value
			}
		case "stop":
			config.Next = value == "1" // stop=1 means continue (next=true)
		case "matchact":
			config.Next = value == "continue" // continue means next=true, stop means next=false
		case "iprate":
			if rate, err := strconv.Atoi(value); err == nil {
				config.IPRate = rate
			}
		case "tos":
			if tosVal, err := strconv.Atoi(value); err == nil {
				config.TOS = tosVal
			}
		case "soid":
			// Skip soid field - we use soname instead for traffic statistics name
			continue
		case "soname":
			if value != "NULL" && value != "" {
				config.SOID = value
			}
		case "priority":
			if priVal, err := strconv.Atoi(value); err == nil {
				config.Pri = priVal
			}
		}
	}

	// Note: Allow cookie=0 for system policies, but they won't be managed by our agent
	// The cookie field is successfully parsed even if it's 0
	return config, nil
}

/*****************************************************************************
 * NAME: ConvertPolicyGroupTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf FlowControlPolicyGroupTask to PolicyGroupConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing PolicyGroupConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     task - Protobuf policy group task message to convert
 *
 * RETURNS:
 *     *PolicyGroupConfig - Converted policy group configuration structure
 *     error              - Error if conversion fails
 *****************************************************************************/
func ConvertPolicyGroupTaskToConfig(task *pb.FlowControlPolicyGroupTask) (*PolicyGroupConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("policy group task is nil")
	}

	// Validate required fields
	if task.GetName() == "" {
		return nil, fmt.Errorf("policy group name is required")
	}

	// Initialize with basic fields and defaults
	config := &PolicyGroupConfig{
		Name:     task.GetName(),
		Disable:  task.GetDisable(),
		Stop:     task.GetStop(),
		Schedule: &PolicyGroupSchedule{},
	}

	// Parse previous field for ordering
	// 当previous为空或者为字符串"null"的时候，默认视为排序在"_subscription_group"后面
	if task.Previous != nil {
		if *task.Previous == "" || *task.Previous == "null" {
			config.Previous = "_subscription_group"
		} else {
			config.Previous = *task.Previous
		}
	} else {
		config.Previous = "_subscription_group" // Default to after _subscription_group
	}

	// Extract time configuration if present
	if task.GetTimeRange() != nil {
		timeRange := task.GetTimeRange()

		// Set day range
		config.Schedule.StartDay = int(timeRange.GetStartDay())
		config.Schedule.EndDay = int(timeRange.GetEndDay())

		// Set start time
		if timeRange.GetStartTime() != nil {
			startTime := timeRange.GetStartTime()
			config.Schedule.StartHour = int(startTime.GetHour())
			config.Schedule.StartMin = int(startTime.GetMin())
			config.Schedule.StartSec = int(startTime.GetSec())
		}

		// Set end time
		if timeRange.GetEndTime() != nil {
			endTime := timeRange.GetEndTime()
			config.Schedule.EndHour = int(endTime.GetHour())
			config.Schedule.EndMin = int(endTime.GetMin())
			config.Schedule.EndSec = int(endTime.GetSec())
		}
	} else {
		// Set default schedule values
		config.Schedule.Month = 0
		config.Schedule.StartDay = 1
		config.Schedule.EndDay = 7
		config.Schedule.StartHour = 0
		config.Schedule.StartMin = 0
		config.Schedule.StartSec = 0
		config.Schedule.EndHour = 23
		config.Schedule.EndMin = 59
		config.Schedule.EndSec = 59
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ConvertPolicyTaskToConfig
 *
 * DESCRIPTION:
 *     Converts a protobuf FlowControlPolicyTask to PolicyConfig structure.
 *     Performs one-time parsing of all protobuf fields, handles type conversions,
 *     and fills default values for optional fields. Reuses existing PolicyConfig
 *     structure to eliminate duplicate definitions.
 *
 * PARAMETERS:
 *     task - Protobuf policy task message to convert
 *
 * RETURNS:
 *     *PolicyConfig - Converted policy configuration structure
 *     error         - Error if conversion fails
 *****************************************************************************/
func ConvertPolicyTaskToConfig(task *pb.FlowControlPolicyTask, logger *logger.Logger) (*PolicyConfig, error) {
	if task == nil {
		return nil, fmt.Errorf("policy task is nil")
	}

	// Validate required fields
	if task.GetCookie() == 0 {
		return nil, fmt.Errorf("policy cookie is required")
	}

	if task.GetGroupName() == "" {
		return nil, fmt.Errorf("policy group name is required")
	}

	// Initialize with basic fields and defaults
	config := &PolicyConfig{
		Cookie:    task.GetCookie(),
		GroupName: task.GetGroupName(),
		Desc:      task.GetDesc(),
		Disable:   task.GetDisable(),
	}

	// Parse previous field for ordering
	if task.Previous != nil {
		config.Previous = *task.Previous
	} else {
		config.Previous = ^uint32(0) // Default to -1 (append position)
	}

	// Convert matching criteria
	config.InIP, _ = BuildAddressSelectorsString(task.GetInIp(), logger)
	config.OutIP, _ = BuildAddressSelectorsString(task.GetOutIp(), logger)
	config.InPort = ParsePortSpec(task.GetInPort())
	config.OutPort = ParsePortSpec(task.GetOutPort())
	config.AppID, config.Protocol = ParseAppProtocolSpec(task.GetApp())

	// Convert interface specifications
	if task.GetInterface() != nil {
		interfaceSpec := task.GetInterface()
		if interfaceSpec.Bridge != nil {
			config.Interface.Bridge = *interfaceSpec.Bridge
		} else {
			config.Interface.Bridge = "any"
		}

		if interfaceSpec.Dir != nil {
			switch *interfaceSpec.Dir {
			case pb.FlowDirection_FLOW_DIRECTION_IN:
				config.Interface.Dir = "in"
			case pb.FlowDirection_FLOW_DIRECTION_OUT:
				config.Interface.Dir = "out"
			case pb.FlowDirection_FLOW_DIRECTION_BOTH:
				config.Interface.Dir = "both"
			default:
				config.Interface.Dir = "both"
			}
		} else {
			config.Interface.Dir = "both"
		}

		if interfaceSpec.Ifname != nil {
			config.Interface.IfName = *interfaceSpec.Ifname
		} else {
			config.Interface.IfName = "any"
		}

		if interfaceSpec.InIf != nil {
			config.Interface.InIf = *interfaceSpec.InIf
		} else {
			config.Interface.InIf = "any"
		}

		if interfaceSpec.Vlan != nil {
			vlanRange := interfaceSpec.Vlan
			if vlanRange.GetStart() == vlanRange.GetEnd() {
				config.Interface.VLAN = strconv.Itoa(int(vlanRange.GetStart()))
			} else {
				config.Interface.VLAN = fmt.Sprintf("%d-%d", vlanRange.GetStart(), vlanRange.GetEnd())
			}
		} else {
			config.Interface.VLAN = "0"
		}
	} else {
		// Set default interface values
		config.Interface.Bridge = "any"
		config.Interface.Dir = "both"
		config.Interface.IfName = "any"
		config.Interface.InIf = "any"
		config.Interface.VLAN = "0"
	}

	// Convert action configuration
	switch task.GetAction() {
	case pb.FlowControlAction_FLOW_CONTROL_ACTION_PERMIT:
		config.Action = "permit"
		if task.GetActionAccept() != nil {
			acceptConfig := task.GetActionAccept()
			config.Next = acceptConfig.GetNext()
			if acceptConfig.IpRate != nil {
				config.IPRate = int(*acceptConfig.IpRate)
			}
			if acceptConfig.Tos != nil {
				config.TOS = int(*acceptConfig.Tos)
			}
			if acceptConfig.SoId != nil {
				config.SOID = *acceptConfig.SoId
			}
		}

	case pb.FlowControlAction_FLOW_CONTROL_ACTION_CHANNEL:
		if task.GetActionChannel() != nil {
			channelConfig := task.GetActionChannel()
			config.Action = "channel"
			config.Channel = channelConfig.GetChannel()
			config.Next = channelConfig.GetNext()
			config.Pri = int(channelConfig.GetPri())
			if channelConfig.IpRate != nil {
				config.IPRate = int(*channelConfig.IpRate)
			}
			if channelConfig.SoId != nil {
				config.SOID = *channelConfig.SoId
			}
		} else {
			return nil, fmt.Errorf("channel action requires channel configuration")
		}

	case pb.FlowControlAction_FLOW_CONTROL_ACTION_DENY:
		config.Action = "deny"

	default:
		return nil, fmt.Errorf("unsupported action type: %v", task.GetAction())
	}

	return config, nil
}

/*****************************************************************************
 * NAME: ComparePolicyGroupConfig
 *
 * DESCRIPTION:
 *     Compares a policy group configuration with local configuration.
 *     Checks if the configurations match to determine if update is needed.
 *     Updated to use converted PolicyGroupConfig instead of protobuf task.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Converted policy group configuration data
 *     localConfig - Local policy group configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func ComparePolicyGroupConfig(logger *logger.Logger, configData *PolicyGroupConfig, localConfig *PolicyGroupConfig) bool {
	if configData == nil || localConfig == nil {
		logger.Debug("policy group comparison: nil input")
		return false
	}

	// Compare basic fields
	if configData.Name != localConfig.Name {
		logger.Debug("policy group name mismatch",
			zap.String("config_name", configData.Name),
			zap.String("local_name", localConfig.Name))
		return false
	}

	if configData.Disable != localConfig.Disable {
		logger.Debug("policy group disable status mismatch",
			zap.Bool("config_disable", configData.Disable),
			zap.Bool("local_disable", localConfig.Disable))
		return false
	}

	if configData.Stop != localConfig.Stop {
		logger.Debug("policy group stop status mismatch",
			zap.Bool("config_stop", configData.Stop),
			zap.Bool("local_stop", localConfig.Stop))
		return false
	}

	// Compare time configuration
	if configData.Schedule != nil && localConfig.Schedule != nil {
		// Compare day range
		if configData.Schedule.StartDay != localConfig.Schedule.StartDay ||
			configData.Schedule.EndDay != localConfig.Schedule.EndDay {
			logger.Debug("policy group day range mismatch",
				zap.Int("config_start_day", configData.Schedule.StartDay),
				zap.Int("config_end_day", configData.Schedule.EndDay),
				zap.Int("local_start_day", localConfig.Schedule.StartDay),
				zap.Int("local_end_day", localConfig.Schedule.EndDay))
			return false
		}

		// Compare start time
		if configData.Schedule.StartHour != localConfig.Schedule.StartHour ||
			configData.Schedule.StartMin != localConfig.Schedule.StartMin ||
			configData.Schedule.StartSec != localConfig.Schedule.StartSec {
			logger.Debug("policy group start time mismatch",
				zap.Int("config_start_hour", configData.Schedule.StartHour),
				zap.Int("local_start_hour", localConfig.Schedule.StartHour))
			return false
		}

		// Compare end time
		if configData.Schedule.EndHour != localConfig.Schedule.EndHour ||
			configData.Schedule.EndMin != localConfig.Schedule.EndMin ||
			configData.Schedule.EndSec != localConfig.Schedule.EndSec {
			logger.Debug("policy group end time mismatch",
				zap.Int("config_end_hour", configData.Schedule.EndHour),
				zap.Int("local_end_hour", localConfig.Schedule.EndHour))
			return false
		}
	}

	logger.Debug("policy group configurations match",
		zap.String("name", configData.Name))
	return true
}

/*****************************************************************************
 * NAME: compareSOID
 *
 * DESCRIPTION:
 *     Compares two SOID values, treating empty string and "0" as equivalent.
 *     This is needed because floweye returns "0" for unset SOID values,
 *     while configuration may have empty string.
 *
 * PARAMETERS:
 *     configSOID - SOID value from configuration
 *     localSOID  - SOID value from floweye
 *
 * RETURNS:
 *     bool - True if SOID values are equivalent, false otherwise
 *****************************************************************************/
func compareSOID(configSOID, localSOID string) bool {
	// Normalize empty values to "0"
	normalizeSOID := func(soid string) string {
		if soid == "" || soid == "0" {
			return "0"
		}
		return soid
	}

	return normalizeSOID(configSOID) == normalizeSOID(localSOID)
}

/*****************************************************************************
 * NAME: ComparePolicyConfig
 *
 * DESCRIPTION:
 *     Compares a policy configuration with local configuration.
 *     Checks if the configurations match to determine if update is needed.
 *     Updated to use converted PolicyConfig instead of protobuf task.
 *
 * PARAMETERS:
 *     logger      - Logger for comparison operations
 *     configData  - Converted policy configuration data
 *     localConfig - Local policy configuration
 *
 * RETURNS:
 *     bool - True if configurations match, false otherwise
 *****************************************************************************/
func ComparePolicyConfig(logger *logger.Logger, configData *PolicyConfig, localConfig *PolicyConfig) bool {
	if configData == nil || localConfig == nil {
		logger.Debug("policy comparison: nil input")
		return false
	}

	// Compare basic fields
	if configData.Cookie != localConfig.Cookie {
		logger.Debug("policy cookie mismatch",
			zap.Uint32("config_cookie", configData.Cookie),
			zap.Uint32("local_cookie", localConfig.Cookie))
		return false
	}

	if configData.Desc != localConfig.Desc {
		logger.Debug("policy description mismatch",
			zap.String("config_desc", configData.Desc),
			zap.String("local_desc", localConfig.Desc))
		return false
	}

	if configData.Disable != localConfig.Disable {
		logger.Debug("policy disable status mismatch",
			zap.Bool("config_disable", configData.Disable),
			zap.Bool("local_disable", localConfig.Disable))
		return false
	}

	// Compare matching criteria - normalize IP formats before comparison
	if !CompareIPFormats(configData.InIP, localConfig.InIP) {
		logger.Debug("policy internal IP mismatch",
			zap.String("config_inip", configData.InIP),
			zap.String("local_inip", localConfig.InIP))
		return false
	}

	if configData.InPort != localConfig.InPort {
		logger.Debug("policy internal port mismatch",
			zap.String("config_inport", configData.InPort),
			zap.String("local_inport", localConfig.InPort))
		return false
	}

	if !CompareIPFormats(configData.OutIP, localConfig.OutIP) {
		logger.Debug("policy external IP mismatch",
			zap.String("config_outip", configData.OutIP),
			zap.String("local_outip", localConfig.OutIP))
		return false
	}

	if configData.OutPort != localConfig.OutPort {
		logger.Debug("policy external port mismatch",
			zap.String("config_outport", configData.OutPort),
			zap.String("local_outport", localConfig.OutPort))
		return false
	}

	if configData.AppID != localConfig.AppID {
		logger.Debug("policy application ID mismatch",
			zap.String("config_appid", configData.AppID),
			zap.String("local_appid", localConfig.AppID))
		return false
	}

	if configData.Protocol != localConfig.Protocol {
		logger.Debug("policy protocol mismatch",
			zap.String("config_protocol", configData.Protocol),
			zap.String("local_protocol", localConfig.Protocol))
		return false
	}

	// Compare interface specifications
	if configData.Interface.Bridge != localConfig.Interface.Bridge {
		logger.Debug("policy bridge mismatch",
			zap.String("config_bridge", configData.Interface.Bridge),
			zap.String("local_bridge", localConfig.Interface.Bridge))
		return false
	}

	if configData.Interface.Dir != localConfig.Interface.Dir {
		logger.Debug("policy direction mismatch",
			zap.String("config_dir", configData.Interface.Dir),
			zap.String("local_dir", localConfig.Interface.Dir))
		return false
	}

	if configData.Interface.IfName != localConfig.Interface.IfName {
		logger.Debug("policy interface name mismatch",
			zap.String("config_ifname", configData.Interface.IfName),
			zap.String("local_ifname", localConfig.Interface.IfName))
		return false
	}

	if configData.Interface.InIf != localConfig.Interface.InIf {
		logger.Debug("policy input interface mismatch",
			zap.String("config_inif", configData.Interface.InIf),
			zap.String("local_inif", localConfig.Interface.InIf))
		return false
	}

	if configData.Interface.VLAN != localConfig.Interface.VLAN {
		logger.Debug("policy VLAN mismatch",
			zap.String("config_vlan", configData.Interface.VLAN),
			zap.String("local_vlan", localConfig.Interface.VLAN))
		return false
	}

	// Compare action configuration
	if configData.Action != localConfig.Action {
		logger.Debug("policy action type mismatch",
			zap.String("config_action", configData.Action),
			zap.String("local_action", localConfig.Action))
		return false
	}

	// Compare action-specific fields
	switch configData.Action {
	case "permit":
		if configData.Next != localConfig.Next {
			logger.Debug("policy next status mismatch",
				zap.Bool("config_next", configData.Next),
				zap.Bool("local_next", localConfig.Next))
			return false
		}

		if configData.IPRate != localConfig.IPRate {
			logger.Debug("policy IP rate mismatch",
				zap.Int("config_iprate", configData.IPRate),
				zap.Int("local_iprate", localConfig.IPRate))
			return false
		}

		if configData.TOS != localConfig.TOS {
			logger.Debug("policy TOS mismatch",
				zap.Int("config_tos", configData.TOS),
				zap.Int("local_tos", localConfig.TOS))
			return false
		}

		if !compareSOID(configData.SOID, localConfig.SOID) {
			logger.Debug("policy SOID mismatch",
				zap.String("config_soid", configData.SOID),
				zap.String("local_soid", localConfig.SOID))
			return false
		}

	case "channel":
		if configData.Next != localConfig.Next {
			logger.Debug("policy next status mismatch",
				zap.Bool("config_next", configData.Next),
				zap.Bool("local_next", localConfig.Next))
			return false
		}

		if configData.Channel != localConfig.Channel {
			logger.Debug("policy channel mismatch",
				zap.String("config_channel", configData.Channel),
				zap.String("local_channel", localConfig.Channel))
			return false
		}

		if configData.Pri != localConfig.Pri {
			logger.Debug("policy priority mismatch",
				zap.Int("config_pri", configData.Pri),
				zap.Int("local_pri", localConfig.Pri))
			return false
		}

		if configData.IPRate != localConfig.IPRate {
			logger.Debug("policy IP rate mismatch",
				zap.Int("config_iprate", configData.IPRate),
				zap.Int("local_iprate", localConfig.IPRate))
			return false
		}

		if !compareSOID(configData.SOID, localConfig.SOID) {
			logger.Debug("policy SOID mismatch",
				zap.String("config_soid", configData.SOID),
				zap.String("local_soid", localConfig.SOID))
			return false
		}

	case "deny":
		// For deny action, only basic fields need to be compared
		// which are already compared above
	}

	logger.Debug("policy configurations match",
		zap.Uint32("cookie", configData.Cookie))
	return true
}
