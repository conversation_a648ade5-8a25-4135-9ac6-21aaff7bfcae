/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_service_processor_test.go
 *
 * DESCRIPTION :    Unit tests for iWAN Service processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/25/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"testing"

	pb "agent/internal/pb"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test NewIwanServiceProcessor
func TestNewIwanServiceProcessor(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil)

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Verify processor
	assert.NotNil(t, processor)
	assert.Equal(t, pb.TaskType_TASK_IWAN_SERVICE, processor.GetTaskType())
	assert.NotNil(t, processor.localConfigs)
	assert.False(t, processor.fullSyncInProgress)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with NEW_CONFIG action
func TestIwanServiceProcessTask_NewConfig(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()

	// Setup mock expectations for handleConfigChange
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`name=iwan-test1
type=iwansvc
addr=************
mtu=1436
auth=free
pool=1299
radsvrid=0
ipv6_prefixlen=0`,
		nil,
	).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Create task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IWAN_SERVICE,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_IwanServiceTask{
			IwanServiceTask: &pb.IwanServiceTask{
				Name:  "iwan-test1",
				Addr:  &pb.IpAddress{Ip: &pb.IpAddress_IpString{IpString: "************"}},
				Mtu:   1436,
				Pool:  1299,
			},
		},
	}

	// Process task
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify result
	assert.NoError(t, err)
	assert.Contains(t, result, "created successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with EDIT_CONFIG action
func TestIwanServiceProcessTask_EditConfig(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`{"id":12,"name":"iwan-test1","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":1299,"pname":"test1299","dns0":"*******","dns1":"0.0.0.0","clntcnt":0,"auth":"free","radid":0,"radname":"DefaultRadius"}`,
		nil,
	).Once()

	// Setup mock for GetIwanServiceConfig
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`name=iwan-test1
type=iwansvc
addr=************
mtu=1436
auth=free
pool=1299
radsvrid=0
ipv6_prefixlen=0`,
		nil,
	).Once()

	// Setup mock for handleConfigChange
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`name=iwan-test1
type=iwansvc
addr=************
mtu=1500
auth=local
pool=2000
radsvrid=0
ipv6_prefixlen=0`,
		nil,
	).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Create task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IWAN_SERVICE,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		Payload: &pb.DeviceTask_IwanServiceTask{
			IwanServiceTask: &pb.IwanServiceTask{
				Name:  "iwan-test1",
				Addr:  &pb.IpAddress{Ip: &pb.IpAddress_IpString{IpString: "************"}},
				Mtu:   1500,
				Pool:  2000,
			},
		},
	}

	// Process task
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify result
	assert.NoError(t, err)
	assert.Contains(t, result, "updated successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with DELETE_CONFIG action
func TestIwanServiceProcessTask_DeleteConfig(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`{"id":12,"name":"iwan-test1","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":1299,"pname":"test1299","dns0":"*******","dns1":"0.0.0.0","clntcnt":0,"auth":"free","radid":0,"radname":"DefaultRadius"}`,
		nil,
	).Once()

	// Setup mock for GetIwanServiceConfig
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`name=iwan-test1
type=iwansvc
addr=************
mtu=1436
auth=free
pool=1299
radsvrid=0
ipv6_prefixlen=0`,
		nil,
	).Once()

	// Setup mock for handleDeleteConfig
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()

	// Setup mock for verification
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Create task
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IWAN_SERVICE,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		Payload: &pb.DeviceTask_IwanServiceTask{
			IwanServiceTask: &pb.IwanServiceTask{
				Name: "iwan-test1",
			},
		},
	}

	// Process task
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify result
	assert.NoError(t, err)
	assert.Contains(t, result, "deleted successfully")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with invalid action
func TestProcessTask_InvalidAction(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Create task with invalid action
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IWAN_SERVICE,
		TaskAction: pb.TaskAction(99), // Invalid action
		Payload: &pb.DeviceTask_IwanServiceTask{
			IwanServiceTask: &pb.IwanServiceTask{
				Name: "iwan-test1",
			},
		},
	}

	// Process task
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify result
	assert.Error(t, err)
	assert.Contains(t, result, "unsupported task action")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test ProcessTask with nil task data
func TestProcessTask_NilTaskData(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Create task with nil task data
	task := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_IWAN_SERVICE,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload:    nil,
	}

	// Process task
	result, err := processor.ProcessTask(context.Background(), task)

	// Verify result
	assert.Error(t, err)
	assert.Contains(t, result, "empty")
}

// Test StartFullSync and EndFullSync
func TestIwanServiceFullSync(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`{"id":12,"name":"iwan-test1","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":1299,"pname":"test1299","dns0":"*******","dns1":"0.0.0.0","clntcnt":0,"auth":"free","radid":0,"radname":"DefaultRadius"}`,
		nil,
	).Once()

	// Setup mock for GetIwanServiceConfig
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return(
		`name=iwan-test1
type=iwansvc
addr=************
mtu=1436
auth=free
pool=1299
radsvrid=0
ipv6_prefixlen=0`,
		nil,
	).Once()

	// Setup mock for EndFullSync
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Start full sync
	err := processor.StartFullSync()
	assert.NoError(t, err)
	assert.True(t, processor.fullSyncInProgress)

	// End full sync
	processor.EndFullSync()
	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localConfigs)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test handleConfigChange with validation errors
func TestHandleConfigChange_ValidationErrors(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Test cases
	testCases := []struct {
		name     string
		task     *pb.IwanServiceTask
		errorMsg string
	}{
		{
			name: "empty name",
			task: &pb.IwanServiceTask{
				Addr:  &pb.IpAddress{Ip: &pb.IpAddress_IpString{IpString: "************"}},
				Mtu:   1436,
				Pool:  1299,
			},
			errorMsg: "name is required",
		},
		{
			name: "empty addr",
			task: &pb.IwanServiceTask{
				Name:  "iwan-test1",
				Mtu:   1436,
				Pool:  1299,
			},
			errorMsg: "address is required",
		},
		{
			name: "invalid mtu",
			task: &pb.IwanServiceTask{
				Name:  "iwan-test1",
				Addr:  &pb.IpAddress{Ip: &pb.IpAddress_IpString{IpString: "************"}},
				Mtu:   100, // Too small
				Pool:  1299,
			},
			errorMsg: "MTU must be between",
		},
		{
			name: "empty pool",
			task: &pb.IwanServiceTask{
				Name:  "iwan-test1",
				Addr:  &pb.IpAddress{Ip: &pb.IpAddress_IpString{IpString: "************"}},
				Mtu:   1436,
				Pool:  0, // Invalid
			},
			errorMsg: "pool ID is required",
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := processor.handleConfigChange(context.Background(), tc.task, pb.TaskAction_NEW_CONFIG)
			assert.Error(t, err)
			assert.Contains(t, result, tc.errorMsg)
		})
	}
}

// Test handleDeleteConfig with validation errors
func TestHandleDeleteConfig_ValidationErrors(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Test empty name
	task := &pb.IwanServiceTask{
		Name: "", // Empty name
	}

	result, err := processor.handleDeleteConfig(context.Background(), task)
	assert.Error(t, err)
	assert.Contains(t, result, "name is required")
}

// Test handleDeleteConfig with non-existent service
func TestHandleDeleteConfig_NonExistentService(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", nil).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Create task
	task := &pb.IwanServiceTask{
		Name: "non-existent-service",
	}

	// Process task
	result, err := processor.handleDeleteConfig(context.Background(), task)

	// Verify result
	assert.NoError(t, err)
	assert.Contains(t, result, "not found, nothing to delete")

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}

// Test refreshLocalConfigs with error
func TestRefreshLocalConfigs_Error(t *testing.T) {
	log := setupIwanServiceTestLogger()

	// Create mock
	mockExecuteCommand, cleanup := setupMockExecuteCommand()
	defer cleanup()

	// Setup mock expectations for refreshLocalConfigs
	mockExecuteCommand.On("Execute", mock.Anything, mock.Anything, mock.Anything).Return("", fmt.Errorf("command failed")).Once()

	// Create processor
	processor := NewIwanServiceProcessor(log)

	// Call refreshLocalConfigs
	err := processor.refreshLocalConfigs()

	// Verify result
	assert.Error(t, err)

	// Verify all mocks were called
	mockExecuteCommand.AssertExpectations(t)
}
