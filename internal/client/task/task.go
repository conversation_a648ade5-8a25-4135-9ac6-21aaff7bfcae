/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      task.go
 *
 * DESCRIPTION :    Base interfaces and common structures for task processing
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	"agent/internal/metrics"
	pb "agent/internal/pb"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: TaskProcessor
 *
 * DESCRIPTION:
 *     Defines the interface for task processors.
 *     Each processor handles a specific type of device task.
 *
 * METHODS:
 *     ProcessTask   - Processes a specific type of device task
 *     GetTaskType   - Returns the type of task this processor can handle
 *     StartFullSync - Starts a full synchronization process
 *     EndFullSync   - Ends a full synchronization process
 *****************************************************************************/
type TaskProcessor interface {
	ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error)
	GetTaskType() pb.TaskType
	StartFullSync() error
	EndFullSync()
}

/*****************************************************************************
 * NAME: TaskManager
 *
 * DESCRIPTION:
 *     Manages all task processors and handles task execution.
 *     Provides thread-safe access to processors and task processing capabilities.
 *
 * FIELDS:
 *     processors - Map of task types to their processors
 *     logger     - Logger for task manager operations
 *     taskMu     - Mutex for thread-safe access to processors
 *     metrics    - Metrics for task manager operations
 *****************************************************************************/
type TaskManager struct {
	processors map[pb.TaskType]TaskProcessor // Map of task types to their processors
	logger     *logger.Logger                // Logger for task manager operations
	taskMu     sync.Mutex                    // Mutex for thread-safe access to processors
	metrics    *metrics.Metrics              // Metrics for task manager operations
}

/*****************************************************************************
 * NAME: NewTaskManager
 *
 * DESCRIPTION:
 *     Creates a new task manager instance.
 *     Initializes the processor map and logger.
 *
 * PARAMETERS:
 *     log - Logger instance for task manager operations
 *     metrics - Metrics for task manager operations
 *
 * RETURNS:
 *     *TaskManager - Initialized task manager instance
 *****************************************************************************/
func NewTaskManager(log *logger.Logger, metrics *metrics.Metrics) *TaskManager {
	return &TaskManager{
		processors: make(map[pb.TaskType]TaskProcessor),
		logger:     log.WithModule("task-manager"),
		metrics:    metrics,
	}
}

/*****************************************************************************
 * NAME: RegisterProcessor
 *
 * DESCRIPTION:
 *     Registers a new task processor.
 *     Thread-safe operation that adds a processor to the manager's processor map.
 *
 * PARAMETERS:
 *     processor - Task processor to register
 *****************************************************************************/
func (tm *TaskManager) RegisterProcessor(processor TaskProcessor) {
	tm.taskMu.Lock()
	defer tm.taskMu.Unlock()

	tm.processors[processor.GetTaskType()] = processor
	tm.logger.Info("registered task processor",
		zap.String("task_type", processor.GetTaskType().String()))
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes a single device task.
 *     Finds the appropriate processor and delegates the task to it.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - Device task to process
 *
 * RETURNS:
 *     string - Description of the operation result
 *     error  - Error if processing fails
 *****************************************************************************/
func (tm *TaskManager) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	start := time.Now()
	defer func() {
		tm.metrics.TaskProcessingTime.Observe(time.Since(start).Seconds())
		tm.metrics.TaskTotal.Inc()
		tm.metrics.TaskByType.WithLabelValues(task.TaskType.String()).Inc()
		tm.metrics.TaskByAction.WithLabelValues(task.TaskAction.String()).Inc()
	}()

	tm.taskMu.Lock()
	processor, exists := tm.processors[task.TaskType]
	tm.taskMu.Unlock()

	if !exists {
		errMsg := fmt.Sprintf("no processor found for task type: %s", task.TaskType.String())
		tm.metrics.TaskErrors.WithLabelValues(task.TaskType.String(), "no_processor").Inc()
		return errMsg, fmt.Errorf("%s", errMsg)
	}

	tm.logger.Info("processing task",
		zap.String("task_type", task.TaskType.String()),
		zap.String("task_action", task.TaskAction.String()))

	desc, err := processor.ProcessTask(ctx, task)
	if err != nil {
		tm.metrics.TaskErrors.WithLabelValues(task.TaskType.String(), err.Error()).Inc()
		tm.metrics.TaskFailuresByType.WithLabelValues(task.TaskType.String()).Inc()
	}
	return desc, err
}

/*****************************************************************************
 * NAME: TaskTxStatus
 *
 * DESCRIPTION:
 *     Represents the execution status of a task transaction.
 *
 * FIELDS:
 *     TxID    - Transaction ID
 *     ErrCode - Error code (0 for success, non-zero for failure)
 *     Desc    - Description of the execution result
 *****************************************************************************/
type TaskTxStatus struct {
	TxID    string // Transaction ID
	ErrCode int32  // Error code (0 for success, non-zero for failure)
	Desc    string // Description of the execution result
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process for all processors.
 *     This method is called at the beginning of a full sync operation.
 *
 * RETURNS:
 *     error - Error if start fails for any processor
 *****************************************************************************/
func (tm *TaskManager) StartFullSync() error {
	start := time.Now()
	defer func() {
		tm.metrics.FullSyncDuration.Observe(time.Since(start).Seconds())
		tm.metrics.FullSyncTotal.Inc()
	}()

	tm.logger.Info("starting full synchronization for all processors")

	tm.taskMu.Lock()
	defer tm.taskMu.Unlock()

	for taskType, processor := range tm.processors {
		tm.logger.Info("starting full sync for processor",
			zap.String("task_type", taskType.String()))

		if err := processor.StartFullSync(); err != nil {
			tm.metrics.SyncErrors.Inc()
			tm.logger.Error("failed to start full sync for processor",
				zap.String("task_type", taskType.String()),
				zap.Error(err))
			//return err
			continue
		}
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process for all processors in reverse order of registration.
 *     This method is called at the end of a full sync operation.
 *     The reverse order ensures that dependent modules are cleaned up before their dependencies.
 *****************************************************************************/
func (tm *TaskManager) EndFullSync() {
	tm.logger.Info("ending full synchronization for all processors in reverse order")

	tm.taskMu.Lock()
	defer tm.taskMu.Unlock()

	// Collect all processor types
	taskTypes := make([]pb.TaskType, 0, len(tm.processors))
	for taskType := range tm.processors {
		taskTypes = append(taskTypes, taskType)
	}

	// Call EndFullSync in reverse order of registration
	// Registration order is: Interface, WAN, LAN, Policy, DHCP, WAN Group
	// So cleanup order should be: WAN Group, DHCP, Policy, LAN, WAN, Interface
	for i := len(taskTypes) - 1; i >= 0; i-- {
		taskType := taskTypes[i]
		processor := tm.processors[taskType]
		tm.logger.Info("ending full sync for processor",
			zap.String("task_type", taskType.String()),
			zap.Int("reverse_order_index", len(taskTypes)-i))
		processor.EndFullSync()
	}
}

/*****************************************************************************
 * NAME: ProcessTasks
 *
 * DESCRIPTION:
 *     Processes multiple task transactions and returns their execution status.
 *     Each task transaction may contain multiple device tasks.
 *     If any device task in a transaction fails, the remaining tasks in that
 *     transaction will still be processed. The transaction will be marked as failed
 *     if any task fails, and all error messages will be concatenated in the description.
 *
 * PARAMETERS:
 *     ctx   - Context for the operation
 *     tasks - List of task transactions to process
 *
 * RETURNS:
 *     []*pb.TaskTxStatus - List of task transaction statuses
 *****************************************************************************/
func (tm *TaskManager) ProcessTasks(ctx context.Context, tasks []*pb.TaskTx) []*pb.TaskTxStatus {
	statuses := make([]*pb.TaskTxStatus, 0, len(tasks))

	for _, taskTx := range tasks {
		tm.logger.Info("processing task transaction",
			zap.String("tx_id", taskTx.TxId),
			zap.Int("device_task_count", len(taskTx.DeviceTasks)))

		// Create context with transaction ID for unified logging
		txCtx := context.WithValue(ctx, "tx_id", taskTx.TxId)

		// Process each device task in the transaction
		isSuccess := true
		var errorDescs []string

		for _, deviceTask := range taskTx.DeviceTasks {
			desc, err := tm.ProcessTask(txCtx, deviceTask)
			if err != nil {
				isSuccess = false
				// Build error message with task type and action
				errorMsg := fmt.Sprintf("[%s:%s] %s: %v",
					deviceTask.TaskType.String(),
					deviceTask.TaskAction.String(),
					desc,
					err)
				errorDescs = append(errorDescs, errorMsg)

				tm.metrics.TaskErrors.WithLabelValues(deviceTask.TaskType.String(), err.Error()).Inc()
				tm.metrics.TaskFailuresByType.WithLabelValues(deviceTask.TaskType.String()).Inc()
				tm.logger.Error("device task failed",
					zap.String("tx_id", taskTx.TxId),
					zap.String("task_type", deviceTask.TaskType.String()),
					zap.String("task_action", deviceTask.TaskAction.String()),
					zap.String("description", desc),
					zap.Error(err))
				// Continue processing next task instead of breaking
			}
		}

		// Create status for this transaction
		status := &pb.TaskTxStatus{
			TxId:    taskTx.TxId,
			ErrCode: 0,
			Desc:    "success",
		}

		if !isSuccess {
			status.ErrCode = 1
			// Concatenate all error messages
			status.Desc = strings.Join(errorDescs, "; ")
		}

		statuses = append(statuses, status)
	}

	return statuses
}
