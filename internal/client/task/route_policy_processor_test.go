/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      route_policy_processor_test.go
 *
 * DESCRIPTION :    Unit tests for route policy processor
 *
 * AUTHOR :         wei
 *
 * HISTORY :        05/06/2025  create
 ******************************************************************************/

package task

import (
	"agent/internal/logger"
	pb "agent/internal/pb"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewRoutePolicyProcessor(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	assert.NotNil(t, processor)
	assert.Equal(t, pb.TaskType_TASK_ROUTE_POLICY, processor.GetTaskType())
	assert.NotNil(t, processor.localConfigs)
	assert.NotNil(t, processor.cookieToID)
	assert.False(t, processor.fullSyncInProgress)
}

func TestRoutePolicyProcessor_GetTaskType(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_ROUTE_POLICY, taskType)
}

func TestRoutePolicyProcessor_StartFullSync(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	// Mock the refreshLocalConfigs to avoid actual floweye calls
	originalConfigs := processor.localConfigs
	processor.localConfigs = make(map[uint32]*RoutePolicyConfig)

	err := processor.StartFullSync()

	// Should not error even if floweye commands fail in test environment
	assert.True(t, processor.fullSyncInProgress)

	// Restore original configs
	processor.localConfigs = originalConfigs
}

func TestRoutePolicyProcessor_EndFullSync(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	// Set up test state
	processor.fullSyncInProgress = true
	processor.localConfigs = map[uint32]*RoutePolicyConfig{
		12345: {
			Cookie: 12345,
			ID:     1,
			Desc:   "Test Policy",
		},
	}

	processor.EndFullSync()

	assert.False(t, processor.fullSyncInProgress)
	assert.Empty(t, processor.localConfigs)
	assert.Empty(t, processor.cookieToID)
}

func TestParseRoutePolicyFromList(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int
		hasError bool
	}{
		{
			name:     "empty input",
			input:    "",
			expected: 0,
			hasError: false,
		},
		{
			name:     "single policy",
			input:    `{"polno":1,"desc":"Test Policy","disabled":0,"srcip":"any","dstip":"any","action":"route"}`,
			expected: 1,
			hasError: false,
		},
		{
			name:     "invalid json",
			input:    `{"polno":1,"desc":"Test Policy"`,
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configs, err := ParseRoutePolicyFromList(tt.input)

			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Len(t, configs, tt.expected)
			}
		})
	}
}

func TestParseRoutePolicyFromGet(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected *RoutePolicyConfig
		hasError bool
	}{
		{
			name: "valid policy config",
			input: `id=1
cookie=12345
desc=Test Policy
disable=0
action=route
actpxyname=wan1`,
			expected: &RoutePolicyConfig{
				ID:      1,
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Action:  "route",
				Proxy:   "wan1",
			},
			hasError: false,
		},
		{
			name:     "missing cookie",
			input:    `id=1\ndesc=Test Policy`,
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := ParseRoutePolicyFromGet(tt.input)

			if tt.hasError {
				assert.Error(t, err)
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				require.NotNil(t, config)
				assert.Equal(t, tt.expected.ID, config.ID)
				assert.Equal(t, tt.expected.Cookie, config.Cookie)
				assert.Equal(t, tt.expected.Desc, config.Desc)
				assert.Equal(t, tt.expected.Disable, config.Disable)
				assert.Equal(t, tt.expected.Action, config.Action)
				assert.Equal(t, tt.expected.Proxy, config.Proxy)
			}
		})
	}
}

func TestCompareRoutePolicyConfig(t *testing.T) {
	log := logger.NewLogger()

	tests := []struct {
		name        string
		task        *pb.RoutePolicyTask
		localConfig *RoutePolicyConfig
		expected    bool
	}{
		{
			name: "matching route configs",
			task: &pb.RoutePolicyTask{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Schtime: 0,
				Action:  pb.RoutePolicyAction_ROUTE_ACTION_ROUTE,
				RouteConfig: &pb.RouteActionConfig{
					Proxy: "wan1",
					NextHop: &pb.IpAddress{
						Ip: &pb.IpAddress_IpString{IpString: "***********"},
					},
				},
			},
			localConfig: &RoutePolicyConfig{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				SchTime: 0,
				Action:  "route",
				Proxy:   "wan1",
				NextHop: "***********",
			},
			expected: true,
		},
		{
			name: "matching NAT configs",
			task: &pb.RoutePolicyTask{
				Cookie:  12346,
				Desc:    "Test NAT Policy",
				Disable: false,
				Schtime: 0,
				Action:  pb.RouteAction_ROUTE_ACTION_NAT,
				RouteConfig: &pb.RouteActionConfig{
					Proxy: "wan1",
				},
				NatConfig: &pb.NatActionConfig{
					NatIp: &pb.NatIpPool{
						Ip: []*pb.IpAddress{
							{Ip: &pb.IpAddress_IpString{IpString: "**********"}},
						},
					},
					FullConeNat: true,
				},
			},
			localConfig: &RoutePolicyConfig{
				Cookie:      12346,
				Desc:        "Test NAT Policy",
				Disable:     false,
				SchTime:     0,
				Action:      "nat",
				Proxy:       "wan1",
				NatIP:       "**********",
				FullConeNat: true,
			},
			expected: true,
		},
		{
			name: "different descriptions",
			task: &pb.RoutePolicyTask{
				Cookie:  12345,
				Desc:    "Test Policy Updated",
				Disable: false,
				Action:  pb.RouteAction_ROUTE_ACTION_ROUTE,
				RouteConfig: &pb.RouteActionConfig{
					Proxy: "wan1",
				},
			},
			localConfig: &RoutePolicyConfig{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Action:  "route",
				Proxy:   "wan1",
			},
			expected: false,
		},
		{
			name: "different actions",
			task: &pb.RoutePolicyTask{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Action:  pb.RouteAction_ROUTE_ACTION_NAT,
				RouteConfig: &pb.RouteActionConfig{
					Proxy: "wan1",
				},
			},
			localConfig: &RoutePolicyConfig{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Action:  "route",
				Proxy:   "wan1",
			},
			expected: false,
		},
		{
			name: "different proxy",
			task: &pb.RoutePolicyTask{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Action:  pb.RouteAction_ROUTE_ACTION_ROUTE,
				RouteConfig: &pb.RouteActionConfig{
					Proxy: "wan2",
				},
			},
			localConfig: &RoutePolicyConfig{
				Cookie:  12345,
				Desc:    "Test Policy",
				Disable: false,
				Action:  "route",
				Proxy:   "wan1",
			},
			expected: false,
		},
		{
			name:        "nil task",
			task:        nil,
			localConfig: &RoutePolicyConfig{},
			expected:    false,
		},
		{
			name: "nil local config",
			task: &pb.RoutePolicyTask{
				Cookie: 12345,
			},
			localConfig: nil,
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompareRoutePolicyConfig(log, tt.task, tt.localConfig)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRoutePolicyProcessor_ProcessTask(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name      string
		task      *pb.DeviceTask
		expectErr bool
	}{
		{
			name: "nil route policy task",
			task: &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_ROUTE_POLICY,
				TaskAction: pb.TaskAction_NEW_CONFIG,
				// RoutePolicyTask is nil
			},
			expectErr: true,
		},
		{
			name: "unknown task action",
			task: &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_ROUTE_POLICY,
				TaskAction: pb.TaskAction_UNKNOWN_ACTION,
				RoutePolicyTask: &pb.RoutePolicyTask{
					Cookie: 12345,
					Desc:   "Test Policy",
				},
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := processor.ProcessTask(context.Background(), tt.task)

			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRoutePolicyProcessor_buildAddressString(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name      string
		selectors []*pb.AddressSelector
		expected  string
	}{
		{
			name:      "empty selectors",
			selectors: []*pb.AddressSelector{},
			expected:  "any",
		},
		{
			name: "single IP address",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_Ip{
						Ip: &pb.IpAddress{
							Ip: &pb.IpAddress_IpString{IpString: "***********/24"},
						},
					},
				},
			},
			expected: "***********/24",
		},
		{
			name: "IP range",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_IpRange{
						IpRange: &pb.IpRange{
							StartIp: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{IpString: "***********"},
							},
							EndIp: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{IpString: "***********00"},
							},
						},
					},
				},
			},
			expected: "***********-***********00",
		},
		{
			name: "user group",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_UserGroupId{
						UserGroupId: 5,
					},
				},
			},
			expected: "usergroup:5",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.buildAddressString(tt.selectors)
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRoutePolicyProcessor_buildPortString(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name     string
		portSpec *pb.PortSpec
		expected string
	}{
		{
			name: "single port",
			portSpec: &pb.PortSpec{
				Ports: []*pb.PortRange{
					{Start: 80, End: 80},
				},
			},
			expected: "80",
		},
		{
			name: "port range",
			portSpec: &pb.PortSpec{
				Ports: []*pb.PortRange{
					{Start: 8000, End: 8999},
				},
			},
			expected: "8000-8999",
		},
		{
			name: "empty port spec",
			portSpec: &pb.PortSpec{
				Ports: []*pb.PortRange{},
			},
			expected: "any",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.buildPortString(tt.portSpec)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRoutePolicyProcessor_parseIPv4ToUint32(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name     string
		ipStr    string
		expected uint32
		hasError bool
	}{
		{
			name:     "valid IP ***********",
			ipStr:    "***********",
			expected: 0xC0A80101, // *********** in hex
			hasError: false,
		},
		{
			name:     "valid IP ********",
			ipStr:    "********",
			expected: 0x0A000001, // ******** in hex
			hasError: false,
		},
		{
			name:     "valid IP 0.0.0.0",
			ipStr:    "0.0.0.0",
			expected: 0x00000000,
			hasError: false,
		},
		{
			name:     "valid IP ***************",
			ipStr:    "***************",
			expected: 0xFFFFFFFF,
			hasError: false,
		},
		{
			name:     "invalid IP format",
			ipStr:    "192.168.1",
			expected: 0,
			hasError: true,
		},
		{
			name:     "invalid octet",
			ipStr:    "192.168.1.256",
			expected: 0,
			hasError: true,
		},
		{
			name:     "non-numeric octet",
			ipStr:    "192.168.1.abc",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.parseIPv4ToUint32(tt.ipStr)

			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestRoutePolicyProcessor_parseIPWithPrefix(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name     string
		ipStr    string
		expected IPWithPrefix
		hasError bool
	}{
		{
			name:  "CIDR notation /24",
			ipStr: "***********/24",
			expected: IPWithPrefix{
				IP:           0xC0A80100, // ***********
				PrefixLength: 24,
				OriginalStr:  "***********/24",
			},
			hasError: false,
		},
		{
			name:  "CIDR notation /32",
			ipStr: "********/32",
			expected: IPWithPrefix{
				IP:           0x0A000001, // ********
				PrefixLength: 32,
				OriginalStr:  "********/32",
			},
			hasError: false,
		},
		{
			name:  "single IP without prefix",
			ipStr: "**********",
			expected: IPWithPrefix{
				IP:           0xAC100001, // **********
				PrefixLength: 32,         // Default to /32
				OriginalStr:  "**********",
			},
			hasError: false,
		},
		{
			name:     "invalid CIDR format",
			ipStr:    "***********/24/extra",
			expected: IPWithPrefix{},
			hasError: true,
		},
		{
			name:     "invalid prefix length",
			ipStr:    "***********/33",
			expected: IPWithPrefix{},
			hasError: true,
		},
		{
			name:     "non-numeric prefix",
			ipStr:    "***********/abc",
			expected: IPWithPrefix{},
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.parseIPWithPrefix(tt.ipStr)

			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected.IP, result.IP)
				assert.Equal(t, tt.expected.PrefixLength, result.PrefixLength)
				assert.Equal(t, tt.expected.OriginalStr, result.OriginalStr)
			}
		})
	}
}

func TestRoutePolicyProcessor_extractDestinationIPs(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name     string
		dstStr   string
		expected []IPWithPrefix
	}{
		{
			name:     "empty destination",
			dstStr:   "",
			expected: []IPWithPrefix{},
		},
		{
			name:     "any destination",
			dstStr:   "any",
			expected: []IPWithPrefix{},
		},
		{
			name:   "single CIDR",
			dstStr: "***********/24",
			expected: []IPWithPrefix{
				{
					IP:           0xC0A80100,
					PrefixLength: 24,
					OriginalStr:  "***********/24",
				},
			},
		},
		{
			name:   "multiple CIDRs",
			dstStr: "***********/24,10.0.0.0/16",
			expected: []IPWithPrefix{
				{
					IP:           0xC0A80100,
					PrefixLength: 24,
					OriginalStr:  "***********/24",
				},
				{
					IP:           0x0A000000,
					PrefixLength: 16,
					OriginalStr:  "10.0.0.0/16",
				},
			},
		},
		{
			name:   "single IP without prefix",
			dstStr: "**********",
			expected: []IPWithPrefix{
				{
					IP:           0xAC100001,
					PrefixLength: 32,
					OriginalStr:  "**********",
				},
			},
		},
		{
			name:     "IP group (should be skipped)",
			dstStr:   "ipgroup:5",
			expected: []IPWithPrefix{},
		},
		{
			name:   "mixed IP and group",
			dstStr: "***********/24,ipgroup:5,********",
			expected: []IPWithPrefix{
				{
					IP:           0xC0A80100,
					PrefixLength: 24,
					OriginalStr:  "***********/24",
				},
				{
					IP:           0x0A000001,
					PrefixLength: 32,
					OriginalStr:  "********",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.extractDestinationIPs(tt.dstStr)
			assert.NoError(t, err)
			assert.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				if i < len(result) {
					assert.Equal(t, expected.IP, result[i].IP)
					assert.Equal(t, expected.PrefixLength, result[i].PrefixLength)
					assert.Equal(t, expected.OriginalStr, result[i].OriginalStr)
				}
			}
		})
	}
}

func TestRoutePolicyProcessor_getMostSpecificIP(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	tests := []struct {
		name           string
		ips            []IPWithPrefix
		expectedPrefix uint32
		expectedMinIP  uint32
	}{
		{
			name:           "empty list",
			ips:            []IPWithPrefix{},
			expectedPrefix: 0,
			expectedMinIP:  0xFFFFFFFF,
		},
		{
			name: "single IP",
			ips: []IPWithPrefix{
				{IP: 0xC0A80101, PrefixLength: 24},
			},
			expectedPrefix: 24,
			expectedMinIP:  0xC0A80101,
		},
		{
			name: "multiple IPs with different prefixes",
			ips: []IPWithPrefix{
				{IP: 0xC0A80100, PrefixLength: 24}, // ***********/24
				{IP: 0xC0A80101, PrefixLength: 32}, // ***********/32
				{IP: 0x0A000000, PrefixLength: 16}, // 10.0.0.0/16
			},
			expectedPrefix: 32, // Highest prefix
			expectedMinIP:  0x0A000000, // Lowest IP (10.0.0.0)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prefix, minIP := processor.getMostSpecificIP(tt.ips)
			assert.Equal(t, tt.expectedPrefix, prefix)
			assert.Equal(t, tt.expectedMinIP, minIP)
		})
	}
}



func TestRoutePolicyProcessor_sortPoliciesForLPM(t *testing.T) {
	log := logger.NewLogger()
	processor := NewRoutePolicyProcessor(log)

	// 测试数据：不同前缀长度和IP地址的策略
	policies := []LPMPolicyInfo{
		{
			ID:     1,
			Cookie: 1001,
			DestinationIPs: []IPWithPrefix{
				{IP: 0xC0A80100, PrefixLength: 24}, // ***********/24
			},
		},
		{
			ID:     2,
			Cookie: 1002,
			DestinationIPs: []IPWithPrefix{
				{IP: 0xC0A80101, PrefixLength: 32}, // ***********/32
			},
		},
		{
			ID:     3,
			Cookie: 1003,
			DestinationIPs: []IPWithPrefix{
				{IP: 0x0A000000, PrefixLength: 16}, // 10.0.0.0/16
			},
		},
		{
			ID:     4,
			Cookie: 1004,
			DestinationIPs: []IPWithPrefix{}, // any destination (无具体目标)
		},
	}

	sorted := processor.sortPoliciesForLPM(policies)

	// 期望的排序结果（按 LPM 规则）：
	// 1. ***********/32 (前缀长度最高 /32)
	// 2. ***********/24 (前缀长度次高 /24)
	// 3. 10.0.0.0/16 (前缀长度最低 /16)
	// 4. any destination (无具体目标，优先级最低)

	assert.Len(t, sorted, 4)
	assert.Equal(t, uint32(1002), sorted[0].Cookie) // ***********/32
	assert.Equal(t, uint32(1001), sorted[1].Cookie) // ***********/24
	assert.Equal(t, uint32(1003), sorted[2].Cookie) // 10.0.0.0/16
	assert.Equal(t, uint32(1004), sorted[3].Cookie) // any destination
}
