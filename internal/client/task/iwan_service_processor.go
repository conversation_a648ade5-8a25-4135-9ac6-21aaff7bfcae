/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      iwan_service_processor.go
 *
 * DESCRIPTION :    iWAN Service processor implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/25/2025  create
 ******************************************************************************/

package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"agent/internal/logger"
	pb "agent/internal/pb"
	"agent/internal/utils"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: IwanServiceProcessor
 *
 * DESCRIPTION:
 *     Processes TASK_IWAN_SERVICE type tasks.
 *     Handles iWAN Service configuration operations.
 *     Implements the TaskProcessor interface.
 *
 * FIELDS:
 *     logger             - Logger for iWAN Service processor operations
 *     localConfigs       - Cache of local iWAN Service configurations (used for full sync redundant deletion)
 *     localNameToID      - Mapping from service name to ID (used for full sync redundant deletion)
 *     workingConfigs     - Cache of working iWAN Service configurations (used for all operations)
 *     workingNameToID    - Mapping from service name to ID (used for all operations)
 *     fullSyncInProgress - Flag indicating if full sync is in progress
 *****************************************************************************/
type IwanServiceProcessor struct {
	logger *logger.Logger // Logger for iWAN Service processor operations

	// Full sync redundant deletion specific configurations
	localConfigs  map[string]*IwanServiceConfig // Cache of local iWAN Service configurations (used for full sync redundant deletion)
	localNameToID map[string]int                // Mapping from service name to ID (used for full sync redundant deletion)

	// Working configurations for all operations
	workingConfigs  map[string]*IwanServiceConfig // Cache of working iWAN Service configurations (used for all operations)
	workingNameToID map[string]int                // Mapping from service name to ID (used for all operations)

	fullSyncInProgress bool // Flag indicating if full sync is in progress
}

/*****************************************************************************
 * NAME: NewIwanServiceProcessor
 *
 * DESCRIPTION:
 *     Creates a new iWAN Service processor instance.
 *     Initializes both local and working configuration caches.
 *
 * PARAMETERS:
 *     log - Logger instance for processor operations
 *
 * RETURNS:
 *     *IwanServiceProcessor - Initialized iWAN Service processor
 *****************************************************************************/
func NewIwanServiceProcessor(log *logger.Logger) *IwanServiceProcessor {
	processor := &IwanServiceProcessor{
		logger: log.WithModule("iwan-service-processor"),

		// Initialize full sync redundant deletion configurations
		localConfigs:  make(map[string]*IwanServiceConfig),
		localNameToID: make(map[string]int),

		// Initialize working configurations for all operations
		workingConfigs:  make(map[string]*IwanServiceConfig),
		workingNameToID: make(map[string]int),

		fullSyncInProgress: false,
	}

	return processor
}

/*****************************************************************************
 * NAME: GetTaskType
 *
 * DESCRIPTION:
 *     Returns the task type this processor handles.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     pb.TaskType - The task type (TASK_IWAN_SERVICE)
 *****************************************************************************/
func (p *IwanServiceProcessor) GetTaskType() pb.TaskType {
	return pb.TaskType_TASK_IWAN_SERVICE
}

/*****************************************************************************
 * NAME: ProcessTask
 *
 * DESCRIPTION:
 *     Processes an iWAN Service configuration task.
 *     Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG actions.
 *     Implements the TaskProcessor interface.
 *
 * PARAMETERS:
 *     ctx  - Context for the operation
 *     task - The task to process
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IwanServiceProcessor) ProcessTask(ctx context.Context, task *pb.DeviceTask) (string, error) {
	// Get iWAN Service task data
	iwanSvcTask := task.GetIwanServiceTask()
	if iwanSvcTask == nil {
		return "iWAN Service task data is empty", fmt.Errorf("iWAN Service task data is nil")
	}

	// Log task details
	p.logger.Info("Processing iWAN Service task",
		zap.String("name", iwanSvcTask.GetName()),
		zap.String("addr", utils.GetIpString(iwanSvcTask.GetAddr())),
		zap.String("action", task.TaskAction.String()),
		zap.Bool("full_sync", p.fullSyncInProgress))

	// Execute different operations based on task action
	switch task.TaskAction {
	case pb.TaskAction_NEW_CONFIG, pb.TaskAction_EDIT_CONFIG:
		return p.handleConfigChange(ctx, iwanSvcTask, task.TaskAction)
	case pb.TaskAction_DELETE_CONFIG:
		return p.handleDeleteConfig(ctx, iwanSvcTask)
	default:
		return fmt.Sprintf("unsupported task action: %s", task.TaskAction.String()),
			fmt.Errorf("unsupported task action: %s", task.TaskAction.String())
	}
}

/*****************************************************************************
 * NAME: handleConfigChange
 *
 * DESCRIPTION:
 *     Handles creating or updating an iWAN Service configuration.
 *     Used for both NEW_CONFIG and EDIT_CONFIG actions.
 *
 * PARAMETERS:
 *     ctx         - Context for the operation
 *     iwanSvcTask - iWAN Service task data
 *     taskAction  - The task action (NEW_CONFIG or EDIT_CONFIG)
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IwanServiceProcessor) handleConfigChange(ctx context.Context, iwanSvcTask *pb.IwanServiceTask, taskAction pb.TaskAction) (string, error) {
	// Convert protobuf message to unified internal data structure at the entry point
	// This is the single conversion point for the entire processing pipeline
	requestedConfig, err := ConvertIwanServiceTaskToConfig(iwanSvcTask)
	if err != nil {
		p.logger.Error("failed to convert iWAN Service task to config",
			zap.String("name", iwanSvcTask.GetName()),
			zap.Error(err))
		return fmt.Sprintf("Failed to convert iWAN Service configuration: %v", err), err
	}

	// Validate required fields using converted data
	if requestedConfig.Name == "" {
		return "iWAN Service name is required", fmt.Errorf("iWAN Service name is required")
	}

	if requestedConfig.Addr == "" {
		return "iWAN Service address is required", fmt.Errorf("iWAN Service address is required")
	}

	if requestedConfig.MTU < 500 || requestedConfig.MTU > 4700 {
		return "iWAN Service MTU must be between 500 and 4700", fmt.Errorf("iWAN Service MTU must be between 500 and 4700")
	}

	if requestedConfig.Pool <= 0 {
		return "iWAN Service pool ID is required", fmt.Errorf("iWAN Service pool ID is required")
	}

	// No need to validate authentication type as we only support local authentication

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := requestedConfig.Name // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if iWAN Service exists using working configs
	existingConfig, exists := p.workingConfigs[requestedConfig.Name]

	/*
		// If iWAN Service exists, check if configuration is the same
		if exists && CompareIwanServiceConfig(p.logger, requestedConfig, existingConfig) {
			p.logger.Info("iWAN Service configuration is unchanged, skipping update",
				zap.String("name", requestedConfig.Name))

			// Remove from local configs if in full sync mode
			if p.fullSyncInProgress {
				delete(p.localConfigs, requestedConfig.Name)
			}

			return "iWAN Service configuration is unchanged", nil
		}
	*/

	// Build command arguments using converted data
	var cmdArgs []string
	if exists {
		// Update existing iWAN Service - requires proxy ID
		cmdArgs = append(cmdArgs, "nat", "setiwansvc",
			"id="+strconv.Itoa(int(existingConfig.ID)),
			"name="+requestedConfig.Name,
			"newname="+requestedConfig.Name)
	} else {
		// Create new iWAN Service
		cmdArgs = append(cmdArgs, "nat", "addiwansvc", "name="+requestedConfig.Name)
	}

	// Add common parameters using converted data
	cmdArgs = append(cmdArgs, "addr="+requestedConfig.Addr)
	cmdArgs = append(cmdArgs, "mtu="+strconv.Itoa(int(requestedConfig.MTU)))
	cmdArgs = append(cmdArgs, "pool="+strconv.Itoa(int(requestedConfig.Pool)))

	// Add authentication type (only local is supported)
	cmdArgs = append(cmdArgs, "auth=local")

	// Add radius server ID (default 0 for local auth)
	cmdArgs = append(cmdArgs, "radsvr=0")

	// Add IPv6 prefix length (default 0)
	cmdArgs = append(cmdArgs, "prefix6len=0")

	// Execute floweye command
	p.logger.Info("Executing floweye command for iWAN Service configuration",
		zap.String("name", requestedConfig.Name),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		p.logger.Error("Failed to execute floweye command for iWAN Service configuration",
			zap.String("name", requestedConfig.Name),
			zap.Error(err),
			zap.String("output", output))
		return fmt.Sprintf("Failed to configure iWAN Service: %v", err), err
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", requestedConfig.Name),
		zap.String("output", output))

	// Refresh working configs to include the newly created/updated service
	if err := p.getConfigsForOperation(); err != nil {
		p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
		// Don't return error as the main operation was successful
	}

	// Verify configuration was applied correctly using converted data
	success, verifyErr := VerifyIwanServiceConfig(p.logger, requestedConfig)
	if verifyErr != nil {
		p.logger.Error("Failed to verify iWAN Service configuration",
			zap.Error(verifyErr))
		return fmt.Sprintf("Failed to verify iWAN Service configuration: %v", verifyErr), verifyErr
	}

	if !success {
		p.logger.Error("iWAN Service configuration verification failed")
		return "iWAN Service configuration verification failed", fmt.Errorf("verification failed")
	}

	p.logger.Info("iWAN Service configuration applied successfully",
		zap.String("name", requestedConfig.Name))

	if exists {
		return "iWAN Service configuration updated successfully", nil
	}
	return "iWAN Service configuration created successfully", nil
}

/*****************************************************************************
 * NAME: handleDeleteConfig
 *
 * DESCRIPTION:
 *     Handles deleting an iWAN Service configuration.
 *
 * PARAMETERS:
 *     ctx         - Context for the operation
 *     iwanSvcTask - iWAN Service task data
 *
 * RETURNS:
 *     string - Result message
 *     error  - Error if processing fails
 *****************************************************************************/
func (p *IwanServiceProcessor) handleDeleteConfig(ctx context.Context, iwanSvcTask *pb.IwanServiceTask) (string, error) {
	// Validate required fields
	if iwanSvcTask.GetName() == "" {
		return "iWAN Service name is required", fmt.Errorf("iWAN Service name is required")
	}

	// Register cleanup defer after validation
	if p.fullSyncInProgress {
		name := iwanSvcTask.GetName() // Copy value to avoid closure capture issues
		defer func() {
			delete(p.localConfigs, name)
		}()
	}

	// Get latest configurations for operation
	if err := p.getConfigsForOperation(); err != nil {
		return fmt.Sprintf("Failed to get configurations: %v", err), err
	}

	// Check if iWAN Service exists using working configs
	_, exists := p.workingConfigs[iwanSvcTask.GetName()]

	// If iWAN Service doesn't exist, nothing to delete
	if !exists {
		p.logger.Info("iWAN Service not found in local configuration, nothing to delete",
			zap.String("name", iwanSvcTask.GetName()))
		return "iWAN Service not found, nothing to delete", nil
	}

	// Build floweye command
	cmdArgs := []string{
		"nat", "rmvproxy", iwanSvcTask.GetName(),
	}

	// Execute floweye command
	p.logger.Info("Executing floweye command to delete iWAN Service",
		zap.String("name", iwanSvcTask.GetName()),
		zap.Strings("args", cmdArgs))

	output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
	if err != nil {
		// Handle NEXIST errors as success (idempotent delete operation)
		if strings.Contains(output, "NEXIST") || strings.Contains(err.Error(), "NEXIST") {
			p.logger.Info("iWAN Service already does not exist, treating as successful delete",
				zap.String("name", iwanSvcTask.GetName()))
		} else {
			p.logger.Error("Failed to execute floweye command to delete iWAN Service",
				zap.String("name", iwanSvcTask.GetName()),
				zap.Error(err),
				zap.String("output", output))
			return fmt.Sprintf("Failed to delete iWAN Service: %v", err), err
		}
	}

	p.logger.Debug("Floweye command executed successfully",
		zap.String("name", iwanSvcTask.GetName()),
		zap.String("output", output))

	// Skip post-delete verification for improved performance and reliability

	p.logger.Info("iWAN Service deleted successfully",
		zap.String("name", iwanSvcTask.GetName()))

	return "iWAN Service deleted successfully", nil
}

/*****************************************************************************
 * NAME: StartFullSync
 *
 * DESCRIPTION:
 *     Starts a full synchronization process.
 *     Refreshes the local configuration cache.
 *     Implements the TaskProcessor interface.
 *
 * RETURNS:
 *     error - Error if starting full sync fails
 *****************************************************************************/
func (p *IwanServiceProcessor) StartFullSync() error {
	p.logger.Info("Starting full sync for iWAN Service")
	p.fullSyncInProgress = true

	// Refresh local configurations
	if err := p.refreshLocalConfigs(); err != nil {
		p.fullSyncInProgress = false
		return fmt.Errorf("failed to refresh local configurations: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: EndFullSync
 *
 * DESCRIPTION:
 *     Ends a full synchronization process.
 *     Deletes any local configurations that were not included in the sync.
 *     Implements the TaskProcessor interface.
 *****************************************************************************/
func (p *IwanServiceProcessor) EndFullSync() {
	p.logger.Info("Ending full sync for iWAN Service")

	// Delete any remaining local configurations
	if p.fullSyncInProgress {
		p.logger.Info("Cleaning up iWAN Services not included in full sync",
			zap.Int("count", len(p.localConfigs)))

		for name := range p.localConfigs {
			// Delete iWAN Service
			p.logger.Info("Deleting iWAN Service not included in full sync",
				zap.String("name", name))

			cmdArgs := []string{
				"nat", "rmvproxy", name,
			}

			output, err := utils.ExecuteCommand(p.logger, 10, "floweye", cmdArgs...)
			if err != nil {
				p.logger.Error("Failed to delete iWAN Service during cleanup",
					zap.String("name", name),
					zap.Error(err),
					zap.String("output", output))
			}
		}
	}

	p.fullSyncInProgress = false

	// Clean up resources
	p.localConfigs = make(map[string]*IwanServiceConfig)
	p.localNameToID = make(map[string]int)
	p.workingConfigs = make(map[string]*IwanServiceConfig)
	p.workingNameToID = make(map[string]int)
}

/*****************************************************************************
 * NAME: fetchIwanServiceConfigs
 *
 * DESCRIPTION:
 *     Fetches iWAN Service configurations from floweye.
 *     This is the common logic used by both local and working config refresh.
 *
 * RETURNS:
 *     map[string]*IwanServiceConfig - iWAN Service configurations by name
 *     map[string]int                - Service name to ID mapping
 *     error                         - Error if fetch fails
 *****************************************************************************/
func (p *IwanServiceProcessor) fetchIwanServiceConfigs() (map[string]*IwanServiceConfig, map[string]int, error) {
	configs := make(map[string]*IwanServiceConfig)
	nameToID := make(map[string]int)

	// Get iWAN Service configurations using existing function
	iwanServiceConfigs, err := GetLocalIwanServiceConfigs(p.logger)
	if err != nil {
		p.logger.Error("failed to get iWAN Service configurations", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to get iWAN Service configurations: %w", err)
	}

	// Build name to ID mapping
	for name, config := range iwanServiceConfigs {
		configs[name] = config
		nameToID[name] = int(config.ID)
	}

	return configs, nameToID, nil
}

/*****************************************************************************
 * NAME: refreshLocalConfigs
 *
 * DESCRIPTION:
 *     Refreshes local iWAN Service configurations.
 *     Used only during StartFullSync to populate localConfigs for redundant deletion.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IwanServiceProcessor) refreshLocalConfigs() error {
	p.logger.Debug("refreshing local iWAN Service configurations")

	configs, nameToID, err := p.fetchIwanServiceConfigs()
	if err != nil {
		return err
	}

	// Update local caches (used for full sync redundant deletion)
	p.localConfigs = configs
	p.localNameToID = nameToID

	p.logger.Debug("refreshed local iWAN Service configurations",
		zap.Int("services", len(p.localConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: refreshWorkingConfigs
 *
 * DESCRIPTION:
 *     Refreshes working iWAN Service configurations.
 *     This is the primary cache used for all operations.
 *     Can be refreshed independently during full sync.
 *
 * RETURNS:
 *     error - Error if refresh fails
 *****************************************************************************/
func (p *IwanServiceProcessor) refreshWorkingConfigs() error {
	p.logger.Debug("refreshing working iWAN Service configurations")

	configs, nameToID, err := p.fetchIwanServiceConfigs()
	if err != nil {
		return fmt.Errorf("failed to fetch configs for working cache: %w", err)
	}

	// Update working caches (used for all operations)
	p.workingConfigs = configs
	p.workingNameToID = nameToID

	p.logger.Debug("refreshed working iWAN Service configurations",
		zap.Int("services", len(p.workingConfigs)))

	return nil
}

/*****************************************************************************
 * NAME: getConfigsForOperation
 *
 * DESCRIPTION:
 *     Gets configurations for operations like create, update, delete, etc.
 *     Always uses workingConfigs which can be refreshed independently.
 *     This simplifies the logic - working configs are the primary cache for all operations.
 *
 * RETURNS:
 *     error - Error if getting configs fails
 *****************************************************************************/
func (p *IwanServiceProcessor) getConfigsForOperation() error {
	// Always use working configs for operations
	// This simplifies logic and ensures consistency
	return p.refreshWorkingConfigs()
}
