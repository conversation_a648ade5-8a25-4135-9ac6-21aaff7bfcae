/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      daemon.go
 *
 * DESCRIPTION :    Daemon process management implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package daemon

import (
	"fmt"
	"os"
	"path/filepath"

	"agent/internal/logger"

	"github.com/sevlyar/go-daemon"
)

const (
	defaultPIDFile = "/var/run/agent.pid"
)

/*****************************************************************************
 * NAME: Daemon
 *
 * DESCRIPTION:
 *     Manages a daemon process with PID file and lock file handling.
 *     Ensures only one instance of the agent runs at a time.
 *
 * FIELDS:
 *     context  - Daemon context for process management
 *     isParent - Flag indicating if current process is the parent
 *****************************************************************************/
type Daemon struct {
	context  *daemon.Context
	isParent bool
}

/*****************************************************************************
 * NAME: New
 *
 * DESCRIPTION:
 *     Creates a new daemon instance with default PID and lock file paths.
 *     Uses logging configuration from the provided LogConfig.
 *
 * PARAMETERS:
 *     logConfig - Logger configuration from config file
 *
 * RETURNS:
 *     *Daemon - A new daemon instance
 *****************************************************************************/
func New(configPath string, logConfig *logger.LogConfig) *Daemon {
	// Get the executable path
	execPath, err := os.Executable()
	if err != nil {
		fmt.Printf("Warning: failed to get executable path: %v\n", err)
		execPath = os.Args[0]
	}

	// Get log file path from config
	logFile := "/var/log/agent.log"
	if len(logConfig.Outputs) > 0 {
		for _, output := range logConfig.Outputs {
			if output.Type == logger.TypeFile && output.File != "" {
				logFile = output.File
				break
			}
		}
	}

	return &Daemon{
		context: &daemon.Context{
			PidFileName: defaultPIDFile,
			PidFilePerm: 0o644,
			LogFileName: logFile,
			LogFilePerm: 0o640,
			WorkDir:     "/var/run",
			Umask:       0o27,
			Args:        []string{execPath, "-config", configPath},
		},
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Initializes and daemonizes the process.
 *     Creates necessary directories, acquires lock, and forks the process.
 *
 * RETURNS:
 *     error - Error if daemonization fails
 *****************************************************************************/
func (d *Daemon) Start() error {
	fmt.Printf("Starting daemon process...\n")

	// Create necessary directories
	if err := os.MkdirAll(filepath.Dir(d.context.PidFileName), 0o755); err != nil {
		return fmt.Errorf("failed to create pid directory: %w", err)
	}
	if err := os.MkdirAll(filepath.Dir(d.context.LogFileName), 0o755); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}
	if err := os.MkdirAll(d.context.WorkDir, 0o755); err != nil {
		return fmt.Errorf("failed to create work directory: %w", err)
	}

	// Check if agent is already running
	process, err := d.context.Search()
	if err == nil && process != nil {
		return fmt.Errorf("agent already running with PID %d", process.Pid)
	}

	// Try to daemonize
	child, err := d.context.Reborn()
	if err != nil {
		return fmt.Errorf("failed to daemonize: %w", err)
	}
	if child != nil {
		fmt.Printf("Parent process exiting, child PID: %d\n", child.Pid)
		d.isParent = true
		return nil
	}

	// Child process continues running
	fmt.Printf("Child process started, PID: %d\n", os.Getpid())

	// Change to working directory
	if err := os.Chdir(d.context.WorkDir); err != nil {
		return fmt.Errorf("failed to change working directory: %w", err)
	}

	return nil
}

/*****************************************************************************
 * NAME: IsParent
 *
 * DESCRIPTION:
 *     Determines if the current process is the parent process.
 *
 * RETURNS:
 *     bool - True if current process is the parent
 *****************************************************************************/
func (d *Daemon) IsParent() bool {
	return d.isParent
}
