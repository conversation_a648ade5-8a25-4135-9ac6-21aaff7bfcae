/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      logger.go
 *
 * DESCRIPTION :    Logger implementation based on zap
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Global atomicLevel variable for dynamically adjusting log level
var globalAtomicLevel = zap.NewAtomicLevel()

/*****************************************************************************
 * NAME: LogLevel
 *
 * DESCRIPTION:
 *     Log level type definition
 *****************************************************************************/
type LogLevel string

const (
	LevelDebug LogLevel = "DEBUG"
	LevelInfo  LogLevel = "INFO"
	LevelWarn  LogLevel = "WARN"
	LevelError LogLevel = "ERROR"
)

/*****************************************************************************
 * NAME: ToZapLevel
 *
 * DESCRIPTION:
 *     Converts LogLevel to zapcore.Level
 *
 * RETURNS:
 *     zapcore.Level - Corresponding zap log level
 *****************************************************************************/
func (l LogLevel) ToZapLevel() zapcore.Level {
	switch strings.ToUpper(string(l)) {
	case "DEBUG":
		return zapcore.DebugLevel
	case "INFO":
		return zapcore.InfoLevel
	case "WARN":
		return zapcore.WarnLevel
	case "ERROR":
		return zapcore.ErrorLevel
	default:
		return zapcore.InfoLevel
	}
}

/*****************************************************************************
 * NAME: LogFormat
 *
 * DESCRIPTION:
 *     Log format type definition
 *****************************************************************************/
type LogFormat string

const (
	FormatText LogFormat = "text"
	FormatJSON LogFormat = "json"
)

/*****************************************************************************
 * NAME: OutputType
 *
 * DESCRIPTION:
 *     Output type definition
 *****************************************************************************/
type OutputType string

const (
	TypeFile    OutputType = "file"
	TypeConsole OutputType = "console"
)

/*****************************************************************************
 * NAME: Output
 *
 * DESCRIPTION:
 *     Output configuration structure
 *
 * FIELDS:
 *     Type       - Output type (file or console)
 *     File       - Log file path for file output
 *     MaxSize    - Maximum size of each log file in megabytes
 *     MaxAge     - Maximum number of days to retain old log files
 *     MaxBackups - Maximum number of old log files to retain
 *     Compress   - Whether to compress old log files
 *     Stderr     - Whether to use stderr for console output
 *****************************************************************************/
type Output struct {
	Type       OutputType `yaml:"type"`
	File       string     `yaml:"file"`
	MaxSize    int        `yaml:"maxSize"`
	MaxAge     int        `yaml:"maxAge"`
	MaxBackups int        `yaml:"maxBackups"`
	Compress   bool       `yaml:"compress"`
	Stderr     bool       `yaml:"stderr"`
}

/*****************************************************************************
 * NAME: LogConfig
 *
 * DESCRIPTION:
 *     Logger configuration structure
 *
 * FIELDS:
 *     Level   - Minimum log level to record
 *     Format  - Log output format (text or json)
 *     Outputs - List of output configurations
 *****************************************************************************/
type LogConfig struct {
	Level   LogLevel  `yaml:"level"`
	Format  LogFormat `yaml:"format"`
	Outputs []Output  `yaml:"outputs"`
}

/*****************************************************************************
 * NAME: Logger
 *
 * DESCRIPTION:
 *     Logger structure that wraps zap.Logger
 *
 * FIELDS:
 *     *zap.Logger - Underlying zap logger instance
 *****************************************************************************/
type Logger struct {
	*zap.Logger
}

/*****************************************************************************
 * NAME: NewLogger
 *
 * DESCRIPTION:
 *     Creates a new logger instance with the specified configuration
 *
 * PARAMETERS:
 *     config - Logger configuration
 *
 * RETURNS:
 *     *Logger - New logger instance
 *     error  - Error if logger creation fails
 *****************************************************************************/
func NewLogger(config LogConfig) (*Logger, error) {
	// Set initial log level
	globalAtomicLevel.SetLevel(config.Level.ToZapLevel())

	var encoder zapcore.Encoder
	switch config.Format {
	case FormatText:
		encoder = zapcore.NewConsoleEncoder(zapcore.EncoderConfig{
			TimeKey:        "ts",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.StringDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		})
	case FormatJSON:
		fallthrough
	default:
		encoder = zapcore.NewJSONEncoder(zapcore.EncoderConfig{
			TimeKey:        "ts",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.StringDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		})
	}

	var cores []zapcore.Core
	for _, out := range config.Outputs {
		var core zapcore.Core
		switch out.Type {
		case TypeFile:
			if out.File == "" {
				continue
			}
			if err := os.MkdirAll(filepath.Dir(out.File), 0o755); err != nil {
				return nil, fmt.Errorf("failed to create output directory: %w", err)
			}

			writer := &lumberjack.Logger{
				Filename:   out.File,
				MaxSize:    out.MaxSize,
				MaxAge:     out.MaxAge,
				MaxBackups: out.MaxBackups,
				Compress:   out.Compress,
			}
			core = zapcore.NewCore(encoder, zapcore.AddSync(writer), globalAtomicLevel)
		case TypeConsole:
			writer := os.Stdout
			if out.Stderr {
				writer = os.Stderr
			}
			core = zapcore.NewCore(encoder, zapcore.AddSync(writer), globalAtomicLevel)
		default:
			continue
		}
		cores = append(cores, core)
	}

	if len(cores) == 0 {
		fmt.Fprintf(os.Stderr, "Warning: no valid outputs specified, falling back to stdout\n")
		core := zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), globalAtomicLevel)
		cores = append(cores, core)
	}

	core := zapcore.NewTee(cores...)

	logger := zap.New(core,
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.AddStacktrace(zapcore.ErrorLevel),
		zap.Fields(
			zap.Int("pid", os.Getpid()),
		),
	)

	return &Logger{Logger: logger}, nil
}

/*****************************************************************************
 * NAME: WithModule
 *
 * DESCRIPTION:
 *     Creates a new logger with the specified module name
 *
 * PARAMETERS:
 *     moduleName - Name of the module
 *
 * RETURNS:
 *     *Logger - New logger instance with module name
 *****************************************************************************/
func (l *Logger) WithModule(moduleName string) *Logger {
	return &Logger{l.Logger.With(zap.String("module", moduleName))}
}

/*****************************************************************************
 * NAME: WithEvent
 *
 * DESCRIPTION:
 *     Creates a new logger with the specified event name
 *
 * PARAMETERS:
 *     event - Name of the event
 *
 * RETURNS:
 *     *Logger - New logger instance with event name
 *****************************************************************************/
func (l *Logger) WithEvent(event string) *Logger {
	return &Logger{l.Logger.With(zap.String("event", event))}
}

/*****************************************************************************
 * NAME: Debug
 *
 * DESCRIPTION:
 *     Logs a debug message
 *
 * PARAMETERS:
 *     msg    - Message to log
 *     fields - Additional fields to log
 *****************************************************************************/
func (l *Logger) Debug(msg string, fields ...zap.Field) {
	l.Logger.Debug(msg, fields...)
}

/*****************************************************************************
 * NAME: Info
 *
 * DESCRIPTION:
 *     Logs an info message
 *
 * PARAMETERS:
 *     msg    - Message to log
 *     fields - Additional fields to log
 *****************************************************************************/
func (l *Logger) Info(msg string, fields ...zap.Field) {
	l.Logger.Info(msg, fields...)
}

/*****************************************************************************
 * NAME: Warn
 *
 * DESCRIPTION:
 *     Logs a warning message
 *
 * PARAMETERS:
 *     msg    - Message to log
 *     fields - Additional fields to log
 *****************************************************************************/
func (l *Logger) Warn(msg string, fields ...zap.Field) {
	l.Logger.Warn(msg, fields...)
}

/*****************************************************************************
 * NAME: Error
 *
 * DESCRIPTION:
 *     Logs an error message
 *
 * PARAMETERS:
 *     msg    - Message to log
 *     fields - Additional fields to log
 *****************************************************************************/
func (l *Logger) Error(msg string, fields ...zap.Field) {
	l.Logger.Error(msg, fields...)
}

/*****************************************************************************
 * NAME: Sync
 *
 * DESCRIPTION:
 *     Synchronizes all log outputs
 *
 * RETURNS:
 *     error - Error if synchronization fails
 *****************************************************************************/
func (l *Logger) Sync() error {
	return l.Logger.Sync()
}

/*****************************************************************************
 * NAME: SetLogLevel
 *
 * DESCRIPTION:
 *     Sets the global log level dynamically
 *
 * PARAMETERS:
 *     level - The new log level to set
 *
 * RETURNS:
 *     error - Error if the log level is invalid
 *****************************************************************************/
func SetLogLevel(level LogLevel) error {
	// Validate if the log level is valid
	switch strings.ToUpper(string(level)) {
	case "DEBUG", "INFO", "WARN", "ERROR":
		globalAtomicLevel.SetLevel(level.ToZapLevel())
		return nil
	default:
		return fmt.Errorf("invalid log level: %s", level)
	}
}

/*****************************************************************************
 * NAME: GetLogLevel
 *
 * DESCRIPTION:
 *     Gets the current global log level
 *
 * RETURNS:
 *     LogLevel - The current log level
 *****************************************************************************/
func GetLogLevel() LogLevel {
	zapLevel := globalAtomicLevel.Level()
	switch zapLevel {
	case zapcore.DebugLevel:
		return LevelDebug
	case zapcore.InfoLevel:
		return LevelInfo
	case zapcore.WarnLevel:
		return LevelWarn
	case zapcore.ErrorLevel:
		return LevelError
	default:
		return LevelInfo
	}
}
