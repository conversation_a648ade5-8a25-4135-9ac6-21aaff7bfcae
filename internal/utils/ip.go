/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      ip.go
 *
 * DESCRIPTION :    IP address utilities for handling protobuf IP types
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package utils

import (
	"fmt"

	pb "agent/internal/pb"
)

/*****************************************************************************
 * NAME: GetIpString
 *
 * DESCRIPTION:
 *     Converts an IpAddress protobuf message to a string representation.
 *     Handles all possible ways an IP can be represented in the protobuf.
 *
 * PARAMETERS:
 *     ipAddr - IpAddress protobuf message
 *
 * RETURNS:
 *     string - String representation of the IP address
 *              Returns "0.0.0.0" if the IP address is nil or invalid
 *****************************************************************************/
func GetIpString(ipAddr *pb.IpAddress) string {
	if ipAddr == nil {
		return "0.0.0.0"
	}

	// Check which field is set and extract the IP string
	switch {
	// String representation
	case ipAddr.GetIpString() != "":
		return ipAddr.GetIpString()

	// IPv4 as uint32
	case ipAddr.GetIpv4() != 0:
		// Convert uint32 to IP address string
		ipv4 := ipAddr.GetIpv4()
		return fmt.Sprintf("%d.%d.%d.%d",
			(ipv4>>24)&0xFF,
			(ipv4>>16)&0xFF,
			(ipv4>>8)&0xFF,
			ipv4&0xFF)

	// IPv6 as bytes
	case len(ipAddr.GetIpv6()) > 0:
		// Convert bytes to IPv6 string
		ipv6 := ipAddr.GetIpv6()
		if len(ipv6) != 16 {
			return "0.0.0.0" // Invalid IPv6 length
		}

		// Format IPv6 address
		var segments [8]uint16
		for i := 0; i < 8; i++ {
			segments[i] = uint16(ipv6[i*2])<<8 | uint16(ipv6[i*2+1])
		}

		return fmt.Sprintf("%x:%x:%x:%x:%x:%x:%x:%x",
			segments[0], segments[1], segments[2], segments[3],
			segments[4], segments[5], segments[6], segments[7])

	// IPv4 CIDR
	case ipAddr.GetV4Cidr() != nil:
		v4cidr := ipAddr.GetV4Cidr()
		ipv4 := v4cidr.GetIp()
		prefix := v4cidr.GetPrefixLength()
		return fmt.Sprintf("%d.%d.%d.%d/%d",
			(ipv4>>24)&0xFF,
			(ipv4>>16)&0xFF,
			(ipv4>>8)&0xFF,
			ipv4&0xFF,
			prefix)

	// IPv6 CIDR
	case ipAddr.GetV6Cidr() != nil:
		v6cidr := ipAddr.GetV6Cidr()
		ipv6 := v6cidr.GetIp()
		prefix := v6cidr.GetPrefixLength()

		if len(ipv6) != 16 {
			return "0.0.0.0" // Invalid IPv6 length
		}

		// Format IPv6 address with prefix
		var segments [8]uint16
		for i := 0; i < 8; i++ {
			segments[i] = uint16(ipv6[i*2])<<8 | uint16(ipv6[i*2+1])
		}

		return fmt.Sprintf("%x:%x:%x:%x:%x:%x:%x:%x/%d",
			segments[0], segments[1], segments[2], segments[3],
			segments[4], segments[5], segments[6], segments[7],
			prefix)

	default:
		return "0.0.0.0"
	}
}

/*****************************************************************************
 * NAME: GetIpRangeString
 *
 * DESCRIPTION:
 *     Converts an IpRange protobuf message to a string representation.
 *     Format: "start_ip-end_ip"
 *
 * PARAMETERS:
 *     ipRange - IpRange protobuf message
 *
 * RETURNS:
 *     string - String representation of the IP range
 *              Returns "0.0.0.0" if the IP range is nil or invalid
 *****************************************************************************/
func GetIpRangeString(ipRange *pb.IpRange) string {
	if ipRange == nil {
		return "0.0.0.0"
	}

	startIp := GetIpString(ipRange.GetStartIp())
	endIp := GetIpString(ipRange.GetEndIp())

	if startIp == "0.0.0.0" && endIp == "0.0.0.0" {
		return "0.0.0.0"
	}

	return startIp + "-" + endIp
}

/*****************************************************************************
 * NAME: GetNatIpString
 *
 * DESCRIPTION:
 *     Converts a NatIp protobuf message to a string representation.
 *     Handles both single IP and IP range cases.
 *
 * PARAMETERS:
 *     natIp - NatIp protobuf message
 *
 * RETURNS:
 *     string - String representation of the NAT IP
 *              Returns "0.0.0.0" if the NAT IP is nil or invalid
 *****************************************************************************/
func GetNatIpString(natIp *pb.NatIp) string {
	if natIp == nil {
		return "0.0.0.0"
	}

	// Check if it's a single IP
	if natIp.GetIp() != nil {
		return GetIpString(natIp.GetIp())
	}

	// Check if it's an IP range
	if natIp.GetIpRange() != nil {
		return GetIpRangeString(natIp.GetIpRange())
	}

	return "0.0.0.0"
}
