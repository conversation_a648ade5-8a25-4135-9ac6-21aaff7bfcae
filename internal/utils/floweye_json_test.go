/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      floweye_json_test.go
 *
 * DESCRIPTION :    Unit tests for floweye JSON parsing utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025/01/XX  create
 ******************************************************************************/

package utils

import (
	"testing"
)

// TestParseFloweyeJSON tests the unified floweye JSON parser
func TestParseFloweyeJSON(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int // expected number of objects
		hasError bool
	}{
		{
			name:     "empty input",
			input:    "",
			expected: 0,
			hasError: false,
		},
		{
			name:     "single JSON object",
			input:    `{"id":1,"name":"test","type":"proxy"}`,
			expected: 1,
			hasError: false,
		},
		{
			name:     "standard JSON array",
			input:    `[{"id":1,"name":"wan","type":"proxy"},{"id":2,"name":"iwan1","type":"iwan"}]`,
			expected: 2,
			hasError: false,
		},
		{
			name:     "multiple JSON objects separated by commas",
			input:    `{"id":1,"name":"wan","type":"proxy"},{"id":2,"name":"iwan1","type":"iwan"}`,
			expected: 2,
			hasError: false,
		},
		{
			name:     "actual floweye proxy output",
			input:    `{"id":1,"name":"wan","type":"proxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth0","ip":"***********00","gw":"***********","mask":"*************","vlan":"0/0"},{"id":4,"name":"iwan1","type":"iwan","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":0,"if":"wan","ip":"0.0.0.0","gw":"0.0.0.0","mask":"*************","vlan":"0/0"}`,
			expected: 2,
			hasError: false,
		},
		{
			name:     "single object in array format",
			input:    `[{"id":1,"name":"test","type":"proxy"}]`,
			expected: 1,
			hasError: false,
		},
		{
			name:     "empty array",
			input:    `[]`,
			expected: 0,
			hasError: false,
		},
		{
			name:     "whitespace input",
			input:    "   \n\t   ",
			expected: 0,
			hasError: false,
		},
		{
			name:     "invalid JSON",
			input:    `{"id":1,"name":}`,
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseFloweyeJSON(tt.input)

			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if len(result) != tt.expected {
					t.Errorf("Expected %d objects, got %d", tt.expected, len(result))
				}
			}
		})
	}
}

// TestParseFloweyeJSONToType tests the generic type conversion
func TestParseFloweyeJSONToType(t *testing.T) {
	type TestStruct struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
		Type string `json:"type"`
	}

	tests := []struct {
		name     string
		input    string
		expected []TestStruct
		hasError bool
	}{
		{
			name:     "empty input",
			input:    "",
			expected: []TestStruct{},
			hasError: false,
		},
		{
			name:  "single object",
			input: `{"id":1,"name":"test","type":"proxy"}`,
			expected: []TestStruct{
				{ID: 1, Name: "test", Type: "proxy"},
			},
			hasError: false,
		},
		{
			name:  "standard JSON array",
			input: `[{"id":1,"name":"wan","type":"proxy"},{"id":2,"name":"iwan1","type":"iwan"}]`,
			expected: []TestStruct{
				{ID: 1, Name: "wan", Type: "proxy"},
				{ID: 2, Name: "iwan1", Type: "iwan"},
			},
			hasError: false,
		},
		{
			name:  "multiple objects separated by commas",
			input: `{"id":1,"name":"wan","type":"proxy"},{"id":2,"name":"iwan1","type":"iwan"}`,
			expected: []TestStruct{
				{ID: 1, Name: "wan", Type: "proxy"},
				{ID: 2, Name: "iwan1", Type: "iwan"},
			},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result []TestStruct
			err := ParseFloweyeJSONToType(tt.input, &result)

			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if len(result) != len(tt.expected) {
					t.Errorf("Expected %d objects, got %d", len(tt.expected), len(result))
				}
				for i, expected := range tt.expected {
					if i >= len(result) {
						t.Errorf("Missing object at index %d", i)
						continue
					}
					if result[i].ID != expected.ID {
						t.Errorf("Object %d: expected ID %d, got %d", i, expected.ID, result[i].ID)
					}
					if result[i].Name != expected.Name {
						t.Errorf("Object %d: expected Name %s, got %s", i, expected.Name, result[i].Name)
					}
					if result[i].Type != expected.Type {
						t.Errorf("Object %d: expected Type %s, got %s", i, expected.Type, result[i].Type)
					}
				}
			}
		})
	}
}
