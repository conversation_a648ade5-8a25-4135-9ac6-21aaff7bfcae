/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      floweye_json.go
 *
 * DESCRIPTION :    Utility functions for parsing floweye JSON output
 *
 * AUTHOR :         wei
 *
 * HISTORY :        2025/01/XX  create
 ******************************************************************************/

package utils

import (
	"encoding/json"
	"fmt"
	"strings"
)

/*****************************************************************************
 * NAME: ParseFloweyeJSON
 *
 * DESCRIPTION:
 *     Parse floweye JSON output which can be in multiple formats:
 *     1. Single JSON object: {"id":1,"name":"test"}
 *     2. Multiple JSON objects separated by commas: {"id":1},{"id":2}
 *     3. Empty output
 *
 * PARAMETERS:
 *     output - Raw output from floweye command
 *
 * RETURNS:
 *     []map[string]interface{} - Parsed JSON objects as array
 *     error                    - Error if parsing fails
 *****************************************************************************/
func ParseFloweyeJSON(output string) ([]map[string]interface{}, error) {
	// Remove whitespace and check if output is empty
	output = strings.TrimSpace(output)
	if output == "" {
		return []map[string]interface{}{}, nil
	}

	var result []map[string]interface{}

	// Method 1: Try parsing as standard JSON array first
	if err := json.Unmarshal([]byte(output), &result); err == nil {
		return result, nil
	}

	// Method 2: Try parsing as single JSON object
	var singleObject map[string]interface{}
	if err := json.Unmarshal([]byte(output), &singleObject); err == nil {
		return []map[string]interface{}{singleObject}, nil
	}

	// Method 3: Try parsing as floweye comma-separated format (add brackets)
	if !strings.HasPrefix(output, "[") && !strings.HasSuffix(output, "]") {
		jsonArray := "[" + output + "]"
		if err := json.Unmarshal([]byte(jsonArray), &result); err == nil {
			return result, nil
		}
	}

	// Method 4: Split by comma and parse individually (fallback for complex cases)
	if strings.Contains(output, "},{") {
		jsonObjects := strings.Split(output, "},{")
		for i, jsonStr := range jsonObjects {
			// Fix JSON format for split objects
			if i > 0 && !strings.HasPrefix(jsonStr, "{") {
				jsonStr = "{" + jsonStr
			}
			if i < len(jsonObjects)-1 && !strings.HasSuffix(jsonStr, "}") {
				jsonStr = jsonStr + "}"
			}

			var obj map[string]interface{}
			if err := json.Unmarshal([]byte(jsonStr), &obj); err != nil {
				return nil, fmt.Errorf("failed to parse JSON object at index %d: %w", i, err)
			}
			result = append(result, obj)
		}
		return result, nil
	}

	return nil, fmt.Errorf("failed to parse floweye JSON output in any supported format")
}

/*****************************************************************************
 * NAME: ParseFloweyeJSONToType
 *
 * DESCRIPTION:
 *     Parse floweye JSON output and unmarshal to specific type
 *     This is a generic helper that combines ParseFloweyeJSON with type conversion
 *
 * PARAMETERS:
 *     output - Raw output from floweye command
 *     result - Pointer to slice where results should be stored
 *
 * RETURNS:
 *     error - Error if parsing or conversion fails
 *
 * EXAMPLE:
 *     var configs []*SomeConfig
 *     err := ParseFloweyeJSONToType(output, &configs)
 *****************************************************************************/
func ParseFloweyeJSONToType[T any](output string, result *[]T) error {
	// First parse as generic JSON objects
	objects, err := ParseFloweyeJSON(output)
	if err != nil {
		return err
	}

	// Convert each object to the target type
	for i, obj := range objects {
		// Marshal back to JSON then unmarshal to target type
		jsonBytes, err := json.Marshal(obj)
		if err != nil {
			return fmt.Errorf("failed to marshal object at index %d: %w", i, err)
		}

		var item T
		if err := json.Unmarshal(jsonBytes, &item); err != nil {
			return fmt.Errorf("failed to unmarshal to target type at index %d: %w", i, err)
		}

		*result = append(*result, item)
	}

	return nil
}
