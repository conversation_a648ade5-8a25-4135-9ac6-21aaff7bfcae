/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      shell.go
 *
 * DESCRIPTION :    Shell command execution utilities
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package utils

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os/exec"
	"strings"
	"time"

	"agent/internal/logger"

	"go.uber.org/zap"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

/*****************************************************************************
 * NAME: CommandResult
 *
 * DESCRIPTION:
 *     Enumeration for command execution results
 *****************************************************************************/
type CommandResult string

const (
	CommandResultSuccess CommandResult = "SUCCESS"
	CommandResultFailed  CommandResult = "FAILED"
	CommandResultTimeout CommandResult = "TIMEOUT"
)

/*****************************************************************************
 * NAME: CommandLogContext
 *
 * DESCRIPTION:
 *     Context structure for unified command execution logging.
 *     Contains all necessary information for consistent logging across shell commands.
 *
 * FIELDS:
 *     Command     - Command being executed
 *     Args        - Command arguments
 *     Timeout     - Command timeout in seconds
 *     StartTime   - Command execution start time
 *     Logger      - Logger instance for recording execution
 *****************************************************************************/
type CommandLogContext struct {
	Command   string
	Args      []string
	Timeout   int
	StartTime time.Time
	Logger    *logger.Logger
}

/*****************************************************************************
 * NAME: NewCommandLogContext
 *
 * DESCRIPTION:
 *     Creates a new command log context for unified logging.
 *
 * PARAMETERS:
 *     logger  - Logger instance for recording command execution
 *     timeout - Maximum execution time in seconds
 *     command - Command to execute
 *     args    - Command arguments
 *
 * RETURNS:
 *     *CommandLogContext - New command log context instance
 *****************************************************************************/
func NewCommandLogContext(logger *logger.Logger, timeout int, command string, args ...string) *CommandLogContext {
	return &CommandLogContext{
		Command:   command,
		Args:      args,
		Timeout:   timeout,
		StartTime: time.Now(),
		Logger:    logger,
	}
}

/*****************************************************************************
 * NAME: LogCommandStart
 *
 * DESCRIPTION:
 *     Logs the start of command execution with unified format.
 *     Records all essential information for debugging and tracking.
 *
 * PARAMETERS:
 *     additionalFields - Optional additional zap fields for specific context
 *****************************************************************************/
func (clc *CommandLogContext) LogCommandStart(additionalFields ...zap.Field) {
	// Build complete command line for logging
	cmdLine := clc.Command
	for _, arg := range clc.Args {
		cmdLine += " " + arg
	}

	fields := []zap.Field{
		zap.String("command", clc.Command),
		zap.Strings("args", clc.Args),
		zap.String("command_line", cmdLine),
		zap.Int("timeout_seconds", clc.Timeout),
		zap.String("result", "STARTED"),
		zap.Time("timestamp", clc.StartTime),
	}
	fields = append(fields, additionalFields...)

	clc.Logger.Info("Shell | Command started", fields...)
}

/*****************************************************************************
 * NAME: LogCommandEnd
 *
 * DESCRIPTION:
 *     Logs the completion of command execution with unified format.
 *     Records result, duration, output, and any error information.
 *
 * PARAMETERS:
 *     result           - Command execution result (SUCCESS/FAILED/TIMEOUT)
 *     output           - Command output (stdout)
 *     err              - Error if command failed (nil for success)
 *     additionalFields - Optional additional zap fields for specific context
 *****************************************************************************/
func (clc *CommandLogContext) LogCommandEnd(result CommandResult, output string, err error, additionalFields ...zap.Field) {
	endTime := time.Now()
	duration := endTime.Sub(clc.StartTime)

	// Build complete command line for logging
	cmdLine := clc.Command
	for _, arg := range clc.Args {
		cmdLine += " " + arg
	}

	fields := []zap.Field{
		zap.String("command", clc.Command),
		zap.String("command_line", cmdLine),
		zap.String("result", string(result)),
		zap.Duration("duration", duration),
		zap.Time("timestamp", endTime),
	}

	// Add output information based on result
	if result == CommandResultSuccess {
		if output != "" {
			fields = append(fields, zap.String("output", output))
		}
	} else {
		// For failed or timeout commands, include error details
		if err != nil {
			fields = append(fields, zap.Error(err))
		}
		if output != "" {
			fields = append(fields, zap.String("error_output", output))
		}
	}

	fields = append(fields, additionalFields...)

	// Use appropriate log level based on result
	switch result {
	case CommandResultSuccess:
		clc.Logger.Info("Shell | Command completed", fields...)
	case CommandResultTimeout:
		clc.Logger.Warn("Shell | Command timed out", fields...)
	case CommandResultFailed:
		clc.Logger.Error("Shell | Command failed", fields...)
	}
}

/*****************************************************************************
 * NAME: UTF8ToGB2312
 *
 * DESCRIPTION:
 *     Converts a UTF-8 encoded string to GB2312 encoding.
 *
 * PARAMETERS:
 *     utf8Str - UTF-8 encoded string
 *
 * RETURNS:
 *     string - GB2312 encoded string
 *     error  - Error if conversion fails
 *****************************************************************************/
func UTF8ToGB2312(utf8Str string) (string, error) {
	reader := transform.NewReader(strings.NewReader(utf8Str), simplifiedchinese.GB18030.NewEncoder())
	d, err := io.ReadAll(reader)
	if err != nil {
		return "", err
	}
	return string(d), nil
}

/*****************************************************************************
 * NAME: GB2312ToUTF8
 *
 * DESCRIPTION:
 *     Converts a GB2312 encoded string to UTF-8 encoding.
 *
 * PARAMETERS:
 *     gb2312Str - GB2312 encoded string
 *
 * RETURNS:
 *     string - UTF-8 encoded string
 *     error  - Error if conversion fails
 *****************************************************************************/
func GB2312ToUTF8(gb2312Str string) (string, error) {
	reader := transform.NewReader(strings.NewReader(gb2312Str), simplifiedchinese.GB18030.NewDecoder())
	d, err := io.ReadAll(reader)
	if err != nil {
		return "", err
	}
	return string(d), nil
}

/*****************************************************************************
 * NAME: ExecuteCommand
 *
 * DESCRIPTION:
 *     Executes a shell command with the given arguments and returns the output.
 *     Uses a context with timeout to prevent hanging on long-running commands.
 *     Handles encoding conversion between UTF-8 and GB2312 for floweye commands.
 *
 * PARAMETERS:
 *     logger  - Logger for recording command execution
 *     timeout - Maximum execution time in seconds
 *     command - Command to execute
 *     args    - Command arguments
 *
 * RETURNS:
 *     string - Command output (stdout) in UTF-8 encoding
 *     error  - Error if command execution fails
 *****************************************************************************/
func ExecuteCommand(logger *logger.Logger, timeout int, command string, args ...string) (string, error) {
	// Create unified command log context
	cmdLogCtx := NewCommandLogContext(logger, timeout, command, args...)

	// Log command start with encoding information for floweye commands
	if command == "floweye" {
		cmdLogCtx.LogCommandStart(zap.Bool("encoding_conversion", true))
	} else {
		cmdLogCtx.LogCommandStart()
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	// If it's a floweye command, convert parameters from UTF-8 to GB2312
	if command == "floweye" && len(args) > 0 {
		// Convert each argument while preserving the original parameter structure
		convertedArgs := make([]string, len(args))
		for i, arg := range args {
			converted, err := UTF8ToGB2312(arg)
			if err != nil {
				return "", fmt.Errorf("failed to convert argument to GB2312: %v", err)
			}
			convertedArgs[i] = converted
		}
		args = convertedArgs
	}

	// Create command
	cmd := exec.CommandContext(ctx, command, args...)

	// Capture stdout and stderr
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Execute command
	err := cmd.Run()

	// Check if context deadline exceeded
	if ctx.Err() == context.DeadlineExceeded {
		timeoutErr := fmt.Errorf("command timed out after %d seconds", timeout)
		cmdLogCtx.LogCommandEnd(CommandResultTimeout, "", timeoutErr)
		return "", timeoutErr
	}

	// Get standard output and standard error
	stdoutStr := strings.TrimSpace(stdout.String())
	stderrStr := strings.TrimSpace(stderr.String())

	// If it's a floweye command, convert output encoding
	if command == "floweye" {
		if stdoutStr != "" {
			converted, err := GB2312ToUTF8(stdoutStr)
			if err != nil {
				return "conversion error", fmt.Errorf("failed to convert stdout to UTF-8: %v", err)
			}
			stdoutStr = converted
		}

		if stderrStr != "" {
			converted, err := GB2312ToUTF8(stderrStr)
			if err != nil {
				return "conversion error", fmt.Errorf("failed to convert stderr to UTF-8: %v", err)
			}
			stderrStr = converted
		}
	}

	// Check execution error
	if err != nil {
		var errorOutput string
		var returnErr error

		// Determine error output and return values
		if stderrStr == "" && stdoutStr != "" {
			// If standard error is empty but standard output is not, use standard output as error message
			errorOutput = stdoutStr
			returnErr = fmt.Errorf("command failed: %w, output: %s", err, stdoutStr)
		} else if stderrStr != "" {
			// Use stderr as error output
			errorOutput = stderrStr
			returnErr = fmt.Errorf("command failed: %w, stderr: %s", err, stderrStr)
		} else {
			// If there is no error output, return error description
			errorOutput = "command failed: " + err.Error()
			returnErr = fmt.Errorf("command failed: %w", err)
		}

		// Log command failure with unified format
		cmdLogCtx.LogCommandEnd(CommandResultFailed, errorOutput, err)
		return errorOutput, returnErr
	}

	// Log successful command execution with unified format
	cmdLogCtx.LogCommandEnd(CommandResultSuccess, stdoutStr, nil)

	// Return output (empty string if no output) - preserving original logic
	return stdoutStr, nil
}

/*****************************************************************************
 * NAME: ExecuteCommandWithInput
 *
 * DESCRIPTION:
 *     Executes a shell command with the given arguments and input,
 *     and returns the output. Uses a context with timeout to prevent
 *     hanging on long-running commands.
 *     Handles encoding conversion between UTF-8 and GB2312 for floweye commands.
 *
 * PARAMETERS:
 *     logger  - Logger for recording command execution
 *     timeout - Maximum execution time in seconds
 *     input   - Input to provide to the command (in UTF-8 encoding)
 *     command - Command to execute
 *     args    - Command arguments
 *
 * RETURNS:
 *     string - Command output (stdout) in UTF-8 encoding
 *     error  - Error if command execution fails
 *****************************************************************************/
func ExecuteCommandWithInput(logger *logger.Logger, timeout int, input string, command string, args ...string) (string, error) {
	// Create unified command log context
	cmdLogCtx := NewCommandLogContext(logger, timeout, command, args...)

	// Log command start with input and encoding information
	if command == "floweye" {
		cmdLogCtx.LogCommandStart(
			zap.String("input", input),
			zap.Bool("encoding_conversion", true))
	} else {
		cmdLogCtx.LogCommandStart(zap.String("input", input))
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	// If it's a floweye command, convert parameters and input from UTF-8 to GB2312
	if command == "floweye" {
		// Convert each argument while preserving the original parameter structure
		if len(args) > 0 {
			convertedArgs := make([]string, len(args))
			for i, arg := range args {
				converted, err := UTF8ToGB2312(arg)
				if err != nil {
					return "", fmt.Errorf("failed to convert argument to GB2312: %v", err)
				}
				convertedArgs[i] = converted
			}
			args = convertedArgs
		}

		// Convert input
		if input != "" {
			convertedInput, err := UTF8ToGB2312(input)
			if err != nil {
				return "", fmt.Errorf("failed to convert input to GB2312: %v", err)
			}
			input = convertedInput
		}
	}

	// Create command
	cmd := exec.CommandContext(ctx, command, args...)

	// Capture stdout and stderr
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	cmd.Stdin = strings.NewReader(input)

	// Execute command
	err := cmd.Run()

	// Check if context deadline exceeded
	if ctx.Err() == context.DeadlineExceeded {
		timeoutErr := fmt.Errorf("command timed out after %d seconds", timeout)
		cmdLogCtx.LogCommandEnd(CommandResultTimeout, "", timeoutErr)
		return "", timeoutErr
	}

	// Get standard output and standard error
	stdoutStr := strings.TrimSpace(stdout.String())
	stderrStr := strings.TrimSpace(stderr.String())

	// If it's a floweye command, convert output encoding
	if command == "floweye" {
		if stdoutStr != "" {
			converted, err := GB2312ToUTF8(stdoutStr)
			if err != nil {
				return "conversion error", fmt.Errorf("failed to convert stdout to UTF-8: %v", err)
			}
			stdoutStr = converted
		}

		if stderrStr != "" {
			converted, err := GB2312ToUTF8(stderrStr)
			if err != nil {
				return "conversion error", fmt.Errorf("failed to convert stderr to UTF-8: %v", err)
			}
			stderrStr = converted
		}
	}

	// Check execution error
	if err != nil {
		var errorOutput string
		var returnErr error

		// Determine error output and return values
		if stderrStr == "" && stdoutStr != "" {
			// If standard error is empty but standard output is not, use standard output as error message
			errorOutput = stdoutStr
			returnErr = fmt.Errorf("command failed: %w, output: %s", err, stdoutStr)
		} else if stderrStr != "" {
			// Use stderr as error output
			errorOutput = stderrStr
			returnErr = fmt.Errorf("command failed: %w, stderr: %s", err, stderrStr)
		} else {
			// If there is no error output, return error description
			errorOutput = "command failed: " + err.Error()
			returnErr = fmt.Errorf("command failed: %w", err)
		}

		// Log command failure with unified format
		cmdLogCtx.LogCommandEnd(CommandResultFailed, errorOutput, err)
		return errorOutput, returnErr
	}

	// Log successful command execution with unified format
	cmdLogCtx.LogCommandEnd(CommandResultSuccess, stdoutStr, nil)

	// If there is no output, return success message instead of empty string - preserving original logic
	if stdoutStr == "" {
		return "success", nil
	}

	return stdoutStr, nil
}
