package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Metrics 包含所有指标
type Metrics struct {
	// 任务处理指标
	TaskTotal          prometheus.Counter
	TaskByType         *prometheus.CounterVec
	TaskByAction       *prometheus.CounterVec
	TaskProcessingTime prometheus.Histogram
	TaskErrors         *prometheus.CounterVec
	TaskFailuresByType *prometheus.CounterVec

	// 配置同步指标
	FullSyncTotal        prometheus.Counter
	FullSyncDuration     prometheus.Histogram
	IncrementalSyncTotal prometheus.Counter
	ConfigChanges        *prometheus.CounterVec
	SyncErrors           prometheus.Counter

	// 接口状态指标
	InterfaceTotal    prometheus.Gauge
	InterfaceStatus   *prometheus.GaugeVec
	InterfaceTraffic  *prometheus.GaugeVec
	InterfaceErrors   *prometheus.CounterVec
	InterfaceFailures *prometheus.CounterVec

	// WAN连接指标
	WanConnections    prometheus.Gauge
	WanStatus         *prometheus.GaugeVec
	WanTraffic        *prometheus.GaugeVec
	HeartbeatStatus   *prometheus.GaugeVec
	ConnectionLatency prometheus.Histogram
	WanFailures       *prometheus.CounterVec

	// LAN和DHCP指标
	LanInterfaces     prometheus.Gauge
	DhcpServiceStatus prometheus.Gauge
	DhcpPoolUsage     *prometheus.GaugeVec
	DhcpLeases        *prometheus.GaugeVec
	DhcpRequests      *prometheus.CounterVec
	LanFailures       *prometheus.CounterVec
	DhcpFailures      *prometheus.CounterVec

	// 系统资源指标
	CpuUsage             prometheus.Gauge
	MemoryUsage          prometheus.Gauge
	DiskUsage            prometheus.Gauge
	CommandExecutionTime prometheus.Histogram
	ErrorLogCount        prometheus.Counter
}

// NewMetrics 创建并初始化所有指标
func NewMetrics() *Metrics {
	return &Metrics{
		// 任务处理指标
		TaskTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "agent_task_total",
			Help: "Total number of tasks processed",
		}),
		TaskByType: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_task_by_type",
			Help: "Number of tasks by type",
		}, []string{"type"}),
		TaskByAction: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_task_by_action",
			Help: "Number of tasks by action",
		}, []string{"action"}),
		TaskProcessingTime: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "agent_task_processing_seconds",
			Help:    "Task processing time in seconds",
			Buckets: prometheus.DefBuckets,
		}),
		TaskErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_task_errors",
			Help: "Number of task errors by type",
		}, []string{"type", "error"}),
		TaskFailuresByType: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_task_failures_by_type",
			Help: "Number of task failures by type",
		}, []string{"type"}),

		// 配置同步指标
		FullSyncTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "agent_full_sync_total",
			Help: "Total number of full synchronizations",
		}),
		FullSyncDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "agent_full_sync_duration_seconds",
			Help:    "Full synchronization duration in seconds",
			Buckets: prometheus.DefBuckets,
		}),
		IncrementalSyncTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "agent_incremental_sync_total",
			Help: "Total number of incremental synchronizations",
		}),
		ConfigChanges: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_config_changes",
			Help: "Number of configuration changes by type",
		}, []string{"type", "action"}),
		SyncErrors: promauto.NewCounter(prometheus.CounterOpts{
			Name: "agent_sync_errors",
			Help: "Total number of synchronization errors",
		}),

		// 接口状态指标
		InterfaceTotal: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_interface_total",
			Help: "Total number of interfaces",
		}),
		InterfaceStatus: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_interface_status",
			Help: "Interface status by name",
		}, []string{"name", "status"}),
		InterfaceTraffic: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_interface_traffic",
			Help: "Interface traffic statistics",
		}, []string{"name", "direction"}),
		InterfaceErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_interface_errors",
			Help: "Number of interface errors by type",
		}, []string{"name", "error_type"}),
		InterfaceFailures: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_interface_failures",
			Help: "Number of interface operation failures",
		}, []string{"operation"}),

		// WAN连接指标
		WanConnections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_wan_connections",
			Help: "Number of WAN connections",
		}),
		WanStatus: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_wan_status",
			Help: "WAN connection status",
		}, []string{"name", "status"}),
		WanTraffic: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_wan_traffic",
			Help: "WAN traffic statistics",
		}, []string{"name", "direction"}),
		HeartbeatStatus: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_wan_heartbeat",
			Help: "WAN heartbeat status",
		}, []string{"name", "status"}),
		ConnectionLatency: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "agent_wan_latency_seconds",
			Help:    "WAN connection latency in seconds",
			Buckets: prometheus.DefBuckets,
		}),
		WanFailures: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_wan_failures",
			Help: "Number of WAN operation failures",
		}, []string{"operation"}),

		// LAN和DHCP指标
		LanInterfaces: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_lan_interfaces",
			Help: "Number of LAN interfaces",
		}),
		DhcpServiceStatus: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_dhcp_service_status",
			Help: "DHCP service status",
		}),
		DhcpPoolUsage: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_dhcp_pool_usage",
			Help: "DHCP pool usage statistics",
		}, []string{"pool"}),
		DhcpLeases: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "agent_dhcp_leases",
			Help: "DHCP lease statistics",
		}, []string{"status"}),
		DhcpRequests: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_dhcp_requests",
			Help: "DHCP request statistics",
		}, []string{"type"}),
		LanFailures: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_lan_failures",
			Help: "Number of LAN operation failures",
		}, []string{"operation"}),
		DhcpFailures: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "agent_dhcp_failures",
			Help: "Number of DHCP operation failures",
		}, []string{"operation"}),

		// 系统资源指标
		CpuUsage: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_cpu_usage",
			Help: "CPU usage percentage",
		}),
		MemoryUsage: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_memory_usage",
			Help: "Memory usage percentage",
		}),
		DiskUsage: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "agent_disk_usage",
			Help: "Disk usage percentage",
		}),
		CommandExecutionTime: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "agent_command_execution_seconds",
			Help:    "Command execution time in seconds",
			Buckets: prometheus.DefBuckets,
		}),
		ErrorLogCount: promauto.NewCounter(prometheus.CounterOpts{
			Name: "agent_error_log_count",
			Help: "Total number of error logs",
		}),
	}
}
