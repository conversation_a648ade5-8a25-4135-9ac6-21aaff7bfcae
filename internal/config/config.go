/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      config.go
 *
 * DESCRIPTION :    Configuration management for the agent. Handles loading and parsing of YAML configuration files.
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package config

import (
	"os"

	"agent/internal/logger"

	"gopkg.in/yaml.v3"
)

/*****************************************************************************
 * NAME: TLSConfig
 *
 * DESCRIPTION:
 *     Represents TLS configuration for secure gRPC communication.
 *     Supports automatic certificate generation and flexible TLS options.
 *
 * FIELDS:
 *     Enabled      - Whether TLS is enabled for gRPC connections
 *     CertDir      - Directory path where certificate files are stored
 *     CertFile     - Certificate file name (e.g., "server.crt")
 *     KeyFile      - Private key file name (e.g., "server.key")
 *     SkipVerify   - Whether to skip server certificate verification
 *     AutoGenerate - Whether to automatically generate self-signed certificates
 *****************************************************************************/
type TLSConfig struct {
	Enabled      bool   `yaml:"enabled"`       // 是否启用 TLS
	CertDir      string `yaml:"cert-dir"`      // 证书目录路径
	CertFile     string `yaml:"cert-file"`     // 证书文件名
	KeyFile      string `yaml:"key-file"`      // 私钥文件名
	SkipVerify   bool   `yaml:"skip-verify"`   // 是否跳过服务器验证
	AutoGenerate bool   `yaml:"auto-generate"` // 是否自动生成证书
}

/*****************************************************************************
 * NAME: Config
 *
 * DESCRIPTION:
 *     Represents the application configuration structure.
 *     Contains client information, communication settings, and logging options.
 *
 * FIELDS:
 *     Client  - Client identification information
 *     Comms   - Communication settings including server addresses and TLS config
 *     Logging - Logging configuration options
 *****************************************************************************/
type Config struct {
	Client struct {
		CustomerID int `yaml:"customer-id"`
		ClientID   int `yaml:"client-id"`
	} `yaml:"client"`
	Comms struct {
		Addrs []string  `yaml:"addrs"`
		TLS   TLSConfig `yaml:"tls"`
	} `yaml:"comms"`
	Logging logger.LogConfig `yaml:"logging"`
}

/*****************************************************************************
 * NAME: setTLSDefaults
 *
 * DESCRIPTION:
 *     Sets default values for TLS configuration if not specified.
 *     Ensures backward compatibility and provides sensible defaults.
 *
 * PARAMETERS:
 *     cfg - Pointer to the configuration structure
 *****************************************************************************/
func setTLSDefaults(cfg *Config) {
	if cfg.Comms.TLS.CertDir == "" {
		cfg.Comms.TLS.CertDir = "./certs"
	}
	if cfg.Comms.TLS.CertFile == "" {
		cfg.Comms.TLS.CertFile = "server.crt"
	}
	if cfg.Comms.TLS.KeyFile == "" {
		cfg.Comms.TLS.KeyFile = "server.key"
	}
	// SkipVerify 默认为 true，适用于自签名证书
	if !cfg.Comms.TLS.Enabled {
		cfg.Comms.TLS.SkipVerify = true
		cfg.Comms.TLS.AutoGenerate = true
	}
}

/*****************************************************************************
 * NAME: LoadConfig
 *
 * DESCRIPTION:
 *     Loads configuration from a YAML file at the specified path.
 *     Reads the file contents and unmarshals them into a Config structure.
 *     Sets default values for TLS configuration.
 *
 * PARAMETERS:
 *     path - Path to the configuration file
 *
 * RETURNS:
 *     *Config - Pointer to the loaded configuration structure
 *     error   - Error if loading or parsing fails
 *****************************************************************************/
func LoadConfig(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, err
	}

	// 设置 TLS 默认值
	setTLSDefaults(&cfg)

	return &cfg, nil
}
