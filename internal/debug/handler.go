/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      handler.go
 *
 * DESCRIPTION :    Debug server request handlers
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package debug

import (
	"agent/internal/client/task"
	pb "agent/internal/pb"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

// handleConfig processes configuration requests
func handleConfig(taskManager *task.TaskManager) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Read request body
		body, err := ioutil.ReadAll(r.Body)
		if err != nil {
			debugLog.Error("failed to read request body", zap.Error(err))
			http.Error(w, fmt.Sprintf("Failed to read request body: %v", err), http.StatusBadRequest)
			return
		}

		// Log raw request body
		debugLog.Info("received request body", zap.String("body", string(body)))

		// First parse as raw JSON array
		var tasksArray []json.RawMessage
		if err := json.Unmarshal(body, &tasksArray); err != nil {
			debugLog.Error("failed to parse tasks array", zap.Error(err))
			http.Error(w, fmt.Sprintf("Failed to parse tasks array: %v", err), http.StatusBadRequest)
			return
		}

		// Check if tasks array is empty
		if len(tasksArray) == 0 {
			debugLog.Error("no tasks found in request")
			http.Error(w, "No tasks found in request", http.StatusBadRequest)
			return
		}

		// Parse each task individually
		tasks := make([]*pb.TaskTx, 0, len(tasksArray))
		opts := protojson.UnmarshalOptions{
			DiscardUnknown: true,
			AllowPartial:   true,
		}

		for i, rawTask := range tasksArray {
			// Log raw task JSON
			debugLog.Info("parsing task", zap.Int("index", i), zap.String("raw_task", string(rawTask)))

			task := &pb.TaskTx{}
			if err := opts.Unmarshal(rawTask, task); err != nil {
				debugLog.Error("failed to parse task", zap.Int("index", i), zap.Error(err))
				http.Error(w, fmt.Sprintf("Failed to parse task at index %d: %s", i, err.Error()), http.StatusBadRequest)
				return
			}

			// Log parsed task
			debugLog.Info("parsed task",
				zap.Int("index", i),
				zap.String("tx_id", task.TxId),
				zap.Int("device_tasks_count", len(task.DeviceTasks)))

			// Validate task
			if task.TxId == "" {
				debugLog.Error("empty tx_id", zap.Int("index", i))
				http.Error(w, fmt.Sprintf("Empty tx_id at index %d", i), http.StatusBadRequest)
				return
			}

			if len(task.DeviceTasks) == 0 {
				debugLog.Error("no device tasks", zap.Int("index", i), zap.String("tx_id", task.TxId))
				http.Error(w, fmt.Sprintf("No device tasks at index %d", i), http.StatusBadRequest)
				return
			}

			// Validate each device task
			for j, deviceTask := range task.DeviceTasks {
				// Log device task details
				debugLog.Info("device task",
					zap.Int("task_index", i),
					zap.Int("device_task_index", j),
					zap.String("task_type", deviceTask.TaskType.String()),
					zap.String("task_action", deviceTask.TaskAction.String()))

				if deviceTask.TaskType == pb.TaskType_TASK_INTERFACE {
					if deviceTask.GetInterfaceTask() == nil {
						debugLog.Error("missing interface task",
							zap.Int("index", i),
							zap.Int("device_task_index", j),
							zap.String("task_type", deviceTask.TaskType.String()))
						http.Error(w, fmt.Sprintf("Missing interface task at index %d, device task %d", i, j), http.StatusBadRequest)
						return
					}
				} else if deviceTask.TaskType == pb.TaskType_TASK_WAN {
					if deviceTask.GetWanTask() == nil {
						debugLog.Error("missing wan task",
							zap.Int("index", i),
							zap.Int("device_task_index", j),
							zap.String("task_type", deviceTask.TaskType.String()))
						http.Error(w, fmt.Sprintf("Missing wan task at index %d, device task %d", i, j), http.StatusBadRequest)
						return
					}
				} else if deviceTask.TaskType == pb.TaskType_TASK_LAN {
					if deviceTask.GetLanTask() == nil {
						debugLog.Error("missing lan task",
							zap.Int("index", i),
							zap.Int("device_task_index", j),
							zap.String("task_type", deviceTask.TaskType.String()))
						http.Error(w, fmt.Sprintf("Missing lan task at index %d, device task %d", i, j), http.StatusBadRequest)
						return
					}
				} else if deviceTask.TaskType == pb.TaskType_TASK_DHCP {
					if deviceTask.GetDhcpTask() == nil {
						debugLog.Error("missing dhcp task",
							zap.Int("index", i),
							zap.Int("device_task_index", j),
							zap.String("task_type", deviceTask.TaskType.String()))
						http.Error(w, fmt.Sprintf("Missing dhcp task at index %d, device task %d", i, j), http.StatusBadRequest)
						return
					}
				}
			}

			tasks = append(tasks, task)
		}

		// Execute tasks
		debugLog.Info("executing tasks", zap.Int("count", len(tasks)))
		statuses := taskManager.ProcessTasks(context.Background(), tasks)

		// Return results
		w.Header().Set("Content-Type", "application/json")
		if err := json.NewEncoder(w).Encode(statuses); err != nil {
			debugLog.Error("failed to encode response", zap.Error(err))
			http.Error(w, fmt.Sprintf("Failed to encode response: %v", err), http.StatusInternalServerError)
			return
		}
	}
}

// handleStatus returns the current status of the debug server
func handleStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	status := map[string]interface{}{
		"running": GetStatus(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// handleStartFullSync starts full synchronization for all processors
func handleStartFullSync(taskManager *task.TaskManager) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		debugLog.Info("debug request: starting full sync")

		err := taskManager.StartFullSync()
		if err != nil {
			debugLog.Error("failed to start full sync", zap.Error(err))
			response := map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			json.NewEncoder(w).Encode(response)
			return
		}

		debugLog.Info("full sync started successfully")
		response := map[string]interface{}{
			"success": true,
			"message": "Full sync started successfully",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
}

// handleEndFullSync ends full synchronization for all processors
func handleEndFullSync(taskManager *task.TaskManager) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		debugLog.Info("debug request: ending full sync")

		taskManager.EndFullSync()

		debugLog.Info("full sync ended successfully")
		response := map[string]interface{}{
			"success": true,
			"message": "Full sync ended successfully",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
}
