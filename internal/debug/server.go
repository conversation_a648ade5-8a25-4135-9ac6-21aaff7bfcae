/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server.go
 *
 * DESCRIPTION :    Debug server implementation for agent
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package debug

import (
	"agent/internal/client/task"
	"agent/internal/logger"
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"
)

var (
	server     *http.Server
	serverLock sync.Mutex
	isRunning  bool
	debugLog   *logger.Logger
)

// Initialize debug logger
func init() {
	logConfig := logger.LogConfig{
		Level: "INFO",
		Outputs: []logger.Output{
			{
				Type: logger.TypeFile,
				File: "/var/log/agent/debug.log",
			},
		},
	}
	var err error
	debugLog, err = logger.NewLogger(logConfig)
	if err != nil {
		// If unable to create dedicated log, use default logger
		defaultLogConfig := logger.LogConfig{
			Level: "INFO",
			Outputs: []logger.Output{
				{
					Type: logger.TypeConsole,
				},
			},
		}
		debugLog, _ = logger.NewLogger(defaultLogConfig)
		debugLog.Warn("failed to create debug log file, using default logger", zap.Error(err))
	}
}

// StartServer starts the debug server
func StartServer(port int, taskManager *task.TaskManager) error {
	serverLock.Lock()
	defer serverLock.Unlock()

	if isRunning {
		return fmt.Errorf("debug server is already running")
	}

	mux := http.NewServeMux()

	// Register API routes
	mux.HandleFunc("/debug/config", logRequest(handleConfig(taskManager)))
	mux.HandleFunc("/debug/status", logRequest(handleStatus))
	mux.HandleFunc("/debug/fullsync/start", logRequest(handleStartFullSync(taskManager)))
	mux.HandleFunc("/debug/fullsync/end", logRequest(handleEndFullSync(taskManager)))

	server = &http.Server{
		Addr:         fmt.Sprintf("127.0.0.1:%d", port),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
	}

	go func() {
		debugLog.Info("starting debug server", zap.Int("port", port))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			debugLog.Error("debug server error", zap.Error(err))
			// We don't return the error here as this is running in a goroutine
		}
	}()

	isRunning = true
	return nil
}

// StopServer stops the debug server
func StopServer() error {
	serverLock.Lock()
	defer serverLock.Unlock()

	if !isRunning {
		return fmt.Errorf("debug server is not running")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	debugLog.Info("stopping debug server")
	if err := server.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown debug server: %w", err)
	}

	isRunning = false
	return nil
}

// GetStatus returns the current status of the debug server
func GetStatus() bool {
	serverLock.Lock()
	defer serverLock.Unlock()
	return isRunning
}

// logRequest is a middleware that logs request information
func logRequest(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Log request information
		debugLog.Info("debug request received",
			zap.String("method", r.Method),
			zap.String("path", r.URL.Path),
			zap.String("remote_addr", r.RemoteAddr),
			zap.String("user_agent", r.UserAgent()))

		next(w, r)

		// Log response information
		debugLog.Info("debug request completed",
			zap.String("method", r.Method),
			zap.String("path", r.URL.Path),
			zap.Duration("duration", time.Since(start)))
	}
}
