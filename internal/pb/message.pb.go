// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: message.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 同步消息逻辑中，任务类型枚举
// 这是站在设备（CPE/POP）视角的类型定义
// 控制器上的一次操作，可能会产生设备内部多个模块的业务操作
type TaskType int32

const (
	TaskType_TASK_INTERFACE           TaskType = 0
	TaskType_TASK_WAN                 TaskType = 1
	TaskType_TASK_LAN                 TaskType = 2
	TaskType_TASK_DHCP                TaskType = 3
	TaskType_TASK_WAN_GROUP           TaskType = 4
	TaskType_TASK_USER_GROUP          TaskType = 5  // 用户组配置任务
	TaskType_TASK_USER                TaskType = 6  // 用户配置任务
	TaskType_TASK_IWAN_PROXY          TaskType = 7  // iWAN Proxy线路配置任务
	TaskType_TASK_IWAN_SERVICE        TaskType = 8  // iWAN Service服务配置任务
	TaskType_TASK_IWAN_MAPPING        TaskType = 9  // iWAN Mapping配置任务
	TaskType_TASK_SR_PROXY            TaskType = 10 // SR Proxy配置任务
	TaskType_TASK_IP_GROUP            TaskType = 11 // IP群组配置任务
	TaskType_TASK_DOMAIN_GROUP        TaskType = 12 // 域名群组配置任务
	TaskType_TASK_EFFECTIVE_TIME      TaskType = 13 // 策略时段配置任务
	TaskType_TASK_TRAFFIC_CHANNEL     TaskType = 14 // 流量通道配置任务
	TaskType_TASK_TRAFFIC_STAT        TaskType = 15 // 流量统计配置任务
	TaskType_TASK_FLOW_CONTROL        TaskType = 16 // 流量控制配置任务
	TaskType_TASK_ROUTE_POLICY        TaskType = 17 // 路由策略配置任务
	TaskType_TASK_DNS_POLICY          TaskType = 18 // DNS管控策略配置任务
	TaskType_TASK_DNS_TRACKING_POLICY TaskType = 19 // DNS跟踪策略配置任务
)

// Enum value maps for TaskType.
var (
	TaskType_name = map[int32]string{
		0:  "TASK_INTERFACE",
		1:  "TASK_WAN",
		2:  "TASK_LAN",
		3:  "TASK_DHCP",
		4:  "TASK_WAN_GROUP",
		5:  "TASK_USER_GROUP",
		6:  "TASK_USER",
		7:  "TASK_IWAN_PROXY",
		8:  "TASK_IWAN_SERVICE",
		9:  "TASK_IWAN_MAPPING",
		10: "TASK_SR_PROXY",
		11: "TASK_IP_GROUP",
		12: "TASK_DOMAIN_GROUP",
		13: "TASK_EFFECTIVE_TIME",
		14: "TASK_TRAFFIC_CHANNEL",
		15: "TASK_TRAFFIC_STAT",
		16: "TASK_FLOW_CONTROL",
		17: "TASK_ROUTE_POLICY",
		18: "TASK_DNS_POLICY",
		19: "TASK_DNS_TRACKING_POLICY",
	}
	TaskType_value = map[string]int32{
		"TASK_INTERFACE":           0,
		"TASK_WAN":                 1,
		"TASK_LAN":                 2,
		"TASK_DHCP":                3,
		"TASK_WAN_GROUP":           4,
		"TASK_USER_GROUP":          5,
		"TASK_USER":                6,
		"TASK_IWAN_PROXY":          7,
		"TASK_IWAN_SERVICE":        8,
		"TASK_IWAN_MAPPING":        9,
		"TASK_SR_PROXY":            10,
		"TASK_IP_GROUP":            11,
		"TASK_DOMAIN_GROUP":        12,
		"TASK_EFFECTIVE_TIME":      13,
		"TASK_TRAFFIC_CHANNEL":     14,
		"TASK_TRAFFIC_STAT":        15,
		"TASK_FLOW_CONTROL":        16,
		"TASK_ROUTE_POLICY":        17,
		"TASK_DNS_POLICY":          18,
		"TASK_DNS_TRACKING_POLICY": 19,
	}
)

func (x TaskType) Enum() *TaskType {
	p := new(TaskType)
	*p = x
	return p
}

func (x TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[0].Descriptor()
}

func (TaskType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[0]
}

func (x TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskType.Descriptor instead.
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{0}
}

// 同步消息逻辑中，任务动作枚举
// 对于大部分模块来说（目前所有的模块），Agent 在处理 NEW_CONFIG 和 EDIT_CONFIG 时行为是一致的；
type TaskAction int32

const (
	TaskAction_NEW_CONFIG    TaskAction = 0
	TaskAction_EDIT_CONFIG   TaskAction = 1
	TaskAction_DELETE_CONFIG TaskAction = 2
)

// Enum value maps for TaskAction.
var (
	TaskAction_name = map[int32]string{
		0: "NEW_CONFIG",
		1: "EDIT_CONFIG",
		2: "DELETE_CONFIG",
	}
	TaskAction_value = map[string]int32{
		"NEW_CONFIG":    0,
		"EDIT_CONFIG":   1,
		"DELETE_CONFIG": 2,
	}
)

func (x TaskAction) Enum() *TaskAction {
	p := new(TaskAction)
	*p = x
	return p
}

func (x TaskAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskAction) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[1].Descriptor()
}

func (TaskAction) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[1]
}

func (x TaskAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskAction.Descriptor instead.
func (TaskAction) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{1}
}

// 同步类型枚举
type SyncType int32

const (
	SyncType_INCREMENTAL_SYNC SyncType = 0 // 增量同步
	SyncType_FULL_SYNC        SyncType = 1 // 全量同步
)

// Enum value maps for SyncType.
var (
	SyncType_name = map[int32]string{
		0: "INCREMENTAL_SYNC",
		1: "FULL_SYNC",
	}
	SyncType_value = map[string]int32{
		"INCREMENTAL_SYNC": 0,
		"FULL_SYNC":        1,
	}
)

func (x SyncType) Enum() *SyncType {
	p := new(SyncType)
	*p = x
	return p
}

func (x SyncType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SyncType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[2].Descriptor()
}

func (SyncType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[2]
}

func (x SyncType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SyncType.Descriptor instead.
func (SyncType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{2}
}

// 网卡接口类型枚举
type InterfaceMode int32

const (
	InterfaceMode_INTERFACE_MODE_MONITOR InterfaceMode = 0 // 监控模式，网卡的普通接入模式
	InterfaceMode_INTERFACE_MODE_BRIDGE1 InterfaceMode = 1 // 网桥模式1
	InterfaceMode_INTERFACE_MODE_BRIDGE2 InterfaceMode = 2 // 网桥模式2
	InterfaceMode_INTERFACE_MODE_BRIDGE3 InterfaceMode = 3 // 网桥模式3
	InterfaceMode_INTERFACE_MODE_BRIDGE4 InterfaceMode = 4 // 网桥模式4
	InterfaceMode_INTERFACE_MODE_BRIDGE5 InterfaceMode = 5 // 网桥模式5
	InterfaceMode_INTERFACE_MODE_BRIDGE6 InterfaceMode = 6 // 网桥模式6
)

// Enum value maps for InterfaceMode.
var (
	InterfaceMode_name = map[int32]string{
		0: "INTERFACE_MODE_MONITOR",
		1: "INTERFACE_MODE_BRIDGE1",
		2: "INTERFACE_MODE_BRIDGE2",
		3: "INTERFACE_MODE_BRIDGE3",
		4: "INTERFACE_MODE_BRIDGE4",
		5: "INTERFACE_MODE_BRIDGE5",
		6: "INTERFACE_MODE_BRIDGE6",
	}
	InterfaceMode_value = map[string]int32{
		"INTERFACE_MODE_MONITOR": 0,
		"INTERFACE_MODE_BRIDGE1": 1,
		"INTERFACE_MODE_BRIDGE2": 2,
		"INTERFACE_MODE_BRIDGE3": 3,
		"INTERFACE_MODE_BRIDGE4": 4,
		"INTERFACE_MODE_BRIDGE5": 5,
		"INTERFACE_MODE_BRIDGE6": 6,
	}
)

func (x InterfaceMode) Enum() *InterfaceMode {
	p := new(InterfaceMode)
	*p = x
	return p
}

func (x InterfaceMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InterfaceMode) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[3].Descriptor()
}

func (InterfaceMode) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[3]
}

func (x InterfaceMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InterfaceMode.Descriptor instead.
func (InterfaceMode) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{3}
}

// 网卡接入位置枚举
type InterfaceZone int32

const (
	InterfaceZone_INTERFACE_ZONE_INSIDE  InterfaceZone = 0 // 接内
	InterfaceZone_INTERFACE_ZONE_OUTSIDE InterfaceZone = 1 // 接外
)

// Enum value maps for InterfaceZone.
var (
	InterfaceZone_name = map[int32]string{
		0: "INTERFACE_ZONE_INSIDE",
		1: "INTERFACE_ZONE_OUTSIDE",
	}
	InterfaceZone_value = map[string]int32{
		"INTERFACE_ZONE_INSIDE":  0,
		"INTERFACE_ZONE_OUTSIDE": 1,
	}
)

func (x InterfaceZone) Enum() *InterfaceZone {
	p := new(InterfaceZone)
	*p = x
	return p
}

func (x InterfaceZone) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InterfaceZone) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[4].Descriptor()
}

func (InterfaceZone) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[4]
}

func (x InterfaceZone) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InterfaceZone.Descriptor instead.
func (InterfaceZone) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{4}
}

// 链路捆绑协议类型枚举
type LacpProtocol int32

const (
	LacpProtocol_LACP_PROTOCOL_STATIC LacpProtocol = 0 // 静态模式
	LacpProtocol_LACP_PROTOCOL_LACP   LacpProtocol = 1 // LACP协议模式
)

// Enum value maps for LacpProtocol.
var (
	LacpProtocol_name = map[int32]string{
		0: "LACP_PROTOCOL_STATIC",
		1: "LACP_PROTOCOL_LACP",
	}
	LacpProtocol_value = map[string]int32{
		"LACP_PROTOCOL_STATIC": 0,
		"LACP_PROTOCOL_LACP":   1,
	}
)

func (x LacpProtocol) Enum() *LacpProtocol {
	p := new(LacpProtocol)
	*p = x
	return p
}

func (x LacpProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LacpProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[5].Descriptor()
}

func (LacpProtocol) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[5]
}

func (x LacpProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LacpProtocol.Descriptor instead.
func (LacpProtocol) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{5}
}

// 链路捆绑老化模式枚举
type LacpTimeout int32

const (
	LacpTimeout_LACP_TIMEOUT_SLOW LacpTimeout = 0 // 慢速模式
	LacpTimeout_LACP_TIMEOUT_FAST LacpTimeout = 1 // 快速模式
)

// Enum value maps for LacpTimeout.
var (
	LacpTimeout_name = map[int32]string{
		0: "LACP_TIMEOUT_SLOW",
		1: "LACP_TIMEOUT_FAST",
	}
	LacpTimeout_value = map[string]int32{
		"LACP_TIMEOUT_SLOW": 0,
		"LACP_TIMEOUT_FAST": 1,
	}
)

func (x LacpTimeout) Enum() *LacpTimeout {
	p := new(LacpTimeout)
	*p = x
	return p
}

func (x LacpTimeout) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LacpTimeout) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[6].Descriptor()
}

func (LacpTimeout) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[6]
}

func (x LacpTimeout) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LacpTimeout.Descriptor instead.
func (LacpTimeout) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{6}
}

// WAN 网关类型枚举
type WanGatewayType int32

const (
	WanGatewayType_WAN_GATEWAY_TYPE_NORMAL   WanGatewayType = 0 // 正常网关
	WanGatewayType_WAN_GATEWAY_TYPE_INTERNET WanGatewayType = 1 // 互联网关
)

// Enum value maps for WanGatewayType.
var (
	WanGatewayType_name = map[int32]string{
		0: "WAN_GATEWAY_TYPE_NORMAL",
		1: "WAN_GATEWAY_TYPE_INTERNET",
	}
	WanGatewayType_value = map[string]int32{
		"WAN_GATEWAY_TYPE_NORMAL":   0,
		"WAN_GATEWAY_TYPE_INTERNET": 1,
	}
)

func (x WanGatewayType) Enum() *WanGatewayType {
	p := new(WanGatewayType)
	*p = x
	return p
}

func (x WanGatewayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WanGatewayType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[7].Descriptor()
}

func (WanGatewayType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[7]
}

func (x WanGatewayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WanGatewayType.Descriptor instead.
func (WanGatewayType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{7}
}

// DHCP 选项值类型枚举
type DhcpOptionValueType int32

const (
	DhcpOptionValueType_DHCP_OPTION_TYPE_STRING DhcpOptionValueType = 0 // 普通字符串
	DhcpOptionValueType_DHCP_OPTION_TYPE_HEX    DhcpOptionValueType = 1 // 十六进制字符串
)

// Enum value maps for DhcpOptionValueType.
var (
	DhcpOptionValueType_name = map[int32]string{
		0: "DHCP_OPTION_TYPE_STRING",
		1: "DHCP_OPTION_TYPE_HEX",
	}
	DhcpOptionValueType_value = map[string]int32{
		"DHCP_OPTION_TYPE_STRING": 0,
		"DHCP_OPTION_TYPE_HEX":    1,
	}
)

func (x DhcpOptionValueType) Enum() *DhcpOptionValueType {
	p := new(DhcpOptionValueType)
	*p = x
	return p
}

func (x DhcpOptionValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DhcpOptionValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[8].Descriptor()
}

func (DhcpOptionValueType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[8]
}

func (x DhcpOptionValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DhcpOptionValueType.Descriptor instead.
func (DhcpOptionValueType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{8}
}

// WAN群组类型枚举
type WanGroupType int32

const (
	WanGroupType_WAN_GROUP_TYPE_SRCDST    WanGroupType = 0 // 源地址+目的地址
	WanGroupType_WAN_GROUP_TYPE_SPDP      WanGroupType = 1 // 源目地址+源目端口
	WanGroupType_WAN_GROUP_TYPE_SRC       WanGroupType = 2 // 源地址
	WanGroupType_WAN_GROUP_TYPE_SRCSPORT  WanGroupType = 3 // 源地址+源端口
	WanGroupType_WAN_GROUP_TYPE_DST       WanGroupType = 4 // 目的地址
	WanGroupType_WAN_GROUP_TYPE_DSTDPORT  WanGroupType = 5 // 目的地址+目的端口
	WanGroupType_WAN_GROUP_TYPE_RX_LEFTBW WanGroupType = 6 // 最大下行空闲带宽
	WanGroupType_WAN_GROUP_TYPE_TX_LEFTBW WanGroupType = 7 // 最大上行空闲带宽
	WanGroupType_WAN_GROUP_TYPE_SESSION   WanGroupType = 8 // 最小连接数
	WanGroupType_WAN_GROUP_TYPE_FAILOVER  WanGroupType = 9 // 主备模式
)

// Enum value maps for WanGroupType.
var (
	WanGroupType_name = map[int32]string{
		0: "WAN_GROUP_TYPE_SRCDST",
		1: "WAN_GROUP_TYPE_SPDP",
		2: "WAN_GROUP_TYPE_SRC",
		3: "WAN_GROUP_TYPE_SRCSPORT",
		4: "WAN_GROUP_TYPE_DST",
		5: "WAN_GROUP_TYPE_DSTDPORT",
		6: "WAN_GROUP_TYPE_RX_LEFTBW",
		7: "WAN_GROUP_TYPE_TX_LEFTBW",
		8: "WAN_GROUP_TYPE_SESSION",
		9: "WAN_GROUP_TYPE_FAILOVER",
	}
	WanGroupType_value = map[string]int32{
		"WAN_GROUP_TYPE_SRCDST":    0,
		"WAN_GROUP_TYPE_SPDP":      1,
		"WAN_GROUP_TYPE_SRC":       2,
		"WAN_GROUP_TYPE_SRCSPORT":  3,
		"WAN_GROUP_TYPE_DST":       4,
		"WAN_GROUP_TYPE_DSTDPORT":  5,
		"WAN_GROUP_TYPE_RX_LEFTBW": 6,
		"WAN_GROUP_TYPE_TX_LEFTBW": 7,
		"WAN_GROUP_TYPE_SESSION":   8,
		"WAN_GROUP_TYPE_FAILOVER":  9,
	}
)

func (x WanGroupType) Enum() *WanGroupType {
	p := new(WanGroupType)
	*p = x
	return p
}

func (x WanGroupType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WanGroupType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[9].Descriptor()
}

func (WanGroupType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[9]
}

func (x WanGroupType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WanGroupType.Descriptor instead.
func (WanGroupType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{9}
}

// 过期账号处理方式枚举
type UserExpiredPolicy int32

const (
	UserExpiredPolicy_USER_EXPIRED_POLICY_REJECT UserExpiredPolicy = 0 // 禁止登录
	UserExpiredPolicy_USER_EXPIRED_POLICY_LOGIN  UserExpiredPolicy = 1 // 允许登录，禁止上网
	UserExpiredPolicy_USER_EXPIRED_POLICY_PASS   UserExpiredPolicy = 2 // 允许登录及上网
)

// Enum value maps for UserExpiredPolicy.
var (
	UserExpiredPolicy_name = map[int32]string{
		0: "USER_EXPIRED_POLICY_REJECT",
		1: "USER_EXPIRED_POLICY_LOGIN",
		2: "USER_EXPIRED_POLICY_PASS",
	}
	UserExpiredPolicy_value = map[string]int32{
		"USER_EXPIRED_POLICY_REJECT": 0,
		"USER_EXPIRED_POLICY_LOGIN":  1,
		"USER_EXPIRED_POLICY_PASS":   2,
	}
)

func (x UserExpiredPolicy) Enum() *UserExpiredPolicy {
	p := new(UserExpiredPolicy)
	*p = x
	return p
}

func (x UserExpiredPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserExpiredPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[10].Descriptor()
}

func (UserExpiredPolicy) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[10]
}

func (x UserExpiredPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserExpiredPolicy.Descriptor instead.
func (UserExpiredPolicy) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{10}
}

// SR加密类型枚举
type SrEncryptType int32

const (
	SrEncryptType_SR_ENCRYPT_NONE   SrEncryptType = 0 // 不加密
	SrEncryptType_SR_ENCRYPT_AES128 SrEncryptType = 1 // AES128加密
	SrEncryptType_SR_ENCRYPT_AES256 SrEncryptType = 2 // AES256加密
)

// Enum value maps for SrEncryptType.
var (
	SrEncryptType_name = map[int32]string{
		0: "SR_ENCRYPT_NONE",
		1: "SR_ENCRYPT_AES128",
		2: "SR_ENCRYPT_AES256",
	}
	SrEncryptType_value = map[string]int32{
		"SR_ENCRYPT_NONE":   0,
		"SR_ENCRYPT_AES128": 1,
		"SR_ENCRYPT_AES256": 2,
	}
)

func (x SrEncryptType) Enum() *SrEncryptType {
	p := new(SrEncryptType)
	*p = x
	return p
}

func (x SrEncryptType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SrEncryptType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[11].Descriptor()
}

func (SrEncryptType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[11]
}

func (x SrEncryptType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SrEncryptType.Descriptor instead.
func (SrEncryptType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{11}
}

// 流量控制策略动作类型枚举
type FlowControlAction int32

const (
	FlowControlAction_FLOW_CONTROL_ACTION_PERMIT  FlowControlAction = 0 // 允许通过
	FlowControlAction_FLOW_CONTROL_ACTION_DENY    FlowControlAction = 1 // 阻断
	FlowControlAction_FLOW_CONTROL_ACTION_CHANNEL FlowControlAction = 2 // 流量通道限速
)

// Enum value maps for FlowControlAction.
var (
	FlowControlAction_name = map[int32]string{
		0: "FLOW_CONTROL_ACTION_PERMIT",
		1: "FLOW_CONTROL_ACTION_DENY",
		2: "FLOW_CONTROL_ACTION_CHANNEL",
	}
	FlowControlAction_value = map[string]int32{
		"FLOW_CONTROL_ACTION_PERMIT":  0,
		"FLOW_CONTROL_ACTION_DENY":    1,
		"FLOW_CONTROL_ACTION_CHANNEL": 2,
	}
)

func (x FlowControlAction) Enum() *FlowControlAction {
	p := new(FlowControlAction)
	*p = x
	return p
}

func (x FlowControlAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowControlAction) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[12].Descriptor()
}

func (FlowControlAction) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[12]
}

func (x FlowControlAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowControlAction.Descriptor instead.
func (FlowControlAction) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{12}
}

// 流量方向枚举
type FlowDirection int32

const (
	FlowDirection_FLOW_DIRECTION_BOTH FlowDirection = 0 // 双向
	FlowDirection_FLOW_DIRECTION_IN   FlowDirection = 1 // 入方向
	FlowDirection_FLOW_DIRECTION_OUT  FlowDirection = 2 // 出方向
)

// Enum value maps for FlowDirection.
var (
	FlowDirection_name = map[int32]string{
		0: "FLOW_DIRECTION_BOTH",
		1: "FLOW_DIRECTION_IN",
		2: "FLOW_DIRECTION_OUT",
	}
	FlowDirection_value = map[string]int32{
		"FLOW_DIRECTION_BOTH": 0,
		"FLOW_DIRECTION_IN":   1,
		"FLOW_DIRECTION_OUT":  2,
	}
)

func (x FlowDirection) Enum() *FlowDirection {
	p := new(FlowDirection)
	*p = x
	return p
}

func (x FlowDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[13].Descriptor()
}

func (FlowDirection) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[13]
}

func (x FlowDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowDirection.Descriptor instead.
func (FlowDirection) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{13}
}

// 路由策略动作类型枚举
type RoutePolicyAction int32

const (
	RoutePolicyAction_ROUTE_ACTION_ROUTE RoutePolicyAction = 0 // 路由
	RoutePolicyAction_ROUTE_ACTION_NAT   RoutePolicyAction = 1 // NAT
	RoutePolicyAction_ROUTE_ACTION_DNAT  RoutePolicyAction = 2 // DNAT
	RoutePolicyAction_ROUTE_ACTION_PROXY RoutePolicyAction = 3 // 代播
)

// Enum value maps for RoutePolicyAction.
var (
	RoutePolicyAction_name = map[int32]string{
		0: "ROUTE_ACTION_ROUTE",
		1: "ROUTE_ACTION_NAT",
		2: "ROUTE_ACTION_DNAT",
		3: "ROUTE_ACTION_PROXY",
	}
	RoutePolicyAction_value = map[string]int32{
		"ROUTE_ACTION_ROUTE": 0,
		"ROUTE_ACTION_NAT":   1,
		"ROUTE_ACTION_DNAT":  2,
		"ROUTE_ACTION_PROXY": 3,
	}
)

func (x RoutePolicyAction) Enum() *RoutePolicyAction {
	p := new(RoutePolicyAction)
	*p = x
	return p
}

func (x RoutePolicyAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoutePolicyAction) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[14].Descriptor()
}

func (RoutePolicyAction) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[14]
}

func (x RoutePolicyAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoutePolicyAction.Descriptor instead.
func (RoutePolicyAction) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{14}
}

// 路由策略Zone枚举 - 用于分层排序管理
type RoutePolicyZone int32

const (
	RoutePolicyZone_CTRL_TIER_T1    RoutePolicyZone = 0 // 平台连接策略层 (优先级1-5000)
	RoutePolicyZone_CUST_TIER_T2    RoutePolicyZone = 1 // 自定义策略层 (优先级5001-50000)
	RoutePolicyZone_LPM_TIER_T3     RoutePolicyZone = 2 // 最长前缀策略层 (优先级50001-60000)
	RoutePolicyZone_DEF_WAN_TIER_T4 RoutePolicyZone = 3 // 默认路由策略层 (优先级60001-65535)
)

// Enum value maps for RoutePolicyZone.
var (
	RoutePolicyZone_name = map[int32]string{
		0: "CTRL_TIER_T1",
		1: "CUST_TIER_T2",
		2: "LPM_TIER_T3",
		3: "DEF_WAN_TIER_T4",
	}
	RoutePolicyZone_value = map[string]int32{
		"CTRL_TIER_T1":    0,
		"CUST_TIER_T2":    1,
		"LPM_TIER_T3":     2,
		"DEF_WAN_TIER_T4": 3,
	}
)

func (x RoutePolicyZone) Enum() *RoutePolicyZone {
	p := new(RoutePolicyZone)
	*p = x
	return p
}

func (x RoutePolicyZone) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoutePolicyZone) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[15].Descriptor()
}

func (RoutePolicyZone) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[15]
}

func (x RoutePolicyZone) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoutePolicyZone.Descriptor instead.
func (RoutePolicyZone) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{15}
}

// 用户类型枚举
type UserType int32

const (
	UserType_USER_TYPE_ANY      UserType = 0 // 任意
	UserType_USER_TYPE_IPPXY    UserType = 1 // 代播用户
	UserType_USER_TYPE_NONIPPXY UserType = 2 // 非代播用户
)

// Enum value maps for UserType.
var (
	UserType_name = map[int32]string{
		0: "USER_TYPE_ANY",
		1: "USER_TYPE_IPPXY",
		2: "USER_TYPE_NONIPPXY",
	}
	UserType_value = map[string]int32{
		"USER_TYPE_ANY":      0,
		"USER_TYPE_IPPXY":    1,
		"USER_TYPE_NONIPPXY": 2,
	}
)

func (x UserType) Enum() *UserType {
	p := new(UserType)
	*p = x
	return p
}

func (x UserType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[16].Descriptor()
}

func (UserType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[16]
}

func (x UserType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserType.Descriptor instead.
func (UserType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{16}
}

// DNS查询类型枚举
type DnsQueryType int32

const (
	DnsQueryType_DNS_QUERY_TYPE_ANY  DnsQueryType = 0 // 任意
	DnsQueryType_DNS_QUERY_TYPE_IPV4 DnsQueryType = 1 // IPv4 (A记录)
	DnsQueryType_DNS_QUERY_TYPE_IPV6 DnsQueryType = 2 // IPv6 (AAAA记录)
)

// Enum value maps for DnsQueryType.
var (
	DnsQueryType_name = map[int32]string{
		0: "DNS_QUERY_TYPE_ANY",
		1: "DNS_QUERY_TYPE_IPV4",
		2: "DNS_QUERY_TYPE_IPV6",
	}
	DnsQueryType_value = map[string]int32{
		"DNS_QUERY_TYPE_ANY":  0,
		"DNS_QUERY_TYPE_IPV4": 1,
		"DNS_QUERY_TYPE_IPV6": 2,
	}
)

func (x DnsQueryType) Enum() *DnsQueryType {
	p := new(DnsQueryType)
	*p = x
	return p
}

func (x DnsQueryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DnsQueryType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[17].Descriptor()
}

func (DnsQueryType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[17]
}

func (x DnsQueryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DnsQueryType.Descriptor instead.
func (DnsQueryType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{17}
}

// DNS策略动作类型枚举
type DnsPolicyAction int32

const (
	DnsPolicyAction_DNS_ACTION_PASS      DnsPolicyAction = 0 // 放行
	DnsPolicyAction_DNS_ACTION_DENY      DnsPolicyAction = 1 // 丢弃
	DnsPolicyAction_DNS_ACTION_RDR       DnsPolicyAction = 2 // 牵引
	DnsPolicyAction_DNS_ACTION_REPLY     DnsPolicyAction = 3 // 解析
	DnsPolicyAction_DNS_ACTION_LIMIT     DnsPolicyAction = 4 // QPS限制
	DnsPolicyAction_DNS_ACTION_IPPXY     DnsPolicyAction = 5 // 代播重定向
	DnsPolicyAction_DNS_ACTION_ZEROREPLY DnsPolicyAction = 6 // 无名应答
)

// Enum value maps for DnsPolicyAction.
var (
	DnsPolicyAction_name = map[int32]string{
		0: "DNS_ACTION_PASS",
		1: "DNS_ACTION_DENY",
		2: "DNS_ACTION_RDR",
		3: "DNS_ACTION_REPLY",
		4: "DNS_ACTION_LIMIT",
		5: "DNS_ACTION_IPPXY",
		6: "DNS_ACTION_ZEROREPLY",
	}
	DnsPolicyAction_value = map[string]int32{
		"DNS_ACTION_PASS":      0,
		"DNS_ACTION_DENY":      1,
		"DNS_ACTION_RDR":       2,
		"DNS_ACTION_REPLY":     3,
		"DNS_ACTION_LIMIT":     4,
		"DNS_ACTION_IPPXY":     5,
		"DNS_ACTION_ZEROREPLY": 6,
	}
)

func (x DnsPolicyAction) Enum() *DnsPolicyAction {
	p := new(DnsPolicyAction)
	*p = x
	return p
}

func (x DnsPolicyAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DnsPolicyAction) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[18].Descriptor()
}

func (DnsPolicyAction) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[18]
}

func (x DnsPolicyAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DnsPolicyAction.Descriptor instead.
func (DnsPolicyAction) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{18}
}

// 设备上报类型枚举
type ReportType int32

const (
	ReportType_REPORT_TYPE_PROXY_STATUS ReportType = 0 // 线路状态上报
)

// Enum value maps for ReportType.
var (
	ReportType_name = map[int32]string{
		0: "REPORT_TYPE_PROXY_STATUS",
	}
	ReportType_value = map[string]int32{
		"REPORT_TYPE_PROXY_STATUS": 0,
	}
)

func (x ReportType) Enum() *ReportType {
	p := new(ReportType)
	*p = x
	return p
}

func (x ReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[19].Descriptor()
}

func (ReportType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[19]
}

func (x ReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportType.Descriptor instead.
func (ReportType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{19}
}

// 上报触发类型枚举
type ReportTriggerType int32

const (
	ReportTriggerType_REPORT_TRIGGER_EVENT    ReportTriggerType = 0 // 事件触发上报
	ReportTriggerType_REPORT_TRIGGER_PERIODIC ReportTriggerType = 1 // 周期性全量上报
)

// Enum value maps for ReportTriggerType.
var (
	ReportTriggerType_name = map[int32]string{
		0: "REPORT_TRIGGER_EVENT",
		1: "REPORT_TRIGGER_PERIODIC",
	}
	ReportTriggerType_value = map[string]int32{
		"REPORT_TRIGGER_EVENT":    0,
		"REPORT_TRIGGER_PERIODIC": 1,
	}
)

func (x ReportTriggerType) Enum() *ReportTriggerType {
	p := new(ReportTriggerType)
	*p = x
	return p
}

func (x ReportTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[20].Descriptor()
}

func (ReportTriggerType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[20]
}

func (x ReportTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportTriggerType.Descriptor instead.
func (ReportTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{20}
}

// 线路类型枚举
type ProxyType int32

const (
	ProxyType_PROXY_TYPE_NONE  ProxyType = 0
	ProxyType_PROXY_TYPE_IWAN  ProxyType = 1 // iWAN线路
	ProxyType_PROXY_TYPE_SRPXY ProxyType = 2 // SR线路
)

// Enum value maps for ProxyType.
var (
	ProxyType_name = map[int32]string{
		0: "PROXY_TYPE_NONE",
		1: "PROXY_TYPE_IWAN",
		2: "PROXY_TYPE_SRPXY",
	}
	ProxyType_value = map[string]int32{
		"PROXY_TYPE_NONE":  0,
		"PROXY_TYPE_IWAN":  1,
		"PROXY_TYPE_SRPXY": 2,
	}
)

func (x ProxyType) Enum() *ProxyType {
	p := new(ProxyType)
	*p = x
	return p
}

func (x ProxyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProxyType) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[21].Descriptor()
}

func (ProxyType) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[21]
}

func (x ProxyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProxyType.Descriptor instead.
func (ProxyType) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{21}
}

// 线路状态枚举
type ProxyState int32

const (
	ProxyState_PROXY_STATE_DOWN ProxyState = 0 // 断线状态
	ProxyState_PROXY_STATE_UP   ProxyState = 1 // 连通状态
)

// Enum value maps for ProxyState.
var (
	ProxyState_name = map[int32]string{
		0: "PROXY_STATE_DOWN",
		1: "PROXY_STATE_UP",
	}
	ProxyState_value = map[string]int32{
		"PROXY_STATE_DOWN": 0,
		"PROXY_STATE_UP":   1,
	}
)

func (x ProxyState) Enum() *ProxyState {
	p := new(ProxyState)
	*p = x
	return p
}

func (x ProxyState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProxyState) Descriptor() protoreflect.EnumDescriptor {
	return file_message_proto_enumTypes[22].Descriptor()
}

func (ProxyState) Type() protoreflect.EnumType {
	return &file_message_proto_enumTypes[22]
}

func (x ProxyState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProxyState.Descriptor instead.
func (ProxyState) EnumDescriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{22}
}

// 客户端（CPE 或 POP）请求消息
type ClientSyncRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustomerId    int32                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`         // 客户ID
	ClientId      int32                  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`               // 客户端ID
	Uuid          string                 `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`                                        // 请求 UUID
	SyncType      SyncType               `protobuf:"varint,4,opt,name=sync_type,json=syncType,proto3,enum=SyncType" json:"sync_type,omitempty"` // 同步类型：增量或全量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientSyncRequest) Reset() {
	*x = ClientSyncRequest{}
	mi := &file_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientSyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSyncRequest) ProtoMessage() {}

func (x *ClientSyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSyncRequest.ProtoReflect.Descriptor instead.
func (*ClientSyncRequest) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{0}
}

func (x *ClientSyncRequest) GetCustomerId() int32 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ClientSyncRequest) GetClientId() int32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ClientSyncRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ClientSyncRequest) GetSyncType() SyncType {
	if x != nil {
		return x.SyncType
	}
	return SyncType_INCREMENTAL_SYNC
}

// 服务器响应消息
// 一次可以响应多个任务
type ServerSyncResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustomerId    int32                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"` // 客户ID
	ClientId      int32                  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`       // 客户端ID
	Uuid          string                 `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`                                // 返回 ClientSyncRequest 中的 uuid
	TaskTxs       []*TaskTx              `protobuf:"bytes,4,rep,name=task_txs,json=taskTxs,proto3" json:"task_txs,omitempty"`           // 子消息集合
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerSyncResponse) Reset() {
	*x = ServerSyncResponse{}
	mi := &file_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerSyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerSyncResponse) ProtoMessage() {}

func (x *ServerSyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerSyncResponse.ProtoReflect.Descriptor instead.
func (*ServerSyncResponse) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{1}
}

func (x *ServerSyncResponse) GetCustomerId() int32 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ServerSyncResponse) GetClientId() int32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ServerSyncResponse) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ServerSyncResponse) GetTaskTxs() []*TaskTx {
	if x != nil {
		return x.TaskTxs
	}
	return nil
}

// 相当于一个事务，内部可以包含多个任务
// 整体成功或者失败，通过 uuid 反馈状态给控制器
// Tx = Transaction
type TaskTx struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TxId          string                 `protobuf:"bytes,1,opt,name=tx_id,json=txId,proto3" json:"tx_id,omitempty"`                      // 来自 redis 中每个配置变更的 uuid
	DeviceTasks   []*DeviceTask          `protobuf:"bytes,2,rep,name=device_tasks,json=deviceTasks,proto3" json:"device_tasks,omitempty"` // 一个事物中包含的多条消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskTx) Reset() {
	*x = TaskTx{}
	mi := &file_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskTx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTx) ProtoMessage() {}

func (x *TaskTx) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTx.ProtoReflect.Descriptor instead.
func (*TaskTx) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{2}
}

func (x *TaskTx) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *TaskTx) GetDeviceTasks() []*DeviceTask {
	if x != nil {
		return x.DeviceTasks
	}
	return nil
}

// CPE/POP 内部对一个模块的一次操作
type DeviceTask struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	TaskType   TaskType               `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3,enum=TaskType" json:"task_type,omitempty"`         // 任务类型
	TaskAction TaskAction             `protobuf:"varint,2,opt,name=task_action,json=taskAction,proto3,enum=TaskAction" json:"task_action,omitempty"` // 任务动作
	// Types that are valid to be assigned to Payload:
	//
	//	*DeviceTask_WanTask
	//	*DeviceTask_LanTask
	//	*DeviceTask_InterfaceTask
	//	*DeviceTask_DhcpTask
	//	*DeviceTask_WanGroupTask
	//	*DeviceTask_UserGroupTask
	//	*DeviceTask_UserTask
	//	*DeviceTask_IwanProxyTask
	//	*DeviceTask_IwanServiceTask
	//	*DeviceTask_IwanMappingTask
	//	*DeviceTask_SrProxyTask
	//	*DeviceTask_IpGroupTask
	//	*DeviceTask_DomainGroupTask
	//	*DeviceTask_EffectiveTimeTask
	//	*DeviceTask_TrafficChannelTask
	//	*DeviceTask_TrafficStatTask
	//	*DeviceTask_FlowControlTask
	//	*DeviceTask_RoutePolicyTask
	//	*DeviceTask_DnsPolicyTask
	//	*DeviceTask_DnsTrackingPolicyTask
	Payload       isDeviceTask_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceTask) Reset() {
	*x = DeviceTask{}
	mi := &file_message_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceTask) ProtoMessage() {}

func (x *DeviceTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceTask.ProtoReflect.Descriptor instead.
func (*DeviceTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{3}
}

func (x *DeviceTask) GetTaskType() TaskType {
	if x != nil {
		return x.TaskType
	}
	return TaskType_TASK_INTERFACE
}

func (x *DeviceTask) GetTaskAction() TaskAction {
	if x != nil {
		return x.TaskAction
	}
	return TaskAction_NEW_CONFIG
}

func (x *DeviceTask) GetPayload() isDeviceTask_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *DeviceTask) GetWanTask() *WanTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_WanTask); ok {
			return x.WanTask
		}
	}
	return nil
}

func (x *DeviceTask) GetLanTask() *LanTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_LanTask); ok {
			return x.LanTask
		}
	}
	return nil
}

func (x *DeviceTask) GetInterfaceTask() *InterfaceTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_InterfaceTask); ok {
			return x.InterfaceTask
		}
	}
	return nil
}

func (x *DeviceTask) GetDhcpTask() *DhcpServerTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_DhcpTask); ok {
			return x.DhcpTask
		}
	}
	return nil
}

func (x *DeviceTask) GetWanGroupTask() *WanGroupTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_WanGroupTask); ok {
			return x.WanGroupTask
		}
	}
	return nil
}

func (x *DeviceTask) GetUserGroupTask() *UserGroupTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_UserGroupTask); ok {
			return x.UserGroupTask
		}
	}
	return nil
}

func (x *DeviceTask) GetUserTask() *UserTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_UserTask); ok {
			return x.UserTask
		}
	}
	return nil
}

func (x *DeviceTask) GetIwanProxyTask() *IwanProxyTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_IwanProxyTask); ok {
			return x.IwanProxyTask
		}
	}
	return nil
}

func (x *DeviceTask) GetIwanServiceTask() *IwanServiceTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_IwanServiceTask); ok {
			return x.IwanServiceTask
		}
	}
	return nil
}

func (x *DeviceTask) GetIwanMappingTask() *IwanMappingTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_IwanMappingTask); ok {
			return x.IwanMappingTask
		}
	}
	return nil
}

func (x *DeviceTask) GetSrProxyTask() *SrProxyTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_SrProxyTask); ok {
			return x.SrProxyTask
		}
	}
	return nil
}

func (x *DeviceTask) GetIpGroupTask() *IpGroupTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_IpGroupTask); ok {
			return x.IpGroupTask
		}
	}
	return nil
}

func (x *DeviceTask) GetDomainGroupTask() *DomainGroupTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_DomainGroupTask); ok {
			return x.DomainGroupTask
		}
	}
	return nil
}

func (x *DeviceTask) GetEffectiveTimeTask() *EffectiveTimeTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_EffectiveTimeTask); ok {
			return x.EffectiveTimeTask
		}
	}
	return nil
}

func (x *DeviceTask) GetTrafficChannelTask() *TrafficChannelTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_TrafficChannelTask); ok {
			return x.TrafficChannelTask
		}
	}
	return nil
}

func (x *DeviceTask) GetTrafficStatTask() *TrafficStatTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_TrafficStatTask); ok {
			return x.TrafficStatTask
		}
	}
	return nil
}

func (x *DeviceTask) GetFlowControlTask() *FlowControlTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_FlowControlTask); ok {
			return x.FlowControlTask
		}
	}
	return nil
}

func (x *DeviceTask) GetRoutePolicyTask() *RoutePolicyTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_RoutePolicyTask); ok {
			return x.RoutePolicyTask
		}
	}
	return nil
}

func (x *DeviceTask) GetDnsPolicyTask() *DnsPolicyTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_DnsPolicyTask); ok {
			return x.DnsPolicyTask
		}
	}
	return nil
}

func (x *DeviceTask) GetDnsTrackingPolicyTask() *DnsTrackingPolicyTask {
	if x != nil {
		if x, ok := x.Payload.(*DeviceTask_DnsTrackingPolicyTask); ok {
			return x.DnsTrackingPolicyTask
		}
	}
	return nil
}

type isDeviceTask_Payload interface {
	isDeviceTask_Payload()
}

type DeviceTask_WanTask struct {
	WanTask *WanTask `protobuf:"bytes,3,opt,name=wan_task,json=wanTask,proto3,oneof"` // WAN配置任务
}

type DeviceTask_LanTask struct {
	LanTask *LanTask `protobuf:"bytes,4,opt,name=lan_task,json=lanTask,proto3,oneof"` // LAN配置任务
}

type DeviceTask_InterfaceTask struct {
	InterfaceTask *InterfaceTask `protobuf:"bytes,5,opt,name=interface_task,json=interfaceTask,proto3,oneof"` // 网卡配置任务
}

type DeviceTask_DhcpTask struct {
	DhcpTask *DhcpServerTask `protobuf:"bytes,6,opt,name=dhcp_task,json=dhcpTask,proto3,oneof"` // DHCP配置任务
}

type DeviceTask_WanGroupTask struct {
	WanGroupTask *WanGroupTask `protobuf:"bytes,7,opt,name=wan_group_task,json=wanGroupTask,proto3,oneof"` // WAN群组配置任务
}

type DeviceTask_UserGroupTask struct {
	UserGroupTask *UserGroupTask `protobuf:"bytes,8,opt,name=user_group_task,json=userGroupTask,proto3,oneof"` // 用户组配置任务
}

type DeviceTask_UserTask struct {
	UserTask *UserTask `protobuf:"bytes,9,opt,name=user_task,json=userTask,proto3,oneof"` // 用户配置任务
}

type DeviceTask_IwanProxyTask struct {
	IwanProxyTask *IwanProxyTask `protobuf:"bytes,10,opt,name=iwan_proxy_task,json=iwanProxyTask,proto3,oneof"` // iWAN Proxy线路配置任务
}

type DeviceTask_IwanServiceTask struct {
	IwanServiceTask *IwanServiceTask `protobuf:"bytes,11,opt,name=iwan_service_task,json=iwanServiceTask,proto3,oneof"` // iWAN Service服务配置任务
}

type DeviceTask_IwanMappingTask struct {
	IwanMappingTask *IwanMappingTask `protobuf:"bytes,12,opt,name=iwan_mapping_task,json=iwanMappingTask,proto3,oneof"` // iWAN Mapping配置任务
}

type DeviceTask_SrProxyTask struct {
	SrProxyTask *SrProxyTask `protobuf:"bytes,13,opt,name=sr_proxy_task,json=srProxyTask,proto3,oneof"` // SR Proxy配置任务
}

type DeviceTask_IpGroupTask struct {
	IpGroupTask *IpGroupTask `protobuf:"bytes,14,opt,name=ip_group_task,json=ipGroupTask,proto3,oneof"` // IP群组配置任务
}

type DeviceTask_DomainGroupTask struct {
	DomainGroupTask *DomainGroupTask `protobuf:"bytes,15,opt,name=domain_group_task,json=domainGroupTask,proto3,oneof"` // 域名群组配置任务
}

type DeviceTask_EffectiveTimeTask struct {
	EffectiveTimeTask *EffectiveTimeTask `protobuf:"bytes,16,opt,name=effective_time_task,json=effectiveTimeTask,proto3,oneof"` // 策略时段配置任务
}

type DeviceTask_TrafficChannelTask struct {
	TrafficChannelTask *TrafficChannelTask `protobuf:"bytes,17,opt,name=traffic_channel_task,json=trafficChannelTask,proto3,oneof"` // 流量通道配置任务
}

type DeviceTask_TrafficStatTask struct {
	TrafficStatTask *TrafficStatTask `protobuf:"bytes,18,opt,name=traffic_stat_task,json=trafficStatTask,proto3,oneof"` // 流量统计配置任务
}

type DeviceTask_FlowControlTask struct {
	FlowControlTask *FlowControlTask `protobuf:"bytes,19,opt,name=flow_control_task,json=flowControlTask,proto3,oneof"` // 流量控制配置任务
}

type DeviceTask_RoutePolicyTask struct {
	RoutePolicyTask *RoutePolicyTask `protobuf:"bytes,20,opt,name=route_policy_task,json=routePolicyTask,proto3,oneof"` // 路由策略配置任务
}

type DeviceTask_DnsPolicyTask struct {
	DnsPolicyTask *DnsPolicyTask `protobuf:"bytes,21,opt,name=dns_policy_task,json=dnsPolicyTask,proto3,oneof"` // DNS管控策略配置任务
}

type DeviceTask_DnsTrackingPolicyTask struct {
	DnsTrackingPolicyTask *DnsTrackingPolicyTask `protobuf:"bytes,22,opt,name=dns_tracking_policy_task,json=dnsTrackingPolicyTask,proto3,oneof"` // DNS跟踪策略配置任务
}

func (*DeviceTask_WanTask) isDeviceTask_Payload() {}

func (*DeviceTask_LanTask) isDeviceTask_Payload() {}

func (*DeviceTask_InterfaceTask) isDeviceTask_Payload() {}

func (*DeviceTask_DhcpTask) isDeviceTask_Payload() {}

func (*DeviceTask_WanGroupTask) isDeviceTask_Payload() {}

func (*DeviceTask_UserGroupTask) isDeviceTask_Payload() {}

func (*DeviceTask_UserTask) isDeviceTask_Payload() {}

func (*DeviceTask_IwanProxyTask) isDeviceTask_Payload() {}

func (*DeviceTask_IwanServiceTask) isDeviceTask_Payload() {}

func (*DeviceTask_IwanMappingTask) isDeviceTask_Payload() {}

func (*DeviceTask_SrProxyTask) isDeviceTask_Payload() {}

func (*DeviceTask_IpGroupTask) isDeviceTask_Payload() {}

func (*DeviceTask_DomainGroupTask) isDeviceTask_Payload() {}

func (*DeviceTask_EffectiveTimeTask) isDeviceTask_Payload() {}

func (*DeviceTask_TrafficChannelTask) isDeviceTask_Payload() {}

func (*DeviceTask_TrafficStatTask) isDeviceTask_Payload() {}

func (*DeviceTask_FlowControlTask) isDeviceTask_Payload() {}

func (*DeviceTask_RoutePolicyTask) isDeviceTask_Payload() {}

func (*DeviceTask_DnsPolicyTask) isDeviceTask_Payload() {}

func (*DeviceTask_DnsTrackingPolicyTask) isDeviceTask_Payload() {}

type TaskTxStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TxId          string                 `protobuf:"bytes,1,opt,name=tx_id,json=txId,proto3" json:"tx_id,omitempty"`           // 事务ID
	ErrCode       int32                  `protobuf:"varint,2,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"` // 0 表示成功，其他的错误场景控制和 Agent 一起定义
	Desc          string                 `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`                       // 描述信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskTxStatus) Reset() {
	*x = TaskTxStatus{}
	mi := &file_message_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskTxStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTxStatus) ProtoMessage() {}

func (x *TaskTxStatus) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTxStatus.ProtoReflect.Descriptor instead.
func (*TaskTxStatus) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{4}
}

func (x *TaskTxStatus) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *TaskTxStatus) GetErrCode() int32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

func (x *TaskTxStatus) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

// 客户端执行情况反馈
type ClientSyncAck struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CustomerId    int32                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`        // 客户ID
	ClientId      int32                  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`              // 客户端ID
	Uuid          string                 `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`                                       // 对应 ClientSyncRequest 中的 uuid
	TaskTxStatus  []*TaskTxStatus        `protobuf:"bytes,4,rep,name=task_tx_status,json=taskTxStatus,proto3" json:"task_tx_status,omitempty"` // 任务执行状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientSyncAck) Reset() {
	*x = ClientSyncAck{}
	mi := &file_message_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientSyncAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSyncAck) ProtoMessage() {}

func (x *ClientSyncAck) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSyncAck.ProtoReflect.Descriptor instead.
func (*ClientSyncAck) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{5}
}

func (x *ClientSyncAck) GetCustomerId() int32 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ClientSyncAck) GetClientId() int32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ClientSyncAck) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ClientSyncAck) GetTaskTxStatus() []*TaskTxStatus {
	if x != nil {
		return x.TaskTxStatus
	}
	return nil
}

// LACP配置消息
type LacpConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Protocol      LacpProtocol           `protobuf:"varint,1,opt,name=protocol,proto3,enum=LacpProtocol" json:"protocol,omitempty"`    // 捆绑协议：static/LACP (required)
	Timeout       *LacpTimeout           `protobuf:"varint,2,opt,name=timeout,proto3,enum=LacpTimeout,oneof" json:"timeout,omitempty"` // 老化模式：slow/fast
	Passive       *bool                  `protobuf:"varint,3,opt,name=passive,proto3,oneof" json:"passive,omitempty"`                  // 被动模式：true/false
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LacpConfig) Reset() {
	*x = LacpConfig{}
	mi := &file_message_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LacpConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LacpConfig) ProtoMessage() {}

func (x *LacpConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LacpConfig.ProtoReflect.Descriptor instead.
func (*LacpConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{6}
}

func (x *LacpConfig) GetProtocol() LacpProtocol {
	if x != nil {
		return x.Protocol
	}
	return LacpProtocol_LACP_PROTOCOL_STATIC
}

func (x *LacpConfig) GetTimeout() LacpTimeout {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return LacpTimeout_LACP_TIMEOUT_SLOW
}

func (x *LacpConfig) GetPassive() bool {
	if x != nil && x.Passive != nil {
		return *x.Passive
	}
	return false
}

// 网卡配置消息
type InterfaceTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                     // 网卡名称，如 eth0 (required)
	Mode          InterfaceMode          `protobuf:"varint,2,opt,name=mode,proto3,enum=InterfaceMode" json:"mode,omitempty"`                 // 接口类型：监控模式或网桥模式 (required)
	Zone          InterfaceZone          `protobuf:"varint,3,opt,name=zone,proto3,enum=InterfaceZone" json:"zone,omitempty"`                 // 接入位置：inside/outside (required)
	MixMode       *bool                  `protobuf:"varint,4,opt,name=mix_mode,json=mixMode,proto3,oneof" json:"mix_mode,omitempty"`         // 混合模式：true/false
	LaGroup       *uint32                `protobuf:"varint,5,opt,name=la_group,json=laGroup,proto3,oneof" json:"la_group,omitempty"`         // 链路捆绑组：0-6，0为不捆绑，1-6为链路组
	Peer          *string                `protobuf:"bytes,6,opt,name=peer,proto3,oneof" json:"peer,omitempty"`                               // 网桥对端网卡名称（网桥模式时必填）
	LacpConfig    *LacpConfig            `protobuf:"bytes,7,opt,name=lacp_config,json=lacpConfig,proto3,oneof" json:"lacp_config,omitempty"` // LACP配置（链路捆绑时使用）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InterfaceTask) Reset() {
	*x = InterfaceTask{}
	mi := &file_message_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceTask) ProtoMessage() {}

func (x *InterfaceTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceTask.ProtoReflect.Descriptor instead.
func (*InterfaceTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{7}
}

func (x *InterfaceTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InterfaceTask) GetMode() InterfaceMode {
	if x != nil {
		return x.Mode
	}
	return InterfaceMode_INTERFACE_MODE_MONITOR
}

func (x *InterfaceTask) GetZone() InterfaceZone {
	if x != nil {
		return x.Zone
	}
	return InterfaceZone_INTERFACE_ZONE_INSIDE
}

func (x *InterfaceTask) GetMixMode() bool {
	if x != nil && x.MixMode != nil {
		return *x.MixMode
	}
	return false
}

func (x *InterfaceTask) GetLaGroup() uint32 {
	if x != nil && x.LaGroup != nil {
		return *x.LaGroup
	}
	return 0
}

func (x *InterfaceTask) GetPeer() string {
	if x != nil && x.Peer != nil {
		return *x.Peer
	}
	return ""
}

func (x *InterfaceTask) GetLacpConfig() *LacpConfig {
	if x != nil {
		return x.LacpConfig
	}
	return nil
}

// 心跳请求消息
type HeartbeatRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CustomerId     int32                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`               // 客户ID
	ClientId       int32                  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`                     // 客户端 ID
	CheckNewConfig bool                   `protobuf:"varint,3,opt,name=check_new_config,json=checkNewConfig,proto3" json:"check_new_config,omitempty"` // 是否检查新配置
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HeartbeatRequest) Reset() {
	*x = HeartbeatRequest{}
	mi := &file_message_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatRequest) ProtoMessage() {}

func (x *HeartbeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatRequest.ProtoReflect.Descriptor instead.
func (*HeartbeatRequest) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{8}
}

func (x *HeartbeatRequest) GetCustomerId() int32 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *HeartbeatRequest) GetClientId() int32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *HeartbeatRequest) GetCheckNewConfig() bool {
	if x != nil {
		return x.CheckNewConfig
	}
	return false
}

// 心跳响应消息
type HeartbeatResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasNewConfig  bool                   `protobuf:"varint,1,opt,name=has_new_config,json=hasNewConfig,proto3" json:"has_new_config,omitempty"` // 是否有新配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartbeatResponse) Reset() {
	*x = HeartbeatResponse{}
	mi := &file_message_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatResponse) ProtoMessage() {}

func (x *HeartbeatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatResponse.ProtoReflect.Descriptor instead.
func (*HeartbeatResponse) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{9}
}

func (x *HeartbeatResponse) GetHasNewConfig() bool {
	if x != nil {
		return x.HasNewConfig
	}
	return false
}

type ErrorCode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorCode) Reset() {
	*x = ErrorCode{}
	mi := &file_message_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorCode) ProtoMessage() {}

func (x *ErrorCode) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorCode.ProtoReflect.Descriptor instead.
func (*ErrorCode) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{10}
}

func (x *ErrorCode) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// IPv4 CIDR 表示
type V4Cidr struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            uint32                 `protobuf:"varint,1,opt,name=ip,proto3" json:"ip,omitempty"`                                         // IPv4 地址（32位整数）(required)
	PrefixLength  uint32                 `protobuf:"varint,2,opt,name=prefix_length,json=prefixLength,proto3" json:"prefix_length,omitempty"` // 前缀长度（0-32）(required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *V4Cidr) Reset() {
	*x = V4Cidr{}
	mi := &file_message_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *V4Cidr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V4Cidr) ProtoMessage() {}

func (x *V4Cidr) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V4Cidr.ProtoReflect.Descriptor instead.
func (*V4Cidr) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{11}
}

func (x *V4Cidr) GetIp() uint32 {
	if x != nil {
		return x.Ip
	}
	return 0
}

func (x *V4Cidr) GetPrefixLength() uint32 {
	if x != nil {
		return x.PrefixLength
	}
	return 0
}

// IPv6 CIDR 表示
type V6Cidr struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            []byte                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`                                          // IPv6 地址（16字节）(required)
	PrefixLength  uint32                 `protobuf:"varint,2,opt,name=prefix_length,json=prefixLength,proto3" json:"prefix_length,omitempty"` // 前缀长度（0-128）(required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *V6Cidr) Reset() {
	*x = V6Cidr{}
	mi := &file_message_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *V6Cidr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V6Cidr) ProtoMessage() {}

func (x *V6Cidr) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V6Cidr.ProtoReflect.Descriptor instead.
func (*V6Cidr) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{12}
}

func (x *V6Cidr) GetIp() []byte {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *V6Cidr) GetPrefixLength() uint32 {
	if x != nil {
		return x.PrefixLength
	}
	return 0
}

// IP 地址通用表示
type IpAddress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Ip:
	//
	//	*IpAddress_Ipv4
	//	*IpAddress_Ipv6
	//	*IpAddress_IpString
	//	*IpAddress_V4Cidr
	//	*IpAddress_V6Cidr
	Ip            isIpAddress_Ip `protobuf_oneof:"ip"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IpAddress) Reset() {
	*x = IpAddress{}
	mi := &file_message_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IpAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpAddress) ProtoMessage() {}

func (x *IpAddress) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpAddress.ProtoReflect.Descriptor instead.
func (*IpAddress) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{13}
}

func (x *IpAddress) GetIp() isIpAddress_Ip {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *IpAddress) GetIpv4() uint32 {
	if x != nil {
		if x, ok := x.Ip.(*IpAddress_Ipv4); ok {
			return x.Ipv4
		}
	}
	return 0
}

func (x *IpAddress) GetIpv6() []byte {
	if x != nil {
		if x, ok := x.Ip.(*IpAddress_Ipv6); ok {
			return x.Ipv6
		}
	}
	return nil
}

func (x *IpAddress) GetIpString() string {
	if x != nil {
		if x, ok := x.Ip.(*IpAddress_IpString); ok {
			return x.IpString
		}
	}
	return ""
}

func (x *IpAddress) GetV4Cidr() *V4Cidr {
	if x != nil {
		if x, ok := x.Ip.(*IpAddress_V4Cidr); ok {
			return x.V4Cidr
		}
	}
	return nil
}

func (x *IpAddress) GetV6Cidr() *V6Cidr {
	if x != nil {
		if x, ok := x.Ip.(*IpAddress_V6Cidr); ok {
			return x.V6Cidr
		}
	}
	return nil
}

type isIpAddress_Ip interface {
	isIpAddress_Ip()
}

type IpAddress_Ipv4 struct {
	Ipv4 uint32 `protobuf:"varint,1,opt,name=ipv4,proto3,oneof"` // IPv4 地址（32位整数）
}

type IpAddress_Ipv6 struct {
	Ipv6 []byte `protobuf:"bytes,2,opt,name=ipv6,proto3,oneof"` // IPv6 地址（16字节）
}

type IpAddress_IpString struct {
	IpString string `protobuf:"bytes,3,opt,name=ip_string,json=ipString,proto3,oneof"` // 字符串形式的 IP 地址
}

type IpAddress_V4Cidr struct {
	V4Cidr *V4Cidr `protobuf:"bytes,4,opt,name=v4_cidr,json=v4Cidr,proto3,oneof"` // IPv4 CIDR 表示
}

type IpAddress_V6Cidr struct {
	V6Cidr *V6Cidr `protobuf:"bytes,5,opt,name=v6_cidr,json=v6Cidr,proto3,oneof"` // IPv6 CIDR 表示
}

func (*IpAddress_Ipv4) isIpAddress_Ip() {}

func (*IpAddress_Ipv6) isIpAddress_Ip() {}

func (*IpAddress_IpString) isIpAddress_Ip() {}

func (*IpAddress_V4Cidr) isIpAddress_Ip() {}

func (*IpAddress_V6Cidr) isIpAddress_Ip() {}

// DHCP 选项值
type DhcpOptionValue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`                                                    // 选项值 (required)
	ValueType     DhcpOptionValueType    `protobuf:"varint,2,opt,name=value_type,json=valueType,proto3,enum=DhcpOptionValueType" json:"value_type,omitempty"` // 值类型：普通字符串或十六进制 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DhcpOptionValue) Reset() {
	*x = DhcpOptionValue{}
	mi := &file_message_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DhcpOptionValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DhcpOptionValue) ProtoMessage() {}

func (x *DhcpOptionValue) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DhcpOptionValue.ProtoReflect.Descriptor instead.
func (*DhcpOptionValue) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{14}
}

func (x *DhcpOptionValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *DhcpOptionValue) GetValueType() DhcpOptionValueType {
	if x != nil {
		return x.ValueType
	}
	return DhcpOptionValueType_DHCP_OPTION_TYPE_STRING
}

// DHCP-V4配置
type DhcpOptionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Option12      *DhcpOptionValue       `protobuf:"bytes,1,opt,name=option12,proto3,oneof" json:"option12,omitempty"` // DHCP HostName
	Option61      *DhcpOptionValue       `protobuf:"bytes,2,opt,name=option61,proto3,oneof" json:"option61,omitempty"` // DHCP Vendor class ID
	Option60      *DhcpOptionValue       `protobuf:"bytes,3,opt,name=option60,proto3,oneof" json:"option60,omitempty"` // DHCP Client ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DhcpOptionConfig) Reset() {
	*x = DhcpOptionConfig{}
	mi := &file_message_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DhcpOptionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DhcpOptionConfig) ProtoMessage() {}

func (x *DhcpOptionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DhcpOptionConfig.ProtoReflect.Descriptor instead.
func (*DhcpOptionConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{15}
}

func (x *DhcpOptionConfig) GetOption12() *DhcpOptionValue {
	if x != nil {
		return x.Option12
	}
	return nil
}

func (x *DhcpOptionConfig) GetOption61() *DhcpOptionValue {
	if x != nil {
		return x.Option61
	}
	return nil
}

func (x *DhcpOptionConfig) GetOption60() *DhcpOptionValue {
	if x != nil {
		return x.Option60
	}
	return nil
}

type NatIp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            *IpAddress             `protobuf:"bytes,1,opt,name=ip,proto3,oneof" json:"ip,omitempty"`                          // NAT IP地址
	IpRange       *IpRange               `protobuf:"bytes,2,opt,name=ip_range,json=ipRange,proto3,oneof" json:"ip_range,omitempty"` // NAT IP地址范围
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NatIp) Reset() {
	*x = NatIp{}
	mi := &file_message_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NatIp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NatIp) ProtoMessage() {}

func (x *NatIp) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NatIp.ProtoReflect.Descriptor instead.
func (*NatIp) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{16}
}

func (x *NatIp) GetIp() *IpAddress {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *NatIp) GetIpRange() *IpRange {
	if x != nil {
		return x.IpRange
	}
	return nil
}

// 心跳服务配置
type HeartbeatConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PingIp        *IpAddress             `protobuf:"bytes,1,opt,name=ping_ip,json=pingIp,proto3,oneof" json:"ping_ip,omitempty"`        // 心跳服务器1
	PingIp2       *IpAddress             `protobuf:"bytes,2,opt,name=ping_ip2,json=pingIp2,proto3,oneof" json:"ping_ip2,omitempty"`     // 心跳服务器2
	MaxDelay      *int32                 `protobuf:"varint,3,opt,name=max_delay,json=maxDelay,proto3,oneof" json:"max_delay,omitempty"` // 最大延迟，单位 ms
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartbeatConfig) Reset() {
	*x = HeartbeatConfig{}
	mi := &file_message_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatConfig) ProtoMessage() {}

func (x *HeartbeatConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatConfig.ProtoReflect.Descriptor instead.
func (*HeartbeatConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{17}
}

func (x *HeartbeatConfig) GetPingIp() *IpAddress {
	if x != nil {
		return x.PingIp
	}
	return nil
}

func (x *HeartbeatConfig) GetPingIp2() *IpAddress {
	if x != nil {
		return x.PingIp2
	}
	return nil
}

func (x *HeartbeatConfig) GetMaxDelay() int32 {
	if x != nil && x.MaxDelay != nil {
		return *x.MaxDelay
	}
	return 0
}

// WAN 配置消息
type WanTask struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Name   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`     // WAN接口名称(wan唯一标识)，ASCII字符，最长15字节 (required)
	Ifname string                 `protobuf:"bytes,2,opt,name=ifname,proto3" json:"ifname,omitempty"` // 网卡名称 (添加修改时必填)
	Mtu    int32                  `protobuf:"varint,3,opt,name=mtu,proto3" json:"mtu,omitempty"`      // 最大传输单元，范围: 500-4700 (添加修改时必填)
	// Types that are valid to be assigned to WanType:
	//
	//	*WanTask_StaticIp
	//	*WanTask_Dhcp
	//	*WanTask_Pppoe
	WanType       isWanTask_WanType     `protobuf_oneof:"wan_type"`
	Heartbeat     *HeartbeatConfig      `protobuf:"bytes,7,opt,name=heartbeat,proto3,oneof" json:"heartbeat,omitempty"` // 心跳服务配置
	Common        *WanTask_CommonConfig `protobuf:"bytes,8,opt,name=common,proto3,oneof" json:"common,omitempty"`       // 其它通用设置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanTask) Reset() {
	*x = WanTask{}
	mi := &file_message_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanTask) ProtoMessage() {}

func (x *WanTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanTask.ProtoReflect.Descriptor instead.
func (*WanTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{18}
}

func (x *WanTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WanTask) GetIfname() string {
	if x != nil {
		return x.Ifname
	}
	return ""
}

func (x *WanTask) GetMtu() int32 {
	if x != nil {
		return x.Mtu
	}
	return 0
}

func (x *WanTask) GetWanType() isWanTask_WanType {
	if x != nil {
		return x.WanType
	}
	return nil
}

func (x *WanTask) GetStaticIp() *WanTask_StaticIpConfig {
	if x != nil {
		if x, ok := x.WanType.(*WanTask_StaticIp); ok {
			return x.StaticIp
		}
	}
	return nil
}

func (x *WanTask) GetDhcp() *DhcpOptionConfig {
	if x != nil {
		if x, ok := x.WanType.(*WanTask_Dhcp); ok {
			return x.Dhcp
		}
	}
	return nil
}

func (x *WanTask) GetPppoe() *WanTask_PppoeConfig {
	if x != nil {
		if x, ok := x.WanType.(*WanTask_Pppoe); ok {
			return x.Pppoe
		}
	}
	return nil
}

func (x *WanTask) GetHeartbeat() *HeartbeatConfig {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

func (x *WanTask) GetCommon() *WanTask_CommonConfig {
	if x != nil {
		return x.Common
	}
	return nil
}

type isWanTask_WanType interface {
	isWanTask_WanType()
}

type WanTask_StaticIp struct {
	StaticIp *WanTask_StaticIpConfig `protobuf:"bytes,4,opt,name=static_ip,json=staticIp,proto3,oneof"` // 固定IP配置
}

type WanTask_Dhcp struct {
	Dhcp *DhcpOptionConfig `protobuf:"bytes,5,opt,name=dhcp,proto3,oneof"` // DHCP配置
}

type WanTask_Pppoe struct {
	Pppoe *WanTask_PppoeConfig `protobuf:"bytes,6,opt,name=pppoe,proto3,oneof"` // PPPoE配置
}

func (*WanTask_StaticIp) isWanTask_WanType() {}

func (*WanTask_Dhcp) isWanTask_WanType() {}

func (*WanTask_Pppoe) isWanTask_WanType() {}

// LAN 配置消息
type LanTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                               // LAN接口名称(lan唯一标识)，ASCII字符，最长15字节 (required)
	Ifname        string                 `protobuf:"bytes,2,opt,name=ifname,proto3" json:"ifname,omitempty"`                           // 网卡名称 (添加修改时必填)
	Mtu           int32                  `protobuf:"varint,3,opt,name=mtu,proto3" json:"mtu,omitempty"`                                // 最大传输单元，范围: 500-4700 (添加修改时必填)
	Addr          *IpAddress             `protobuf:"bytes,4,opt,name=addr,proto3" json:"addr,omitempty"`                               // 接口ip (添加修改时必填)
	Mask          *IpAddress             `protobuf:"bytes,5,opt,name=mask,proto3" json:"mask,omitempty"`                               // 掩码 (添加修改时必填)
	CloneMac      *string                `protobuf:"bytes,6,opt,name=clone_mac,json=cloneMac,proto3,oneof" json:"clone_mac,omitempty"` // 克隆MAC，格式：00-00-00-00-00-00，前4字节不能为空
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LanTask) Reset() {
	*x = LanTask{}
	mi := &file_message_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LanTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LanTask) ProtoMessage() {}

func (x *LanTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LanTask.ProtoReflect.Descriptor instead.
func (*LanTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{19}
}

func (x *LanTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LanTask) GetIfname() string {
	if x != nil {
		return x.Ifname
	}
	return ""
}

func (x *LanTask) GetMtu() int32 {
	if x != nil {
		return x.Mtu
	}
	return 0
}

func (x *LanTask) GetAddr() *IpAddress {
	if x != nil {
		return x.Addr
	}
	return nil
}

func (x *LanTask) GetMask() *IpAddress {
	if x != nil {
		return x.Mask
	}
	return nil
}

func (x *LanTask) GetCloneMac() string {
	if x != nil && x.CloneMac != nil {
		return *x.CloneMac
	}
	return ""
}

type IpRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartIp       *IpAddress             `protobuf:"bytes,1,opt,name=start_ip,json=startIp,proto3" json:"start_ip,omitempty"` // 起始IP地址
	EndIp         *IpAddress             `protobuf:"bytes,2,opt,name=end_ip,json=endIp,proto3" json:"end_ip,omitempty"`       // 结束IP地址
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IpRange) Reset() {
	*x = IpRange{}
	mi := &file_message_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IpRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpRange) ProtoMessage() {}

func (x *IpRange) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpRange.ProtoReflect.Descriptor instead.
func (*IpRange) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{20}
}

func (x *IpRange) GetStartIp() *IpAddress {
	if x != nil {
		return x.StartIp
	}
	return nil
}

func (x *IpRange) GetEndIp() *IpAddress {
	if x != nil {
		return x.EndIp
	}
	return nil
}

// DHCP 配置消息
type DhcpServerTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                         // LAN接口名称(dhcp唯一标识)，ASCII字符，最长15字节 (required)
	DhcpPool      *IpRange               `protobuf:"bytes,2,opt,name=dhcp_pool,json=dhcpPool,proto3" json:"dhcp_pool,omitempty"`                 // dhcp地址范围 (添加修改时必填)
	LeaseTtl      int32                  `protobuf:"varint,3,opt,name=lease_ttl,json=leaseTtl,proto3" json:"lease_ttl,omitempty"`                // 租约时间，单位 s (添加修改时必填)
	DhcpEnable    bool                   `protobuf:"varint,4,opt,name=dhcp_enable,json=dhcpEnable,proto3" json:"dhcp_enable,omitempty"`          // 开启/关闭dhcp (添加修改时必填)
	Dns0          *IpAddress             `protobuf:"bytes,5,opt,name=dns0,proto3,oneof" json:"dns0,omitempty"`                                   // dhcp主DNS
	Dns1          *IpAddress             `protobuf:"bytes,6,opt,name=dns1,proto3,oneof" json:"dns1,omitempty"`                                   // dhcp次DNS
	DhcpGateway   *IpAddress             `protobuf:"bytes,7,opt,name=dhcp_gateway,json=dhcpGateway,proto3,oneof" json:"dhcp_gateway,omitempty"`  // dhcp网关，如果为0.0.0.0或不填，则使用接口IP地址作为网关
	DhcpMask      *IpAddress             `protobuf:"bytes,8,opt,name=dhcp_mask,json=dhcpMask,proto3,oneof" json:"dhcp_mask,omitempty"`           // dhcp掩码，如果为0.0.0.0或不填，则使用接口的掩码
	DhcpDomain    *string                `protobuf:"bytes,9,opt,name=dhcp_domain,json=dhcpDomain,proto3,oneof" json:"dhcp_domain,omitempty"`     // dhcp分配给客户端的域名
	DhcpOptions   *DhcpOptionConfig      `protobuf:"bytes,10,opt,name=dhcp_options,json=dhcpOptions,proto3,oneof" json:"dhcp_options,omitempty"` // DHCP options
	DhcpAcAddr    *IpAddress             `protobuf:"bytes,11,opt,name=dhcp_ac_addr,json=dhcpAcAddr,proto3,oneof" json:"dhcp_ac_addr,omitempty"`  // dhcp分配给客户端的AC地址
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DhcpServerTask) Reset() {
	*x = DhcpServerTask{}
	mi := &file_message_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DhcpServerTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DhcpServerTask) ProtoMessage() {}

func (x *DhcpServerTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DhcpServerTask.ProtoReflect.Descriptor instead.
func (*DhcpServerTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{21}
}

func (x *DhcpServerTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DhcpServerTask) GetDhcpPool() *IpRange {
	if x != nil {
		return x.DhcpPool
	}
	return nil
}

func (x *DhcpServerTask) GetLeaseTtl() int32 {
	if x != nil {
		return x.LeaseTtl
	}
	return 0
}

func (x *DhcpServerTask) GetDhcpEnable() bool {
	if x != nil {
		return x.DhcpEnable
	}
	return false
}

func (x *DhcpServerTask) GetDns0() *IpAddress {
	if x != nil {
		return x.Dns0
	}
	return nil
}

func (x *DhcpServerTask) GetDns1() *IpAddress {
	if x != nil {
		return x.Dns1
	}
	return nil
}

func (x *DhcpServerTask) GetDhcpGateway() *IpAddress {
	if x != nil {
		return x.DhcpGateway
	}
	return nil
}

func (x *DhcpServerTask) GetDhcpMask() *IpAddress {
	if x != nil {
		return x.DhcpMask
	}
	return nil
}

func (x *DhcpServerTask) GetDhcpDomain() string {
	if x != nil && x.DhcpDomain != nil {
		return *x.DhcpDomain
	}
	return ""
}

func (x *DhcpServerTask) GetDhcpOptions() *DhcpOptionConfig {
	if x != nil {
		return x.DhcpOptions
	}
	return nil
}

func (x *DhcpServerTask) GetDhcpAcAddr() *IpAddress {
	if x != nil {
		return x.DhcpAcAddr
	}
	return nil
}

// WAN群组成员配置
type WanGroupMember struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProxyName     string                 `protobuf:"bytes,1,opt,name=proxy_name,json=proxyName,proto3" json:"proxy_name,omitempty"` // WAN成员名称 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanGroupMember) Reset() {
	*x = WanGroupMember{}
	mi := &file_message_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanGroupMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanGroupMember) ProtoMessage() {}

func (x *WanGroupMember) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanGroupMember.ProtoReflect.Descriptor instead.
func (*WanGroupMember) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{22}
}

func (x *WanGroupMember) GetProxyName() string {
	if x != nil {
		return x.ProxyName
	}
	return ""
}

// WAN群组配置消息
type WanGroupTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                       // 群组ID(wan group唯一标识)，范围: 1-128 (required)
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                    // 群组名称，ASCII字符，最长15字节 (添加修改时必填)
	Type          WanGroupType           `protobuf:"varint,3,opt,name=type,proto3,enum=WanGroupType" json:"type,omitempty"` // 群组类型 (添加修改时必填)
	Members       []*WanGroupMember      `protobuf:"bytes,4,rep,name=members,proto3" json:"members,omitempty"`              // 群组成员列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanGroupTask) Reset() {
	*x = WanGroupTask{}
	mi := &file_message_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanGroupTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanGroupTask) ProtoMessage() {}

func (x *WanGroupTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanGroupTask.ProtoReflect.Descriptor instead.
func (*WanGroupTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{23}
}

func (x *WanGroupTask) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WanGroupTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WanGroupTask) GetType() WanGroupType {
	if x != nil {
		return x.Type
	}
	return WanGroupType_WAN_GROUP_TYPE_SRCDST
}

func (x *WanGroupTask) GetMembers() []*WanGroupMember {
	if x != nil {
		return x.Members
	}
	return nil
}

type UserBwLimit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RateIn        *int32                 `protobuf:"varint,1,opt,name=rate_in,json=rateIn,proto3,oneof" json:"rate_in,omitempty"`    // kbps, 0表示不限制
	RateOut       *int32                 `protobuf:"varint,2,opt,name=rate_out,json=rateOut,proto3,oneof" json:"rate_out,omitempty"` // kbps，0表示不限制
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserBwLimit) Reset() {
	*x = UserBwLimit{}
	mi := &file_message_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBwLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBwLimit) ProtoMessage() {}

func (x *UserBwLimit) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBwLimit.ProtoReflect.Descriptor instead.
func (*UserBwLimit) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{24}
}

func (x *UserBwLimit) GetRateIn() int32 {
	if x != nil && x.RateIn != nil {
		return *x.RateIn
	}
	return 0
}

func (x *UserBwLimit) GetRateOut() int32 {
	if x != nil && x.RateOut != nil {
		return *x.RateOut
	}
	return 0
}

// 用户组配置消息
type UserGroupTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    // 用户组ID(用户组唯一标识)，范围: 1-2063 (required)
	Pid   int32                  `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`  // 上级组ID (添加修改时必填)
	Name  string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"` // 用户组名称 (添加修改时必填)
	// IPv4地址范围
	V4Range *IpRange `protobuf:"bytes,4,opt,name=v4_range,json=v4Range,proto3,oneof" json:"v4_range,omitempty"` // IPv4地址范围起始
	// IPv6地址
	V6Range *IpAddress `protobuf:"bytes,5,opt,name=v6_range,json=v6Range,proto3,oneof" json:"v6_range,omitempty"` // IPv6地址范围
	// 账号带宽限制
	V4Rate        *UserBwLimit       `protobuf:"bytes,6,opt,name=v4_rate,json=v4Rate,proto3,oneof" json:"v4_rate,omitempty"`                             // IPv4带宽限制
	V6Rate        *UserBwLimit       `protobuf:"bytes,7,opt,name=v6_rate,json=v6Rate,proto3,oneof" json:"v6_rate,omitempty"`                             // IPv6带宽限制
	Dns           []*IpAddress       `protobuf:"bytes,8,rep,name=dns,proto3" json:"dns,omitempty"`                                                       // DNS服务器，可配置多个，用逗号分隔
	MaxOnlineTime *int32             `protobuf:"varint,9,opt,name=max_online_time,json=maxOnlineTime,proto3,oneof" json:"max_online_time,omitempty"`     // 在线时间，单位小时，0表示不控制
	ClntEpa       *UserExpiredPolicy `protobuf:"varint,10,opt,name=clnt_epa,json=clntEpa,proto3,enum=UserExpiredPolicy,oneof" json:"clnt_epa,omitempty"` // 过期账号处理方式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGroupTask) Reset() {
	*x = UserGroupTask{}
	mi := &file_message_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGroupTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGroupTask) ProtoMessage() {}

func (x *UserGroupTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGroupTask.ProtoReflect.Descriptor instead.
func (*UserGroupTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{25}
}

func (x *UserGroupTask) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGroupTask) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *UserGroupTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserGroupTask) GetV4Range() *IpRange {
	if x != nil {
		return x.V4Range
	}
	return nil
}

func (x *UserGroupTask) GetV6Range() *IpAddress {
	if x != nil {
		return x.V6Range
	}
	return nil
}

func (x *UserGroupTask) GetV4Rate() *UserBwLimit {
	if x != nil {
		return x.V4Rate
	}
	return nil
}

func (x *UserGroupTask) GetV6Rate() *UserBwLimit {
	if x != nil {
		return x.V6Rate
	}
	return nil
}

func (x *UserGroupTask) GetDns() []*IpAddress {
	if x != nil {
		return x.Dns
	}
	return nil
}

func (x *UserGroupTask) GetMaxOnlineTime() int32 {
	if x != nil && x.MaxOnlineTime != nil {
		return *x.MaxOnlineTime
	}
	return 0
}

func (x *UserGroupTask) GetClntEpa() UserExpiredPolicy {
	if x != nil && x.ClntEpa != nil {
		return *x.ClntEpa
	}
	return UserExpiredPolicy_USER_EXPIRED_POLICY_REJECT
}

// 用户限制配置
type UserRestriction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MaxOnline     *int32                 `protobuf:"varint,1,opt,name=max_online,json=maxOnline,proto3,oneof" json:"max_online,omitempty"` // 最大在线数，0表示不控制
	BindIp        *IpAddress             `protobuf:"bytes,2,opt,name=bind_ip,json=bindIp,proto3,oneof" json:"bind_ip,omitempty"`           // 绑定IP，0.0.0.0或为空表示不绑定
	BindMac       []string               `protobuf:"bytes,3,rep,name=bind_mac,json=bindMac,proto3" json:"bind_mac,omitempty"`              // 绑定MAC，00-00-00-00-00-00表示不绑定，可以为多个
	OutVlan       *int32                 `protobuf:"varint,4,opt,name=out_vlan,json=outVlan,proto3,oneof" json:"out_vlan,omitempty"`       // 绑定VLAN，默认不绑定
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserRestriction) Reset() {
	*x = UserRestriction{}
	mi := &file_message_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRestriction) ProtoMessage() {}

func (x *UserRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRestriction.ProtoReflect.Descriptor instead.
func (*UserRestriction) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{26}
}

func (x *UserRestriction) GetMaxOnline() int32 {
	if x != nil && x.MaxOnline != nil {
		return *x.MaxOnline
	}
	return 0
}

func (x *UserRestriction) GetBindIp() *IpAddress {
	if x != nil {
		return x.BindIp
	}
	return nil
}

func (x *UserRestriction) GetBindMac() []string {
	if x != nil {
		return x.BindMac
	}
	return nil
}

func (x *UserRestriction) GetOutVlan() int32 {
	if x != nil && x.OutVlan != nil {
		return *x.OutVlan
	}
	return 0
}

// 用户身份信息
type UserIdentity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cname         *string                `protobuf:"bytes,1,opt,name=cname,proto3,oneof" json:"cname,omitempty"` // 用户姓名
	Card          *string                `protobuf:"bytes,2,opt,name=card,proto3,oneof" json:"card,omitempty"`   // 用户身份证
	Phone         *string                `protobuf:"bytes,3,opt,name=phone,proto3,oneof" json:"phone,omitempty"` // 用户联系电话
	Other         *string                `protobuf:"bytes,4,opt,name=other,proto3,oneof" json:"other,omitempty"` // 用户其他信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdentity) Reset() {
	*x = UserIdentity{}
	mi := &file_message_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdentity) ProtoMessage() {}

func (x *UserIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdentity.ProtoReflect.Descriptor instead.
func (*UserIdentity) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{27}
}

func (x *UserIdentity) GetCname() string {
	if x != nil && x.Cname != nil {
		return *x.Cname
	}
	return ""
}

func (x *UserIdentity) GetCard() string {
	if x != nil && x.Card != nil {
		return *x.Card
	}
	return ""
}

func (x *UserIdentity) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *UserIdentity) GetOther() string {
	if x != nil && x.Other != nil {
		return *x.Other
	}
	return ""
}

type Date struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Year          int32                  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`   // 年
	Month         int32                  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"` // 月
	Day           int32                  `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`     // 日
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Date) Reset() {
	*x = Date{}
	mi := &file_message_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Date) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Date) ProtoMessage() {}

func (x *Date) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Date.ProtoReflect.Descriptor instead.
func (*Date) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{28}
}

func (x *Date) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *Date) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *Date) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

// 用户配置消息
type UserTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                     // 用户账号(用户唯一标识)，不超过30个英文字符或者15个中文字符 (required)
	PoolId        int32                  `protobuf:"varint,2,opt,name=pool_id,json=poolId,proto3" json:"pool_id,omitempty"`  // 用户组ID (添加修改时必填)
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`             // 用户密码，不超过30个英文字符 (添加修改时必填)
	Start         *Date                  `protobuf:"bytes,4,opt,name=start,proto3" json:"start,omitempty"`                   // 开通日期，格式：年-月-日 (添加修改时必填)
	Expire        *Date                  `protobuf:"bytes,5,opt,name=expire,proto3" json:"expire,omitempty"`                 // 截止日期，格式：年-月-日 (添加修改时必填)
	Enable        bool                   `protobuf:"varint,6,opt,name=enable,proto3" json:"enable,omitempty"`                // 账号是否启用 (添加修改时必填)
	Restriction   *UserRestriction       `protobuf:"bytes,7,opt,name=restriction,proto3,oneof" json:"restriction,omitempty"` // 用户限制配置
	Identity      *UserIdentity          `protobuf:"bytes,8,opt,name=identity,proto3,oneof" json:"identity,omitempty"`       // 用户身份信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserTask) Reset() {
	*x = UserTask{}
	mi := &file_message_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTask) ProtoMessage() {}

func (x *UserTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTask.ProtoReflect.Descriptor instead.
func (*UserTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{29}
}

func (x *UserTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserTask) GetPoolId() int32 {
	if x != nil {
		return x.PoolId
	}
	return 0
}

func (x *UserTask) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UserTask) GetStart() *Date {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *UserTask) GetExpire() *Date {
	if x != nil {
		return x.Expire
	}
	return nil
}

func (x *UserTask) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *UserTask) GetRestriction() *UserRestriction {
	if x != nil {
		return x.Restriction
	}
	return nil
}

func (x *UserTask) GetIdentity() *UserIdentity {
	if x != nil {
		return x.Identity
	}
	return nil
}

// iWAN Proxy线路配置消息
type IwanProxyTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                           // iWAN线路名称(iWAN线路唯一标识)，ASCII字符，最長15字节 (required)
	Ifname        string                 `protobuf:"bytes,2,opt,name=ifname,proto3" json:"ifname,omitempty"`                       // 承载的线路名称 (添加修改时必填)
	Mtu           int32                  `protobuf:"varint,3,opt,name=mtu,proto3" json:"mtu,omitempty"`                            // 最大传输单元，范围: 500-4700 (添加修改时必填)
	SvrAddr       string                 `protobuf:"bytes,4,opt,name=svr_addr,json=svrAddr,proto3" json:"svr_addr,omitempty"`      // 服务器ip/域名 (添加修改时必填)
	SvrPort       int32                  `protobuf:"varint,5,opt,name=svr_port,json=svrPort,proto3" json:"svr_port,omitempty"`     // 服务器端口 (添加修改时必填)
	Username      string                 `protobuf:"bytes,6,opt,name=username,proto3" json:"username,omitempty"`                   // iWAN账号 (添加修改时必填)
	Password      string                 `protobuf:"bytes,7,opt,name=password,proto3" json:"password,omitempty"`                   // iWAN密码 (添加修改时必填)
	Encrypt       *bool                  `protobuf:"varint,8,opt,name=encrypt,proto3,oneof" json:"encrypt,omitempty"`              // 是否加密
	Link          *int32                 `protobuf:"varint,9,opt,name=link,proto3,oneof" json:"link,omitempty"`                    // 分段标识
	Heartbeat     *HeartbeatConfig       `protobuf:"bytes,10,opt,name=heartbeat,proto3,oneof" json:"heartbeat,omitempty"`          // 心跳服务配置
	DnsPxy        *bool                  `protobuf:"varint,11,opt,name=dns_pxy,json=dnsPxy,proto3,oneof" json:"dns_pxy,omitempty"` // DNS线路开关
	Ifname2       string                 `protobuf:"bytes,12,opt,name=ifname2,proto3" json:"ifname2,omitempty"`                    // 备份承载的线路名称，可与ifname相同
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IwanProxyTask) Reset() {
	*x = IwanProxyTask{}
	mi := &file_message_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IwanProxyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IwanProxyTask) ProtoMessage() {}

func (x *IwanProxyTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IwanProxyTask.ProtoReflect.Descriptor instead.
func (*IwanProxyTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{30}
}

func (x *IwanProxyTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IwanProxyTask) GetIfname() string {
	if x != nil {
		return x.Ifname
	}
	return ""
}

func (x *IwanProxyTask) GetMtu() int32 {
	if x != nil {
		return x.Mtu
	}
	return 0
}

func (x *IwanProxyTask) GetSvrAddr() string {
	if x != nil {
		return x.SvrAddr
	}
	return ""
}

func (x *IwanProxyTask) GetSvrPort() int32 {
	if x != nil {
		return x.SvrPort
	}
	return 0
}

func (x *IwanProxyTask) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *IwanProxyTask) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *IwanProxyTask) GetEncrypt() bool {
	if x != nil && x.Encrypt != nil {
		return *x.Encrypt
	}
	return false
}

func (x *IwanProxyTask) GetLink() int32 {
	if x != nil && x.Link != nil {
		return *x.Link
	}
	return 0
}

func (x *IwanProxyTask) GetHeartbeat() *HeartbeatConfig {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

func (x *IwanProxyTask) GetDnsPxy() bool {
	if x != nil && x.DnsPxy != nil {
		return *x.DnsPxy
	}
	return false
}

func (x *IwanProxyTask) GetIfname2() string {
	if x != nil {
		return x.Ifname2
	}
	return ""
}

// iWAN Service服务配置消息
type IwanServiceTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`  // iWAN服务名称(iWAN服务唯一标识)，ASCII字符，最長15字节 (required)
	Addr          *IpAddress             `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`  // 服务器网关 (添加修改时必填)
	Mtu           int32                  `protobuf:"varint,3,opt,name=mtu,proto3" json:"mtu,omitempty"`   // 最大传输单元，范围: 500-4700 (添加修改时必填)
	Pool          int32                  `protobuf:"varint,4,opt,name=pool,proto3" json:"pool,omitempty"` // 地址池(用户组)ID (添加修改时必填)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IwanServiceTask) Reset() {
	*x = IwanServiceTask{}
	mi := &file_message_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IwanServiceTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IwanServiceTask) ProtoMessage() {}

func (x *IwanServiceTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IwanServiceTask.ProtoReflect.Descriptor instead.
func (*IwanServiceTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{31}
}

func (x *IwanServiceTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IwanServiceTask) GetAddr() *IpAddress {
	if x != nil {
		return x.Addr
	}
	return nil
}

func (x *IwanServiceTask) GetMtu() int32 {
	if x != nil {
		return x.Mtu
	}
	return 0
}

func (x *IwanServiceTask) GetPool() int32 {
	if x != nil {
		return x.Pool
	}
	return 0
}

type IwanMappingTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxy         string                 `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`         // 映射线路 (required) proxy+port作为唯一标识
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`          // 映射端口 (required)
	Server        *string                `protobuf:"bytes,3,opt,name=server,proto3,oneof" json:"server,omitempty"` // iWAN服务名称, NULL for delete
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IwanMappingTask) Reset() {
	*x = IwanMappingTask{}
	mi := &file_message_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IwanMappingTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IwanMappingTask) ProtoMessage() {}

func (x *IwanMappingTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IwanMappingTask.ProtoReflect.Descriptor instead.
func (*IwanMappingTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{32}
}

func (x *IwanMappingTask) GetProxy() string {
	if x != nil {
		return x.Proxy
	}
	return ""
}

func (x *IwanMappingTask) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *IwanMappingTask) GetServer() string {
	if x != nil && x.Server != nil {
		return *x.Server
	}
	return ""
}

// SR加密配置消息
type SrEncryptConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EncryptType   SrEncryptType          `protobuf:"varint,1,opt,name=encrypt_type,json=encryptType,proto3,enum=SrEncryptType" json:"encrypt_type,omitempty"` // 加密方式 (required)
	Password      *string                `protobuf:"bytes,2,opt,name=password,proto3,oneof" json:"password,omitempty"`                                        // 加密密钥，当encrypt_type不为NONE时使用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SrEncryptConfig) Reset() {
	*x = SrEncryptConfig{}
	mi := &file_message_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SrEncryptConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SrEncryptConfig) ProtoMessage() {}

func (x *SrEncryptConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SrEncryptConfig.ProtoReflect.Descriptor instead.
func (*SrEncryptConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{33}
}

func (x *SrEncryptConfig) GetEncryptType() SrEncryptType {
	if x != nil {
		return x.EncryptType
	}
	return SrEncryptType_SR_ENCRYPT_NONE
}

func (x *SrEncryptConfig) GetPassword() string {
	if x != nil && x.Password != nil {
		return *x.Password
	}
	return ""
}

// SR路径配置消息
type SrPath struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Links         []int32                `protobuf:"varint,1,rep,packed,name=links,proto3" json:"links,omitempty"` // iWAN Proxy分段标识，多个标识用','分隔，0表示删除
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SrPath) Reset() {
	*x = SrPath{}
	mi := &file_message_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SrPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SrPath) ProtoMessage() {}

func (x *SrPath) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SrPath.ProtoReflect.Descriptor instead.
func (*SrPath) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{34}
}

func (x *SrPath) GetLinks() []int32 {
	if x != nil {
		return x.Links
	}
	return nil
}

// SR Proxy配置消息
type SrProxyTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                              // SR名称(SR唯一标识)，ASCII字符，最长15字节 (required)
	Paths         []*SrPath              `protobuf:"bytes,2,rep,name=paths,proto3" json:"paths,omitempty"`                                            // SR路径配置列表 (添加修改时必填)
	FromIn        bool                   `protobuf:"varint,3,opt,name=from_in,json=fromIn,proto3" json:"from_in,omitempty"`                           // 接内标记 (添加修改时必填)
	Mtu           int32                  `protobuf:"varint,4,opt,name=mtu,proto3" json:"mtu,omitempty"`                                               // 最大传输单元，范围: 500-4700 (添加修改时必填)
	Keepalive     bool                   `protobuf:"varint,5,opt,name=keepalive,proto3" json:"keepalive,omitempty"`                                   // 是否开启保活，true为开启，false为关闭 (添加修改时必填)
	EncryptConfig *SrEncryptConfig       `protobuf:"bytes,6,opt,name=encrypt_config,json=encryptConfig,proto3,oneof" json:"encrypt_config,omitempty"` // 加密配置，可选
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SrProxyTask) Reset() {
	*x = SrProxyTask{}
	mi := &file_message_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SrProxyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SrProxyTask) ProtoMessage() {}

func (x *SrProxyTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SrProxyTask.ProtoReflect.Descriptor instead.
func (*SrProxyTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{35}
}

func (x *SrProxyTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SrProxyTask) GetPaths() []*SrPath {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *SrProxyTask) GetFromIn() bool {
	if x != nil {
		return x.FromIn
	}
	return false
}

func (x *SrProxyTask) GetMtu() int32 {
	if x != nil {
		return x.Mtu
	}
	return 0
}

func (x *SrProxyTask) GetKeepalive() bool {
	if x != nil {
		return x.Keepalive
	}
	return false
}

func (x *SrProxyTask) GetEncryptConfig() *SrEncryptConfig {
	if x != nil {
		return x.EncryptConfig
	}
	return nil
}

// IP群组成员
// 表示单个IP成员配置
type IpGroupMember struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to IpAddr:
	//
	//	*IpGroupMember_Ip
	//	*IpGroupMember_IpRange
	IpAddr        isIpGroupMember_IpAddr `protobuf_oneof:"ip_addr"`
	Info          *string                `protobuf:"bytes,3,opt,name=info,proto3,oneof" json:"info,omitempty"` // 备注信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IpGroupMember) Reset() {
	*x = IpGroupMember{}
	mi := &file_message_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IpGroupMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpGroupMember) ProtoMessage() {}

func (x *IpGroupMember) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpGroupMember.ProtoReflect.Descriptor instead.
func (*IpGroupMember) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{36}
}

func (x *IpGroupMember) GetIpAddr() isIpGroupMember_IpAddr {
	if x != nil {
		return x.IpAddr
	}
	return nil
}

func (x *IpGroupMember) GetIp() *IpAddress {
	if x != nil {
		if x, ok := x.IpAddr.(*IpGroupMember_Ip); ok {
			return x.Ip
		}
	}
	return nil
}

func (x *IpGroupMember) GetIpRange() *IpRange {
	if x != nil {
		if x, ok := x.IpAddr.(*IpGroupMember_IpRange); ok {
			return x.IpRange
		}
	}
	return nil
}

func (x *IpGroupMember) GetInfo() string {
	if x != nil && x.Info != nil {
		return *x.Info
	}
	return ""
}

type isIpGroupMember_IpAddr interface {
	isIpGroupMember_IpAddr()
}

type IpGroupMember_Ip struct {
	Ip *IpAddress `protobuf:"bytes,1,opt,name=ip,proto3,oneof"` // IP地址，格式: ip,ip/mask
}

type IpGroupMember_IpRange struct {
	IpRange *IpRange `protobuf:"bytes,2,opt,name=ip_range,json=ipRange,proto3,oneof"` // IP地址范围,ip-ip
}

func (*IpGroupMember_Ip) isIpGroupMember_IpAddr() {}

func (*IpGroupMember_IpRange) isIpGroupMember_IpAddr() {}

// IP群组配置消息
type IpGroupTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // IP群组名称(IP群组唯一标识)，ASCII字符，最长15字节 (required)
	Members       []*IpGroupMember       `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`                                  // IP成员列表，当IP成员数量较少时使用
	FileContent   []byte                 `protobuf:"bytes,3,opt,name=file_content,json=fileContent,proto3,oneof" json:"file_content,omitempty"` // 文件形式IP群组内容，当IP成员数量较多时使用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IpGroupTask) Reset() {
	*x = IpGroupTask{}
	mi := &file_message_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IpGroupTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpGroupTask) ProtoMessage() {}

func (x *IpGroupTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpGroupTask.ProtoReflect.Descriptor instead.
func (*IpGroupTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{37}
}

func (x *IpGroupTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IpGroupTask) GetMembers() []*IpGroupMember {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *IpGroupTask) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

// 域名群组配置消息
type DomainGroupTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // 域名群组名称(域名群组唯一标识)，ASCII字符 (required)
	Domain        []string               `protobuf:"bytes,2,rep,name=domain,proto3" json:"domain,omitempty"`                                    // 域名，支持前缀匹配格式：*sohu.com, @sohu.com, ^sohu.com
	FileContent   []byte                 `protobuf:"bytes,3,opt,name=file_content,json=fileContent,proto3,oneof" json:"file_content,omitempty"` // 文件形式域名群组内容，当域名成员数量较多时使用，每行一个域名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DomainGroupTask) Reset() {
	*x = DomainGroupTask{}
	mi := &file_message_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainGroupTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainGroupTask) ProtoMessage() {}

func (x *DomainGroupTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainGroupTask.ProtoReflect.Descriptor instead.
func (*DomainGroupTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{38}
}

func (x *DomainGroupTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DomainGroupTask) GetDomain() []string {
	if x != nil {
		return x.Domain
	}
	return nil
}

func (x *DomainGroupTask) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

// 每日时间点定义消息
type DailyTime struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hour          int32                  `protobuf:"varint,1,opt,name=hour,proto3" json:"hour,omitempty"` // 小时，范围: 0-23
	Min           int32                  `protobuf:"varint,2,opt,name=min,proto3" json:"min,omitempty"`   // 分钟，范围: 0-59
	Sec           int32                  `protobuf:"varint,3,opt,name=sec,proto3" json:"sec,omitempty"`   // 秒，范围: 0-59
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DailyTime) Reset() {
	*x = DailyTime{}
	mi := &file_message_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DailyTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyTime) ProtoMessage() {}

func (x *DailyTime) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyTime.ProtoReflect.Descriptor instead.
func (*DailyTime) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{39}
}

func (x *DailyTime) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *DailyTime) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *DailyTime) GetSec() int32 {
	if x != nil {
		return x.Sec
	}
	return 0
}

// 时间规格定义消息 - 表示时间段
// 用于定义"每周某几天的某个时间段内生效"的时间规则
// 示例：
//   - 每周一到周五的9:00-18:00: start_day=1, end_day=5, start_time=9:00:00, end_time=18:00:00
type TimeSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartDay      int32                  `protobuf:"varint,1,opt,name=start_day,json=startDay,proto3" json:"start_day,omitempty"`   // 起始星期几，范围: 1-7 (1=周一, 2=周二, ..., 7=周日)
	EndDay        int32                  `protobuf:"varint,2,opt,name=end_day,json=endDay,proto3" json:"end_day,omitempty"`         // 结束星期几，范围: 1-7 (1=周一, 2=周二, ..., 7=周日)
	StartTime     *DailyTime             `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 每日起始时间 (required)
	EndTime       *DailyTime             `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 每日结束时间 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeSpec) Reset() {
	*x = TimeSpec{}
	mi := &file_message_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeSpec) ProtoMessage() {}

func (x *TimeSpec) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeSpec.ProtoReflect.Descriptor instead.
func (*TimeSpec) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{40}
}

func (x *TimeSpec) GetStartDay() int32 {
	if x != nil {
		return x.StartDay
	}
	return 0
}

func (x *TimeSpec) GetEndDay() int32 {
	if x != nil {
		return x.EndDay
	}
	return 0
}

func (x *TimeSpec) GetStartTime() *DailyTime {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TimeSpec) GetEndTime() *DailyTime {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// 策略时段配置消息 用于路由/NAT & DNS 管控
type EffectiveTimeTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                               // 策略时段ID(策略时段唯一标识)，范围: 1-128 (required)
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                            // 策略时段名称，ASCII字符 (添加修改时必填)
	TimeRange     *TimeSpec              `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"` // 策略时段时间范围 (添加修改时必填)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EffectiveTimeTask) Reset() {
	*x = EffectiveTimeTask{}
	mi := &file_message_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EffectiveTimeTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EffectiveTimeTask) ProtoMessage() {}

func (x *EffectiveTimeTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EffectiveTimeTask.ProtoReflect.Descriptor instead.
func (*EffectiveTimeTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{41}
}

func (x *EffectiveTimeTask) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EffectiveTimeTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EffectiveTimeTask) GetTimeRange() *TimeSpec {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

// 流量通道优先级配置
type TrafficChannelPriority struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pri           int32                  `protobuf:"varint,1,opt,name=pri,proto3" json:"pri,omitempty"`                        // 优先级，范围: 1-16，1优先级最高 (required)
	MaxRate       int32                  `protobuf:"varint,2,opt,name=max_rate,json=maxRate,proto3" json:"max_rate,omitempty"` // 最大带宽，单位kbps (required)
	Gbw           int32                  `protobuf:"varint,3,opt,name=gbw,proto3" json:"gbw,omitempty"`                        // 保证带宽，单位kbps (required)
	Desc          *string                `protobuf:"bytes,4,opt,name=desc,proto3,oneof" json:"desc,omitempty"`                 // 描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrafficChannelPriority) Reset() {
	*x = TrafficChannelPriority{}
	mi := &file_message_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficChannelPriority) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficChannelPriority) ProtoMessage() {}

func (x *TrafficChannelPriority) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficChannelPriority.ProtoReflect.Descriptor instead.
func (*TrafficChannelPriority) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{42}
}

func (x *TrafficChannelPriority) GetPri() int32 {
	if x != nil {
		return x.Pri
	}
	return 0
}

func (x *TrafficChannelPriority) GetMaxRate() int32 {
	if x != nil {
		return x.MaxRate
	}
	return 0
}

func (x *TrafficChannelPriority) GetGbw() int32 {
	if x != nil {
		return x.Gbw
	}
	return 0
}

func (x *TrafficChannelPriority) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

// 流量通道配置消息
type TrafficChannelTask struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Name          string                    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`             // 流量通道名称(流量通道唯一标识)，ASCII字符 (required)
	Rate          int32                     `protobuf:"varint,2,opt,name=rate,proto3" json:"rate,omitempty"`            // 流量通道带宽，单位kbits/s，范围: 1-16000000 (添加修改时必填)
	Quota         *int32                    `protobuf:"varint,3,opt,name=quota,proto3,oneof" json:"quota,omitempty"`    // 当日限额，单位Mbytes，0表示不限额
	Priorities    []*TrafficChannelPriority `protobuf:"bytes,4,rep,name=priorities,proto3" json:"priorities,omitempty"` // 优先级设置列表，最多16个优先级
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrafficChannelTask) Reset() {
	*x = TrafficChannelTask{}
	mi := &file_message_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficChannelTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficChannelTask) ProtoMessage() {}

func (x *TrafficChannelTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficChannelTask.ProtoReflect.Descriptor instead.
func (*TrafficChannelTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{43}
}

func (x *TrafficChannelTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrafficChannelTask) GetRate() int32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *TrafficChannelTask) GetQuota() int32 {
	if x != nil && x.Quota != nil {
		return *x.Quota
	}
	return 0
}

func (x *TrafficChannelTask) GetPriorities() []*TrafficChannelPriority {
	if x != nil {
		return x.Priorities
	}
	return nil
}

// 流量统计配置消息
type TrafficStatTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                       // 流量统计名称(流量统计唯一标识)，ASCII字符 (required)
	TrackIp       bool                   `protobuf:"varint,2,opt,name=track_ip,json=trackIp,proto3" json:"track_ip,omitempty"` // 跟踪IP标志，true表示跟踪IP，false表示不跟踪 (添加修改时必填)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrafficStatTask) Reset() {
	*x = TrafficStatTask{}
	mi := &file_message_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficStatTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficStatTask) ProtoMessage() {}

func (x *TrafficStatTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficStatTask.ProtoReflect.Descriptor instead.
func (*TrafficStatTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{44}
}

func (x *TrafficStatTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrafficStatTask) GetTrackIp() bool {
	if x != nil {
		return x.TrackIp
	}
	return false
}

type AddressSelector struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Selector:
	//
	//	*AddressSelector_Ip
	//	*AddressSelector_IpRange
	//	*AddressSelector_IpGroupName
	//	*AddressSelector_MacGroupId
	//	*AddressSelector_UserGroupId
	//	*AddressSelector_Username
	//	*AddressSelector_DomainGroupName
	Selector      isAddressSelector_Selector `protobuf_oneof:"selector"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddressSelector) Reset() {
	*x = AddressSelector{}
	mi := &file_message_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddressSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressSelector) ProtoMessage() {}

func (x *AddressSelector) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressSelector.ProtoReflect.Descriptor instead.
func (*AddressSelector) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{45}
}

func (x *AddressSelector) GetSelector() isAddressSelector_Selector {
	if x != nil {
		return x.Selector
	}
	return nil
}

func (x *AddressSelector) GetIp() *IpAddress {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_Ip); ok {
			return x.Ip
		}
	}
	return nil
}

func (x *AddressSelector) GetIpRange() *IpRange {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_IpRange); ok {
			return x.IpRange
		}
	}
	return nil
}

func (x *AddressSelector) GetIpGroupName() string {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_IpGroupName); ok {
			return x.IpGroupName
		}
	}
	return ""
}

func (x *AddressSelector) GetMacGroupId() int32 {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_MacGroupId); ok {
			return x.MacGroupId
		}
	}
	return 0
}

func (x *AddressSelector) GetUserGroupId() int32 {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_UserGroupId); ok {
			return x.UserGroupId
		}
	}
	return 0
}

func (x *AddressSelector) GetUsername() string {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_Username); ok {
			return x.Username
		}
	}
	return ""
}

func (x *AddressSelector) GetDomainGroupName() string {
	if x != nil {
		if x, ok := x.Selector.(*AddressSelector_DomainGroupName); ok {
			return x.DomainGroupName
		}
	}
	return ""
}

type isAddressSelector_Selector interface {
	isAddressSelector_Selector()
}

type AddressSelector_Ip struct {
	Ip *IpAddress `protobuf:"bytes,1,opt,name=ip,proto3,oneof"` // IP地址
}

type AddressSelector_IpRange struct {
	IpRange *IpRange `protobuf:"bytes,2,opt,name=ip_range,json=ipRange,proto3,oneof"` // IP地址范围
}

type AddressSelector_IpGroupName struct {
	IpGroupName string `protobuf:"bytes,3,opt,name=ip_group_name,json=ipGroupName,proto3,oneof"` // IP群组名称
}

type AddressSelector_MacGroupId struct {
	MacGroupId int32 `protobuf:"varint,4,opt,name=mac_group_id,json=macGroupId,proto3,oneof"` // MAC群组ID
}

type AddressSelector_UserGroupId struct {
	UserGroupId int32 `protobuf:"varint,5,opt,name=user_group_id,json=userGroupId,proto3,oneof"` // 用户群组ID
}

type AddressSelector_Username struct {
	Username string `protobuf:"bytes,6,opt,name=username,proto3,oneof"` // 用户名
}

type AddressSelector_DomainGroupName struct {
	DomainGroupName string `protobuf:"bytes,7,opt,name=domain_group_name,json=domainGroupName,proto3,oneof"` // 域名群组名称
}

func (*AddressSelector_Ip) isAddressSelector_Selector() {}

func (*AddressSelector_IpRange) isAddressSelector_Selector() {}

func (*AddressSelector_IpGroupName) isAddressSelector_Selector() {}

func (*AddressSelector_MacGroupId) isAddressSelector_Selector() {}

func (*AddressSelector_UserGroupId) isAddressSelector_Selector() {}

func (*AddressSelector_Username) isAddressSelector_Selector() {}

func (*AddressSelector_DomainGroupName) isAddressSelector_Selector() {}

// 整数范围配置（用于TTL、DSCP、VLAN等）
type IntRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         uint32                 `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"` // 起始值
	End           uint32                 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`     // 结束值，若 start == end 表示单个值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntRange) Reset() {
	*x = IntRange{}
	mi := &file_message_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntRange) ProtoMessage() {}

func (x *IntRange) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntRange.ProtoReflect.Descriptor instead.
func (*IntRange) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{46}
}

func (x *IntRange) GetStart() uint32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *IntRange) GetEnd() uint32 {
	if x != nil {
		return x.End
	}
	return 0
}

// 端口范围配置
type PortRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         uint32                 `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"` // 起始端口
	End           uint32                 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`     // 结束端口，若 start == end 表示单个端口
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortRange) Reset() {
	*x = PortRange{}
	mi := &file_message_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortRange) ProtoMessage() {}

func (x *PortRange) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortRange.ProtoReflect.Descriptor instead.
func (*PortRange) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{47}
}

func (x *PortRange) GetStart() uint32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *PortRange) GetEnd() uint32 {
	if x != nil {
		return x.End
	}
	return 0
}

// 端口规格配置
type PortSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ports         []*PortRange           `protobuf:"bytes,1,rep,name=ports,proto3" json:"ports,omitempty"` // 端口范围列表，支持单端口和端口范围
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortSpec) Reset() {
	*x = PortSpec{}
	mi := &file_message_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortSpec) ProtoMessage() {}

func (x *PortSpec) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortSpec.ProtoReflect.Descriptor instead.
func (*PortSpec) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{48}
}

func (x *PortSpec) GetPorts() []*PortRange {
	if x != nil {
		return x.Ports
	}
	return nil
}

// 应用协议配置
type AppProtocolSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`             // 应用名称，如 "any", "httpgroup", "ftpgroup" (required)
	AppProtocol   string                 `protobuf:"bytes,2,opt,name=app_protocol,json=appProtocol,proto3" json:"app_protocol,omitempty"` // 协议类型，如 "tcp", "udp", "icmp" 等 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppProtocolSpec) Reset() {
	*x = AppProtocolSpec{}
	mi := &file_message_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppProtocolSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppProtocolSpec) ProtoMessage() {}

func (x *AppProtocolSpec) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppProtocolSpec.ProtoReflect.Descriptor instead.
func (*AppProtocolSpec) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{49}
}

func (x *AppProtocolSpec) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AppProtocolSpec) GetAppProtocol() string {
	if x != nil {
		return x.AppProtocol
	}
	return ""
}

// VLAN范围配置
type VlanRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         uint32                 `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"` // 起始VLAN ID
	End           uint32                 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`     // 结束VLAN ID，若 start == end 表示单个VLAN
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VlanRange) Reset() {
	*x = VlanRange{}
	mi := &file_message_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VlanRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VlanRange) ProtoMessage() {}

func (x *VlanRange) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VlanRange.ProtoReflect.Descriptor instead.
func (*VlanRange) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{50}
}

func (x *VlanRange) GetStart() uint32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *VlanRange) GetEnd() uint32 {
	if x != nil {
		return x.End
	}
	return 0
}

// 接口配置
type InterfaceSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bridge        *string                `protobuf:"bytes,1,opt,name=bridge,proto3,oneof" json:"bridge,omitempty"`               // 线路名称，支持线路群组格式：_wg.线路群组名称
	Dir           *FlowDirection         `protobuf:"varint,2,opt,name=dir,proto3,enum=FlowDirection,oneof" json:"dir,omitempty"` // 流向：both/in/out
	Ifname        *string                `protobuf:"bytes,3,opt,name=ifname,proto3,oneof" json:"ifname,omitempty"`               // 首包接口：网卡名/线路名称
	InIf          *string                `protobuf:"bytes,4,opt,name=in_if,json=inIf,proto3,oneof" json:"in_if,omitempty"`       // 源接口：网卡名/线路名称
	Vlan          *VlanRange             `protobuf:"bytes,5,opt,name=vlan,proto3,oneof" json:"vlan,omitempty"`                   // VLAN配置，支持单VLAN和VLAN范围
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InterfaceSpec) Reset() {
	*x = InterfaceSpec{}
	mi := &file_message_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InterfaceSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterfaceSpec) ProtoMessage() {}

func (x *InterfaceSpec) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterfaceSpec.ProtoReflect.Descriptor instead.
func (*InterfaceSpec) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{51}
}

func (x *InterfaceSpec) GetBridge() string {
	if x != nil && x.Bridge != nil {
		return *x.Bridge
	}
	return ""
}

func (x *InterfaceSpec) GetDir() FlowDirection {
	if x != nil && x.Dir != nil {
		return *x.Dir
	}
	return FlowDirection_FLOW_DIRECTION_BOTH
}

func (x *InterfaceSpec) GetIfname() string {
	if x != nil && x.Ifname != nil {
		return *x.Ifname
	}
	return ""
}

func (x *InterfaceSpec) GetInIf() string {
	if x != nil && x.InIf != nil {
		return *x.InIf
	}
	return ""
}

func (x *InterfaceSpec) GetVlan() *VlanRange {
	if x != nil {
		return x.Vlan
	}
	return nil
}

// 策略组配置消息
type FlowControlPolicyGroupTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                            // 策略组名称(策略组唯一标识)，ASCII字符 (required)
	TimeRange     *TimeSpec              `protobuf:"bytes,2,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"` // 策略组时间范围 (添加修改时必填)
	Disable       bool                   `protobuf:"varint,3,opt,name=disable,proto3" json:"disable,omitempty"`                     // 是否启用：false为启用，true为禁用 (添加修改时必填)
	Stop          bool                   `protobuf:"varint,4,opt,name=stop,proto3" json:"stop,omitempty"`                           // 是否继续匹配：false为继续匹配，true为停止 (添加修改时必填)
	Previous      *string                `protobuf:"bytes,5,opt,name=previous,proto3,oneof" json:"previous,omitempty"`              // 前一个策略组名称，用于排序，null表示首个策略组
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowControlPolicyGroupTask) Reset() {
	*x = FlowControlPolicyGroupTask{}
	mi := &file_message_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowControlPolicyGroupTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControlPolicyGroupTask) ProtoMessage() {}

func (x *FlowControlPolicyGroupTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControlPolicyGroupTask.ProtoReflect.Descriptor instead.
func (*FlowControlPolicyGroupTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{52}
}

func (x *FlowControlPolicyGroupTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FlowControlPolicyGroupTask) GetTimeRange() *TimeSpec {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *FlowControlPolicyGroupTask) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *FlowControlPolicyGroupTask) GetStop() bool {
	if x != nil {
		return x.Stop
	}
	return false
}

func (x *FlowControlPolicyGroupTask) GetPrevious() string {
	if x != nil && x.Previous != nil {
		return *x.Previous
	}
	return ""
}

// 策略执行动作配置
type ActionChannelConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Next          bool                   `protobuf:"varint,1,opt,name=next,proto3" json:"next,omitempty"`                         // 匹配后状态：false为继续匹配，true为停止匹配 (required)
	Channel       string                 `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`                    // 流量通道名称（当action为CHANNEL时必填）
	Pri           int32                  `protobuf:"varint,3,opt,name=pri,proto3" json:"pri,omitempty"`                           // 通道优先级（当action为CHANNEL时必填）
	IpRate        *int32                 `protobuf:"varint,4,opt,name=ip_rate,json=ipRate,proto3,oneof" json:"ip_rate,omitempty"` // 内网IP限速，单位kbits/s，0为不限速
	SoId          *string                `protobuf:"bytes,5,opt,name=so_id,json=soId,proto3,oneof" json:"so_id,omitempty"`        // 流量统计名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionChannelConfig) Reset() {
	*x = ActionChannelConfig{}
	mi := &file_message_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionChannelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionChannelConfig) ProtoMessage() {}

func (x *ActionChannelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionChannelConfig.ProtoReflect.Descriptor instead.
func (*ActionChannelConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{53}
}

func (x *ActionChannelConfig) GetNext() bool {
	if x != nil {
		return x.Next
	}
	return false
}

func (x *ActionChannelConfig) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *ActionChannelConfig) GetPri() int32 {
	if x != nil {
		return x.Pri
	}
	return 0
}

func (x *ActionChannelConfig) GetIpRate() int32 {
	if x != nil && x.IpRate != nil {
		return *x.IpRate
	}
	return 0
}

func (x *ActionChannelConfig) GetSoId() string {
	if x != nil && x.SoId != nil {
		return *x.SoId
	}
	return ""
}

// 策略执行动作配置
type ActionAcceptConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Next          bool                   `protobuf:"varint,1,opt,name=next,proto3" json:"next,omitempty"`                         // 匹配后状态：false为继续匹配，true为停止匹配 (required)
	IpRate        *int32                 `protobuf:"varint,3,opt,name=ip_rate,json=ipRate,proto3,oneof" json:"ip_rate,omitempty"` // 内网IP限速，单位kbits/s，0为不限速
	Tos           *int32                 `protobuf:"varint,4,opt,name=tos,proto3,oneof" json:"tos,omitempty"`                     // 修改DSCP，值为1~63
	SoId          *string                `protobuf:"bytes,5,opt,name=so_id,json=soId,proto3,oneof" json:"so_id,omitempty"`        // 流量统计名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionAcceptConfig) Reset() {
	*x = ActionAcceptConfig{}
	mi := &file_message_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionAcceptConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionAcceptConfig) ProtoMessage() {}

func (x *ActionAcceptConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionAcceptConfig.ProtoReflect.Descriptor instead.
func (*ActionAcceptConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{54}
}

func (x *ActionAcceptConfig) GetNext() bool {
	if x != nil {
		return x.Next
	}
	return false
}

func (x *ActionAcceptConfig) GetIpRate() int32 {
	if x != nil && x.IpRate != nil {
		return *x.IpRate
	}
	return 0
}

func (x *ActionAcceptConfig) GetTos() int32 {
	if x != nil && x.Tos != nil {
		return *x.Tos
	}
	return 0
}

func (x *ActionAcceptConfig) GetSoId() string {
	if x != nil && x.SoId != nil {
		return *x.SoId
	}
	return ""
}

// 策略配置消息
type FlowControlPolicyTask struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Cookie    uint32                 `protobuf:"varint,1,opt,name=cookie,proto3" json:"cookie,omitempty"`                       // 策略cookie(策略唯一标识)，uint32 (required)
	Desc      string                 `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`                            // 策略描述 (添加修改时必填)
	GroupName string                 `protobuf:"bytes,3,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"` // 所属策略组名称 (required)
	Disable   bool                   `protobuf:"varint,4,opt,name=disable,proto3" json:"disable,omitempty"`                     // 是否启用：false为启用，true为禁用 (添加修改时必填)
	Previous  *uint32                `protobuf:"varint,5,opt,name=previous,proto3,oneof" json:"previous,omitempty"`             // 前一个策略cookie，用于排序，0表示首个策略
	// 用户/访问者配置
	InIp   []*AddressSelector `protobuf:"bytes,6,rep,name=in_ip,json=inIp,proto3" json:"in_ip,omitempty"`             // 内网IP配置列表
	InPort *PortSpec          `protobuf:"bytes,7,opt,name=in_port,json=inPort,proto3,oneof" json:"in_port,omitempty"` // 内网端口配置
	// 服务者/服务配置
	OutIp   []*AddressSelector `protobuf:"bytes,8,rep,name=out_ip,json=outIp,proto3" json:"out_ip,omitempty"`             // 外网IP配置列表
	OutPort *PortSpec          `protobuf:"bytes,9,opt,name=out_port,json=outPort,proto3,oneof" json:"out_port,omitempty"` // 外网端口配置
	App     *AppProtocolSpec   `protobuf:"bytes,10,opt,name=app,proto3,oneof" json:"app,omitempty"`                       // 协议配置
	// 接口配置
	Interface *InterfaceSpec `protobuf:"bytes,11,opt,name=interface,proto3,oneof" json:"interface,omitempty"` // 接口配置
	// 执行动作
	Action FlowControlAction `protobuf:"varint,12,opt,name=action,proto3,enum=FlowControlAction" json:"action,omitempty"` // 执行动作类型 (required)
	// Types that are valid to be assigned to ActionConfig:
	//
	//	*FlowControlPolicyTask_ActionAccept
	//	*FlowControlPolicyTask_ActionChannel
	ActionConfig  isFlowControlPolicyTask_ActionConfig `protobuf_oneof:"action_config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowControlPolicyTask) Reset() {
	*x = FlowControlPolicyTask{}
	mi := &file_message_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowControlPolicyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControlPolicyTask) ProtoMessage() {}

func (x *FlowControlPolicyTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControlPolicyTask.ProtoReflect.Descriptor instead.
func (*FlowControlPolicyTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{55}
}

func (x *FlowControlPolicyTask) GetCookie() uint32 {
	if x != nil {
		return x.Cookie
	}
	return 0
}

func (x *FlowControlPolicyTask) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FlowControlPolicyTask) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *FlowControlPolicyTask) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *FlowControlPolicyTask) GetPrevious() uint32 {
	if x != nil && x.Previous != nil {
		return *x.Previous
	}
	return 0
}

func (x *FlowControlPolicyTask) GetInIp() []*AddressSelector {
	if x != nil {
		return x.InIp
	}
	return nil
}

func (x *FlowControlPolicyTask) GetInPort() *PortSpec {
	if x != nil {
		return x.InPort
	}
	return nil
}

func (x *FlowControlPolicyTask) GetOutIp() []*AddressSelector {
	if x != nil {
		return x.OutIp
	}
	return nil
}

func (x *FlowControlPolicyTask) GetOutPort() *PortSpec {
	if x != nil {
		return x.OutPort
	}
	return nil
}

func (x *FlowControlPolicyTask) GetApp() *AppProtocolSpec {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *FlowControlPolicyTask) GetInterface() *InterfaceSpec {
	if x != nil {
		return x.Interface
	}
	return nil
}

func (x *FlowControlPolicyTask) GetAction() FlowControlAction {
	if x != nil {
		return x.Action
	}
	return FlowControlAction_FLOW_CONTROL_ACTION_PERMIT
}

func (x *FlowControlPolicyTask) GetActionConfig() isFlowControlPolicyTask_ActionConfig {
	if x != nil {
		return x.ActionConfig
	}
	return nil
}

func (x *FlowControlPolicyTask) GetActionAccept() *ActionAcceptConfig {
	if x != nil {
		if x, ok := x.ActionConfig.(*FlowControlPolicyTask_ActionAccept); ok {
			return x.ActionAccept
		}
	}
	return nil
}

func (x *FlowControlPolicyTask) GetActionChannel() *ActionChannelConfig {
	if x != nil {
		if x, ok := x.ActionConfig.(*FlowControlPolicyTask_ActionChannel); ok {
			return x.ActionChannel
		}
	}
	return nil
}

type isFlowControlPolicyTask_ActionConfig interface {
	isFlowControlPolicyTask_ActionConfig()
}

type FlowControlPolicyTask_ActionAccept struct {
	ActionAccept *ActionAcceptConfig `protobuf:"bytes,13,opt,name=action_accept,json=actionAccept,proto3,oneof"` // 允许动作配置
}

type FlowControlPolicyTask_ActionChannel struct {
	ActionChannel *ActionChannelConfig `protobuf:"bytes,14,opt,name=action_channel,json=actionChannel,proto3,oneof"` // 通道动作配置
}

func (*FlowControlPolicyTask_ActionAccept) isFlowControlPolicyTask_ActionConfig() {}

func (*FlowControlPolicyTask_ActionChannel) isFlowControlPolicyTask_ActionConfig() {}

// 流量控制任务配置消息
type FlowControlTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to TaskConfig:
	//
	//	*FlowControlTask_PolicyGroup
	//	*FlowControlTask_Policy
	TaskConfig    isFlowControlTask_TaskConfig `protobuf_oneof:"task_config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlowControlTask) Reset() {
	*x = FlowControlTask{}
	mi := &file_message_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlowControlTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowControlTask) ProtoMessage() {}

func (x *FlowControlTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowControlTask.ProtoReflect.Descriptor instead.
func (*FlowControlTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{56}
}

func (x *FlowControlTask) GetTaskConfig() isFlowControlTask_TaskConfig {
	if x != nil {
		return x.TaskConfig
	}
	return nil
}

func (x *FlowControlTask) GetPolicyGroup() *FlowControlPolicyGroupTask {
	if x != nil {
		if x, ok := x.TaskConfig.(*FlowControlTask_PolicyGroup); ok {
			return x.PolicyGroup
		}
	}
	return nil
}

func (x *FlowControlTask) GetPolicy() *FlowControlPolicyTask {
	if x != nil {
		if x, ok := x.TaskConfig.(*FlowControlTask_Policy); ok {
			return x.Policy
		}
	}
	return nil
}

type isFlowControlTask_TaskConfig interface {
	isFlowControlTask_TaskConfig()
}

type FlowControlTask_PolicyGroup struct {
	PolicyGroup *FlowControlPolicyGroupTask `protobuf:"bytes,1,opt,name=policy_group,json=policyGroup,proto3,oneof"` // 策略组配置任务
}

type FlowControlTask_Policy struct {
	Policy *FlowControlPolicyTask `protobuf:"bytes,2,opt,name=policy,proto3,oneof"` // 策略配置任务
}

func (*FlowControlTask_PolicyGroup) isFlowControlTask_TaskConfig() {}

func (*FlowControlTask_Policy) isFlowControlTask_TaskConfig() {}

// NAT IP地址池配置
type NatIpPool struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            []*IpAddress           `protobuf:"bytes,1,rep,name=ip,proto3" json:"ip,omitempty"`                             // 单个IP地址列表
	IpRanges      []*IpRange             `protobuf:"bytes,2,rep,name=ip_ranges,json=ipRanges,proto3" json:"ip_ranges,omitempty"` // IP地址范围列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NatIpPool) Reset() {
	*x = NatIpPool{}
	mi := &file_message_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NatIpPool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NatIpPool) ProtoMessage() {}

func (x *NatIpPool) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NatIpPool.ProtoReflect.Descriptor instead.
func (*NatIpPool) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{57}
}

func (x *NatIpPool) GetIp() []*IpAddress {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *NatIpPool) GetIpRanges() []*IpRange {
	if x != nil {
		return x.IpRanges
	}
	return nil
}

// 路由策略动作配置
type RouteActionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxy         string                 `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`                          // 线路名称 (required)
	NextHop       *IpAddress             `protobuf:"bytes,2,opt,name=next_hop,json=nextHop,proto3,oneof" json:"next_hop,omitempty"` // 下一跳地址（路由动作时使用）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteActionConfig) Reset() {
	*x = RouteActionConfig{}
	mi := &file_message_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteActionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteActionConfig) ProtoMessage() {}

func (x *RouteActionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteActionConfig.ProtoReflect.Descriptor instead.
func (*RouteActionConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{58}
}

func (x *RouteActionConfig) GetProxy() string {
	if x != nil {
		return x.Proxy
	}
	return ""
}

func (x *RouteActionConfig) GetNextHop() *IpAddress {
	if x != nil {
		return x.NextHop
	}
	return nil
}

// 路由策略动作配置
type NatActionConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxy         string                 `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`                                         // 线路名称 (required)
	NewDstIp      *IpAddress             `protobuf:"bytes,2,opt,name=new_dst_ip,json=newDstIp,proto3,oneof" json:"new_dst_ip,omitempty"`           // DNAT目标地址（DNAT动作时使用）
	NatIp         *NatIpPool             `protobuf:"bytes,3,opt,name=nat_ip,json=natIp,proto3,oneof" json:"nat_ip,omitempty"`                      // SNAT地址池（NAT/DNAT动作时使用）
	NextHop       *IpAddress             `protobuf:"bytes,4,opt,name=next_hop,json=nextHop,proto3,oneof" json:"next_hop,omitempty"`                // 下一跳地址（路由动作时使用）
	FullConeNat   *bool                  `protobuf:"varint,5,opt,name=full_cone_nat,json=fullConeNat,proto3,oneof" json:"full_cone_nat,omitempty"` // 全锥形NAT（NAT时有效）
	NoSnat        *bool                  `protobuf:"varint,6,opt,name=no_snat,json=noSnat,proto3,oneof" json:"no_snat,omitempty"`                  // 不改变源地址（DNAT时有效）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NatActionConfig) Reset() {
	*x = NatActionConfig{}
	mi := &file_message_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NatActionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NatActionConfig) ProtoMessage() {}

func (x *NatActionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NatActionConfig.ProtoReflect.Descriptor instead.
func (*NatActionConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{59}
}

func (x *NatActionConfig) GetProxy() string {
	if x != nil {
		return x.Proxy
	}
	return ""
}

func (x *NatActionConfig) GetNewDstIp() *IpAddress {
	if x != nil {
		return x.NewDstIp
	}
	return nil
}

func (x *NatActionConfig) GetNatIp() *NatIpPool {
	if x != nil {
		return x.NatIp
	}
	return nil
}

func (x *NatActionConfig) GetNextHop() *IpAddress {
	if x != nil {
		return x.NextHop
	}
	return nil
}

func (x *NatActionConfig) GetFullConeNat() bool {
	if x != nil && x.FullConeNat != nil {
		return *x.FullConeNat
	}
	return false
}

func (x *NatActionConfig) GetNoSnat() bool {
	if x != nil && x.NoSnat != nil {
		return *x.NoSnat
	}
	return false
}

// 路由策略配置消息
type RoutePolicyTask struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Cookie   uint32                 `protobuf:"varint,1,opt,name=cookie,proto3" json:"cookie,omitempty"`                        // 策略cookie(策略唯一标识)，uint32 (required)
	Desc     string                 `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`                             // 策略描述 (添加修改时必填)
	Previous *uint32                `protobuf:"varint,3,opt,name=previous,proto3,oneof" json:"previous,omitempty"`              // 前一个策略cookie，用于排序; zone首个策略为0;zone追加策略为-1
	Disable  bool                   `protobuf:"varint,4,opt,name=disable,proto3" json:"disable,omitempty"`                      // 是否启用：false为启用，true为禁用 (添加修改时必填)
	SchTime  *int32                 `protobuf:"varint,5,opt,name=sch_time,json=schTime,proto3,oneof" json:"sch_time,omitempty"` // 策略时段ID，0为任意
	Zone     *RoutePolicyZone       `protobuf:"varint,6,opt,name=zone,proto3,enum=RoutePolicyZone,oneof" json:"zone,omitempty"` // 策略所属Zone，用于分层排序管理
	// 用户/访问者配置
	Src     []*AddressSelector `protobuf:"bytes,7,rep,name=src,proto3" json:"src,omitempty"`                                             // 源地址配置列表
	SrcPort *PortSpec          `protobuf:"bytes,8,opt,name=src_port,json=srcPort,proto3,oneof" json:"src_port,omitempty"`                // 源端口配置
	UsrType *UserType          `protobuf:"varint,9,opt,name=usr_type,json=usrType,proto3,enum=UserType,oneof" json:"usr_type,omitempty"` // 用户类型
	Pool    *int32             `protobuf:"varint,10,opt,name=pool,proto3,oneof" json:"pool,omitempty"`                                   // 用户组ID，0为任意
	// 服务者/服务配置
	Dst     []*AddressSelector `protobuf:"bytes,11,rep,name=dst,proto3" json:"dst,omitempty"`                              // 目的地址配置列表
	DstPort *PortSpec          `protobuf:"bytes,12,opt,name=dst_port,json=dstPort,proto3,oneof" json:"dst_port,omitempty"` // 目的端口配置
	Proto   *AppProtocolSpec   `protobuf:"bytes,13,opt,name=proto,proto3,oneof" json:"proto,omitempty"`                    // 协议类型
	// 接口配置
	InIf     *string   `protobuf:"bytes,15,opt,name=in_if,json=inIf,proto3,oneof" json:"in_if,omitempty"`                // 源接口，any为任意
	WanBw    *int32    `protobuf:"varint,16,opt,name=wan_bw,json=wanBw,proto3,oneof" json:"wan_bw,omitempty"`            // 上行带宽，0为不限制
	WanBwOut *int32    `protobuf:"varint,17,opt,name=wan_bw_out,json=wanBwOut,proto3,oneof" json:"wan_bw_out,omitempty"` // 下行带宽，0为不限制
	Vlan     *IntRange `protobuf:"bytes,18,opt,name=vlan,proto3,oneof" json:"vlan,omitempty"`                            // VLAN ID范围
	Ttl      *IntRange `protobuf:"bytes,19,opt,name=ttl,proto3,oneof" json:"ttl,omitempty"`                              // TTL值范围
	Dscp     *IntRange `protobuf:"bytes,20,opt,name=dscp,proto3,oneof" json:"dscp,omitempty"`                            // DSCP值范围
	// 执行动作
	Action        RoutePolicyAction  `protobuf:"varint,21,opt,name=action,proto3,enum=RoutePolicyAction" json:"action,omitempty"`            // 执行动作类型 (required)
	RouteConfig   *RouteActionConfig `protobuf:"bytes,22,opt,name=route_config,json=routeConfig,proto3,oneof" json:"route_config,omitempty"` // 路由动作配置 (required)
	NatConfig     *NatActionConfig   `protobuf:"bytes,23,opt,name=nat_config,json=natConfig,proto3,oneof" json:"nat_config,omitempty"`       // NAT动作配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoutePolicyTask) Reset() {
	*x = RoutePolicyTask{}
	mi := &file_message_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoutePolicyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoutePolicyTask) ProtoMessage() {}

func (x *RoutePolicyTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoutePolicyTask.ProtoReflect.Descriptor instead.
func (*RoutePolicyTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{60}
}

func (x *RoutePolicyTask) GetCookie() uint32 {
	if x != nil {
		return x.Cookie
	}
	return 0
}

func (x *RoutePolicyTask) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RoutePolicyTask) GetPrevious() uint32 {
	if x != nil && x.Previous != nil {
		return *x.Previous
	}
	return 0
}

func (x *RoutePolicyTask) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *RoutePolicyTask) GetSchTime() int32 {
	if x != nil && x.SchTime != nil {
		return *x.SchTime
	}
	return 0
}

func (x *RoutePolicyTask) GetZone() RoutePolicyZone {
	if x != nil && x.Zone != nil {
		return *x.Zone
	}
	return RoutePolicyZone_CTRL_TIER_T1
}

func (x *RoutePolicyTask) GetSrc() []*AddressSelector {
	if x != nil {
		return x.Src
	}
	return nil
}

func (x *RoutePolicyTask) GetSrcPort() *PortSpec {
	if x != nil {
		return x.SrcPort
	}
	return nil
}

func (x *RoutePolicyTask) GetUsrType() UserType {
	if x != nil && x.UsrType != nil {
		return *x.UsrType
	}
	return UserType_USER_TYPE_ANY
}

func (x *RoutePolicyTask) GetPool() int32 {
	if x != nil && x.Pool != nil {
		return *x.Pool
	}
	return 0
}

func (x *RoutePolicyTask) GetDst() []*AddressSelector {
	if x != nil {
		return x.Dst
	}
	return nil
}

func (x *RoutePolicyTask) GetDstPort() *PortSpec {
	if x != nil {
		return x.DstPort
	}
	return nil
}

func (x *RoutePolicyTask) GetProto() *AppProtocolSpec {
	if x != nil {
		return x.Proto
	}
	return nil
}

func (x *RoutePolicyTask) GetInIf() string {
	if x != nil && x.InIf != nil {
		return *x.InIf
	}
	return ""
}

func (x *RoutePolicyTask) GetWanBw() int32 {
	if x != nil && x.WanBw != nil {
		return *x.WanBw
	}
	return 0
}

func (x *RoutePolicyTask) GetWanBwOut() int32 {
	if x != nil && x.WanBwOut != nil {
		return *x.WanBwOut
	}
	return 0
}

func (x *RoutePolicyTask) GetVlan() *IntRange {
	if x != nil {
		return x.Vlan
	}
	return nil
}

func (x *RoutePolicyTask) GetTtl() *IntRange {
	if x != nil {
		return x.Ttl
	}
	return nil
}

func (x *RoutePolicyTask) GetDscp() *IntRange {
	if x != nil {
		return x.Dscp
	}
	return nil
}

func (x *RoutePolicyTask) GetAction() RoutePolicyAction {
	if x != nil {
		return x.Action
	}
	return RoutePolicyAction_ROUTE_ACTION_ROUTE
}

func (x *RoutePolicyTask) GetRouteConfig() *RouteActionConfig {
	if x != nil {
		return x.RouteConfig
	}
	return nil
}

func (x *RoutePolicyTask) GetNatConfig() *NatActionConfig {
	if x != nil {
		return x.NatConfig
	}
	return nil
}

// DNS策略动作配置 - 放行/代播重定向
type DnsPolicyActionPassConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpQps         *uint32                `protobuf:"varint,1,opt,name=ip_qps,json=ipQps,proto3,oneof" json:"ip_qps,omitempty"` // 单用户QPS，单位/s，0为不限制
	Next          bool                   `protobuf:"varint,2,opt,name=next,proto3" json:"next,omitempty"`                      // 是否继续匹配：false为继续匹配，true为停止匹配
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsPolicyActionPassConfig) Reset() {
	*x = DnsPolicyActionPassConfig{}
	mi := &file_message_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsPolicyActionPassConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsPolicyActionPassConfig) ProtoMessage() {}

func (x *DnsPolicyActionPassConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsPolicyActionPassConfig.ProtoReflect.Descriptor instead.
func (*DnsPolicyActionPassConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{61}
}

func (x *DnsPolicyActionPassConfig) GetIpQps() uint32 {
	if x != nil && x.IpQps != nil {
		return *x.IpQps
	}
	return 0
}

func (x *DnsPolicyActionPassConfig) GetNext() bool {
	if x != nil {
		return x.Next
	}
	return false
}

// DNS策略动作配置 - 牵引
type DnsPolicyActionRdrConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ActArg        string                 `protobuf:"bytes,1,opt,name=act_arg,json=actArg,proto3" json:"act_arg,omitempty"`        // 牵引线路 (required)
	NoSnat        *bool                  `protobuf:"varint,2,opt,name=no_snat,json=noSnat,proto3,oneof" json:"no_snat,omitempty"` // 不改变源地址：false为改变，true为不改变
	DnsList       []*IpAddress           `protobuf:"bytes,3,rep,name=dns_list,json=dnsList,proto3" json:"dns_list,omitempty"`     // 牵引DNS列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsPolicyActionRdrConfig) Reset() {
	*x = DnsPolicyActionRdrConfig{}
	mi := &file_message_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsPolicyActionRdrConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsPolicyActionRdrConfig) ProtoMessage() {}

func (x *DnsPolicyActionRdrConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsPolicyActionRdrConfig.ProtoReflect.Descriptor instead.
func (*DnsPolicyActionRdrConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{62}
}

func (x *DnsPolicyActionRdrConfig) GetActArg() string {
	if x != nil {
		return x.ActArg
	}
	return ""
}

func (x *DnsPolicyActionRdrConfig) GetNoSnat() bool {
	if x != nil && x.NoSnat != nil {
		return *x.NoSnat
	}
	return false
}

func (x *DnsPolicyActionRdrConfig) GetDnsList() []*IpAddress {
	if x != nil {
		return x.DnsList
	}
	return nil
}

// DNS策略动作配置 - 解析
type DnsPolicyActionReplyConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ActArg        []*IpAddress           `protobuf:"bytes,1,rep,name=act_arg,json=actArg,proto3" json:"act_arg,omitempty"` // 解析IP列表 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsPolicyActionReplyConfig) Reset() {
	*x = DnsPolicyActionReplyConfig{}
	mi := &file_message_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsPolicyActionReplyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsPolicyActionReplyConfig) ProtoMessage() {}

func (x *DnsPolicyActionReplyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsPolicyActionReplyConfig.ProtoReflect.Descriptor instead.
func (*DnsPolicyActionReplyConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{63}
}

func (x *DnsPolicyActionReplyConfig) GetActArg() []*IpAddress {
	if x != nil {
		return x.ActArg
	}
	return nil
}

// DNS策略动作配置 - QPS限制
type DnsPolicyActionLimitConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpQps         *uint32                `protobuf:"varint,1,opt,name=ip_qps,json=ipQps,proto3,oneof" json:"ip_qps,omitempty"`    // 单用户QPS，单位/s，0为不限制
	ActArg        *uint32                `protobuf:"varint,2,opt,name=act_arg,json=actArg,proto3,oneof" json:"act_arg,omitempty"` // 总QPS，单位/s，0为不限制
	Next          bool                   `protobuf:"varint,3,opt,name=next,proto3" json:"next,omitempty"`                         // 是否继续匹配：false为继续匹配，true为停止匹配
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsPolicyActionLimitConfig) Reset() {
	*x = DnsPolicyActionLimitConfig{}
	mi := &file_message_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsPolicyActionLimitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsPolicyActionLimitConfig) ProtoMessage() {}

func (x *DnsPolicyActionLimitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsPolicyActionLimitConfig.ProtoReflect.Descriptor instead.
func (*DnsPolicyActionLimitConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{64}
}

func (x *DnsPolicyActionLimitConfig) GetIpQps() uint32 {
	if x != nil && x.IpQps != nil {
		return *x.IpQps
	}
	return 0
}

func (x *DnsPolicyActionLimitConfig) GetActArg() uint32 {
	if x != nil && x.ActArg != nil {
		return *x.ActArg
	}
	return 0
}

func (x *DnsPolicyActionLimitConfig) GetNext() bool {
	if x != nil {
		return x.Next
	}
	return false
}

// DNS管控策略配置消息
type DnsPolicyTask struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Cookie   uint32                 `protobuf:"varint,1,opt,name=cookie,proto3" json:"cookie,omitempty"`                        // 策略cookie(策略唯一标识)，uint32 (required)
	Previous *uint32                `protobuf:"varint,2,opt,name=previous,proto3,oneof" json:"previous,omitempty"`              // 前一个策略cookie，用于排序，0表示首个策略，-1表示追加到最后
	Disable  bool                   `protobuf:"varint,3,opt,name=disable,proto3" json:"disable,omitempty"`                      // 是否启用：false为启用，true为禁用 (添加修改时必填)
	SchTime  *int32                 `protobuf:"varint,4,opt,name=sch_time,json=schTime,proto3,oneof" json:"sch_time,omitempty"` // 策略时段ID，0为任意
	// 用户/访问者配置
	InIp    []*AddressSelector `protobuf:"bytes,5,rep,name=in_ip,json=inIp,proto3" json:"in_ip,omitempty"`                               // 源地址配置列表
	Pool    *int32             `protobuf:"varint,6,opt,name=pool,proto3,oneof" json:"pool,omitempty"`                                    // 用户组ID，0为任意
	UsrType *UserType          `protobuf:"varint,7,opt,name=usr_type,json=usrType,proto3,enum=UserType,oneof" json:"usr_type,omitempty"` // 用户类型
	// 服务者/服务配置
	OutIp       []*AddressSelector `protobuf:"bytes,8,rep,name=out_ip,json=outIp,proto3" json:"out_ip,omitempty"`                           // 目的地址配置列表
	App         *AppProtocolSpec   `protobuf:"bytes,9,opt,name=app,proto3,oneof" json:"app,omitempty"`                                      // 应用协议配置
	DomainGroup []string           `protobuf:"bytes,10,rep,name=domain_group,json=domainGroup,proto3" json:"domain_group,omitempty"`        // 域名群组名称，空为任意
	AType       *DnsQueryType      `protobuf:"varint,11,opt,name=a_type,json=aType,proto3,enum=DnsQueryType,oneof" json:"a_type,omitempty"` // 查询类型
	// 接口配置
	InIf   *string   `protobuf:"bytes,12,opt,name=in_if,json=inIf,proto3,oneof" json:"in_if,omitempty"` // 源接口，any为任意
	Bridge *int32    `protobuf:"varint,13,opt,name=bridge,proto3,oneof" json:"bridge,omitempty"`        // 路径网桥接口
	Vlan   *IntRange `protobuf:"bytes,14,opt,name=vlan,proto3,oneof" json:"vlan,omitempty"`             // VLAN ID范围
	// 执行动作
	Action DnsPolicyAction `protobuf:"varint,15,opt,name=action,proto3,enum=DnsPolicyAction" json:"action,omitempty"` // 执行动作类型 (required)
	// Types that are valid to be assigned to ActionConfig:
	//
	//	*DnsPolicyTask_ActionPass
	//	*DnsPolicyTask_ActionRdr
	//	*DnsPolicyTask_ActionReply
	//	*DnsPolicyTask_ActionLimit
	ActionConfig  isDnsPolicyTask_ActionConfig `protobuf_oneof:"action_config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsPolicyTask) Reset() {
	*x = DnsPolicyTask{}
	mi := &file_message_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsPolicyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsPolicyTask) ProtoMessage() {}

func (x *DnsPolicyTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsPolicyTask.ProtoReflect.Descriptor instead.
func (*DnsPolicyTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{65}
}

func (x *DnsPolicyTask) GetCookie() uint32 {
	if x != nil {
		return x.Cookie
	}
	return 0
}

func (x *DnsPolicyTask) GetPrevious() uint32 {
	if x != nil && x.Previous != nil {
		return *x.Previous
	}
	return 0
}

func (x *DnsPolicyTask) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *DnsPolicyTask) GetSchTime() int32 {
	if x != nil && x.SchTime != nil {
		return *x.SchTime
	}
	return 0
}

func (x *DnsPolicyTask) GetInIp() []*AddressSelector {
	if x != nil {
		return x.InIp
	}
	return nil
}

func (x *DnsPolicyTask) GetPool() int32 {
	if x != nil && x.Pool != nil {
		return *x.Pool
	}
	return 0
}

func (x *DnsPolicyTask) GetUsrType() UserType {
	if x != nil && x.UsrType != nil {
		return *x.UsrType
	}
	return UserType_USER_TYPE_ANY
}

func (x *DnsPolicyTask) GetOutIp() []*AddressSelector {
	if x != nil {
		return x.OutIp
	}
	return nil
}

func (x *DnsPolicyTask) GetApp() *AppProtocolSpec {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *DnsPolicyTask) GetDomainGroup() []string {
	if x != nil {
		return x.DomainGroup
	}
	return nil
}

func (x *DnsPolicyTask) GetAType() DnsQueryType {
	if x != nil && x.AType != nil {
		return *x.AType
	}
	return DnsQueryType_DNS_QUERY_TYPE_ANY
}

func (x *DnsPolicyTask) GetInIf() string {
	if x != nil && x.InIf != nil {
		return *x.InIf
	}
	return ""
}

func (x *DnsPolicyTask) GetBridge() int32 {
	if x != nil && x.Bridge != nil {
		return *x.Bridge
	}
	return 0
}

func (x *DnsPolicyTask) GetVlan() *IntRange {
	if x != nil {
		return x.Vlan
	}
	return nil
}

func (x *DnsPolicyTask) GetAction() DnsPolicyAction {
	if x != nil {
		return x.Action
	}
	return DnsPolicyAction_DNS_ACTION_PASS
}

func (x *DnsPolicyTask) GetActionConfig() isDnsPolicyTask_ActionConfig {
	if x != nil {
		return x.ActionConfig
	}
	return nil
}

func (x *DnsPolicyTask) GetActionPass() *DnsPolicyActionPassConfig {
	if x != nil {
		if x, ok := x.ActionConfig.(*DnsPolicyTask_ActionPass); ok {
			return x.ActionPass
		}
	}
	return nil
}

func (x *DnsPolicyTask) GetActionRdr() *DnsPolicyActionRdrConfig {
	if x != nil {
		if x, ok := x.ActionConfig.(*DnsPolicyTask_ActionRdr); ok {
			return x.ActionRdr
		}
	}
	return nil
}

func (x *DnsPolicyTask) GetActionReply() *DnsPolicyActionReplyConfig {
	if x != nil {
		if x, ok := x.ActionConfig.(*DnsPolicyTask_ActionReply); ok {
			return x.ActionReply
		}
	}
	return nil
}

func (x *DnsPolicyTask) GetActionLimit() *DnsPolicyActionLimitConfig {
	if x != nil {
		if x, ok := x.ActionConfig.(*DnsPolicyTask_ActionLimit); ok {
			return x.ActionLimit
		}
	}
	return nil
}

type isDnsPolicyTask_ActionConfig interface {
	isDnsPolicyTask_ActionConfig()
}

type DnsPolicyTask_ActionPass struct {
	ActionPass *DnsPolicyActionPassConfig `protobuf:"bytes,16,opt,name=action_pass,json=actionPass,proto3,oneof"` // 放行/代播重定向动作配置
}

type DnsPolicyTask_ActionRdr struct {
	ActionRdr *DnsPolicyActionRdrConfig `protobuf:"bytes,17,opt,name=action_rdr,json=actionRdr,proto3,oneof"` // 牵引动作配置
}

type DnsPolicyTask_ActionReply struct {
	ActionReply *DnsPolicyActionReplyConfig `protobuf:"bytes,18,opt,name=action_reply,json=actionReply,proto3,oneof"` // 解析动作配置
}

type DnsPolicyTask_ActionLimit struct {
	ActionLimit *DnsPolicyActionLimitConfig `protobuf:"bytes,19,opt,name=action_limit,json=actionLimit,proto3,oneof"` // QPS限制动作配置
}

func (*DnsPolicyTask_ActionPass) isDnsPolicyTask_ActionConfig() {}

func (*DnsPolicyTask_ActionRdr) isDnsPolicyTask_ActionConfig() {}

func (*DnsPolicyTask_ActionReply) isDnsPolicyTask_ActionConfig() {}

func (*DnsPolicyTask_ActionLimit) isDnsPolicyTask_ActionConfig() {}

// DNS跟踪策略配置消息
type DnsTrackingPolicyTask struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cookie        uint32                 `protobuf:"varint,1,opt,name=cookie,proto3" json:"cookie,omitempty"`                              // 策略cookie(策略唯一标识)，uint32 (required)
	Previous      *uint32                `protobuf:"varint,2,opt,name=previous,proto3,oneof" json:"previous,omitempty"`                    // 前一个策略cookie，用于排序，0表示首个策略，-1表示追加到最后
	Disable       bool                   `protobuf:"varint,3,opt,name=disable,proto3" json:"disable,omitempty"`                            // 是否启用：false为启用，true为禁用 (添加修改时必填)
	DomainGroup   []string               `protobuf:"bytes,4,rep,name=domain_group,json=domainGroup,proto3" json:"domain_group,omitempty"`  // 域名群组名称列表，agent需要通过域名群组名称反查ID (required)
	Pxy           string                 `protobuf:"bytes,5,opt,name=pxy,proto3" json:"pxy,omitempty"`                                     // 线路名称 (required)
	BackupPxy     string                 `protobuf:"bytes,6,opt,name=backup_pxy,json=backupPxy,proto3" json:"backup_pxy,omitempty"`        // 备份线路名称 (required)
	DnsAddr       *IpAddress             `protobuf:"bytes,7,opt,name=dns_addr,json=dnsAddr,proto3,oneof" json:"dns_addr,omitempty"`        // DNS服务器地址，为空时表示使用线路DNS服务器
	TrackHost     *bool                  `protobuf:"varint,8,opt,name=track_host,json=trackHost,proto3,oneof" json:"track_host,omitempty"` // 跟踪host标志，true表示跟踪，false表示不跟踪
	CacheTtl      *uint32                `protobuf:"varint,9,opt,name=cache_ttl,json=cacheTtl,proto3,oneof" json:"cache_ttl,omitempty"`    // DNS缓存应答TTL，单位秒，0表示关闭
	Desc          *string                `protobuf:"bytes,10,opt,name=desc,proto3,oneof" json:"desc,omitempty"`                            // 备注描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsTrackingPolicyTask) Reset() {
	*x = DnsTrackingPolicyTask{}
	mi := &file_message_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsTrackingPolicyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsTrackingPolicyTask) ProtoMessage() {}

func (x *DnsTrackingPolicyTask) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsTrackingPolicyTask.ProtoReflect.Descriptor instead.
func (*DnsTrackingPolicyTask) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{66}
}

func (x *DnsTrackingPolicyTask) GetCookie() uint32 {
	if x != nil {
		return x.Cookie
	}
	return 0
}

func (x *DnsTrackingPolicyTask) GetPrevious() uint32 {
	if x != nil && x.Previous != nil {
		return *x.Previous
	}
	return 0
}

func (x *DnsTrackingPolicyTask) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *DnsTrackingPolicyTask) GetDomainGroup() []string {
	if x != nil {
		return x.DomainGroup
	}
	return nil
}

func (x *DnsTrackingPolicyTask) GetPxy() string {
	if x != nil {
		return x.Pxy
	}
	return ""
}

func (x *DnsTrackingPolicyTask) GetBackupPxy() string {
	if x != nil {
		return x.BackupPxy
	}
	return ""
}

func (x *DnsTrackingPolicyTask) GetDnsAddr() *IpAddress {
	if x != nil {
		return x.DnsAddr
	}
	return nil
}

func (x *DnsTrackingPolicyTask) GetTrackHost() bool {
	if x != nil && x.TrackHost != nil {
		return *x.TrackHost
	}
	return false
}

func (x *DnsTrackingPolicyTask) GetCacheTtl() uint32 {
	if x != nil && x.CacheTtl != nil {
		return *x.CacheTtl
	}
	return 0
}

func (x *DnsTrackingPolicyTask) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

// 单个线路状态信息
type ProxyStatusInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                    // 线路名称 (required)
	Type          ProxyType              `protobuf:"varint,2,opt,name=type,proto3,enum=ProxyType" json:"type,omitempty"`    // 线路类型 (required)
	State         ProxyState             `protobuf:"varint,3,opt,name=state,proto3,enum=ProxyState" json:"state,omitempty"` // 线路状态 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProxyStatusInfo) Reset() {
	*x = ProxyStatusInfo{}
	mi := &file_message_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProxyStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProxyStatusInfo) ProtoMessage() {}

func (x *ProxyStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProxyStatusInfo.ProtoReflect.Descriptor instead.
func (*ProxyStatusInfo) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{67}
}

func (x *ProxyStatusInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProxyStatusInfo) GetType() ProxyType {
	if x != nil {
		return x.Type
	}
	return ProxyType_PROXY_TYPE_NONE
}

func (x *ProxyStatusInfo) GetState() ProxyState {
	if x != nil {
		return x.State
	}
	return ProxyState_PROXY_STATE_DOWN
}

// 线路状态上报数据
type ProxyStatusReport struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TriggerType   ReportTriggerType      `protobuf:"varint,1,opt,name=trigger_type,json=triggerType,proto3,enum=ReportTriggerType" json:"trigger_type,omitempty"` // 触发类型：事件触发或周期性 (required)
	ProxyStatus   []*ProxyStatusInfo     `protobuf:"bytes,2,rep,name=proxy_status,json=proxyStatus,proto3" json:"proxy_status,omitempty"`                         // 线路状态信息列表 (required)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProxyStatusReport) Reset() {
	*x = ProxyStatusReport{}
	mi := &file_message_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProxyStatusReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProxyStatusReport) ProtoMessage() {}

func (x *ProxyStatusReport) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProxyStatusReport.ProtoReflect.Descriptor instead.
func (*ProxyStatusReport) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{68}
}

func (x *ProxyStatusReport) GetTriggerType() ReportTriggerType {
	if x != nil {
		return x.TriggerType
	}
	return ReportTriggerType_REPORT_TRIGGER_EVENT
}

func (x *ProxyStatusReport) GetProxyStatus() []*ProxyStatusInfo {
	if x != nil {
		return x.ProxyStatus
	}
	return nil
}

// 设备上报消息（类似于 DeviceTask）
type DeviceReport struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	ReportType ReportType             `protobuf:"varint,1,opt,name=report_type,json=reportType,proto3,enum=ReportType" json:"report_type,omitempty"` // 上报类型 (required)
	// Types that are valid to be assigned to Payload:
	//
	//	*DeviceReport_ProxyStatusReport
	Payload       isDeviceReport_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceReport) Reset() {
	*x = DeviceReport{}
	mi := &file_message_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReport) ProtoMessage() {}

func (x *DeviceReport) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReport.ProtoReflect.Descriptor instead.
func (*DeviceReport) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{69}
}

func (x *DeviceReport) GetReportType() ReportType {
	if x != nil {
		return x.ReportType
	}
	return ReportType_REPORT_TYPE_PROXY_STATUS
}

func (x *DeviceReport) GetPayload() isDeviceReport_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *DeviceReport) GetProxyStatusReport() *ProxyStatusReport {
	if x != nil {
		if x, ok := x.Payload.(*DeviceReport_ProxyStatusReport); ok {
			return x.ProxyStatusReport
		}
	}
	return nil
}

type isDeviceReport_Payload interface {
	isDeviceReport_Payload()
}

type DeviceReport_ProxyStatusReport struct {
	ProxyStatusReport *ProxyStatusReport `protobuf:"bytes,2,opt,name=proxy_status_report,json=proxyStatusReport,proto3,oneof"` // 线路状态上报
}

func (*DeviceReport_ProxyStatusReport) isDeviceReport_Payload() {}

// 设备上报请求消息
type DeviceReportRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	CustomerId      int32                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`                // 客户ID
	ClientId        int32                  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`                      // 客户端ID
	Uuid            string                 `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`                                               // 请求 UUID
	DeviceReports   []*DeviceReport        `protobuf:"bytes,4,rep,name=device_reports,json=deviceReports,proto3" json:"device_reports,omitempty"`        // 设备上报列表
	ReportTimestamp int64                  `protobuf:"varint,5,opt,name=report_timestamp,json=reportTimestamp,proto3" json:"report_timestamp,omitempty"` // 上报时间戳，Unix时间戳（秒） (required)
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DeviceReportRequest) Reset() {
	*x = DeviceReportRequest{}
	mi := &file_message_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReportRequest) ProtoMessage() {}

func (x *DeviceReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReportRequest.ProtoReflect.Descriptor instead.
func (*DeviceReportRequest) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{70}
}

func (x *DeviceReportRequest) GetCustomerId() int32 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *DeviceReportRequest) GetClientId() int32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *DeviceReportRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DeviceReportRequest) GetDeviceReports() []*DeviceReport {
	if x != nil {
		return x.DeviceReports
	}
	return nil
}

func (x *DeviceReportRequest) GetReportTimestamp() int64 {
	if x != nil {
		return x.ReportTimestamp
	}
	return 0
}

// 设备上报响应消息
type DeviceReportResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uuid          string                 `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`                       // 返回 DeviceReportRequest 中的 uuid
	ErrCode       int32                  `protobuf:"varint,2,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"` // 0 表示成功，其他表示错误
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceReportResponse) Reset() {
	*x = DeviceReportResponse{}
	mi := &file_message_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceReportResponse) ProtoMessage() {}

func (x *DeviceReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceReportResponse.ProtoReflect.Descriptor instead.
func (*DeviceReportResponse) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{71}
}

func (x *DeviceReportResponse) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DeviceReportResponse) GetErrCode() int32 {
	if x != nil {
		return x.ErrCode
	}
	return 0
}

// 固定IP-V4配置
type WanTask_StaticIpConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GwPxy         WanGatewayType         `protobuf:"varint,1,opt,name=gw_pxy,json=gwPxy,proto3,enum=WanGatewayType" json:"gw_pxy,omitempty"` // 网关类型 (required)
	Addr          *IpAddress             `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`                                     // 接口ip (required)
	Gateway       *IpAddress             `protobuf:"bytes,3,opt,name=gateway,proto3" json:"gateway,omitempty"`                               // 网关地址 (required)
	Dns           *IpAddress             `protobuf:"bytes,4,opt,name=dns,proto3,oneof" json:"dns,omitempty"`                                 // dns服务器地址
	NatIp         *NatIp                 `protobuf:"bytes,5,opt,name=nat_ip,json=natIp,proto3,oneof" json:"nat_ip,omitempty"`                // NAT地址池
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanTask_StaticIpConfig) Reset() {
	*x = WanTask_StaticIpConfig{}
	mi := &file_message_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanTask_StaticIpConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanTask_StaticIpConfig) ProtoMessage() {}

func (x *WanTask_StaticIpConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanTask_StaticIpConfig.ProtoReflect.Descriptor instead.
func (*WanTask_StaticIpConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{18, 0}
}

func (x *WanTask_StaticIpConfig) GetGwPxy() WanGatewayType {
	if x != nil {
		return x.GwPxy
	}
	return WanGatewayType_WAN_GATEWAY_TYPE_NORMAL
}

func (x *WanTask_StaticIpConfig) GetAddr() *IpAddress {
	if x != nil {
		return x.Addr
	}
	return nil
}

func (x *WanTask_StaticIpConfig) GetGateway() *IpAddress {
	if x != nil {
		return x.Gateway
	}
	return nil
}

func (x *WanTask_StaticIpConfig) GetDns() *IpAddress {
	if x != nil {
		return x.Dns
	}
	return nil
}

func (x *WanTask_StaticIpConfig) GetNatIp() *NatIp {
	if x != nil {
		return x.NatIp
	}
	return nil
}

// PPPoE-V4配置
type WanTask_PppoeConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`                        // PPPoE账号，ASCII字符，max 255字节 (required)
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`                        // PPPoE密码 (required)
	AcName        *string                `protobuf:"bytes,3,opt,name=ac_name,json=acName,proto3,oneof" json:"ac_name,omitempty"`        // BRAS 名称，ASCII字符，max 64字节
	SvcName       *string                `protobuf:"bytes,4,opt,name=svc_name,json=svcName,proto3,oneof" json:"svc_name,omitempty"`     // Service名称
	WaitTime      *int32                 `protobuf:"varint,5,opt,name=wait_time,json=waitTime,proto3,oneof" json:"wait_time,omitempty"` // 等待时间，单位秒
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanTask_PppoeConfig) Reset() {
	*x = WanTask_PppoeConfig{}
	mi := &file_message_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanTask_PppoeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanTask_PppoeConfig) ProtoMessage() {}

func (x *WanTask_PppoeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanTask_PppoeConfig.ProtoReflect.Descriptor instead.
func (*WanTask_PppoeConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{18, 1}
}

func (x *WanTask_PppoeConfig) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *WanTask_PppoeConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *WanTask_PppoeConfig) GetAcName() string {
	if x != nil && x.AcName != nil {
		return *x.AcName
	}
	return ""
}

func (x *WanTask_PppoeConfig) GetSvcName() string {
	if x != nil && x.SvcName != nil {
		return *x.SvcName
	}
	return ""
}

func (x *WanTask_PppoeConfig) GetWaitTime() int32 {
	if x != nil && x.WaitTime != nil {
		return *x.WaitTime
	}
	return 0
}

// 其它通用设置
type WanTask_CommonConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DnsPxy        *bool                  `protobuf:"varint,1,opt,name=dns_pxy,json=dnsPxy,proto3,oneof" json:"dns_pxy,omitempty"`                // DNS线路开关
	CloneMac      *string                `protobuf:"bytes,2,opt,name=clone_mac,json=cloneMac,proto3,oneof" json:"clone_mac,omitempty"`           // 克隆MAC，格式：00-00-00-00-00-00
	PingDisable   *bool                  `protobuf:"varint,3,opt,name=ping_disable,json=pingDisable,proto3,oneof" json:"ping_disable,omitempty"` // 外网ping不应答
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanTask_CommonConfig) Reset() {
	*x = WanTask_CommonConfig{}
	mi := &file_message_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanTask_CommonConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanTask_CommonConfig) ProtoMessage() {}

func (x *WanTask_CommonConfig) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanTask_CommonConfig.ProtoReflect.Descriptor instead.
func (*WanTask_CommonConfig) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{18, 2}
}

func (x *WanTask_CommonConfig) GetDnsPxy() bool {
	if x != nil && x.DnsPxy != nil {
		return *x.DnsPxy
	}
	return false
}

func (x *WanTask_CommonConfig) GetCloneMac() string {
	if x != nil && x.CloneMac != nil {
		return *x.CloneMac
	}
	return ""
}

func (x *WanTask_CommonConfig) GetPingDisable() bool {
	if x != nil && x.PingDisable != nil {
		return *x.PingDisable
	}
	return false
}

var File_message_proto protoreflect.FileDescriptor

const file_message_proto_rawDesc = "" +
	"\n" +
	"\rmessage.proto\"\x8d\x01\n" +
	"\x11ClientSyncRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x05R\n" +
	"customerId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\x05R\bclientId\x12\x12\n" +
	"\x04uuid\x18\x03 \x01(\tR\x04uuid\x12&\n" +
	"\tsync_type\x18\x04 \x01(\x0e2\t.SyncTypeR\bsyncType\"\x8a\x01\n" +
	"\x12ServerSyncResponse\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x05R\n" +
	"customerId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\x05R\bclientId\x12\x12\n" +
	"\x04uuid\x18\x03 \x01(\tR\x04uuid\x12\"\n" +
	"\btask_txs\x18\x04 \x03(\v2\a.TaskTxR\ataskTxs\"M\n" +
	"\x06TaskTx\x12\x13\n" +
	"\x05tx_id\x18\x01 \x01(\tR\x04txId\x12.\n" +
	"\fdevice_tasks\x18\x02 \x03(\v2\v.DeviceTaskR\vdeviceTasks\"\xfd\t\n" +
	"\n" +
	"DeviceTask\x12&\n" +
	"\ttask_type\x18\x01 \x01(\x0e2\t.TaskTypeR\btaskType\x12,\n" +
	"\vtask_action\x18\x02 \x01(\x0e2\v.TaskActionR\n" +
	"taskAction\x12%\n" +
	"\bwan_task\x18\x03 \x01(\v2\b.WanTaskH\x00R\awanTask\x12%\n" +
	"\blan_task\x18\x04 \x01(\v2\b.LanTaskH\x00R\alanTask\x127\n" +
	"\x0einterface_task\x18\x05 \x01(\v2\x0e.InterfaceTaskH\x00R\rinterfaceTask\x12.\n" +
	"\tdhcp_task\x18\x06 \x01(\v2\x0f.DhcpServerTaskH\x00R\bdhcpTask\x125\n" +
	"\x0ewan_group_task\x18\a \x01(\v2\r.WanGroupTaskH\x00R\fwanGroupTask\x128\n" +
	"\x0fuser_group_task\x18\b \x01(\v2\x0e.UserGroupTaskH\x00R\ruserGroupTask\x12(\n" +
	"\tuser_task\x18\t \x01(\v2\t.UserTaskH\x00R\buserTask\x128\n" +
	"\x0fiwan_proxy_task\x18\n" +
	" \x01(\v2\x0e.IwanProxyTaskH\x00R\riwanProxyTask\x12>\n" +
	"\x11iwan_service_task\x18\v \x01(\v2\x10.IwanServiceTaskH\x00R\x0fiwanServiceTask\x12>\n" +
	"\x11iwan_mapping_task\x18\f \x01(\v2\x10.IwanMappingTaskH\x00R\x0fiwanMappingTask\x122\n" +
	"\rsr_proxy_task\x18\r \x01(\v2\f.SrProxyTaskH\x00R\vsrProxyTask\x122\n" +
	"\rip_group_task\x18\x0e \x01(\v2\f.IpGroupTaskH\x00R\vipGroupTask\x12>\n" +
	"\x11domain_group_task\x18\x0f \x01(\v2\x10.DomainGroupTaskH\x00R\x0fdomainGroupTask\x12D\n" +
	"\x13effective_time_task\x18\x10 \x01(\v2\x12.EffectiveTimeTaskH\x00R\x11effectiveTimeTask\x12G\n" +
	"\x14traffic_channel_task\x18\x11 \x01(\v2\x13.TrafficChannelTaskH\x00R\x12trafficChannelTask\x12>\n" +
	"\x11traffic_stat_task\x18\x12 \x01(\v2\x10.TrafficStatTaskH\x00R\x0ftrafficStatTask\x12>\n" +
	"\x11flow_control_task\x18\x13 \x01(\v2\x10.FlowControlTaskH\x00R\x0fflowControlTask\x12>\n" +
	"\x11route_policy_task\x18\x14 \x01(\v2\x10.RoutePolicyTaskH\x00R\x0froutePolicyTask\x128\n" +
	"\x0fdns_policy_task\x18\x15 \x01(\v2\x0e.DnsPolicyTaskH\x00R\rdnsPolicyTask\x12Q\n" +
	"\x18dns_tracking_policy_task\x18\x16 \x01(\v2\x16.DnsTrackingPolicyTaskH\x00R\x15dnsTrackingPolicyTaskB\t\n" +
	"\apayload\"R\n" +
	"\fTaskTxStatus\x12\x13\n" +
	"\x05tx_id\x18\x01 \x01(\tR\x04txId\x12\x19\n" +
	"\berr_code\x18\x02 \x01(\x05R\aerrCode\x12\x12\n" +
	"\x04desc\x18\x03 \x01(\tR\x04desc\"\x96\x01\n" +
	"\rClientSyncAck\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x05R\n" +
	"customerId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\x05R\bclientId\x12\x12\n" +
	"\x04uuid\x18\x03 \x01(\tR\x04uuid\x123\n" +
	"\x0etask_tx_status\x18\x04 \x03(\v2\r.TaskTxStatusR\ftaskTxStatus\"\x9b\x01\n" +
	"\n" +
	"LacpConfig\x12)\n" +
	"\bprotocol\x18\x01 \x01(\x0e2\r.LacpProtocolR\bprotocol\x12+\n" +
	"\atimeout\x18\x02 \x01(\x0e2\f.LacpTimeoutH\x00R\atimeout\x88\x01\x01\x12\x1d\n" +
	"\apassive\x18\x03 \x01(\bH\x01R\apassive\x88\x01\x01B\n" +
	"\n" +
	"\b_timeoutB\n" +
	"\n" +
	"\b_passive\"\xaa\x02\n" +
	"\rInterfaceTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\"\n" +
	"\x04mode\x18\x02 \x01(\x0e2\x0e.InterfaceModeR\x04mode\x12\"\n" +
	"\x04zone\x18\x03 \x01(\x0e2\x0e.InterfaceZoneR\x04zone\x12\x1e\n" +
	"\bmix_mode\x18\x04 \x01(\bH\x00R\amixMode\x88\x01\x01\x12\x1e\n" +
	"\bla_group\x18\x05 \x01(\rH\x01R\alaGroup\x88\x01\x01\x12\x17\n" +
	"\x04peer\x18\x06 \x01(\tH\x02R\x04peer\x88\x01\x01\x121\n" +
	"\vlacp_config\x18\a \x01(\v2\v.LacpConfigH\x03R\n" +
	"lacpConfig\x88\x01\x01B\v\n" +
	"\t_mix_modeB\v\n" +
	"\t_la_groupB\a\n" +
	"\x05_peerB\x0e\n" +
	"\f_lacp_config\"z\n" +
	"\x10HeartbeatRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x05R\n" +
	"customerId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\x05R\bclientId\x12(\n" +
	"\x10check_new_config\x18\x03 \x01(\bR\x0echeckNewConfig\"9\n" +
	"\x11HeartbeatResponse\x12$\n" +
	"\x0ehas_new_config\x18\x01 \x01(\bR\fhasNewConfig\"\x1f\n" +
	"\tErrorCode\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\"=\n" +
	"\x06V4Cidr\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\rR\x02ip\x12#\n" +
	"\rprefix_length\x18\x02 \x01(\rR\fprefixLength\"=\n" +
	"\x06V6Cidr\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\fR\x02ip\x12#\n" +
	"\rprefix_length\x18\x02 \x01(\rR\fprefixLength\"\xa4\x01\n" +
	"\tIpAddress\x12\x14\n" +
	"\x04ipv4\x18\x01 \x01(\rH\x00R\x04ipv4\x12\x14\n" +
	"\x04ipv6\x18\x02 \x01(\fH\x00R\x04ipv6\x12\x1d\n" +
	"\tip_string\x18\x03 \x01(\tH\x00R\bipString\x12\"\n" +
	"\av4_cidr\x18\x04 \x01(\v2\a.V4CidrH\x00R\x06v4Cidr\x12\"\n" +
	"\av6_cidr\x18\x05 \x01(\v2\a.V6CidrH\x00R\x06v6CidrB\x04\n" +
	"\x02ip\"\\\n" +
	"\x0fDhcpOptionValue\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x123\n" +
	"\n" +
	"value_type\x18\x02 \x01(\x0e2\x14.DhcpOptionValueTypeR\tvalueType\"\xd2\x01\n" +
	"\x10DhcpOptionConfig\x121\n" +
	"\boption12\x18\x01 \x01(\v2\x10.DhcpOptionValueH\x00R\boption12\x88\x01\x01\x121\n" +
	"\boption61\x18\x02 \x01(\v2\x10.DhcpOptionValueH\x01R\boption61\x88\x01\x01\x121\n" +
	"\boption60\x18\x03 \x01(\v2\x10.DhcpOptionValueH\x02R\boption60\x88\x01\x01B\v\n" +
	"\t_option12B\v\n" +
	"\t_option61B\v\n" +
	"\t_option60\"f\n" +
	"\x05NatIp\x12\x1f\n" +
	"\x02ip\x18\x01 \x01(\v2\n" +
	".IpAddressH\x00R\x02ip\x88\x01\x01\x12(\n" +
	"\bip_range\x18\x02 \x01(\v2\b.IpRangeH\x01R\aipRange\x88\x01\x01B\x05\n" +
	"\x03_ipB\v\n" +
	"\t_ip_range\"\xb0\x01\n" +
	"\x0fHeartbeatConfig\x12(\n" +
	"\aping_ip\x18\x01 \x01(\v2\n" +
	".IpAddressH\x00R\x06pingIp\x88\x01\x01\x12*\n" +
	"\bping_ip2\x18\x02 \x01(\v2\n" +
	".IpAddressH\x01R\apingIp2\x88\x01\x01\x12 \n" +
	"\tmax_delay\x18\x03 \x01(\x05H\x02R\bmaxDelay\x88\x01\x01B\n" +
	"\n" +
	"\b_ping_ipB\v\n" +
	"\t_ping_ip2B\f\n" +
	"\n" +
	"_max_delay\"\xb2\a\n" +
	"\aWanTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06ifname\x18\x02 \x01(\tR\x06ifname\x12\x10\n" +
	"\x03mtu\x18\x03 \x01(\x05R\x03mtu\x126\n" +
	"\tstatic_ip\x18\x04 \x01(\v2\x17.WanTask.StaticIpConfigH\x00R\bstaticIp\x12'\n" +
	"\x04dhcp\x18\x05 \x01(\v2\x11.DhcpOptionConfigH\x00R\x04dhcp\x12,\n" +
	"\x05pppoe\x18\x06 \x01(\v2\x14.WanTask.PppoeConfigH\x00R\x05pppoe\x123\n" +
	"\theartbeat\x18\a \x01(\v2\x10.HeartbeatConfigH\x01R\theartbeat\x88\x01\x01\x122\n" +
	"\x06common\x18\b \x01(\v2\x15.WanTask.CommonConfigH\x02R\x06common\x88\x01\x01\x1a\xd8\x01\n" +
	"\x0eStaticIpConfig\x12&\n" +
	"\x06gw_pxy\x18\x01 \x01(\x0e2\x0f.WanGatewayTypeR\x05gwPxy\x12\x1e\n" +
	"\x04addr\x18\x02 \x01(\v2\n" +
	".IpAddressR\x04addr\x12$\n" +
	"\agateway\x18\x03 \x01(\v2\n" +
	".IpAddressR\agateway\x12!\n" +
	"\x03dns\x18\x04 \x01(\v2\n" +
	".IpAddressH\x00R\x03dns\x88\x01\x01\x12\"\n" +
	"\x06nat_ip\x18\x05 \x01(\v2\x06.NatIpH\x01R\x05natIp\x88\x01\x01B\x06\n" +
	"\x04_dnsB\t\n" +
	"\a_nat_ip\x1a\xcc\x01\n" +
	"\vPppoeConfig\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x1c\n" +
	"\aac_name\x18\x03 \x01(\tH\x00R\x06acName\x88\x01\x01\x12\x1e\n" +
	"\bsvc_name\x18\x04 \x01(\tH\x01R\asvcName\x88\x01\x01\x12 \n" +
	"\twait_time\x18\x05 \x01(\x05H\x02R\bwaitTime\x88\x01\x01B\n" +
	"\n" +
	"\b_ac_nameB\v\n" +
	"\t_svc_nameB\f\n" +
	"\n" +
	"_wait_time\x1a\xa1\x01\n" +
	"\fCommonConfig\x12\x1c\n" +
	"\adns_pxy\x18\x01 \x01(\bH\x00R\x06dnsPxy\x88\x01\x01\x12 \n" +
	"\tclone_mac\x18\x02 \x01(\tH\x01R\bcloneMac\x88\x01\x01\x12&\n" +
	"\fping_disable\x18\x03 \x01(\bH\x02R\vpingDisable\x88\x01\x01B\n" +
	"\n" +
	"\b_dns_pxyB\f\n" +
	"\n" +
	"_clone_macB\x0f\n" +
	"\r_ping_disableB\n" +
	"\n" +
	"\bwan_typeB\f\n" +
	"\n" +
	"_heartbeatB\t\n" +
	"\a_common\"\xb7\x01\n" +
	"\aLanTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06ifname\x18\x02 \x01(\tR\x06ifname\x12\x10\n" +
	"\x03mtu\x18\x03 \x01(\x05R\x03mtu\x12\x1e\n" +
	"\x04addr\x18\x04 \x01(\v2\n" +
	".IpAddressR\x04addr\x12\x1e\n" +
	"\x04mask\x18\x05 \x01(\v2\n" +
	".IpAddressR\x04mask\x12 \n" +
	"\tclone_mac\x18\x06 \x01(\tH\x00R\bcloneMac\x88\x01\x01B\f\n" +
	"\n" +
	"_clone_mac\"S\n" +
	"\aIpRange\x12%\n" +
	"\bstart_ip\x18\x01 \x01(\v2\n" +
	".IpAddressR\astartIp\x12!\n" +
	"\x06end_ip\x18\x02 \x01(\v2\n" +
	".IpAddressR\x05endIp\"\xac\x04\n" +
	"\x0eDhcpServerTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12%\n" +
	"\tdhcp_pool\x18\x02 \x01(\v2\b.IpRangeR\bdhcpPool\x12\x1b\n" +
	"\tlease_ttl\x18\x03 \x01(\x05R\bleaseTtl\x12\x1f\n" +
	"\vdhcp_enable\x18\x04 \x01(\bR\n" +
	"dhcpEnable\x12#\n" +
	"\x04dns0\x18\x05 \x01(\v2\n" +
	".IpAddressH\x00R\x04dns0\x88\x01\x01\x12#\n" +
	"\x04dns1\x18\x06 \x01(\v2\n" +
	".IpAddressH\x01R\x04dns1\x88\x01\x01\x122\n" +
	"\fdhcp_gateway\x18\a \x01(\v2\n" +
	".IpAddressH\x02R\vdhcpGateway\x88\x01\x01\x12,\n" +
	"\tdhcp_mask\x18\b \x01(\v2\n" +
	".IpAddressH\x03R\bdhcpMask\x88\x01\x01\x12$\n" +
	"\vdhcp_domain\x18\t \x01(\tH\x04R\n" +
	"dhcpDomain\x88\x01\x01\x129\n" +
	"\fdhcp_options\x18\n" +
	" \x01(\v2\x11.DhcpOptionConfigH\x05R\vdhcpOptions\x88\x01\x01\x121\n" +
	"\fdhcp_ac_addr\x18\v \x01(\v2\n" +
	".IpAddressH\x06R\n" +
	"dhcpAcAddr\x88\x01\x01B\a\n" +
	"\x05_dns0B\a\n" +
	"\x05_dns1B\x0f\n" +
	"\r_dhcp_gatewayB\f\n" +
	"\n" +
	"_dhcp_maskB\x0e\n" +
	"\f_dhcp_domainB\x0f\n" +
	"\r_dhcp_optionsB\x0f\n" +
	"\r_dhcp_ac_addr\"/\n" +
	"\x0eWanGroupMember\x12\x1d\n" +
	"\n" +
	"proxy_name\x18\x01 \x01(\tR\tproxyName\"\x80\x01\n" +
	"\fWanGroupTask\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12!\n" +
	"\x04type\x18\x03 \x01(\x0e2\r.WanGroupTypeR\x04type\x12)\n" +
	"\amembers\x18\x04 \x03(\v2\x0f.WanGroupMemberR\amembers\"d\n" +
	"\vUserBwLimit\x12\x1c\n" +
	"\arate_in\x18\x01 \x01(\x05H\x00R\x06rateIn\x88\x01\x01\x12\x1e\n" +
	"\brate_out\x18\x02 \x01(\x05H\x01R\arateOut\x88\x01\x01B\n" +
	"\n" +
	"\b_rate_inB\v\n" +
	"\t_rate_out\"\xc5\x03\n" +
	"\rUserGroupTask\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x10\n" +
	"\x03pid\x18\x02 \x01(\x05R\x03pid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12(\n" +
	"\bv4_range\x18\x04 \x01(\v2\b.IpRangeH\x00R\av4Range\x88\x01\x01\x12*\n" +
	"\bv6_range\x18\x05 \x01(\v2\n" +
	".IpAddressH\x01R\av6Range\x88\x01\x01\x12*\n" +
	"\av4_rate\x18\x06 \x01(\v2\f.UserBwLimitH\x02R\x06v4Rate\x88\x01\x01\x12*\n" +
	"\av6_rate\x18\a \x01(\v2\f.UserBwLimitH\x03R\x06v6Rate\x88\x01\x01\x12\x1c\n" +
	"\x03dns\x18\b \x03(\v2\n" +
	".IpAddressR\x03dns\x12+\n" +
	"\x0fmax_online_time\x18\t \x01(\x05H\x04R\rmaxOnlineTime\x88\x01\x01\x122\n" +
	"\bclnt_epa\x18\n" +
	" \x01(\x0e2\x12.UserExpiredPolicyH\x05R\aclntEpa\x88\x01\x01B\v\n" +
	"\t_v4_rangeB\v\n" +
	"\t_v6_rangeB\n" +
	"\n" +
	"\b_v4_rateB\n" +
	"\n" +
	"\b_v6_rateB\x12\n" +
	"\x10_max_online_timeB\v\n" +
	"\t_clnt_epa\"\xc2\x01\n" +
	"\x0fUserRestriction\x12\"\n" +
	"\n" +
	"max_online\x18\x01 \x01(\x05H\x00R\tmaxOnline\x88\x01\x01\x12(\n" +
	"\abind_ip\x18\x02 \x01(\v2\n" +
	".IpAddressH\x01R\x06bindIp\x88\x01\x01\x12\x19\n" +
	"\bbind_mac\x18\x03 \x03(\tR\abindMac\x12\x1e\n" +
	"\bout_vlan\x18\x04 \x01(\x05H\x02R\aoutVlan\x88\x01\x01B\r\n" +
	"\v_max_onlineB\n" +
	"\n" +
	"\b_bind_ipB\v\n" +
	"\t_out_vlan\"\x9f\x01\n" +
	"\fUserIdentity\x12\x19\n" +
	"\x05cname\x18\x01 \x01(\tH\x00R\x05cname\x88\x01\x01\x12\x17\n" +
	"\x04card\x18\x02 \x01(\tH\x01R\x04card\x88\x01\x01\x12\x19\n" +
	"\x05phone\x18\x03 \x01(\tH\x02R\x05phone\x88\x01\x01\x12\x19\n" +
	"\x05other\x18\x04 \x01(\tH\x03R\x05other\x88\x01\x01B\b\n" +
	"\x06_cnameB\a\n" +
	"\x05_cardB\b\n" +
	"\x06_phoneB\b\n" +
	"\x06_other\"B\n" +
	"\x04Date\x12\x12\n" +
	"\x04year\x18\x01 \x01(\x05R\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\x05R\x05month\x12\x10\n" +
	"\x03day\x18\x03 \x01(\x05R\x03day\"\xad\x02\n" +
	"\bUserTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x17\n" +
	"\apool_id\x18\x02 \x01(\x05R\x06poolId\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1b\n" +
	"\x05start\x18\x04 \x01(\v2\x05.DateR\x05start\x12\x1d\n" +
	"\x06expire\x18\x05 \x01(\v2\x05.DateR\x06expire\x12\x16\n" +
	"\x06enable\x18\x06 \x01(\bR\x06enable\x127\n" +
	"\vrestriction\x18\a \x01(\v2\x10.UserRestrictionH\x00R\vrestriction\x88\x01\x01\x12.\n" +
	"\bidentity\x18\b \x01(\v2\r.UserIdentityH\x01R\bidentity\x88\x01\x01B\x0e\n" +
	"\f_restrictionB\v\n" +
	"\t_identity\"\x8f\x03\n" +
	"\rIwanProxyTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06ifname\x18\x02 \x01(\tR\x06ifname\x12\x10\n" +
	"\x03mtu\x18\x03 \x01(\x05R\x03mtu\x12\x19\n" +
	"\bsvr_addr\x18\x04 \x01(\tR\asvrAddr\x12\x19\n" +
	"\bsvr_port\x18\x05 \x01(\x05R\asvrPort\x12\x1a\n" +
	"\busername\x18\x06 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\a \x01(\tR\bpassword\x12\x1d\n" +
	"\aencrypt\x18\b \x01(\bH\x00R\aencrypt\x88\x01\x01\x12\x17\n" +
	"\x04link\x18\t \x01(\x05H\x01R\x04link\x88\x01\x01\x123\n" +
	"\theartbeat\x18\n" +
	" \x01(\v2\x10.HeartbeatConfigH\x02R\theartbeat\x88\x01\x01\x12\x1c\n" +
	"\adns_pxy\x18\v \x01(\bH\x03R\x06dnsPxy\x88\x01\x01\x12\x18\n" +
	"\aifname2\x18\f \x01(\tR\aifname2B\n" +
	"\n" +
	"\b_encryptB\a\n" +
	"\x05_linkB\f\n" +
	"\n" +
	"_heartbeatB\n" +
	"\n" +
	"\b_dns_pxy\"k\n" +
	"\x0fIwanServiceTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\x04addr\x18\x02 \x01(\v2\n" +
	".IpAddressR\x04addr\x12\x10\n" +
	"\x03mtu\x18\x03 \x01(\x05R\x03mtu\x12\x12\n" +
	"\x04pool\x18\x04 \x01(\x05R\x04pool\"c\n" +
	"\x0fIwanMappingTask\x12\x14\n" +
	"\x05proxy\x18\x01 \x01(\tR\x05proxy\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x1b\n" +
	"\x06server\x18\x03 \x01(\tH\x00R\x06server\x88\x01\x01B\t\n" +
	"\a_server\"r\n" +
	"\x0fSrEncryptConfig\x121\n" +
	"\fencrypt_type\x18\x01 \x01(\x0e2\x0e.SrEncryptTypeR\vencryptType\x12\x1f\n" +
	"\bpassword\x18\x02 \x01(\tH\x00R\bpassword\x88\x01\x01B\v\n" +
	"\t_password\"\x1e\n" +
	"\x06SrPath\x12\x14\n" +
	"\x05links\x18\x01 \x03(\x05R\x05links\"\xda\x01\n" +
	"\vSrProxyTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1d\n" +
	"\x05paths\x18\x02 \x03(\v2\a.SrPathR\x05paths\x12\x17\n" +
	"\afrom_in\x18\x03 \x01(\bR\x06fromIn\x12\x10\n" +
	"\x03mtu\x18\x04 \x01(\x05R\x03mtu\x12\x1c\n" +
	"\tkeepalive\x18\x05 \x01(\bR\tkeepalive\x12<\n" +
	"\x0eencrypt_config\x18\x06 \x01(\v2\x10.SrEncryptConfigH\x00R\rencryptConfig\x88\x01\x01B\x11\n" +
	"\x0f_encrypt_config\"\x81\x01\n" +
	"\rIpGroupMember\x12\x1c\n" +
	"\x02ip\x18\x01 \x01(\v2\n" +
	".IpAddressH\x00R\x02ip\x12%\n" +
	"\bip_range\x18\x02 \x01(\v2\b.IpRangeH\x00R\aipRange\x12\x17\n" +
	"\x04info\x18\x03 \x01(\tH\x01R\x04info\x88\x01\x01B\t\n" +
	"\aip_addrB\a\n" +
	"\x05_info\"\x84\x01\n" +
	"\vIpGroupTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12(\n" +
	"\amembers\x18\x02 \x03(\v2\x0e.IpGroupMemberR\amembers\x12&\n" +
	"\ffile_content\x18\x03 \x01(\fH\x00R\vfileContent\x88\x01\x01B\x0f\n" +
	"\r_file_content\"v\n" +
	"\x0fDomainGroupTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06domain\x18\x02 \x03(\tR\x06domain\x12&\n" +
	"\ffile_content\x18\x03 \x01(\fH\x00R\vfileContent\x88\x01\x01B\x0f\n" +
	"\r_file_content\"C\n" +
	"\tDailyTime\x12\x12\n" +
	"\x04hour\x18\x01 \x01(\x05R\x04hour\x12\x10\n" +
	"\x03min\x18\x02 \x01(\x05R\x03min\x12\x10\n" +
	"\x03sec\x18\x03 \x01(\x05R\x03sec\"\x92\x01\n" +
	"\bTimeSpec\x12\x1b\n" +
	"\tstart_day\x18\x01 \x01(\x05R\bstartDay\x12\x17\n" +
	"\aend_day\x18\x02 \x01(\x05R\x06endDay\x12)\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\n" +
	".DailyTimeR\tstartTime\x12%\n" +
	"\bend_time\x18\x04 \x01(\v2\n" +
	".DailyTimeR\aendTime\"a\n" +
	"\x11EffectiveTimeTask\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12(\n" +
	"\n" +
	"time_range\x18\x03 \x01(\v2\t.TimeSpecR\ttimeRange\"y\n" +
	"\x16TrafficChannelPriority\x12\x10\n" +
	"\x03pri\x18\x01 \x01(\x05R\x03pri\x12\x19\n" +
	"\bmax_rate\x18\x02 \x01(\x05R\amaxRate\x12\x10\n" +
	"\x03gbw\x18\x03 \x01(\x05R\x03gbw\x12\x17\n" +
	"\x04desc\x18\x04 \x01(\tH\x00R\x04desc\x88\x01\x01B\a\n" +
	"\x05_desc\"\x9a\x01\n" +
	"\x12TrafficChannelTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04rate\x18\x02 \x01(\x05R\x04rate\x12\x19\n" +
	"\x05quota\x18\x03 \x01(\x05H\x00R\x05quota\x88\x01\x01\x127\n" +
	"\n" +
	"priorities\x18\x04 \x03(\v2\x17.TrafficChannelPriorityR\n" +
	"prioritiesB\b\n" +
	"\x06_quota\"@\n" +
	"\x0fTrafficStatTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x19\n" +
	"\btrack_ip\x18\x02 \x01(\bR\atrackIp\"\x9e\x02\n" +
	"\x0fAddressSelector\x12\x1c\n" +
	"\x02ip\x18\x01 \x01(\v2\n" +
	".IpAddressH\x00R\x02ip\x12%\n" +
	"\bip_range\x18\x02 \x01(\v2\b.IpRangeH\x00R\aipRange\x12$\n" +
	"\rip_group_name\x18\x03 \x01(\tH\x00R\vipGroupName\x12\"\n" +
	"\fmac_group_id\x18\x04 \x01(\x05H\x00R\n" +
	"macGroupId\x12$\n" +
	"\ruser_group_id\x18\x05 \x01(\x05H\x00R\vuserGroupId\x12\x1c\n" +
	"\busername\x18\x06 \x01(\tH\x00R\busername\x12,\n" +
	"\x11domain_group_name\x18\a \x01(\tH\x00R\x0fdomainGroupNameB\n" +
	"\n" +
	"\bselector\"2\n" +
	"\bIntRange\x12\x14\n" +
	"\x05start\x18\x01 \x01(\rR\x05start\x12\x10\n" +
	"\x03end\x18\x02 \x01(\rR\x03end\"3\n" +
	"\tPortRange\x12\x14\n" +
	"\x05start\x18\x01 \x01(\rR\x05start\x12\x10\n" +
	"\x03end\x18\x02 \x01(\rR\x03end\",\n" +
	"\bPortSpec\x12 \n" +
	"\x05ports\x18\x01 \x03(\v2\n" +
	".PortRangeR\x05ports\"O\n" +
	"\x0fAppProtocolSpec\x12\x19\n" +
	"\bapp_name\x18\x01 \x01(\tR\aappName\x12!\n" +
	"\fapp_protocol\x18\x02 \x01(\tR\vappProtocol\"3\n" +
	"\tVlanRange\x12\x14\n" +
	"\x05start\x18\x01 \x01(\rR\x05start\x12\x10\n" +
	"\x03end\x18\x02 \x01(\rR\x03end\"\xe0\x01\n" +
	"\rInterfaceSpec\x12\x1b\n" +
	"\x06bridge\x18\x01 \x01(\tH\x00R\x06bridge\x88\x01\x01\x12%\n" +
	"\x03dir\x18\x02 \x01(\x0e2\x0e.FlowDirectionH\x01R\x03dir\x88\x01\x01\x12\x1b\n" +
	"\x06ifname\x18\x03 \x01(\tH\x02R\x06ifname\x88\x01\x01\x12\x18\n" +
	"\x05in_if\x18\x04 \x01(\tH\x03R\x04inIf\x88\x01\x01\x12#\n" +
	"\x04vlan\x18\x05 \x01(\v2\n" +
	".VlanRangeH\x04R\x04vlan\x88\x01\x01B\t\n" +
	"\a_bridgeB\x06\n" +
	"\x04_dirB\t\n" +
	"\a_ifnameB\b\n" +
	"\x06_in_ifB\a\n" +
	"\x05_vlan\"\xb6\x01\n" +
	"\x1aFlowControlPolicyGroupTask\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12(\n" +
	"\n" +
	"time_range\x18\x02 \x01(\v2\t.TimeSpecR\ttimeRange\x12\x18\n" +
	"\adisable\x18\x03 \x01(\bR\adisable\x12\x12\n" +
	"\x04stop\x18\x04 \x01(\bR\x04stop\x12\x1f\n" +
	"\bprevious\x18\x05 \x01(\tH\x00R\bprevious\x88\x01\x01B\v\n" +
	"\t_previous\"\xa3\x01\n" +
	"\x13ActionChannelConfig\x12\x12\n" +
	"\x04next\x18\x01 \x01(\bR\x04next\x12\x18\n" +
	"\achannel\x18\x02 \x01(\tR\achannel\x12\x10\n" +
	"\x03pri\x18\x03 \x01(\x05R\x03pri\x12\x1c\n" +
	"\aip_rate\x18\x04 \x01(\x05H\x00R\x06ipRate\x88\x01\x01\x12\x18\n" +
	"\x05so_id\x18\x05 \x01(\tH\x01R\x04soId\x88\x01\x01B\n" +
	"\n" +
	"\b_ip_rateB\b\n" +
	"\x06_so_id\"\x95\x01\n" +
	"\x12ActionAcceptConfig\x12\x12\n" +
	"\x04next\x18\x01 \x01(\bR\x04next\x12\x1c\n" +
	"\aip_rate\x18\x03 \x01(\x05H\x00R\x06ipRate\x88\x01\x01\x12\x15\n" +
	"\x03tos\x18\x04 \x01(\x05H\x01R\x03tos\x88\x01\x01\x12\x18\n" +
	"\x05so_id\x18\x05 \x01(\tH\x02R\x04soId\x88\x01\x01B\n" +
	"\n" +
	"\b_ip_rateB\x06\n" +
	"\x04_tosB\b\n" +
	"\x06_so_id\"\x91\x05\n" +
	"\x15FlowControlPolicyTask\x12\x16\n" +
	"\x06cookie\x18\x01 \x01(\rR\x06cookie\x12\x12\n" +
	"\x04desc\x18\x02 \x01(\tR\x04desc\x12\x1d\n" +
	"\n" +
	"group_name\x18\x03 \x01(\tR\tgroupName\x12\x18\n" +
	"\adisable\x18\x04 \x01(\bR\adisable\x12\x1f\n" +
	"\bprevious\x18\x05 \x01(\rH\x01R\bprevious\x88\x01\x01\x12%\n" +
	"\x05in_ip\x18\x06 \x03(\v2\x10.AddressSelectorR\x04inIp\x12'\n" +
	"\ain_port\x18\a \x01(\v2\t.PortSpecH\x02R\x06inPort\x88\x01\x01\x12'\n" +
	"\x06out_ip\x18\b \x03(\v2\x10.AddressSelectorR\x05outIp\x12)\n" +
	"\bout_port\x18\t \x01(\v2\t.PortSpecH\x03R\aoutPort\x88\x01\x01\x12'\n" +
	"\x03app\x18\n" +
	" \x01(\v2\x10.AppProtocolSpecH\x04R\x03app\x88\x01\x01\x121\n" +
	"\tinterface\x18\v \x01(\v2\x0e.InterfaceSpecH\x05R\tinterface\x88\x01\x01\x12*\n" +
	"\x06action\x18\f \x01(\x0e2\x12.FlowControlActionR\x06action\x12:\n" +
	"\raction_accept\x18\r \x01(\v2\x13.ActionAcceptConfigH\x00R\factionAccept\x12=\n" +
	"\x0eaction_channel\x18\x0e \x01(\v2\x14.ActionChannelConfigH\x00R\ractionChannelB\x0f\n" +
	"\raction_configB\v\n" +
	"\t_previousB\n" +
	"\n" +
	"\b_in_portB\v\n" +
	"\t_out_portB\x06\n" +
	"\x04_appB\f\n" +
	"\n" +
	"_interface\"\x94\x01\n" +
	"\x0fFlowControlTask\x12@\n" +
	"\fpolicy_group\x18\x01 \x01(\v2\x1b.FlowControlPolicyGroupTaskH\x00R\vpolicyGroup\x120\n" +
	"\x06policy\x18\x02 \x01(\v2\x16.FlowControlPolicyTaskH\x00R\x06policyB\r\n" +
	"\vtask_config\"N\n" +
	"\tNatIpPool\x12\x1a\n" +
	"\x02ip\x18\x01 \x03(\v2\n" +
	".IpAddressR\x02ip\x12%\n" +
	"\tip_ranges\x18\x02 \x03(\v2\b.IpRangeR\bipRanges\"b\n" +
	"\x11RouteActionConfig\x12\x14\n" +
	"\x05proxy\x18\x01 \x01(\tR\x05proxy\x12*\n" +
	"\bnext_hop\x18\x02 \x01(\v2\n" +
	".IpAddressH\x00R\anextHop\x88\x01\x01B\v\n" +
	"\t_next_hop\"\xb6\x02\n" +
	"\x0fNatActionConfig\x12\x14\n" +
	"\x05proxy\x18\x01 \x01(\tR\x05proxy\x12-\n" +
	"\n" +
	"new_dst_ip\x18\x02 \x01(\v2\n" +
	".IpAddressH\x00R\bnewDstIp\x88\x01\x01\x12&\n" +
	"\x06nat_ip\x18\x03 \x01(\v2\n" +
	".NatIpPoolH\x01R\x05natIp\x88\x01\x01\x12*\n" +
	"\bnext_hop\x18\x04 \x01(\v2\n" +
	".IpAddressH\x02R\anextHop\x88\x01\x01\x12'\n" +
	"\rfull_cone_nat\x18\x05 \x01(\bH\x03R\vfullConeNat\x88\x01\x01\x12\x1c\n" +
	"\ano_snat\x18\x06 \x01(\bH\x04R\x06noSnat\x88\x01\x01B\r\n" +
	"\v_new_dst_ipB\t\n" +
	"\a_nat_ipB\v\n" +
	"\t_next_hopB\x10\n" +
	"\x0e_full_cone_natB\n" +
	"\n" +
	"\b_no_snat\"\xee\a\n" +
	"\x0fRoutePolicyTask\x12\x16\n" +
	"\x06cookie\x18\x01 \x01(\rR\x06cookie\x12\x12\n" +
	"\x04desc\x18\x02 \x01(\tR\x04desc\x12\x1f\n" +
	"\bprevious\x18\x03 \x01(\rH\x00R\bprevious\x88\x01\x01\x12\x18\n" +
	"\adisable\x18\x04 \x01(\bR\adisable\x12\x1e\n" +
	"\bsch_time\x18\x05 \x01(\x05H\x01R\aschTime\x88\x01\x01\x12)\n" +
	"\x04zone\x18\x06 \x01(\x0e2\x10.RoutePolicyZoneH\x02R\x04zone\x88\x01\x01\x12\"\n" +
	"\x03src\x18\a \x03(\v2\x10.AddressSelectorR\x03src\x12)\n" +
	"\bsrc_port\x18\b \x01(\v2\t.PortSpecH\x03R\asrcPort\x88\x01\x01\x12)\n" +
	"\busr_type\x18\t \x01(\x0e2\t.UserTypeH\x04R\ausrType\x88\x01\x01\x12\x17\n" +
	"\x04pool\x18\n" +
	" \x01(\x05H\x05R\x04pool\x88\x01\x01\x12\"\n" +
	"\x03dst\x18\v \x03(\v2\x10.AddressSelectorR\x03dst\x12)\n" +
	"\bdst_port\x18\f \x01(\v2\t.PortSpecH\x06R\adstPort\x88\x01\x01\x12+\n" +
	"\x05proto\x18\r \x01(\v2\x10.AppProtocolSpecH\aR\x05proto\x88\x01\x01\x12\x18\n" +
	"\x05in_if\x18\x0f \x01(\tH\bR\x04inIf\x88\x01\x01\x12\x1a\n" +
	"\x06wan_bw\x18\x10 \x01(\x05H\tR\x05wanBw\x88\x01\x01\x12!\n" +
	"\n" +
	"wan_bw_out\x18\x11 \x01(\x05H\n" +
	"R\bwanBwOut\x88\x01\x01\x12\"\n" +
	"\x04vlan\x18\x12 \x01(\v2\t.IntRangeH\vR\x04vlan\x88\x01\x01\x12 \n" +
	"\x03ttl\x18\x13 \x01(\v2\t.IntRangeH\fR\x03ttl\x88\x01\x01\x12\"\n" +
	"\x04dscp\x18\x14 \x01(\v2\t.IntRangeH\rR\x04dscp\x88\x01\x01\x12*\n" +
	"\x06action\x18\x15 \x01(\x0e2\x12.RoutePolicyActionR\x06action\x12:\n" +
	"\froute_config\x18\x16 \x01(\v2\x12.RouteActionConfigH\x0eR\vrouteConfig\x88\x01\x01\x124\n" +
	"\n" +
	"nat_config\x18\x17 \x01(\v2\x10.NatActionConfigH\x0fR\tnatConfig\x88\x01\x01B\v\n" +
	"\t_previousB\v\n" +
	"\t_sch_timeB\a\n" +
	"\x05_zoneB\v\n" +
	"\t_src_portB\v\n" +
	"\t_usr_typeB\a\n" +
	"\x05_poolB\v\n" +
	"\t_dst_portB\b\n" +
	"\x06_protoB\b\n" +
	"\x06_in_ifB\t\n" +
	"\a_wan_bwB\r\n" +
	"\v_wan_bw_outB\a\n" +
	"\x05_vlanB\x06\n" +
	"\x04_ttlB\a\n" +
	"\x05_dscpB\x0f\n" +
	"\r_route_configB\r\n" +
	"\v_nat_config\"V\n" +
	"\x19DnsPolicyActionPassConfig\x12\x1a\n" +
	"\x06ip_qps\x18\x01 \x01(\rH\x00R\x05ipQps\x88\x01\x01\x12\x12\n" +
	"\x04next\x18\x02 \x01(\bR\x04nextB\t\n" +
	"\a_ip_qps\"\x84\x01\n" +
	"\x18DnsPolicyActionRdrConfig\x12\x17\n" +
	"\aact_arg\x18\x01 \x01(\tR\x06actArg\x12\x1c\n" +
	"\ano_snat\x18\x02 \x01(\bH\x00R\x06noSnat\x88\x01\x01\x12%\n" +
	"\bdns_list\x18\x03 \x03(\v2\n" +
	".IpAddressR\adnsListB\n" +
	"\n" +
	"\b_no_snat\"A\n" +
	"\x1aDnsPolicyActionReplyConfig\x12#\n" +
	"\aact_arg\x18\x01 \x03(\v2\n" +
	".IpAddressR\x06actArg\"\x81\x01\n" +
	"\x1aDnsPolicyActionLimitConfig\x12\x1a\n" +
	"\x06ip_qps\x18\x01 \x01(\rH\x00R\x05ipQps\x88\x01\x01\x12\x1c\n" +
	"\aact_arg\x18\x02 \x01(\rH\x01R\x06actArg\x88\x01\x01\x12\x12\n" +
	"\x04next\x18\x03 \x01(\bR\x04nextB\t\n" +
	"\a_ip_qpsB\n" +
	"\n" +
	"\b_act_arg\"\x83\a\n" +
	"\rDnsPolicyTask\x12\x16\n" +
	"\x06cookie\x18\x01 \x01(\rR\x06cookie\x12\x1f\n" +
	"\bprevious\x18\x02 \x01(\rH\x01R\bprevious\x88\x01\x01\x12\x18\n" +
	"\adisable\x18\x03 \x01(\bR\adisable\x12\x1e\n" +
	"\bsch_time\x18\x04 \x01(\x05H\x02R\aschTime\x88\x01\x01\x12%\n" +
	"\x05in_ip\x18\x05 \x03(\v2\x10.AddressSelectorR\x04inIp\x12\x17\n" +
	"\x04pool\x18\x06 \x01(\x05H\x03R\x04pool\x88\x01\x01\x12)\n" +
	"\busr_type\x18\a \x01(\x0e2\t.UserTypeH\x04R\ausrType\x88\x01\x01\x12'\n" +
	"\x06out_ip\x18\b \x03(\v2\x10.AddressSelectorR\x05outIp\x12'\n" +
	"\x03app\x18\t \x01(\v2\x10.AppProtocolSpecH\x05R\x03app\x88\x01\x01\x12!\n" +
	"\fdomain_group\x18\n" +
	" \x03(\tR\vdomainGroup\x12)\n" +
	"\x06a_type\x18\v \x01(\x0e2\r.DnsQueryTypeH\x06R\x05aType\x88\x01\x01\x12\x18\n" +
	"\x05in_if\x18\f \x01(\tH\aR\x04inIf\x88\x01\x01\x12\x1b\n" +
	"\x06bridge\x18\r \x01(\x05H\bR\x06bridge\x88\x01\x01\x12\"\n" +
	"\x04vlan\x18\x0e \x01(\v2\t.IntRangeH\tR\x04vlan\x88\x01\x01\x12(\n" +
	"\x06action\x18\x0f \x01(\x0e2\x10.DnsPolicyActionR\x06action\x12=\n" +
	"\vaction_pass\x18\x10 \x01(\v2\x1a.DnsPolicyActionPassConfigH\x00R\n" +
	"actionPass\x12:\n" +
	"\n" +
	"action_rdr\x18\x11 \x01(\v2\x19.DnsPolicyActionRdrConfigH\x00R\tactionRdr\x12@\n" +
	"\faction_reply\x18\x12 \x01(\v2\x1b.DnsPolicyActionReplyConfigH\x00R\vactionReply\x12@\n" +
	"\faction_limit\x18\x13 \x01(\v2\x1b.DnsPolicyActionLimitConfigH\x00R\vactionLimitB\x0f\n" +
	"\raction_configB\v\n" +
	"\t_previousB\v\n" +
	"\t_sch_timeB\a\n" +
	"\x05_poolB\v\n" +
	"\t_usr_typeB\x06\n" +
	"\x04_appB\t\n" +
	"\a_a_typeB\b\n" +
	"\x06_in_ifB\t\n" +
	"\a_bridgeB\a\n" +
	"\x05_vlan\"\x89\x03\n" +
	"\x15DnsTrackingPolicyTask\x12\x16\n" +
	"\x06cookie\x18\x01 \x01(\rR\x06cookie\x12\x1f\n" +
	"\bprevious\x18\x02 \x01(\rH\x00R\bprevious\x88\x01\x01\x12\x18\n" +
	"\adisable\x18\x03 \x01(\bR\adisable\x12!\n" +
	"\fdomain_group\x18\x04 \x03(\tR\vdomainGroup\x12\x10\n" +
	"\x03pxy\x18\x05 \x01(\tR\x03pxy\x12\x1d\n" +
	"\n" +
	"backup_pxy\x18\x06 \x01(\tR\tbackupPxy\x12*\n" +
	"\bdns_addr\x18\a \x01(\v2\n" +
	".IpAddressH\x01R\adnsAddr\x88\x01\x01\x12\"\n" +
	"\n" +
	"track_host\x18\b \x01(\bH\x02R\ttrackHost\x88\x01\x01\x12 \n" +
	"\tcache_ttl\x18\t \x01(\rH\x03R\bcacheTtl\x88\x01\x01\x12\x17\n" +
	"\x04desc\x18\n" +
	" \x01(\tH\x04R\x04desc\x88\x01\x01B\v\n" +
	"\t_previousB\v\n" +
	"\t_dns_addrB\r\n" +
	"\v_track_hostB\f\n" +
	"\n" +
	"_cache_ttlB\a\n" +
	"\x05_desc\"h\n" +
	"\x0fProxyStatusInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\x04type\x18\x02 \x01(\x0e2\n" +
	".ProxyTypeR\x04type\x12!\n" +
	"\x05state\x18\x03 \x01(\x0e2\v.ProxyStateR\x05state\"\x7f\n" +
	"\x11ProxyStatusReport\x125\n" +
	"\ftrigger_type\x18\x01 \x01(\x0e2\x12.ReportTriggerTypeR\vtriggerType\x123\n" +
	"\fproxy_status\x18\x02 \x03(\v2\x10.ProxyStatusInfoR\vproxyStatus\"\x8d\x01\n" +
	"\fDeviceReport\x12,\n" +
	"\vreport_type\x18\x01 \x01(\x0e2\v.ReportTypeR\n" +
	"reportType\x12D\n" +
	"\x13proxy_status_report\x18\x02 \x01(\v2\x12.ProxyStatusReportH\x00R\x11proxyStatusReportB\t\n" +
	"\apayload\"\xc8\x01\n" +
	"\x13DeviceReportRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x05R\n" +
	"customerId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\x05R\bclientId\x12\x12\n" +
	"\x04uuid\x18\x03 \x01(\tR\x04uuid\x124\n" +
	"\x0edevice_reports\x18\x04 \x03(\v2\r.DeviceReportR\rdeviceReports\x12)\n" +
	"\x10report_timestamp\x18\x05 \x01(\x03R\x0freportTimestamp\"E\n" +
	"\x14DeviceReportResponse\x12\x12\n" +
	"\x04uuid\x18\x01 \x01(\tR\x04uuid\x12\x19\n" +
	"\berr_code\x18\x02 \x01(\x05R\aerrCode*\xac\x03\n" +
	"\bTaskType\x12\x12\n" +
	"\x0eTASK_INTERFACE\x10\x00\x12\f\n" +
	"\bTASK_WAN\x10\x01\x12\f\n" +
	"\bTASK_LAN\x10\x02\x12\r\n" +
	"\tTASK_DHCP\x10\x03\x12\x12\n" +
	"\x0eTASK_WAN_GROUP\x10\x04\x12\x13\n" +
	"\x0fTASK_USER_GROUP\x10\x05\x12\r\n" +
	"\tTASK_USER\x10\x06\x12\x13\n" +
	"\x0fTASK_IWAN_PROXY\x10\a\x12\x15\n" +
	"\x11TASK_IWAN_SERVICE\x10\b\x12\x15\n" +
	"\x11TASK_IWAN_MAPPING\x10\t\x12\x11\n" +
	"\rTASK_SR_PROXY\x10\n" +
	"\x12\x11\n" +
	"\rTASK_IP_GROUP\x10\v\x12\x15\n" +
	"\x11TASK_DOMAIN_GROUP\x10\f\x12\x17\n" +
	"\x13TASK_EFFECTIVE_TIME\x10\r\x12\x18\n" +
	"\x14TASK_TRAFFIC_CHANNEL\x10\x0e\x12\x15\n" +
	"\x11TASK_TRAFFIC_STAT\x10\x0f\x12\x15\n" +
	"\x11TASK_FLOW_CONTROL\x10\x10\x12\x15\n" +
	"\x11TASK_ROUTE_POLICY\x10\x11\x12\x13\n" +
	"\x0fTASK_DNS_POLICY\x10\x12\x12\x1c\n" +
	"\x18TASK_DNS_TRACKING_POLICY\x10\x13*@\n" +
	"\n" +
	"TaskAction\x12\x0e\n" +
	"\n" +
	"NEW_CONFIG\x10\x00\x12\x0f\n" +
	"\vEDIT_CONFIG\x10\x01\x12\x11\n" +
	"\rDELETE_CONFIG\x10\x02*/\n" +
	"\bSyncType\x12\x14\n" +
	"\x10INCREMENTAL_SYNC\x10\x00\x12\r\n" +
	"\tFULL_SYNC\x10\x01*\xd3\x01\n" +
	"\rInterfaceMode\x12\x1a\n" +
	"\x16INTERFACE_MODE_MONITOR\x10\x00\x12\x1a\n" +
	"\x16INTERFACE_MODE_BRIDGE1\x10\x01\x12\x1a\n" +
	"\x16INTERFACE_MODE_BRIDGE2\x10\x02\x12\x1a\n" +
	"\x16INTERFACE_MODE_BRIDGE3\x10\x03\x12\x1a\n" +
	"\x16INTERFACE_MODE_BRIDGE4\x10\x04\x12\x1a\n" +
	"\x16INTERFACE_MODE_BRIDGE5\x10\x05\x12\x1a\n" +
	"\x16INTERFACE_MODE_BRIDGE6\x10\x06*F\n" +
	"\rInterfaceZone\x12\x19\n" +
	"\x15INTERFACE_ZONE_INSIDE\x10\x00\x12\x1a\n" +
	"\x16INTERFACE_ZONE_OUTSIDE\x10\x01*@\n" +
	"\fLacpProtocol\x12\x18\n" +
	"\x14LACP_PROTOCOL_STATIC\x10\x00\x12\x16\n" +
	"\x12LACP_PROTOCOL_LACP\x10\x01*;\n" +
	"\vLacpTimeout\x12\x15\n" +
	"\x11LACP_TIMEOUT_SLOW\x10\x00\x12\x15\n" +
	"\x11LACP_TIMEOUT_FAST\x10\x01*L\n" +
	"\x0eWanGatewayType\x12\x1b\n" +
	"\x17WAN_GATEWAY_TYPE_NORMAL\x10\x00\x12\x1d\n" +
	"\x19WAN_GATEWAY_TYPE_INTERNET\x10\x01*L\n" +
	"\x13DhcpOptionValueType\x12\x1b\n" +
	"\x17DHCP_OPTION_TYPE_STRING\x10\x00\x12\x18\n" +
	"\x14DHCP_OPTION_TYPE_HEX\x10\x01*\xa1\x02\n" +
	"\fWanGroupType\x12\x19\n" +
	"\x15WAN_GROUP_TYPE_SRCDST\x10\x00\x12\x17\n" +
	"\x13WAN_GROUP_TYPE_SPDP\x10\x01\x12\x16\n" +
	"\x12WAN_GROUP_TYPE_SRC\x10\x02\x12\x1b\n" +
	"\x17WAN_GROUP_TYPE_SRCSPORT\x10\x03\x12\x16\n" +
	"\x12WAN_GROUP_TYPE_DST\x10\x04\x12\x1b\n" +
	"\x17WAN_GROUP_TYPE_DSTDPORT\x10\x05\x12\x1c\n" +
	"\x18WAN_GROUP_TYPE_RX_LEFTBW\x10\x06\x12\x1c\n" +
	"\x18WAN_GROUP_TYPE_TX_LEFTBW\x10\a\x12\x1a\n" +
	"\x16WAN_GROUP_TYPE_SESSION\x10\b\x12\x1b\n" +
	"\x17WAN_GROUP_TYPE_FAILOVER\x10\t*p\n" +
	"\x11UserExpiredPolicy\x12\x1e\n" +
	"\x1aUSER_EXPIRED_POLICY_REJECT\x10\x00\x12\x1d\n" +
	"\x19USER_EXPIRED_POLICY_LOGIN\x10\x01\x12\x1c\n" +
	"\x18USER_EXPIRED_POLICY_PASS\x10\x02*R\n" +
	"\rSrEncryptType\x12\x13\n" +
	"\x0fSR_ENCRYPT_NONE\x10\x00\x12\x15\n" +
	"\x11SR_ENCRYPT_AES128\x10\x01\x12\x15\n" +
	"\x11SR_ENCRYPT_AES256\x10\x02*r\n" +
	"\x11FlowControlAction\x12\x1e\n" +
	"\x1aFLOW_CONTROL_ACTION_PERMIT\x10\x00\x12\x1c\n" +
	"\x18FLOW_CONTROL_ACTION_DENY\x10\x01\x12\x1f\n" +
	"\x1bFLOW_CONTROL_ACTION_CHANNEL\x10\x02*W\n" +
	"\rFlowDirection\x12\x17\n" +
	"\x13FLOW_DIRECTION_BOTH\x10\x00\x12\x15\n" +
	"\x11FLOW_DIRECTION_IN\x10\x01\x12\x16\n" +
	"\x12FLOW_DIRECTION_OUT\x10\x02*p\n" +
	"\x11RoutePolicyAction\x12\x16\n" +
	"\x12ROUTE_ACTION_ROUTE\x10\x00\x12\x14\n" +
	"\x10ROUTE_ACTION_NAT\x10\x01\x12\x15\n" +
	"\x11ROUTE_ACTION_DNAT\x10\x02\x12\x16\n" +
	"\x12ROUTE_ACTION_PROXY\x10\x03*[\n" +
	"\x0fRoutePolicyZone\x12\x10\n" +
	"\fCTRL_TIER_T1\x10\x00\x12\x10\n" +
	"\fCUST_TIER_T2\x10\x01\x12\x0f\n" +
	"\vLPM_TIER_T3\x10\x02\x12\x13\n" +
	"\x0fDEF_WAN_TIER_T4\x10\x03*J\n" +
	"\bUserType\x12\x11\n" +
	"\rUSER_TYPE_ANY\x10\x00\x12\x13\n" +
	"\x0fUSER_TYPE_IPPXY\x10\x01\x12\x16\n" +
	"\x12USER_TYPE_NONIPPXY\x10\x02*X\n" +
	"\fDnsQueryType\x12\x16\n" +
	"\x12DNS_QUERY_TYPE_ANY\x10\x00\x12\x17\n" +
	"\x13DNS_QUERY_TYPE_IPV4\x10\x01\x12\x17\n" +
	"\x13DNS_QUERY_TYPE_IPV6\x10\x02*\xab\x01\n" +
	"\x0fDnsPolicyAction\x12\x13\n" +
	"\x0fDNS_ACTION_PASS\x10\x00\x12\x13\n" +
	"\x0fDNS_ACTION_DENY\x10\x01\x12\x12\n" +
	"\x0eDNS_ACTION_RDR\x10\x02\x12\x14\n" +
	"\x10DNS_ACTION_REPLY\x10\x03\x12\x14\n" +
	"\x10DNS_ACTION_LIMIT\x10\x04\x12\x14\n" +
	"\x10DNS_ACTION_IPPXY\x10\x05\x12\x18\n" +
	"\x14DNS_ACTION_ZEROREPLY\x10\x06**\n" +
	"\n" +
	"ReportType\x12\x1c\n" +
	"\x18REPORT_TYPE_PROXY_STATUS\x10\x00*J\n" +
	"\x11ReportTriggerType\x12\x18\n" +
	"\x14REPORT_TRIGGER_EVENT\x10\x00\x12\x1b\n" +
	"\x17REPORT_TRIGGER_PERIODIC\x10\x01*K\n" +
	"\tProxyType\x12\x13\n" +
	"\x0fPROXY_TYPE_NONE\x10\x00\x12\x13\n" +
	"\x0fPROXY_TYPE_IWAN\x10\x01\x12\x14\n" +
	"\x10PROXY_TYPE_SRPXY\x10\x02*6\n" +
	"\n" +
	"ProxyState\x12\x14\n" +
	"\x10PROXY_STATE_DOWN\x10\x00\x12\x12\n" +
	"\x0ePROXY_STATE_UP\x10\x012\x8a\x01\n" +
	"\x11ConfigSyncService\x12B\n" +
	"\x17HandleConfigSyncRequest\x12\x12.ClientSyncRequest\x1a\x13.ServerSyncResponse\x121\n" +
	"\x13HandleClientSyncAck\x12\x0e.ClientSyncAck\x1a\n" +
	".ErrorCode2J\n" +
	"\x10HeartbeatService\x126\n" +
	"\rSendHeartbeat\x12\x11.HeartbeatRequest\x1a\x12.HeartbeatResponse2V\n" +
	"\x13DeviceReportService\x12?\n" +
	"\x10SendDeviceReport\x12\x14.DeviceReportRequest\x1a\x15.DeviceReportResponseB*\n" +
	"\x15com.unisase.rpc.protoP\x01Z\x0fgrpc/proto;grpcb\x06proto3"

var (
	file_message_proto_rawDescOnce sync.Once
	file_message_proto_rawDescData []byte
)

func file_message_proto_rawDescGZIP() []byte {
	file_message_proto_rawDescOnce.Do(func() {
		file_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_message_proto_rawDesc), len(file_message_proto_rawDesc)))
	})
	return file_message_proto_rawDescData
}

var file_message_proto_enumTypes = make([]protoimpl.EnumInfo, 23)
var file_message_proto_msgTypes = make([]protoimpl.MessageInfo, 75)
var file_message_proto_goTypes = []any{
	(TaskType)(0),                      // 0: TaskType
	(TaskAction)(0),                    // 1: TaskAction
	(SyncType)(0),                      // 2: SyncType
	(InterfaceMode)(0),                 // 3: InterfaceMode
	(InterfaceZone)(0),                 // 4: InterfaceZone
	(LacpProtocol)(0),                  // 5: LacpProtocol
	(LacpTimeout)(0),                   // 6: LacpTimeout
	(WanGatewayType)(0),                // 7: WanGatewayType
	(DhcpOptionValueType)(0),           // 8: DhcpOptionValueType
	(WanGroupType)(0),                  // 9: WanGroupType
	(UserExpiredPolicy)(0),             // 10: UserExpiredPolicy
	(SrEncryptType)(0),                 // 11: SrEncryptType
	(FlowControlAction)(0),             // 12: FlowControlAction
	(FlowDirection)(0),                 // 13: FlowDirection
	(RoutePolicyAction)(0),             // 14: RoutePolicyAction
	(RoutePolicyZone)(0),               // 15: RoutePolicyZone
	(UserType)(0),                      // 16: UserType
	(DnsQueryType)(0),                  // 17: DnsQueryType
	(DnsPolicyAction)(0),               // 18: DnsPolicyAction
	(ReportType)(0),                    // 19: ReportType
	(ReportTriggerType)(0),             // 20: ReportTriggerType
	(ProxyType)(0),                     // 21: ProxyType
	(ProxyState)(0),                    // 22: ProxyState
	(*ClientSyncRequest)(nil),          // 23: ClientSyncRequest
	(*ServerSyncResponse)(nil),         // 24: ServerSyncResponse
	(*TaskTx)(nil),                     // 25: TaskTx
	(*DeviceTask)(nil),                 // 26: DeviceTask
	(*TaskTxStatus)(nil),               // 27: TaskTxStatus
	(*ClientSyncAck)(nil),              // 28: ClientSyncAck
	(*LacpConfig)(nil),                 // 29: LacpConfig
	(*InterfaceTask)(nil),              // 30: InterfaceTask
	(*HeartbeatRequest)(nil),           // 31: HeartbeatRequest
	(*HeartbeatResponse)(nil),          // 32: HeartbeatResponse
	(*ErrorCode)(nil),                  // 33: ErrorCode
	(*V4Cidr)(nil),                     // 34: V4Cidr
	(*V6Cidr)(nil),                     // 35: V6Cidr
	(*IpAddress)(nil),                  // 36: IpAddress
	(*DhcpOptionValue)(nil),            // 37: DhcpOptionValue
	(*DhcpOptionConfig)(nil),           // 38: DhcpOptionConfig
	(*NatIp)(nil),                      // 39: NatIp
	(*HeartbeatConfig)(nil),            // 40: HeartbeatConfig
	(*WanTask)(nil),                    // 41: WanTask
	(*LanTask)(nil),                    // 42: LanTask
	(*IpRange)(nil),                    // 43: IpRange
	(*DhcpServerTask)(nil),             // 44: DhcpServerTask
	(*WanGroupMember)(nil),             // 45: WanGroupMember
	(*WanGroupTask)(nil),               // 46: WanGroupTask
	(*UserBwLimit)(nil),                // 47: UserBwLimit
	(*UserGroupTask)(nil),              // 48: UserGroupTask
	(*UserRestriction)(nil),            // 49: UserRestriction
	(*UserIdentity)(nil),               // 50: UserIdentity
	(*Date)(nil),                       // 51: Date
	(*UserTask)(nil),                   // 52: UserTask
	(*IwanProxyTask)(nil),              // 53: IwanProxyTask
	(*IwanServiceTask)(nil),            // 54: IwanServiceTask
	(*IwanMappingTask)(nil),            // 55: IwanMappingTask
	(*SrEncryptConfig)(nil),            // 56: SrEncryptConfig
	(*SrPath)(nil),                     // 57: SrPath
	(*SrProxyTask)(nil),                // 58: SrProxyTask
	(*IpGroupMember)(nil),              // 59: IpGroupMember
	(*IpGroupTask)(nil),                // 60: IpGroupTask
	(*DomainGroupTask)(nil),            // 61: DomainGroupTask
	(*DailyTime)(nil),                  // 62: DailyTime
	(*TimeSpec)(nil),                   // 63: TimeSpec
	(*EffectiveTimeTask)(nil),          // 64: EffectiveTimeTask
	(*TrafficChannelPriority)(nil),     // 65: TrafficChannelPriority
	(*TrafficChannelTask)(nil),         // 66: TrafficChannelTask
	(*TrafficStatTask)(nil),            // 67: TrafficStatTask
	(*AddressSelector)(nil),            // 68: AddressSelector
	(*IntRange)(nil),                   // 69: IntRange
	(*PortRange)(nil),                  // 70: PortRange
	(*PortSpec)(nil),                   // 71: PortSpec
	(*AppProtocolSpec)(nil),            // 72: AppProtocolSpec
	(*VlanRange)(nil),                  // 73: VlanRange
	(*InterfaceSpec)(nil),              // 74: InterfaceSpec
	(*FlowControlPolicyGroupTask)(nil), // 75: FlowControlPolicyGroupTask
	(*ActionChannelConfig)(nil),        // 76: ActionChannelConfig
	(*ActionAcceptConfig)(nil),         // 77: ActionAcceptConfig
	(*FlowControlPolicyTask)(nil),      // 78: FlowControlPolicyTask
	(*FlowControlTask)(nil),            // 79: FlowControlTask
	(*NatIpPool)(nil),                  // 80: NatIpPool
	(*RouteActionConfig)(nil),          // 81: RouteActionConfig
	(*NatActionConfig)(nil),            // 82: NatActionConfig
	(*RoutePolicyTask)(nil),            // 83: RoutePolicyTask
	(*DnsPolicyActionPassConfig)(nil),  // 84: DnsPolicyActionPassConfig
	(*DnsPolicyActionRdrConfig)(nil),   // 85: DnsPolicyActionRdrConfig
	(*DnsPolicyActionReplyConfig)(nil), // 86: DnsPolicyActionReplyConfig
	(*DnsPolicyActionLimitConfig)(nil), // 87: DnsPolicyActionLimitConfig
	(*DnsPolicyTask)(nil),              // 88: DnsPolicyTask
	(*DnsTrackingPolicyTask)(nil),      // 89: DnsTrackingPolicyTask
	(*ProxyStatusInfo)(nil),            // 90: ProxyStatusInfo
	(*ProxyStatusReport)(nil),          // 91: ProxyStatusReport
	(*DeviceReport)(nil),               // 92: DeviceReport
	(*DeviceReportRequest)(nil),        // 93: DeviceReportRequest
	(*DeviceReportResponse)(nil),       // 94: DeviceReportResponse
	(*WanTask_StaticIpConfig)(nil),     // 95: WanTask.StaticIpConfig
	(*WanTask_PppoeConfig)(nil),        // 96: WanTask.PppoeConfig
	(*WanTask_CommonConfig)(nil),       // 97: WanTask.CommonConfig
}
var file_message_proto_depIdxs = []int32{
	2,   // 0: ClientSyncRequest.sync_type:type_name -> SyncType
	25,  // 1: ServerSyncResponse.task_txs:type_name -> TaskTx
	26,  // 2: TaskTx.device_tasks:type_name -> DeviceTask
	0,   // 3: DeviceTask.task_type:type_name -> TaskType
	1,   // 4: DeviceTask.task_action:type_name -> TaskAction
	41,  // 5: DeviceTask.wan_task:type_name -> WanTask
	42,  // 6: DeviceTask.lan_task:type_name -> LanTask
	30,  // 7: DeviceTask.interface_task:type_name -> InterfaceTask
	44,  // 8: DeviceTask.dhcp_task:type_name -> DhcpServerTask
	46,  // 9: DeviceTask.wan_group_task:type_name -> WanGroupTask
	48,  // 10: DeviceTask.user_group_task:type_name -> UserGroupTask
	52,  // 11: DeviceTask.user_task:type_name -> UserTask
	53,  // 12: DeviceTask.iwan_proxy_task:type_name -> IwanProxyTask
	54,  // 13: DeviceTask.iwan_service_task:type_name -> IwanServiceTask
	55,  // 14: DeviceTask.iwan_mapping_task:type_name -> IwanMappingTask
	58,  // 15: DeviceTask.sr_proxy_task:type_name -> SrProxyTask
	60,  // 16: DeviceTask.ip_group_task:type_name -> IpGroupTask
	61,  // 17: DeviceTask.domain_group_task:type_name -> DomainGroupTask
	64,  // 18: DeviceTask.effective_time_task:type_name -> EffectiveTimeTask
	66,  // 19: DeviceTask.traffic_channel_task:type_name -> TrafficChannelTask
	67,  // 20: DeviceTask.traffic_stat_task:type_name -> TrafficStatTask
	79,  // 21: DeviceTask.flow_control_task:type_name -> FlowControlTask
	83,  // 22: DeviceTask.route_policy_task:type_name -> RoutePolicyTask
	88,  // 23: DeviceTask.dns_policy_task:type_name -> DnsPolicyTask
	89,  // 24: DeviceTask.dns_tracking_policy_task:type_name -> DnsTrackingPolicyTask
	27,  // 25: ClientSyncAck.task_tx_status:type_name -> TaskTxStatus
	5,   // 26: LacpConfig.protocol:type_name -> LacpProtocol
	6,   // 27: LacpConfig.timeout:type_name -> LacpTimeout
	3,   // 28: InterfaceTask.mode:type_name -> InterfaceMode
	4,   // 29: InterfaceTask.zone:type_name -> InterfaceZone
	29,  // 30: InterfaceTask.lacp_config:type_name -> LacpConfig
	34,  // 31: IpAddress.v4_cidr:type_name -> V4Cidr
	35,  // 32: IpAddress.v6_cidr:type_name -> V6Cidr
	8,   // 33: DhcpOptionValue.value_type:type_name -> DhcpOptionValueType
	37,  // 34: DhcpOptionConfig.option12:type_name -> DhcpOptionValue
	37,  // 35: DhcpOptionConfig.option61:type_name -> DhcpOptionValue
	37,  // 36: DhcpOptionConfig.option60:type_name -> DhcpOptionValue
	36,  // 37: NatIp.ip:type_name -> IpAddress
	43,  // 38: NatIp.ip_range:type_name -> IpRange
	36,  // 39: HeartbeatConfig.ping_ip:type_name -> IpAddress
	36,  // 40: HeartbeatConfig.ping_ip2:type_name -> IpAddress
	95,  // 41: WanTask.static_ip:type_name -> WanTask.StaticIpConfig
	38,  // 42: WanTask.dhcp:type_name -> DhcpOptionConfig
	96,  // 43: WanTask.pppoe:type_name -> WanTask.PppoeConfig
	40,  // 44: WanTask.heartbeat:type_name -> HeartbeatConfig
	97,  // 45: WanTask.common:type_name -> WanTask.CommonConfig
	36,  // 46: LanTask.addr:type_name -> IpAddress
	36,  // 47: LanTask.mask:type_name -> IpAddress
	36,  // 48: IpRange.start_ip:type_name -> IpAddress
	36,  // 49: IpRange.end_ip:type_name -> IpAddress
	43,  // 50: DhcpServerTask.dhcp_pool:type_name -> IpRange
	36,  // 51: DhcpServerTask.dns0:type_name -> IpAddress
	36,  // 52: DhcpServerTask.dns1:type_name -> IpAddress
	36,  // 53: DhcpServerTask.dhcp_gateway:type_name -> IpAddress
	36,  // 54: DhcpServerTask.dhcp_mask:type_name -> IpAddress
	38,  // 55: DhcpServerTask.dhcp_options:type_name -> DhcpOptionConfig
	36,  // 56: DhcpServerTask.dhcp_ac_addr:type_name -> IpAddress
	9,   // 57: WanGroupTask.type:type_name -> WanGroupType
	45,  // 58: WanGroupTask.members:type_name -> WanGroupMember
	43,  // 59: UserGroupTask.v4_range:type_name -> IpRange
	36,  // 60: UserGroupTask.v6_range:type_name -> IpAddress
	47,  // 61: UserGroupTask.v4_rate:type_name -> UserBwLimit
	47,  // 62: UserGroupTask.v6_rate:type_name -> UserBwLimit
	36,  // 63: UserGroupTask.dns:type_name -> IpAddress
	10,  // 64: UserGroupTask.clnt_epa:type_name -> UserExpiredPolicy
	36,  // 65: UserRestriction.bind_ip:type_name -> IpAddress
	51,  // 66: UserTask.start:type_name -> Date
	51,  // 67: UserTask.expire:type_name -> Date
	49,  // 68: UserTask.restriction:type_name -> UserRestriction
	50,  // 69: UserTask.identity:type_name -> UserIdentity
	40,  // 70: IwanProxyTask.heartbeat:type_name -> HeartbeatConfig
	36,  // 71: IwanServiceTask.addr:type_name -> IpAddress
	11,  // 72: SrEncryptConfig.encrypt_type:type_name -> SrEncryptType
	57,  // 73: SrProxyTask.paths:type_name -> SrPath
	56,  // 74: SrProxyTask.encrypt_config:type_name -> SrEncryptConfig
	36,  // 75: IpGroupMember.ip:type_name -> IpAddress
	43,  // 76: IpGroupMember.ip_range:type_name -> IpRange
	59,  // 77: IpGroupTask.members:type_name -> IpGroupMember
	62,  // 78: TimeSpec.start_time:type_name -> DailyTime
	62,  // 79: TimeSpec.end_time:type_name -> DailyTime
	63,  // 80: EffectiveTimeTask.time_range:type_name -> TimeSpec
	65,  // 81: TrafficChannelTask.priorities:type_name -> TrafficChannelPriority
	36,  // 82: AddressSelector.ip:type_name -> IpAddress
	43,  // 83: AddressSelector.ip_range:type_name -> IpRange
	70,  // 84: PortSpec.ports:type_name -> PortRange
	13,  // 85: InterfaceSpec.dir:type_name -> FlowDirection
	73,  // 86: InterfaceSpec.vlan:type_name -> VlanRange
	63,  // 87: FlowControlPolicyGroupTask.time_range:type_name -> TimeSpec
	68,  // 88: FlowControlPolicyTask.in_ip:type_name -> AddressSelector
	71,  // 89: FlowControlPolicyTask.in_port:type_name -> PortSpec
	68,  // 90: FlowControlPolicyTask.out_ip:type_name -> AddressSelector
	71,  // 91: FlowControlPolicyTask.out_port:type_name -> PortSpec
	72,  // 92: FlowControlPolicyTask.app:type_name -> AppProtocolSpec
	74,  // 93: FlowControlPolicyTask.interface:type_name -> InterfaceSpec
	12,  // 94: FlowControlPolicyTask.action:type_name -> FlowControlAction
	77,  // 95: FlowControlPolicyTask.action_accept:type_name -> ActionAcceptConfig
	76,  // 96: FlowControlPolicyTask.action_channel:type_name -> ActionChannelConfig
	75,  // 97: FlowControlTask.policy_group:type_name -> FlowControlPolicyGroupTask
	78,  // 98: FlowControlTask.policy:type_name -> FlowControlPolicyTask
	36,  // 99: NatIpPool.ip:type_name -> IpAddress
	43,  // 100: NatIpPool.ip_ranges:type_name -> IpRange
	36,  // 101: RouteActionConfig.next_hop:type_name -> IpAddress
	36,  // 102: NatActionConfig.new_dst_ip:type_name -> IpAddress
	80,  // 103: NatActionConfig.nat_ip:type_name -> NatIpPool
	36,  // 104: NatActionConfig.next_hop:type_name -> IpAddress
	15,  // 105: RoutePolicyTask.zone:type_name -> RoutePolicyZone
	68,  // 106: RoutePolicyTask.src:type_name -> AddressSelector
	71,  // 107: RoutePolicyTask.src_port:type_name -> PortSpec
	16,  // 108: RoutePolicyTask.usr_type:type_name -> UserType
	68,  // 109: RoutePolicyTask.dst:type_name -> AddressSelector
	71,  // 110: RoutePolicyTask.dst_port:type_name -> PortSpec
	72,  // 111: RoutePolicyTask.proto:type_name -> AppProtocolSpec
	69,  // 112: RoutePolicyTask.vlan:type_name -> IntRange
	69,  // 113: RoutePolicyTask.ttl:type_name -> IntRange
	69,  // 114: RoutePolicyTask.dscp:type_name -> IntRange
	14,  // 115: RoutePolicyTask.action:type_name -> RoutePolicyAction
	81,  // 116: RoutePolicyTask.route_config:type_name -> RouteActionConfig
	82,  // 117: RoutePolicyTask.nat_config:type_name -> NatActionConfig
	36,  // 118: DnsPolicyActionRdrConfig.dns_list:type_name -> IpAddress
	36,  // 119: DnsPolicyActionReplyConfig.act_arg:type_name -> IpAddress
	68,  // 120: DnsPolicyTask.in_ip:type_name -> AddressSelector
	16,  // 121: DnsPolicyTask.usr_type:type_name -> UserType
	68,  // 122: DnsPolicyTask.out_ip:type_name -> AddressSelector
	72,  // 123: DnsPolicyTask.app:type_name -> AppProtocolSpec
	17,  // 124: DnsPolicyTask.a_type:type_name -> DnsQueryType
	69,  // 125: DnsPolicyTask.vlan:type_name -> IntRange
	18,  // 126: DnsPolicyTask.action:type_name -> DnsPolicyAction
	84,  // 127: DnsPolicyTask.action_pass:type_name -> DnsPolicyActionPassConfig
	85,  // 128: DnsPolicyTask.action_rdr:type_name -> DnsPolicyActionRdrConfig
	86,  // 129: DnsPolicyTask.action_reply:type_name -> DnsPolicyActionReplyConfig
	87,  // 130: DnsPolicyTask.action_limit:type_name -> DnsPolicyActionLimitConfig
	36,  // 131: DnsTrackingPolicyTask.dns_addr:type_name -> IpAddress
	21,  // 132: ProxyStatusInfo.type:type_name -> ProxyType
	22,  // 133: ProxyStatusInfo.state:type_name -> ProxyState
	20,  // 134: ProxyStatusReport.trigger_type:type_name -> ReportTriggerType
	90,  // 135: ProxyStatusReport.proxy_status:type_name -> ProxyStatusInfo
	19,  // 136: DeviceReport.report_type:type_name -> ReportType
	91,  // 137: DeviceReport.proxy_status_report:type_name -> ProxyStatusReport
	92,  // 138: DeviceReportRequest.device_reports:type_name -> DeviceReport
	7,   // 139: WanTask.StaticIpConfig.gw_pxy:type_name -> WanGatewayType
	36,  // 140: WanTask.StaticIpConfig.addr:type_name -> IpAddress
	36,  // 141: WanTask.StaticIpConfig.gateway:type_name -> IpAddress
	36,  // 142: WanTask.StaticIpConfig.dns:type_name -> IpAddress
	39,  // 143: WanTask.StaticIpConfig.nat_ip:type_name -> NatIp
	23,  // 144: ConfigSyncService.HandleConfigSyncRequest:input_type -> ClientSyncRequest
	28,  // 145: ConfigSyncService.HandleClientSyncAck:input_type -> ClientSyncAck
	31,  // 146: HeartbeatService.SendHeartbeat:input_type -> HeartbeatRequest
	93,  // 147: DeviceReportService.SendDeviceReport:input_type -> DeviceReportRequest
	24,  // 148: ConfigSyncService.HandleConfigSyncRequest:output_type -> ServerSyncResponse
	33,  // 149: ConfigSyncService.HandleClientSyncAck:output_type -> ErrorCode
	32,  // 150: HeartbeatService.SendHeartbeat:output_type -> HeartbeatResponse
	94,  // 151: DeviceReportService.SendDeviceReport:output_type -> DeviceReportResponse
	148, // [148:152] is the sub-list for method output_type
	144, // [144:148] is the sub-list for method input_type
	144, // [144:144] is the sub-list for extension type_name
	144, // [144:144] is the sub-list for extension extendee
	0,   // [0:144] is the sub-list for field type_name
}

func init() { file_message_proto_init() }
func file_message_proto_init() {
	if File_message_proto != nil {
		return
	}
	file_message_proto_msgTypes[3].OneofWrappers = []any{
		(*DeviceTask_WanTask)(nil),
		(*DeviceTask_LanTask)(nil),
		(*DeviceTask_InterfaceTask)(nil),
		(*DeviceTask_DhcpTask)(nil),
		(*DeviceTask_WanGroupTask)(nil),
		(*DeviceTask_UserGroupTask)(nil),
		(*DeviceTask_UserTask)(nil),
		(*DeviceTask_IwanProxyTask)(nil),
		(*DeviceTask_IwanServiceTask)(nil),
		(*DeviceTask_IwanMappingTask)(nil),
		(*DeviceTask_SrProxyTask)(nil),
		(*DeviceTask_IpGroupTask)(nil),
		(*DeviceTask_DomainGroupTask)(nil),
		(*DeviceTask_EffectiveTimeTask)(nil),
		(*DeviceTask_TrafficChannelTask)(nil),
		(*DeviceTask_TrafficStatTask)(nil),
		(*DeviceTask_FlowControlTask)(nil),
		(*DeviceTask_RoutePolicyTask)(nil),
		(*DeviceTask_DnsPolicyTask)(nil),
		(*DeviceTask_DnsTrackingPolicyTask)(nil),
	}
	file_message_proto_msgTypes[6].OneofWrappers = []any{}
	file_message_proto_msgTypes[7].OneofWrappers = []any{}
	file_message_proto_msgTypes[13].OneofWrappers = []any{
		(*IpAddress_Ipv4)(nil),
		(*IpAddress_Ipv6)(nil),
		(*IpAddress_IpString)(nil),
		(*IpAddress_V4Cidr)(nil),
		(*IpAddress_V6Cidr)(nil),
	}
	file_message_proto_msgTypes[15].OneofWrappers = []any{}
	file_message_proto_msgTypes[16].OneofWrappers = []any{}
	file_message_proto_msgTypes[17].OneofWrappers = []any{}
	file_message_proto_msgTypes[18].OneofWrappers = []any{
		(*WanTask_StaticIp)(nil),
		(*WanTask_Dhcp)(nil),
		(*WanTask_Pppoe)(nil),
	}
	file_message_proto_msgTypes[19].OneofWrappers = []any{}
	file_message_proto_msgTypes[21].OneofWrappers = []any{}
	file_message_proto_msgTypes[24].OneofWrappers = []any{}
	file_message_proto_msgTypes[25].OneofWrappers = []any{}
	file_message_proto_msgTypes[26].OneofWrappers = []any{}
	file_message_proto_msgTypes[27].OneofWrappers = []any{}
	file_message_proto_msgTypes[29].OneofWrappers = []any{}
	file_message_proto_msgTypes[30].OneofWrappers = []any{}
	file_message_proto_msgTypes[32].OneofWrappers = []any{}
	file_message_proto_msgTypes[33].OneofWrappers = []any{}
	file_message_proto_msgTypes[35].OneofWrappers = []any{}
	file_message_proto_msgTypes[36].OneofWrappers = []any{
		(*IpGroupMember_Ip)(nil),
		(*IpGroupMember_IpRange)(nil),
	}
	file_message_proto_msgTypes[37].OneofWrappers = []any{}
	file_message_proto_msgTypes[38].OneofWrappers = []any{}
	file_message_proto_msgTypes[42].OneofWrappers = []any{}
	file_message_proto_msgTypes[43].OneofWrappers = []any{}
	file_message_proto_msgTypes[45].OneofWrappers = []any{
		(*AddressSelector_Ip)(nil),
		(*AddressSelector_IpRange)(nil),
		(*AddressSelector_IpGroupName)(nil),
		(*AddressSelector_MacGroupId)(nil),
		(*AddressSelector_UserGroupId)(nil),
		(*AddressSelector_Username)(nil),
		(*AddressSelector_DomainGroupName)(nil),
	}
	file_message_proto_msgTypes[51].OneofWrappers = []any{}
	file_message_proto_msgTypes[52].OneofWrappers = []any{}
	file_message_proto_msgTypes[53].OneofWrappers = []any{}
	file_message_proto_msgTypes[54].OneofWrappers = []any{}
	file_message_proto_msgTypes[55].OneofWrappers = []any{
		(*FlowControlPolicyTask_ActionAccept)(nil),
		(*FlowControlPolicyTask_ActionChannel)(nil),
	}
	file_message_proto_msgTypes[56].OneofWrappers = []any{
		(*FlowControlTask_PolicyGroup)(nil),
		(*FlowControlTask_Policy)(nil),
	}
	file_message_proto_msgTypes[58].OneofWrappers = []any{}
	file_message_proto_msgTypes[59].OneofWrappers = []any{}
	file_message_proto_msgTypes[60].OneofWrappers = []any{}
	file_message_proto_msgTypes[61].OneofWrappers = []any{}
	file_message_proto_msgTypes[62].OneofWrappers = []any{}
	file_message_proto_msgTypes[64].OneofWrappers = []any{}
	file_message_proto_msgTypes[65].OneofWrappers = []any{
		(*DnsPolicyTask_ActionPass)(nil),
		(*DnsPolicyTask_ActionRdr)(nil),
		(*DnsPolicyTask_ActionReply)(nil),
		(*DnsPolicyTask_ActionLimit)(nil),
	}
	file_message_proto_msgTypes[66].OneofWrappers = []any{}
	file_message_proto_msgTypes[69].OneofWrappers = []any{
		(*DeviceReport_ProxyStatusReport)(nil),
	}
	file_message_proto_msgTypes[72].OneofWrappers = []any{}
	file_message_proto_msgTypes[73].OneofWrappers = []any{}
	file_message_proto_msgTypes[74].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_message_proto_rawDesc), len(file_message_proto_rawDesc)),
			NumEnums:      23,
			NumMessages:   75,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_message_proto_goTypes,
		DependencyIndexes: file_message_proto_depIdxs,
		EnumInfos:         file_message_proto_enumTypes,
		MessageInfos:      file_message_proto_msgTypes,
	}.Build()
	File_message_proto = out.File
	file_message_proto_goTypes = nil
	file_message_proto_depIdxs = nil
}
