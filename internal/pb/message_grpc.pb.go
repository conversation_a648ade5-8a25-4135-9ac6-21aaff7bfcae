// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: message.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ConfigSyncService_HandleConfigSyncRequest_FullMethodName = "/ConfigSyncService/HandleConfigSyncRequest"
	ConfigSyncService_HandleClientSyncAck_FullMethodName     = "/ConfigSyncService/HandleClientSyncAck"
)

// ConfigSyncServiceClient is the client API for ConfigSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CPE/POP 拉取配置服务
type ConfigSyncServiceClient interface {
	HandleConfigSyncRequest(ctx context.Context, in *ClientSyncRequest, opts ...grpc.CallOption) (*ServerSyncResponse, error)
	HandleClientSyncAck(ctx context.Context, in *ClientSyncAck, opts ...grpc.CallOption) (*ErrorCode, error)
}

type configSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigSyncServiceClient(cc grpc.ClientConnInterface) ConfigSyncServiceClient {
	return &configSyncServiceClient{cc}
}

func (c *configSyncServiceClient) HandleConfigSyncRequest(ctx context.Context, in *ClientSyncRequest, opts ...grpc.CallOption) (*ServerSyncResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ServerSyncResponse)
	err := c.cc.Invoke(ctx, ConfigSyncService_HandleConfigSyncRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configSyncServiceClient) HandleClientSyncAck(ctx context.Context, in *ClientSyncAck, opts ...grpc.CallOption) (*ErrorCode, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ErrorCode)
	err := c.cc.Invoke(ctx, ConfigSyncService_HandleClientSyncAck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigSyncServiceServer is the server API for ConfigSyncService service.
// All implementations must embed UnimplementedConfigSyncServiceServer
// for forward compatibility.
//
// CPE/POP 拉取配置服务
type ConfigSyncServiceServer interface {
	HandleConfigSyncRequest(context.Context, *ClientSyncRequest) (*ServerSyncResponse, error)
	HandleClientSyncAck(context.Context, *ClientSyncAck) (*ErrorCode, error)
	mustEmbedUnimplementedConfigSyncServiceServer()
}

// UnimplementedConfigSyncServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedConfigSyncServiceServer struct{}

func (UnimplementedConfigSyncServiceServer) HandleConfigSyncRequest(context.Context, *ClientSyncRequest) (*ServerSyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleConfigSyncRequest not implemented")
}
func (UnimplementedConfigSyncServiceServer) HandleClientSyncAck(context.Context, *ClientSyncAck) (*ErrorCode, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleClientSyncAck not implemented")
}
func (UnimplementedConfigSyncServiceServer) mustEmbedUnimplementedConfigSyncServiceServer() {}
func (UnimplementedConfigSyncServiceServer) testEmbeddedByValue()                           {}

// UnsafeConfigSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigSyncServiceServer will
// result in compilation errors.
type UnsafeConfigSyncServiceServer interface {
	mustEmbedUnimplementedConfigSyncServiceServer()
}

func RegisterConfigSyncServiceServer(s grpc.ServiceRegistrar, srv ConfigSyncServiceServer) {
	// If the following call pancis, it indicates UnimplementedConfigSyncServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ConfigSyncService_ServiceDesc, srv)
}

func _ConfigSyncService_HandleConfigSyncRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientSyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigSyncServiceServer).HandleConfigSyncRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigSyncService_HandleConfigSyncRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigSyncServiceServer).HandleConfigSyncRequest(ctx, req.(*ClientSyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigSyncService_HandleClientSyncAck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientSyncAck)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigSyncServiceServer).HandleClientSyncAck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConfigSyncService_HandleClientSyncAck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigSyncServiceServer).HandleClientSyncAck(ctx, req.(*ClientSyncAck))
	}
	return interceptor(ctx, in, info, handler)
}

// ConfigSyncService_ServiceDesc is the grpc.ServiceDesc for ConfigSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConfigSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ConfigSyncService",
	HandlerType: (*ConfigSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleConfigSyncRequest",
			Handler:    _ConfigSyncService_HandleConfigSyncRequest_Handler,
		},
		{
			MethodName: "HandleClientSyncAck",
			Handler:    _ConfigSyncService_HandleClientSyncAck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "message.proto",
}

const (
	HeartbeatService_SendHeartbeat_FullMethodName = "/HeartbeatService/SendHeartbeat"
)

// HeartbeatServiceClient is the client API for HeartbeatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CPE/POP 发送心跳服务
type HeartbeatServiceClient interface {
	SendHeartbeat(ctx context.Context, in *HeartbeatRequest, opts ...grpc.CallOption) (*HeartbeatResponse, error)
}

type heartbeatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHeartbeatServiceClient(cc grpc.ClientConnInterface) HeartbeatServiceClient {
	return &heartbeatServiceClient{cc}
}

func (c *heartbeatServiceClient) SendHeartbeat(ctx context.Context, in *HeartbeatRequest, opts ...grpc.CallOption) (*HeartbeatResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HeartbeatResponse)
	err := c.cc.Invoke(ctx, HeartbeatService_SendHeartbeat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HeartbeatServiceServer is the server API for HeartbeatService service.
// All implementations must embed UnimplementedHeartbeatServiceServer
// for forward compatibility.
//
// CPE/POP 发送心跳服务
type HeartbeatServiceServer interface {
	SendHeartbeat(context.Context, *HeartbeatRequest) (*HeartbeatResponse, error)
	mustEmbedUnimplementedHeartbeatServiceServer()
}

// UnimplementedHeartbeatServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedHeartbeatServiceServer struct{}

func (UnimplementedHeartbeatServiceServer) SendHeartbeat(context.Context, *HeartbeatRequest) (*HeartbeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendHeartbeat not implemented")
}
func (UnimplementedHeartbeatServiceServer) mustEmbedUnimplementedHeartbeatServiceServer() {}
func (UnimplementedHeartbeatServiceServer) testEmbeddedByValue()                          {}

// UnsafeHeartbeatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HeartbeatServiceServer will
// result in compilation errors.
type UnsafeHeartbeatServiceServer interface {
	mustEmbedUnimplementedHeartbeatServiceServer()
}

func RegisterHeartbeatServiceServer(s grpc.ServiceRegistrar, srv HeartbeatServiceServer) {
	// If the following call pancis, it indicates UnimplementedHeartbeatServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&HeartbeatService_ServiceDesc, srv)
}

func _HeartbeatService_SendHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HeartbeatServiceServer).SendHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HeartbeatService_SendHeartbeat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HeartbeatServiceServer).SendHeartbeat(ctx, req.(*HeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HeartbeatService_ServiceDesc is the grpc.ServiceDesc for HeartbeatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HeartbeatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "HeartbeatService",
	HandlerType: (*HeartbeatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendHeartbeat",
			Handler:    _HeartbeatService_SendHeartbeat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "message.proto",
}

const (
	DeviceReportService_SendDeviceReport_FullMethodName = "/DeviceReportService/SendDeviceReport"
)

// DeviceReportServiceClient is the client API for DeviceReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CPE/POP 设备上报服务
type DeviceReportServiceClient interface {
	SendDeviceReport(ctx context.Context, in *DeviceReportRequest, opts ...grpc.CallOption) (*DeviceReportResponse, error)
}

type deviceReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceReportServiceClient(cc grpc.ClientConnInterface) DeviceReportServiceClient {
	return &deviceReportServiceClient{cc}
}

func (c *deviceReportServiceClient) SendDeviceReport(ctx context.Context, in *DeviceReportRequest, opts ...grpc.CallOption) (*DeviceReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceReportResponse)
	err := c.cc.Invoke(ctx, DeviceReportService_SendDeviceReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceReportServiceServer is the server API for DeviceReportService service.
// All implementations must embed UnimplementedDeviceReportServiceServer
// for forward compatibility.
//
// CPE/POP 设备上报服务
type DeviceReportServiceServer interface {
	SendDeviceReport(context.Context, *DeviceReportRequest) (*DeviceReportResponse, error)
	mustEmbedUnimplementedDeviceReportServiceServer()
}

// UnimplementedDeviceReportServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDeviceReportServiceServer struct{}

func (UnimplementedDeviceReportServiceServer) SendDeviceReport(context.Context, *DeviceReportRequest) (*DeviceReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendDeviceReport not implemented")
}
func (UnimplementedDeviceReportServiceServer) mustEmbedUnimplementedDeviceReportServiceServer() {}
func (UnimplementedDeviceReportServiceServer) testEmbeddedByValue()                             {}

// UnsafeDeviceReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceReportServiceServer will
// result in compilation errors.
type UnsafeDeviceReportServiceServer interface {
	mustEmbedUnimplementedDeviceReportServiceServer()
}

func RegisterDeviceReportServiceServer(s grpc.ServiceRegistrar, srv DeviceReportServiceServer) {
	// If the following call pancis, it indicates UnimplementedDeviceReportServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DeviceReportService_ServiceDesc, srv)
}

func _DeviceReportService_SendDeviceReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceReportServiceServer).SendDeviceReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeviceReportService_SendDeviceReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceReportServiceServer).SendDeviceReport(ctx, req.(*DeviceReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DeviceReportService_ServiceDesc is the grpc.ServiceDesc for DeviceReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeviceReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "DeviceReportService",
	HandlerType: (*DeviceReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendDeviceReport",
			Handler:    _DeviceReportService_SendDeviceReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "message.proto",
}
