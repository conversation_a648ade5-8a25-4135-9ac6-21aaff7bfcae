/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      unix_server.go
 *
 * DESCRIPTION :    Unix domain socket based IPC server implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package ipc

import (
	"encoding/json"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"time"

	"agent/internal/logger"

	"go.uber.org/zap"
)

/*****************************************************************************
 * NAME: unixServer
 *
 * DESCRIPTION:
 *     Server implementation using Unix domain sockets for IPC communication.
 *     This is an internal implementation of the IpcServer interface.
 *
 * FIELDS:
 *     socketPath - Path to the Unix domain socket
 *     listener   - Network listener for accepting connections
 *     startTime  - Server start timestamp
 *     log        - Logger instance for this server
 *****************************************************************************/
type unixServer struct {
	socketPath string
	listener   net.Listener
	startTime  time.Time
	log        *logger.Logger
}

/*****************************************************************************
 * NAME: NewUnixServer
 *
 * DESCRIPTION:
 *     Creates a new Unix domain socket based IPC server.
 *
 * PARAMETERS:
 *     log - Logger instance for the server
 *
 * RETURNS:
 *     IpcServer - New server instance
 *****************************************************************************/
func NewUnixServer(log *logger.Logger) IpcServer {
	return &unixServer{
		socketPath: DefaultSocketPath,
		startTime:  time.Now(),
		log:        log.WithModule("ipc-server"),
	}
}

/*****************************************************************************
 * NAME: Start
 *
 * DESCRIPTION:
 *     Starts the IPC server by creating a Unix domain socket and
 *     listening for incoming connections.
 *
 * RETURNS:
 *     error - Error if server fails to start
 *****************************************************************************/
func (s *unixServer) Start() error {
	s.log.Info("Starting IPC server", zap.String("socket", s.socketPath))

	// Remove existing socket file if it exists
	if err := os.RemoveAll(s.socketPath); err != nil {
		return fmt.Errorf("failed to remove existing socket: %w", err)
	}

	// Create directory for socket if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(s.socketPath), 0o755); err != nil {
		return fmt.Errorf("failed to create socket directory: %w", err)
	}

	// Create Unix domain socket listener
	listener, err := net.Listen("unix", s.socketPath)
	if err != nil {
		return fmt.Errorf("failed to create socket listener: %w", err)
	}
	s.listener = listener

	// Set socket permissions
	if err := os.Chmod(s.socketPath, 0o660); err != nil {
		s.listener.Close()
		return fmt.Errorf("failed to set socket permissions: %w", err)
	}

	// Start accepting connections in a goroutine
	go s.acceptConnections()

	s.log.Info("IPC server started successfully")
	return nil
}

/*****************************************************************************
 * NAME: acceptConnections
 *
 * DESCRIPTION:
 *     Accepts incoming connections and handles them in separate goroutines.
 *     This method runs in a background goroutine.
 *****************************************************************************/
func (s *unixServer) acceptConnections() {
	for {
		conn, err := s.listener.Accept()
		if err != nil {
			// Check if the listener was closed
			if opErr, ok := err.(*net.OpError); ok && opErr.Err.Error() == "use of closed network connection" {
				s.log.Info("Listener closed, stopping accept loop")
				return
			}
			s.log.Error("Failed to accept connection", zap.Error(err))
			continue
		}

		// Handle connection in a separate goroutine
		go s.handleConnection(conn)
	}
}

/*****************************************************************************
 * NAME: handleConnection
 *
 * DESCRIPTION:
 *     Handles a client connection by reading commands and sending responses.
 *
 * PARAMETERS:
 *     conn - Client connection
 *****************************************************************************/
func (s *unixServer) handleConnection(conn net.Conn) {
	defer conn.Close()

	s.log.Debug("New client connection accepted")

	// Create a decoder for reading JSON commands
	decoder := json.NewDecoder(conn)
	var cmd Command
	if err := decoder.Decode(&cmd); err != nil {
		s.log.Error("Failed to decode command", zap.Error(err))
		return
	}

	s.log.Debug("Received command", zap.String("type", string(cmd.Type)))

	// Process the command
	var response Response
	switch cmd.Type {
	case CmdStop:
		response = s.handleStopCommand()
	case CmdStatus:
		status, err := s.GetStatus()
		if err != nil {
			response = Response{Success: false, Error: err.Error()}
		} else {
			response = Response{Success: true, Data: status}
		}
	case CmdSetLogLevel:
		response = s.handleSetLogLevelCommand(cmd.LogLevel)
	case CmdDebug:
		response = s.handleDebugCommand(cmd.DebugCmd, cmd.DebugPort, cmd.DebugArg)
	default:
		response = Response{Success: false, Error: fmt.Sprintf("unknown command: %s", cmd.Type)}
	}

	// Send response back to client
	encoder := json.NewEncoder(conn)
	if err := encoder.Encode(response); err != nil {
		s.log.Error("Failed to encode response", zap.Error(err))
	}
}

/*****************************************************************************
 * NAME: handleStopCommand
 *
 * DESCRIPTION:
 *     Handles a stop command by initiating a graceful shutdown.
 *
 * RETURNS:
 *     Response - Response to send back to the client
 *****************************************************************************/
func (s *unixServer) handleStopCommand() Response {
	s.log.Info("Received stop command, initiating shutdown")

	// Notify the main program to shut down
	close(ShutdownChan)

	return Response{Success: true}
}

/*****************************************************************************
 * NAME: handleSetLogLevelCommand
 *
 * DESCRIPTION:
 *     Handles a setLogLevel command by changing the global log level.
 *
 * PARAMETERS:
 *     level - New log level to set
 *
 * RETURNS:
 *     Response - Response to send back to the client
 *****************************************************************************/
func (s *unixServer) handleSetLogLevelCommand(level string) Response {
	s.log.Info("Setting log level", zap.String("level", level))

	if err := logger.SetLogLevel(logger.LogLevel(level)); err != nil {
		s.log.Error("Failed to set log level", zap.Error(err))
		return Response{Success: false, Error: err.Error()}
	}

	s.log.Info("Log level set successfully", zap.String("level", level))
	return Response{Success: true}
}

/*****************************************************************************
 * NAME: handleDebugCommand
 *
 * DESCRIPTION:
 *     Handles the debug command and its subcommands.
 *     Manages the debug server for configuration testing.
 *
 * PARAMETERS:
 *     debugCmd  - Debug subcommand (start, stop, status)
 *     debugPort - Port for the debug server (used with start subcommand)
 *
 * RETURNS:
 *     Response - Command response
 *****************************************************************************/
func (s *unixServer) handleDebugCommand(debugCmd string, debugPort int, debugArg string) Response {
	s.log.Info("Handling debug command",
		zap.String("subcommand", debugCmd),
		zap.Int("port", debugPort),
		zap.String("arg", debugArg))

	// Process debug subcommand
	switch debugCmd {
	case "start":
		// Use default port if not specified
		if debugPort <= 0 {
			debugPort = 8080
		}

		// Start debug server
		result, err := startDebugServer(debugPort)
		if err != nil {
			return Response{Success: false, Error: err.Error()}
		}
		return Response{Success: true, Data: result}

	case "stop":
		// Stop debug server
		result, err := stopDebugServer()
		if err != nil {
			return Response{Success: false, Error: err.Error()}
		}
		return Response{Success: true, Data: result}

	case "status":
		// Get debug server status
		result, err := getDebugStatus()
		if err != nil {
			return Response{Success: false, Error: err.Error()}
		}
		return Response{Success: true, Data: result}

	case "fullsync":
		// Handle fullsync commands
		if debugArg == "" {
			return Response{Success: false, Error: "fullsync command requires a subcommand (start|end)"}
		}

		result, err := handleFullSyncCommand(debugArg)
		if err != nil {
			return Response{Success: false, Error: err.Error()}
		}
		return Response{Success: true, Data: result}

	default:
		return Response{Success: false, Error: fmt.Sprintf("unknown debug subcommand: %s", debugCmd)}
	}
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Stops the IPC server by closing the listener and removing the socket.
 *
 * RETURNS:
 *     error - Error if server fails to stop
 *****************************************************************************/
func (s *unixServer) Stop() error {
	s.log.Info("Stopping IPC server")

	if s.listener != nil {
		if err := s.listener.Close(); err != nil {
			return fmt.Errorf("failed to close listener: %w", err)
		}
	}

	// Remove socket file
	if err := os.RemoveAll(s.socketPath); err != nil {
		return fmt.Errorf("failed to remove socket: %w", err)
	}

	s.log.Info("IPC server stopped successfully")
	return nil
}

/*****************************************************************************
 * NAME: GetStatus
 *
 * DESCRIPTION:
 *     Returns the current status of the server.
 *
 * RETURNS:
 *     *Status - Server status information
 *     error   - Error if status retrieval fails
 *****************************************************************************/
func (s *unixServer) GetStatus() (*Status, error) {
	uptime := time.Since(s.startTime).String()
	return &Status{
		PID:       os.Getpid(),
		StartTime: s.startTime,
		Uptime:    uptime,
	}, nil
}
