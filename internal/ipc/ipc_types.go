/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      types.go
 *
 * DESCRIPTION :    IPC types and interfaces definition
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package ipc

import "time"

/*****************************************************************************
 * NAME: DefaultSocketPath
 *
 * DESCRIPTION:
 *     Default path for the Unix domain socket used for IPC communication
 *****************************************************************************/
const DefaultSocketPath = "/var/run/agentIpc.sock"

/*****************************************************************************
 * NAME: IpcClient
 *
 * DESCRIPTION:
 *     Interface defining the client-side IPC communication methods
 *
 * METHODS:
 *     SendCommand - Sends a command to the server and returns the response
 *     Stop       - Sends a stop command to the server
 *     GetStatus  - Retrieves the current status of the server
 *     Close      - Closes the connection to the server
 *     SetLogLevel - Sets the log level on the server
 *****************************************************************************/
type IpcClient interface {
	SendCommand(cmd Command) (*Response, error)
	Stop() error
	GetStatus() (*Status, error)
	Close() error
	SetLogLevel(level string) error
	Debug(subCmd string, portOrArg interface{}) (string, error)
}

/*****************************************************************************
 * NAME: IpcServer
 *
 * DESCRIPTION:
 *     Interface defining the basic operations for IPC server
 *
 * METHODS:
 *     Start     - Starts the IPC server
 *     Stop      - Stops the IPC server
 *     GetStatus - Returns the current server status
 *****************************************************************************/
type IpcServer interface {
	Start() error
	Stop() error
	GetStatus() (*Status, error)
}

// CommandType defines the type of IPC command
type CommandType string

const (
	// System commands
	CmdStop   CommandType = "stop"
	CmdStatus CommandType = "status"
	// Log commands
	CmdSetLogLevel CommandType = "setLogLevel"
	// Debug commands
	CmdDebug CommandType = "debug"
)

// Command represents an IPC command structure
type Command struct {
	Type     CommandType `json:"type"`               // Command type
	Content  interface{} `json:"content,omitempty"`  // Command content
	LogLevel string      `json:"logLevel,omitempty"` // Log level for setLogLevel command
	DebugCmd string      `json:"debugCmd,omitempty"` // Debug subcommand
	DebugPort int         `json:"debugPort,omitempty"` // Debug server port
	DebugArg string      `json:"debugArg,omitempty"` // Debug command argument
}

/*****************************************************************************
 * NAME: Response
 *
 * DESCRIPTION:
 *     Structure representing a response from the server
 *
 * FIELDS:
 *     Success - Indicates if the command was successful
 *     Data    - Optional data returned by the server
 *     Error   - Error message if the command failed
 *****************************************************************************/
type Response struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

/*****************************************************************************
 * NAME: Status
 *
 * DESCRIPTION:
 *     Structure representing the server status information
 *
 * FIELDS:
 *     PID       - Process ID of the server
 *     StartTime - Server start timestamp
 *     Uptime    - Server uptime duration
 *****************************************************************************/
type Status struct {
	PID       int       `json:"pid"`
	StartTime time.Time `json:"start_time"`
	Uptime    string    `json:"uptime"`
}

/*****************************************************************************
 * NAME: ShutdownChan
 *
 * DESCRIPTION:
 *     Channel used to notify the main program to shut down
 *     This channel is closed when a shutdown request is received
 *****************************************************************************/
var ShutdownChan = make(chan struct{})
