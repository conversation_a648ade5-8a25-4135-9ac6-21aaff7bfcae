/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      client.go
 *
 * DESCRIPTION :    IPC client factory implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package ipc

import "runtime"

/*****************************************************************************
 * NAME: NewClient
 *
 * DESCRIPTION:
 *     Creates a new IPC client based on the current platform.
 *     This is a factory function that returns the appropriate
 *     platform-specific implementation of the IpcClient interface.
 *
 * INPUTS:
 *     None
 *
 * RETURN:
 *     IpcClient - Platform-specific client implementation
 *                 Returns nil for unsupported platforms
 *
 * NOTES:
 *     - Currently supports Unix-like systems
 *     - Windows support is planned for future implementation
 *****************************************************************************/
func NewClient() IpcClient {
	switch runtime.GOOS {
	case "windows":
		// TODO: Implement Windows platform client
		// return NewWindowsClient()
		return nil
	default:
		return NewUnixClient()
	}
}
