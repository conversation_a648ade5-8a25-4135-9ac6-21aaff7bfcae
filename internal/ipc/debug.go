/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      debug.go
 *
 * DESCRIPTION :    IPC debug command implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package ipc

import (
	"agent/internal/client"
	"agent/internal/debug"
	"fmt"
	"strconv"
	"strings"
)

// handleDebugCommand handles the debug command and its subcommands
func handleDebugCommand(args []string) (string, error) {
	if len(args) < 1 {
		return "", fmt.Errorf("debug command requires at least one argument")
	}

	switch args[0] {
	case "start":
		port := 8080 // Default port
		if len(args) > 1 && strings.HasPrefix(args[1], "--port=") {
			portStr := strings.TrimPrefix(args[1], "--port=")
			if p, err := strconv.Atoi(portStr); err == nil {
				port = p
			}
		}
		return startDebugServer(port)
	case "stop":
		return stopDebugServer()
	case "status":
		return getDebugStatus()
	case "fullsync":
		if len(args) < 2 {
			return "", fmt.Errorf("fullsync command requires a subcommand (start|end)")
		}
		return handleFullSyncCommand(args[1])
	default:
		return "", fmt.Errorf("unknown debug subcommand: %s", args[0])
	}
}

// startDebugServer starts the debug server
func startDebugServer(port int) (string, error) {
	// Get task manager instance
	clientInstance := client.GetInstance()
	if clientInstance == nil {
		return "", fmt.Errorf("client instance not available")
	}

	taskManager := clientInstance.GetTaskManager()
	if taskManager == nil {
		return "", fmt.Errorf("task manager not available")
	}

	if err := debug.StartServer(port, taskManager); err != nil {
		return "", fmt.Errorf("failed to start debug server: %v", err)
	}

	return fmt.Sprintf("Debug server started on port %d (localhost only)", port), nil
}

// stopDebugServer stops the debug server
func stopDebugServer() (string, error) {
	if err := debug.StopServer(); err != nil {
		return "", fmt.Errorf("failed to stop debug server: %v", err)
	}

	return "Debug server stopped", nil
}

// getDebugStatus returns the current status of the debug server
func getDebugStatus() (string, error) {
	if debug.GetStatus() {
		return "Debug server is running", nil
	}
	return "Debug server is not running", nil
}

// handleFullSyncCommand handles fullsync subcommands
func handleFullSyncCommand(subCmd string) (string, error) {
	// Get task manager instance
	clientInstance := client.GetInstance()
	if clientInstance == nil {
		return "", fmt.Errorf("client instance not available")
	}

	taskManager := clientInstance.GetTaskManager()
	if taskManager == nil {
		return "", fmt.Errorf("task manager not available")
	}

	switch subCmd {
	case "start":
		err := taskManager.StartFullSync()
		if err != nil {
			return "", fmt.Errorf("failed to start full sync: %v", err)
		}
		return "Full sync started successfully", nil
	case "end":
		taskManager.EndFullSync()
		return "Full sync ended successfully", nil
	default:
		return "", fmt.Errorf("unknown fullsync subcommand: %s (available: start, end)", subCmd)
	}
}
