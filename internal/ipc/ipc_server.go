/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      server.go
 *
 * DESCRIPTION :    IPC server factory implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package ipc

import (
	"agent/internal/logger"
	"runtime"
)

/*****************************************************************************
 * NAME: NewServer
 *
 * DESCRIPTION:
 *     Creates a new IPC server based on the current platform.
 *     This is a factory function that returns the appropriate
 *     platform-specific implementation of the IpcServer interface.
 *
 * PARAMETERS:
 *     log - Logger instance for the server
 *
 * RETURNS:
 *     IpcServer - Platform-specific server implementation
 *                 Returns nil for unsupported platforms
 *
 * NOTES:
 *     - Currently supports Unix-like systems
 *     - Windows support is planned for future implementation
 *****************************************************************************/
func NewServer(log *logger.Logger) IpcServer {
	switch runtime.GOOS {
	case "windows":
		// TODO: Implement Windows platform server
		// return NewWindowsServer()
		return nil
	default:
		return NewUnixServer(log)
	}
}
