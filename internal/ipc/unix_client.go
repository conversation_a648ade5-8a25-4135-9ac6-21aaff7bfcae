/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      unix_client.go
 *
 * DESCRIPTION :    Unix domain socket based IPC client implementation
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/
package ipc

import (
	"encoding/json"
	"fmt"
	"net"
	"time"
)

/*****************************************************************************
 * NAME: unixClient
 *
 * DESCRIPTION:
 *     Client implementation using Unix domain sockets for IPC communication.
 *     This is an internal implementation of the IpcClient interface.
 *
 * FIELDS:
 *     socketPath - Path to the Unix domain socket
 *     conn       - Network connection to the server
 *****************************************************************************/
type unixClient struct {
	socketPath string
	conn       net.Conn
}

/*****************************************************************************
 * NAME: NewUnixClient
 *
 * DESCRIPTION:
 *     Creates a new Unix domain socket client instance.
 *     This is the only way to create a client instance, ensuring proper initialization.
 *
 * RETURNS:
 *     IpcClient - A new client instance implementing the IpcClient interface
 *****************************************************************************/
func NewUnixClient() IpcClient {
	return &unixClient{
		socketPath: DefaultSocketPath,
	}
}

/*****************************************************************************
 * NAME: SendCommand
 *
 * DESCRIPTION:
 *     Sends a command to the server and waits for a response.
 *     Implements the IpcClient interface.
 *
 * PARAMETERS:
 *     cmd - The command to be sent
 *
 * RETURNS:
 *     *Response - Server response containing the result
 *     error    - Error if the command fails
 *****************************************************************************/
func (c *unixClient) SendCommand(cmd Command) (*Response, error) {
	if err := c.ensureConnection(); err != nil {
		return nil, err
	}

	// Set write deadline
	c.conn.SetWriteDeadline(time.Now().Add(5 * time.Second))

	// Send command
	encoder := json.NewEncoder(c.conn)
	if err := encoder.Encode(cmd); err != nil {
		return nil, fmt.Errorf("failed to encode command: %w", err)
	}

	// Set read deadline
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// Read response
	var response Response
	decoder := json.NewDecoder(c.conn)
	if err := decoder.Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

/*****************************************************************************
 * NAME: Stop
 *
 * DESCRIPTION:
 *     Sends a stop command to the server.
 *     Implements the IpcClient interface.
 *
 * RETURNS:
 *     error - Error if the stop command fails
 *****************************************************************************/
func (c *unixClient) Stop() error {
	resp, err := c.SendCommand(Command{Type: CmdStop})
	if err != nil {
		return err
	}
	if !resp.Success {
		return fmt.Errorf("stop command failed: %s", resp.Error)
	}
	return nil
}

/*****************************************************************************
 * NAME: GetStatus
 *
 * DESCRIPTION:
 *     Retrieves the current status of the server.
 *     Implements the IpcClient interface.
 *
 * RETURNS:
 *     *Status - Current server status information
 *     error  - Error if status retrieval fails
 *****************************************************************************/
func (c *unixClient) GetStatus() (*Status, error) {
	resp, err := c.SendCommand(Command{Type: CmdStatus})
	if err != nil {
		return nil, err
	}
	if !resp.Success {
		return nil, fmt.Errorf("status command failed: %s", resp.Error)
	}

	// Parse status data
	statusData, ok := resp.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid status data format")
	}

	// Create Status object
	status := &Status{}

	// Parse PID
	if pid, ok := statusData["pid"].(float64); ok {
		status.PID = int(pid)
	}

	// Parse StartTime
	if startTimeStr, ok := statusData["start_time"].(string); ok {
		startTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err == nil {
			status.StartTime = startTime
		}
	}

	// Parse Uptime
	if uptime, ok := statusData["uptime"].(string); ok {
		status.Uptime = uptime
	}

	return status, nil
}

/*****************************************************************************
 * NAME: Close
 *
 * DESCRIPTION:
 *     Closes the connection to the server.
 *     Implements the IpcClient interface.
 *
 * RETURNS:
 *     error - Error if closing the connection fails
 *****************************************************************************/
func (c *unixClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

/*****************************************************************************
 * NAME: ensureConnection
 *
 * DESCRIPTION:
 *     Ensures that a connection to the server is established.
 *     This is an internal method that creates a new connection if one doesn't exist.
 *
 * RETURNS:
 *     error - Error if connection establishment fails
 *****************************************************************************/
func (c *unixClient) ensureConnection() error {
	if c.conn == nil {
		conn, err := net.Dial("unix", c.socketPath)
		if err != nil {
			return fmt.Errorf("failed to connect to socket: %w", err)
		}
		c.conn = conn
	}
	return nil
}

/*****************************************************************************
 * NAME: SetLogLevel
 *
 * DESCRIPTION:
 *     Sends a command to set the log level on the server
 *
 * PARAMETERS:
 *     level - The log level to set (DEBUG, INFO, WARN, ERROR)
 *
 * RETURNS:
 *     error - Error if the command fails
 *****************************************************************************/
func (c *unixClient) SetLogLevel(level string) error {
	cmd := Command{
		Type:     CmdSetLogLevel,
		LogLevel: level,
	}

	resp, err := c.SendCommand(cmd)
	if err != nil {
		return err
	}

	if !resp.Success {
		return fmt.Errorf("failed to set log level: %s", resp.Error)
	}

	return nil
}

/*****************************************************************************
 * NAME: Debug
 *
 * DESCRIPTION:
 *     Sends a debug command to the server
 *
 * PARAMETERS:
 *     subCmd - The debug subcommand (start, stop, status)
 *     port   - The port to use for the debug server (only used with start subcommand)
 *
 * RETURNS:
 *     string - Response message from the server
 *     error  - Error if the command fails
 *****************************************************************************/
func (c *unixClient) Debug(subCmd string, portOrArg interface{}) (string, error) {
	cmd := Command{
		Type:     CmdDebug,
		DebugCmd: subCmd,
	}

	// Handle different types of second argument
	switch v := portOrArg.(type) {
	case int:
		cmd.DebugPort = v
	case string:
		cmd.DebugArg = v
	default:
		if port, ok := portOrArg.(int); ok {
			cmd.DebugPort = port
		}
	}

	resp, err := c.SendCommand(cmd)
	if err != nil {
		return "", err
	}

	if !resp.Success {
		return "", fmt.Errorf("failed to execute debug command: %s", resp.Error)
	}

	result, ok := resp.Data.(string)
	if !ok {
		return "", fmt.Errorf("unexpected response format")
	}

	return result, nil
}


