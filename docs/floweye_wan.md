1. WAN字段
字段		释义	字段类型	必填项	示例	备注
name		WAN接口名称	string	是	wan1	ASCII字符，最长15字节；WAN唯一标识
ifname		网卡名称	string	是	eth0	
mtu		最大传输单元	int	是	1500	范围: 500-4700
固定IP-V4	gwpxy	网关类型	int	是	0/1	0: 正常网关，一般的网关类型。
1: 互联网关，当网关地址是某条用于互联的线路的地址时，需选择互联网关。
	addr	接口ip	string	是	************	无需掩码
当前仅支持ipv4
	gateway	网关地址	string	是	************	
	dns	dns服务器地址	string	否	*******	当设置 DNS 管控策略的时候，这个选项才会起作用。
仅支持配置一个DNS服务器。
	natip	NAT地址池	string	否	0.0.0.0	NAT 时用的地址，不填或 0.0.0.0 则使用线路 IP。
DHCP-V4	option12	DHCP HostName	string/hex	否	host-123	单个options的payload长度不应该超过255字节
总options的长度应该控制在312字节内
	option61	DHCP Vendor class ID	string/hex	否	MSFT 5.0	
	option60	DHCP Client ID	string/hex	否	01080027c1766275	
PPPoE-V4	username	PPPoE账号	string	是	admin	ASCII字符，max 255字节
	password	PPPoE密码	string	是	12345	
	acname	BRAS 名称	string	否		ASCII字符，max 64字节
	svcname	Service名称	string	否		
心跳服务	pingip	心跳服务器1	string	否	0.0.0.0	
	pingip2	心跳服务器2	string	否	0.0.0.0	
	maxdelay	最大延迟	int	否		单位 ms
其它通用设置	dnspxy	DNS代理开关	bool	否	true/false	新建时指定开启也生效
	clonemac	克隆MAC	string	否		不使用自身携带的 MAC 地址，而是使用自定义手工输入的 MAC 地址。
格式：00-00-00-00-00-00，前 4 字节不能为空。
	ping_disable	外网ping不应答	bool	否	true/false	

⚠️暂先仅列出可能使用部分。
⚠️不支持修改WAN 类型（固定IP,DHCP,PPPoE），仅支持删除重建。
⚠️所有操作以“WAN 接口名称” 为索引进行操作，暂不涉及到ID转换。
⚠️删除WAN时PA可能默认会删除与其关联的策略，例如策略路由等等。
⚠️被iWAN引用时，无法删除。

⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。
2. WAN 查询
2.1 查询所有WAN
floweye nat listproxy type=wan
[root@Panabit:~]# floweye nat listproxy type=wan group=
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 112 38689 30.13K 35.41K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 38689 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 38689 0 0 1156 0.17 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 38689 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 38689 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 12 test wan 0.0.0.0 0.0.0.0 0.0.0.0 0/0 c0-a4-76-a8-67-87 0 38689 0 0 0 0.00 0 enable
proxy 13 wan1-2223 eth2 ************ ************ ******* 0/0 00-00-00-00-00-00 0 984 0 0 0 0.00 0 enable
[root@Panabit:~]# floweye nat listproxy type=wan json=1
{"id":1,"name":"wan","type":"dhcpwan","state":1,"standby":0,"disable":0,"inbps":1896,"outbps":2504,"dnsreq":4,"dnsfail":"0.00","flowcnt":103,"mtu":1500,"group":"","consecs":0,"if":"eth0","ip":"***********","gw":"*************","mask":"*************","vlan":"0/0"},{"id":3,"name":"iwan2kphz","type":"iwan","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":1,"mtu":1420,"group":"","consecs":168478,"if":"wan","ip":"*************","gw":"**********","mask":"*************","vlan":"0/0"},{"id":4,"name":"SR-kphz","type":"srpxy","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":5,"name":"iwan-BJ-POP","type":"iwan","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":1158,"dnsfail":"0.17","flowcnt":0,"mtu":1420,"group":"","consecs":168386,"if":"wan","ip":"**********","gw":"**********","mask":"*************","vlan":"0/0"},{"id":6,"name":"SR-BJ-HK","type":"srpxy","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":2173,"dnsfail":"0.05","flowcnt":5,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":7,"name":"iwan-GZ-POP","type":"iwan","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":168477,"if":"wan","ip":"***********","gw":"**********","mask":"*************","vlan":"0/0"},{"id":8,"name":"iwan-SH-POP","type":"iwan","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":168478,"if":"wan","ip":"*************","gw":"************","mask":"*************","vlan":"0/0"},{"id":9,"name":"SR-Singapore","type":"srpxy","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":13,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":10,"name":"SR-SH-US","type":"srpxy","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":1402,"dnsfail":"0.14","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":11,"name":"SR-SH-JP","type":"srpxy","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":3796,"dnsfail":"0.08","flowcnt":11,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":12,"name":"test","type":"iwan","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":0,"if":"wan","ip":"0.0.0.0","gw":"0.0.0.0","mask":"*************","vlan":"0/0"},{"id":13,"name":"wan1-2223","type":"proxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth2","ip":"************","gw":"************","mask":"*************","vlan":"0/0"}
{"id":1,"name":"wan","type":"dhcpwan","state":1,"standby":0,"disable":0,"inbps":2296,"outbps":766848,"dnsreq":4,"dnsfail":"0.00","flowcnt":61,"mtu":1500,"group":"","consecs":0,"if":"eth0","ip":"***********","gw":"*************","mask":"*************","vlan":"0/0"},{"id":13,"name":"wan2","type":"proxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth2","ip":"************","gw":"************","mask":"*************","vlan":"0/0"},{"id":15,"name":"wan3","type":"dhcpwan","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth2","ip":"***********3","gw":"*************","mask":"*************","vlan":"0/0"}
2.2 查询特定WAN信息
可以通过name 或 id 查询
floweye nat getproxy <name>
[root@Panabit:~]# floweye nat getproxy wan1-2223
linkid=17
proxyid=13
name=wan1-2223
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=1
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=1390
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-c0
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=proxy
ifname=eth2
ifstatus=down
addr=************
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=0.0.0.0
pingip2=0.0.0.0
maxdelay=0
dnspxy=0
ping_disable=0
gateway=************
dnsaddr=*******
vrrp_exitstdby=0
netmask=*************

3. WAN 配置
3.1 新增WAN
3.1.1 固定IP-V4
floweye nat addproxy <name=> <ifname=> <mtu=> <ping_disable=0/1> <pingip=> <pingip2=> <maxdelay=> <addr=> <gateway=> <dns=> <vlan=> <vlan1=> <clonemac=> <natip=> <gwpxy=> <dnspxy=> 
[root@Panabit:/usr/panabit/bin]# floweye nat addproxy name=wan1 ifname=eth0 mtu=1500 ping_disable=1 pingip=******* pingip2=0.0.0.0 maxdelay=100 addr=************ gateway=************ dns=******* vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 natip=0.0.0.0 gwpxy=0 dnspxy=

[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 87 45717 13.13K 7.93K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 45717 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 45717 0 0 1219 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 45717 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 45717 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 12 test wan 0.0.0.0 0.0.0.0 0.0.0.0 0/0 c0-a4-76-a8-67-87 0 45717 0 0 0 0.00 0 enable
proxy 13 wan1 eth0 ************ ************ ******* 0/0 00-00-00-00-00-00 0 2 0 0 0 0.00 0 enable

[root@Panabit:/usr/panabit/bin]# floweye nat getproxy wan1
linkid=17
proxyid=13
name=wan1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=1
disable=0
ping_disable=1
datattl=0
hbfail=0
arpttl=42
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-c0
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=proxy
ifname=eth0
ifstatus=up
addr=************
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=*******
pingip2=0.0.0.0
maxdelay=100
dnspxy=0
ping_disable=1
gateway=************
dnsaddr=*******
vrrp_exitstdby=0
netmask=*************
3.1.2 DHCP-V4
floweye nat adddhcpwan <name=> <ifname=> <mtu=> <ping_disable=> <pingip=> <pingip2=> <maxdelay=> <vlan=> <vlan1=> <clonemac=> <dnspxy=> <dhcp_option=> <dhcp_option=>
[root@Panabit:/usr/panabit/bin]# floweye nat rmvproxy wan1
[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 95 46191 840 26.75K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 46191 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46191 0 25.38K 1224 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46191 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 46191 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
[root@Panabit:/usr/panabit/bin]# floweye nat adddhcpwan name=wan1 ifname=eth0 mtu=1500 ping_disable=1 pingip=******* pingip2=0.0.0.0 maxdelay=100 vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 dnspxy= dhcp_option=12,str,host-123 dhcp_option=61,str,MSFT 5.0 dhcp_option=60,hex,010800279a4f624e
[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 85 46202 6.92K 6.70K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 46202 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46202 0 0 1224 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46202 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 46202 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
dhcpwan 12 wan1 eth0 ************ ************* ************* 0/0 00-00-00-00-00-00 0 2 0 0 0 0.00 0 enable
[root@Panabit:/usr/panabit/bin]# floweye nat getproxy wan1
linkid=16
proxyid=12
name=wan1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=48.01
minping_ms=46.23
curping_ms=46.34
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=17
disable=0
ping_disable=1
datattl=0
hbfail=0
arpttl=29
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=46233
maxping=48008
curping=46344
gottime=0
bigpkts=0
active=1
state=1
type=dhcpwan
ifname=eth0
ifstatus=up
addr=************
flowcnt=0
vlan=0
vlan1=0
linkup=1
gatewayup=1
gwmac=c0-a4-76-a8-67-87
pingip=*******
pingip2=0.0.0.0
maxdelay=100
dnspxy=0
ping_disable=1
dhcp_state=DONE
dhcp_lease=2700
dhcp_svrid=*************
dhcp_svrmac=c0-a4-76-a8-67-87
dhcp_ltime=2025-04-11/16:57:14
gateway=*************
dnsaddr=*************
secondary_dns=0.0.0.0
netmask=*************
dhcp_option=61,str,MSFT
dhcp_option=12,str,host-123
3.1.3 PPPoE-V4
floweye nat addpppoe <name=> <ifname=> <mtu=> <ping_disable=> <pingip=> <pingip2=> <maxdelay=> <username=> <password=> <waitime=> <ipv6=> <vlan=> <vlan1=> <clonemac=> <acname=> <svcname=> <dnspxy=>
[root@Panabit:/usr/panabit/bin]# floweye nat rmvproxy wan1
[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 85 46380 16.06K 25.64K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 46380 0 2.17K 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 751 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46380 0 1.17K 1225 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46380 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 46380 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 783 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
[root@Panabit:/usr/panabit/bin]# floweye nat addpppoe name=wan1 ifname=eth0 mtu=1460 ping_disable=1 pingip=******* pingip2=0.0.0.0 maxdelay=100 username=admin password=123456 waitime=5 ipv6=0 vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 acname=BRAS1 svcname=Service1 dnspxy=
[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 82 46389 10.83K 26.90K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 46389 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46389 0 0 1225 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 46389 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 46389 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
pppoe 12 wan1 eth0 0.0.0.0 0.0.0.0 0.0.0.0 0/0 00-00-00-00-00-00 0 2 0 0 0 0.00 0 enable
[root@Panabit:/usr/panabit/bin]# floweye nat getproxy wan1
linkid=16
proxyid=12
name=wan1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1460
nextipid=1
disable=0
ping_disable=1
datattl=0
hbfail=0
arpttl=15
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=pppoe
ifname=eth0
ipv6en=0
ipv6ok=0
ifstatus=up
addr=0.0.0.0
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=*******
pingip2=0.0.0.0
maxdelay=100
dnspxy=0
ping_disable=1
cfgmtu=1460
gateway=0.0.0.0
pppoemtu=1460
dnsaddr=0.0.0.0
username=admin
password=123456
sessionid=0x0000
acname=BRAS1
svcname=Service1
peermac=ff:ff:ff:ff:ff:ff
brasname=NULL
gateway=0.0.0.0
lcp_thisacked=0
lcp_peeracked=0
lcp_thismagic=0x916519d0
lcp_peermagic=0x00000000
lcp_thismru=1460
lcp_peermru=0
lastrid=1
ipcp_nakip=0.0.0.0
ipcp_nakdns=0.0.0.0
ipcp_nakdns2=0.0.0.0
ipcp_ackip=0.0.0.0
ipcp_ackdns=0.0.0.0
ipcp_ackdns2=0.0.0.0
ipcp_nodns=0
ipcp_nodns2=0
ackmyifid=0
myifid=0
peerifid=0
flags=0x0
ipv6=0/0/0[pppoe/en/ok]
state=PRESTART
lasterr=OK
authtype=pap
dialfail=0
lastdial=1970-01-01/08:00:00
waitime=5
waitime_left=3
last_echo_stat=**********/**********[lastechotime/lastechoresp]
last_term_cause=1
3.2 修改WAN
floweye nat setproxy/setpppoe/setdhcpwan <对应类型配置>
修改时为全量配置替换修改.
固定IP-V4
floweye nat setproxy name=wan1 newname=wan1 ifname=eth0 mtu=1500 ping_disable=0 pingip=******* pingip2=0.0.0.0 maxdelay=100 addr=************ gateway=************ dns=******* vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 natip=0.0.0.0 gwpxy=0 dnspxy=0

PPPoE-V4
floweye nat setpppoe name=wan1 newname=wan1 ifname=eth2 mtu=1460 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 username=admin password=123455 waitime=5 ipv6=0 vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 acname=NULL svcname=NULL dnspxy=0

DHCP-V4
floweye nat setdhcpwan name=wan1 newname=wan1 ifname=eth2 mtu=1500 ping_disable=0 pingip=******* pingip2=0.0.0.0 maxdelay=0 vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 dnspxy=0 dhcp_option=12,str,host-123 dhcp_option=61,str,MSFT dhcp_option=60,hex,010800279a4f624e
[root@Panabit:/usr/panabit/bin]# floweye nat getproxy wan1 | grep ping_dis
ping_disable=1
ping_disable=1
[root@Panabit:/usr/panabit/bin]#
[root@Panabit:/usr/panabit/bin]# floweye nat setproxy name=wan1 newname=wan1 ifname=eth0 mtu=1500 ping_disable=0 pingip=******* pingip2=0.0.0.0 maxdelay=100 addr=************ gateway=************ dns=******* vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 natip=0.0.0.0 gwpxy=0 dnspxy=0
[root@Panabit:/usr/panabit/bin]# floweye nat getproxy wan1 | grep ping_dis
ping_disable=0
ping_disable=0
3.3 删除WAN
floweye nat rmvproxy <name>
可以通过name 或 id 进行删除。
[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 83 45853 9.57K 2.81K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 45853 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 45853 0 0 1219 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 45853 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 45853 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 12 test wan 0.0.0.0 0.0.0.0 0.0.0.0 0/0 c0-a4-76-a8-67-87 0 45853 0 0 0 0.00 0 enable
proxy 13 wan1 eth0 ************ ************ ******* 0/0 00-00-00-00-00-00 0 138 0 0 0 0.00 0 enable

[root@Panabit:/usr/panabit/bin]# floweye nat rmvproxy wan1
[root@Panabit:/usr/panabit/bin]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 72 45869 84.96K 45.79K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 45869 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan ********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 45869 0 0 1220 0.16 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 45869 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 45869 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 12 test wan 0.0.0.0 0.0.0.0 0.0.0.0 0/0 c0-a4-76-a8-67-87 0 45869 0 0 0 0.00 0 enable


