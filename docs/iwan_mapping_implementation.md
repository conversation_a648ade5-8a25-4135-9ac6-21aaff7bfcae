# iWAN Mapping模块配置实现文档

## 概述

iWAN Mapping模块负责处理iWAN映射配置，将WAN线路的特定端口映射到iWAN服务。本文档描述了iWAN Mapping模块的实现方案和关键代码。

## 配置项

根据`floweye_iwan_mapping.md`文档和`message.proto`定义，iWAN Mapping模块支持以下配置项：

1. **基本配置**
   - 映射线路名称(proxy) - 必填，WAN线路名称
   - 映射端口(port) - 必填，映射端口
   - iWAN服务名称(server) - 必填，iWAN服务名称，删除时设为NULL

## 配置一致性和同步机制

根据`Configuration_Consistency_and_Synchronization_Mechanism.md`文档，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye iwanmap list`命令）
   - 将配置缓存在内存中

2. **配置处理策略**
   - 对象不在全量配置中：执行创建流程
   - 对象已在全量配置中：
     - 配置一致：忽略，无需修改
     - 配置不一致：替换配置
   - 每处理完一个对象后，从全量配置缓存中移除

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 删除本地存在但全量同步中未包含的配置
   - 清理资源

### 增量同步

1. **配置变更**
   - 接收到配置变更任务
   - 获取本地配置
   - 比较配置差异
   - 应用配置变更

2. **配置删除**
   - 接收到配置删除任务
   - 验证配置是否存在
   - 执行删除操作

## 实现细节

### 数据结构

1. **IwanMappingConfig**
   - 表示iWAN Mapping配置
   - 包含映射线路名称、映射线路ID、映射端口、iWAN服务名称、iWAN服务ID等字段

2. **IwanMappingKey**
   - 表示iWAN Mapping的唯一标识
   - 由映射线路名称和映射端口组成

### 处理器实现

1. **IwanMappingProcessor**
   - 实现TaskProcessor接口
   - 管理本地配置缓存
   - 处理配置任务

### 关键方法

1. **GetTaskType()**
   - 返回处理器处理的任务类型
   - 返回`pb.TaskType_TASK_IWAN_MAPPING`

2. **ProcessTask()**
   - 处理iWAN Mapping任务
   - 根据任务动作分发到不同的处理方法

3. **StartFullSync()**
   - 开始全量同步
   - 获取本地全量配置

4. **EndFullSync()**
   - 结束全量同步
   - 处理剩余的本地配置
   - 清理资源

5. **handleConfigChange()**
   - 处理新建和编辑配置任务
   - 实现配置一致性检查
   - 构建命令参数
   - 执行配置命令并验证结果

6. **handleDeleteConfig()**
   - 处理删除配置任务
   - 构建删除命令
   - 执行删除命令并验证结果

7. **refreshLocalConfigs()**
   - 获取所有本地iWAN Mapping配置
   - 使用结构化的方式解析命令输出
   - 将结果转换为易于处理的结构化数据

8. **VerifyIwanMappingConfig()**
   - 验证配置是否成功应用
   - 确保命令执行成功后配置确实生效

9. **CompareIwanMappingConfig()**
   - 比较请求的配置与本地配置
   - 决定是否需要修改配置

## 命令行交互

iWAN Mapping模块通过以下命令与系统交互：

1. **查询所有iWAN Mapping**
   ```
   floweye iwanmap list
   ```

2. **配置iWAN Mapping**
   ```
   floweye iwanmap set proxy=<映射线路名称> port=<映射端口> server=<iWAN服务名称>
   ```

3. **删除iWAN Mapping**
   ```
   floweye iwanmap set proxy=<映射线路名称> port=<映射端口> server=NULL
   ```

## 特殊处理

1. **统一的命令接口**
   - 新增、修改、删除操作都使用相同的`floweye iwanmap set`命令，只是参数不同
   - 删除操作通过设置`server=NULL`实现

2. **全量查询**
   - 只能通过`floweye iwanmap list`获取全量配置
   - 没有查询特定映射的方法

3. **唯一性约束**
   - 一个WAN的唯一端口对应一个iWAN服务
   - 如果对应service改变，会被替换

## 注意事项

1. 映射线路必须存在，否则配置失败
2. iWAN服务必须存在，否则配置失败
3. 删除操作通过设置`server=NULL`实现
4. 命令返回0不一定代表成功，需要再次查询确认配置是否生效

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 必填字段缺失：记录错误并返回

## 测试方案

1. 单元测试：验证处理器的基本功能和错误处理
2. 集成测试：验证与floweye命令的交互
3. 系统测试：验证配置在实际环境中的生效情况
4. 全量同步测试：验证全量同步机制
5. 增量同步测试：验证增量同步机制

## 实现改进

1. **结构化配置解析**
   - 使用map来解析命令输出
   - 提高了解析效率和可读性

2. **配置一致性检查**
   - 在应用配置前比较配置差异
   - 避免不必要的配置操作

3. **错误处理和日志记录**
   - 详细记录操作和错误信息
   - 便于问题排查和调试

4. **配置验证**
   - 在应用配置后验证配置是否生效
   - 确保配置操作的可靠性
