1. 配置字段
1.1 iWAN 线路字段
字段		释义	字段类型	必填项	示例	备注
name		iWAN线路名称	string	是	iwan1	ASCII字符，最长15字节；iWAN唯一标识
ifname		承载的线路 ID	string	是	1	Agent需要进行ifname->if ID的转化；ifname=1
ifname2		备份承载线路ID	string	是	1	
mtu		最大传输单元	int	是	1500	范围: 500-4700
iWAN参数	svraddr	服务器ip/域名	string	是	***********	svraddr=**************
	svrport	服务器端口	int	是	8080	svrport=8000
	username	iWAN账号	string	是	admin	username=admin
	password	iWAN密码	string	是	123456	password=123456
	encrypt	是否加密	bool	否	0	默认为0
	link	分段标识	string	否	123	默认为0
心跳服务	pingip	心跳服务器1	string	否	0.0.0.0	默认为0.0.0.0
	pingip2	心跳服务器2	string	否	0.0.0.0	默认为0.0.0.0
	maxdelay	最大延迟	int	否		单位 ms；默认为0
dnspxy		DNS代理开关	bool	否	true/false	默认为空
⚠️暂先仅列出可能使用部分。
⚠️所有操作以“iWAN 接口名称” 为标识进行操作。
⚠️添加iWAN时，需确保其承载的线路已添加。
⚠️删除iWAN时PA可能默认会删除与其关联的策略，例如策略路由等等。
⚠️WAN被iWAN引用时，无法删除WAN。

⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

1.2 iWAN 服务字段
字段		释义	字段类型	必填项	示例	备注
name		iWAN服务名称	string	是	iwan1	ASCII字符，最长15字节；iWAN服务唯一标识
addr		服务器网关	string	是		************
mtu		最大传输单元	int	是	1500	范围: 500-4700
auth		认证方式	string	是	local/radius/free	local: 本地认证
radiu: Radius 认证
free: 免认证
当前默认为本地认证，无法配置。
pool		地址池(用户组)	int	是	128	

⚠️iWAN服务添加时，确保依赖的用户组已添加。
⚠️当引用的用户组被删除时，引用的用户组变为UNKNOWN，可以重新进行赋值。

2. 查询
2.1 iWAN线路查询
2.1.1 查询所有iWAN线路
floweye nat listproxy type=wan json=1
[root@Panabit:~]# floweye nat listproxy type=wan json=1
{"id":1,"name":"wan","type":"dhcpwan","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth0","ip":"0.0.0.0","gw":"0.0.0.0","mask":"*************","vlan":"0/0"},{"id":2,"name":"wan1","type":"proxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth0","ip":"************","gw":"************","mask":"*************","vlan":"0/0"},{"id":4,"name":"iwan1","type":"iwan","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":0,"if":"wan","ip":"0.0.0.0","gw":"0.0.0.0","mask":"*************","vlan":"0/0"},{"id":5,"name":"sr1","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":7,"name":"iwan2","type":"iwan","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1420,"group":"","consecs":0,"if":"wan1","ip":"0.0.0.0","gw":"0.0.0.0","mask":"*************","vlan":"0/0"}
⚠️floweye命令行支持过滤 type=iwan，但防止其过滤存在bug，我们获取指定wan配置时通过判断type=iwan进行过滤。

2.1.1 查询指定iWAN线路
floweye nat getproxy <iWAN名称>
[root@Panabit:~]# floweye nat getproxy iwan2
linkid=11
proxyid=7
name=iwan2
myparent=wan1
hasaddr4=1
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1420
nextipid=1
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=1870
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-60
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=iwan
ifname=wan1
ifname2=wan
parent=wan1
interface_name=eth0
ifstatus=down
svraddr=**************
svrport=8000
myport=1077
srid=0
addr=0.0.0.0
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=0.0.0.0
pingip2=0.0.0.0
maxdelay=0
dnspxy=0
ping_disable=0
gateway=0.0.0.0
pppoemtu=1420
dnsaddr=0.0.0.0
netmask=*************
cfgencrypt=0
peerencrypt=0
cfgduppkt=0
peerduppkt=0
state=ALLOC
peerok=1
peerv6=0
iwansid=0
peerip=**************
peerport=8000
myip=0.0.0.0
myport=1077
token=0
cfgmtu=1420
peermtu=0
sdwan_ip=0.0.0.0
sdwan_gateway=0.0.0.0
sdwan_dns0=0.0.0.0
sdwan_dns1=0.0.0.0
username=admin
password=123456
brg_l2vlan=0
brg_id=0
srlink_type=CLNT
srlink_link=0
srlink_sdwpxyid=7
srlink_hits=0

2.2 iWAN服务查询
2.2.1 查询所有iWAN服务
floweye nat listproxy json=1 type=iwansvc
[root@Panabit:~]# floweye nat listproxy json=1 type=iwansvc
{"id":12,"name":"iwan-test","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":1299,"pname":"test1299","dns0":"*******","dns1":"0.0.0.0","clntcnt":0,"auth":"free","radid":0,"radname":"DefaultRadius"},{"id":14,"name":"aaa","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"*************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"}
2.2.2 查询指定iWAN服务
floweye nat getproxy <name=iwan服务名称>
[root@Panabit:~]# floweye nat getproxy name=iwan-test
linkid=16
proxyid=12
name=iwan-test
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1436
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=0
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
active=1
state=0
type=iwansvc
ifname=NULL
ifstatus=up
addr=************
ipv6_prefix=::
ipv6_prefixlen=0
ipv6_dns=::
ipv6_gatway=0
vlan=0
vlan1=0
clntcnt=0
linkup=1
auth=free
radsvrid=0
radsvr=DefaultRadius
pool=1299
poolname=test1299

3. 配置
3.1 iWAN线路配置
3.1.1 新增iWAN线路
floweye nat addiwan <name=> <ifname=> <mtu=> <ping_disable=> <pingip=> <pingip2=> <maxdelay=> <ipv6=> <svraddr=> <svrport=> <username=> <password=> <encrypt=> <srid=> <dnspxy=> <link=> <ifname2=>
floweye nat addiwan name=iwan2 ifname=2 mtu=1420 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 ipv6=0 svraddr=************** svrport=8000 username=admin password=123456 encrypt=0 srid= dnspxy= link=0 ifname2=1
[root@Panabit:~]# floweye nat addiwan name=iwan2 ifname=2 mtu=1420 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 ipv6=0 svraddr=************** svrport=8000 username=admin password=123456 encrypt=0 srid= dnspxy= link=0 ifname2=1
[root@Panabit:~]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 0.0.0.0 0.0.0.0 0.0.0.0 0/0 00-00-00-00-00-00 0 32695 0 0 0 0.00 0 enable
proxy 2 wan1 eth0 ************ ************ ******* 0/0 00-00-00-00-00-00 0 32695 0 0 0 0.00 0 enable
iwan 4 iwan1 wan 0.0.0.0 0.0.0.0 0.0.0.0 0/0 00-00-00-00-00-00 0 32695 0 0 0 0.00 0 enable
srpxy 5 sr1 NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 0 enable
iwan 7 iwan2 wan1 0.0.0.0 0.0.0.0 0.0.0.0 0/0 00-00-00-00-00-00 0 2013 0 0 0 0.00 0 enable

[root@Panabit:~]# floweye nat getproxy iwan2
linkid=11
proxyid=7
name=iwan2
myparent=wan1
hasaddr4=1
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1420
nextipid=1
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=2030
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-60
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=iwan
ifname=wan1
ifname2=wan
parent=wan1
interface_name=eth0
ifstatus=down
svraddr=**************
svrport=8000
myport=1077
srid=0
addr=0.0.0.0
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=0.0.0.0
pingip2=0.0.0.0
maxdelay=0
dnspxy=0
ping_disable=0
gateway=0.0.0.0
pppoemtu=1420
dnsaddr=0.0.0.0
netmask=*************
cfgencrypt=0
peerencrypt=0
cfgduppkt=0
peerduppkt=0
state=ALLOC
peerok=1
peerv6=0
iwansid=0
peerip=**************
peerport=8000
myip=0.0.0.0
myport=1077
token=0
cfgmtu=1420
peermtu=0
sdwan_ip=0.0.0.0
sdwan_gateway=0.0.0.0
sdwan_dns0=0.0.0.0
sdwan_dns1=0.0.0.0
username=admin
password=123456
brg_l2vlan=0
brg_id=0
srlink_type=CLNT
srlink_link=0
srlink_sdwpxyid=7
srlink_hits=0
⚠️添加iWAN时，需确保其承载的线路已添加。
3.1.2 修改iWAN线路
floweye nat setiwan <name=> <newname=> <ifname=> <mtu=> <ping_disable=> <pingip=> <pingip2=> <maxdelay=> <ipv6=> <svraddr=> <svrport=> <username=> <password=> <encrypt=> <srid=> <dnspxy=> <link=> <ifname2=>
floweye nat setiwan name=iwan1 newname=iwan1 ifname=1 mtu=1300 ping_disable=0 pingip=******* pingip2=******* maxdelay=200 ipv6=0 svraddr=************** svrport=223 username=admin1 password=1234567 encrypt=1 srid=0 dnspxy=0 link=223 l2vlan=0
[root@Panabit:~]# floweye nat getproxy iwan2
linkid=11
proxyid=7
name=iwan2
myparent=wan1
hasaddr4=1
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1420
nextipid=1
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=2030
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-60
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=iwan
ifname=wan1
ifname2=wan
parent=wan1
interface_name=eth0
ifstatus=down
svraddr=**************
svrport=8000
myport=1077
srid=0
addr=0.0.0.0
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=0.0.0.0
pingip2=0.0.0.0
maxdelay=0
dnspxy=0
ping_disable=0
gateway=0.0.0.0
pppoemtu=1420
dnsaddr=0.0.0.0
netmask=*************
cfgencrypt=0
peerencrypt=0
cfgduppkt=0
peerduppkt=0
state=ALLOC
peerok=1
peerv6=0
iwansid=0
peerip=**************
peerport=8000
myip=0.0.0.0
myport=1077
token=0
cfgmtu=1420
peermtu=0
sdwan_ip=0.0.0.0
sdwan_gateway=0.0.0.0
sdwan_dns0=0.0.0.0
sdwan_dns1=0.0.0.0
username=admin
password=123456
brg_l2vlan=0
brg_id=0
srlink_type=CLNT
srlink_link=0
srlink_sdwpxyid=7
srlink_hits=0

[root@Panabit:~]# floweye nat setiwan name=iwan2 ifname=2 mtu=1420 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 ipv6=0 svraddr=************** svrport=8000 username=admin password=123456 encrypt=0 srid= dnspxy= link=0 ifname2=2
[root@Panabit:~]# floweye nat getproxy iwan2
linkid=11
proxyid=7
name=iwan2
myparent=wan1
hasaddr4=1
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1420
nextipid=1
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=2159
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-60
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=iwan
ifname=wan1
ifname2=wan1
parent=wan1
interface_name=eth0
ifstatus=down
svraddr=**************
svrport=8000
myport=1077
srid=0
addr=0.0.0.0
flowcnt=0
vlan=0
vlan1=0
linkup=0
gatewayup=0
gwmac=00-00-00-00-00-00
pingip=0.0.0.0
pingip2=0.0.0.0
maxdelay=0
dnspxy=0
ping_disable=0
gateway=0.0.0.0
pppoemtu=1420
dnsaddr=0.0.0.0
netmask=*************
cfgencrypt=0
peerencrypt=0
cfgduppkt=0
peerduppkt=0
state=ALLOC
peerok=1
peerv6=0
iwansid=0
peerip=**************
peerport=8000
myip=0.0.0.0
myport=1077
token=0
cfgmtu=1420
peermtu=0
sdwan_ip=0.0.0.0
sdwan_gateway=0.0.0.0
sdwan_dns0=0.0.0.0
sdwan_dns1=0.0.0.0
username=admin
password=123456
brg_l2vlan=0
brg_id=0
srlink_type=CLNT
srlink_link=0
srlink_sdwpxyid=7
srlink_hits=0

3.1.3 删除iWAN线路
floweye nat rmvproxy <iWAN名称>
[root@Panabit:~]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 61 48598 1.49K 2.78K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 48599 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 48598 0 0 564 0.00 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 48598 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 48598 0 0 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 12 iwan1 wan 0.0.0.0 0.0.0.0 0.0.0.0 0/0 c0-a4-76-a8-67-87 0 357 0 0 0 0.00 0 enable
srpxy 15 SR-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
[root@Panabit:~]# floweye nat rmvproxy iwan1
[root@Panabit:~]# floweye nat listproxy type=wan
dhcpwan 1 wan eth0 *********** ************* ************* 0/0 c0-a4-76-a8-67-87 67 48675 60.69K 330.54K 4 0.00 1 enable
iwan 3 iwan2kphz wan ************* ********** *************** 0/0 c0-a4-76-a8-67-87 1 48675 0 0 0 0.00 1 enable
srpxy 4 SR-kphz NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 5 iwan-BJ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 48675 0 0 564 0.00 1 enable
srpxy 6 SR-BJ-HK NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
iwan 7 iwan-GZ-POP wan *********** ********** ******* 0/0 c0-a4-76-a8-67-87 0 48675 0 0 0 0.00 1 enable
iwan 8 iwan-SH-POP wan ************* ************ ******* 0/0 c0-a4-76-a8-67-87 0 48675 0 76.84K 0 0.00 1 enable
srpxy 9 SR-Singapore NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 10 SR-SH-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable
srpxy 11 SR-SH-JP NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 55.05K 0 1 enable
srpxy 15 SR-US NULL 0.0.0.0 0 1500 NULL 0.0.0.0 0.0.0.0 0 0 1 enable

3.2 iWAN服务配置
3.2.1 新增iWAN服务
floweye nat addiwansvc <name=> <addr=> <mtu=> <auth=> <pool=> <radsvr=> <prefix6len=>
[root@Panabit:~]# floweye nat listproxy json=1 type=iwansvc
{"id":14,"name":"aaa","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"*************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"}
[root@Panabit:~]# floweye nat addiwansvc name=iwan-test addr=************ mtu=1436 auth=local pool=2064 radsvr=0 prefix6len=0
[root@Panabit:~]# floweye nat listproxy json=1 type=iwansvc
{"id":12,"name":"iwan-test","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"},{"id":14,"name":"aaa","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"*************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"}
[root@Panabit:~]# floweye nat getproxy name=iwan-test
linkid=16
proxyid=12
name=iwan-test
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1436
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=0
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
active=1
state=0
type=iwansvc
ifname=NULL
ifstatus=up
addr=************
ipv6_prefix=::
ipv6_prefixlen=0
ipv6_dns=::
ipv6_gatway=0
vlan=0
vlan1=0
clntcnt=0
linkup=1
auth=local
radsvrid=0
radsvr=DefaultRadius
pool=2064
poolname=TempAccounts
3.2.2 修改iWAN服务
floweye nat setiwansvc <id=> <name=> <newname=> <addr=> <mtu=> <auth=> <pool=> <radsvr=> <prefix6len=>
[root@Panabit:~]# floweye nat getproxy name=iwan-test
linkid=16
proxyid=12
name=iwan-test
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1436
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=0
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
active=1
state=0
type=iwansvc
ifname=NULL
ifstatus=up
addr=************
ipv6_prefix=::
ipv6_prefixlen=0
ipv6_dns=::
ipv6_gatway=0
vlan=0
vlan1=0
clntcnt=0
linkup=1
auth=local
radsvrid=0
radsvr=DefaultRadius
pool=2064
poolname=TempAccounts
[root@Panabit:~]# floweye nat setiwansvc id=12 name=iwan-test newname=iwan-test123 addr=************ mtu=******** auth=free pool=1299 radsvr=0 prefix6len=0
[root@Panabit:~]# floweye nat getproxy name=iwan-test
NEXIST
[root@Panabit:~]# floweye nat getproxy name=iwan-test123
linkid=16
proxyid=12
name=iwan-test123
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1436
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=0
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
active=1
state=0
type=iwansvc
ifname=NULL
ifstatus=up
addr=************
ipv6_prefix=::
ipv6_prefixlen=0
ipv6_dns=::
ipv6_gatway=0
vlan=0
vlan1=0
clntcnt=0
linkup=1
auth=free
radsvrid=0
radsvr=DefaultRadius
pool=1299
poolname=test1299
3.2.3 删除iWAN服务
floweye nat rmvproxy <iwan服务名>
[root@Panabit:~]# floweye nat listproxy json=1 type=iwansvc
{"id":12,"name":"iwan-test123","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":1299,"pname":"test1299","dns0":"*******","dns1":"0.0.0.0","clntcnt":0,"auth":"free","radid":0,"radname":"DefaultRadius"},{"id":14,"name":"aaa","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"*************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"}
[root@Panabit:~]# floweye nat rmvproxy iwan-test123
[root@Panabit:~]# floweye nat listproxy json=1 type=iwansvc
{"id":14,"name":"aaa","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"*************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"}[root@Panabit:~]#
