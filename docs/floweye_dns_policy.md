1. DNS管控策略字段
字段			释义		字段类型	必填项	示例	备注
cookie			策略cookie		int	是	1	唯一标识; uint32
previous			前一个策略cookie		int	是	0	首个策略为0
追加策略为-1
disable			是否启用		int	否	0/1	0为启用，1位禁用
schtime			策略时段Id		int	否		0为任意
用户/访问者	inip		源地址	IP	IpAddr	否	***************/32	src和dst分别代表源地址和目的地址src=,4代表IP群组id为4的群组代表原地址dst=,2,代表IP群组id为2的群组代表目的地址
src/des=mac.1代表MAC组的DefaultGroup
src/des=mac.2代表MAC组的MAC分流
src/des=,pppoe.1代表用户组的DefaultGroup
src/des=,pppoe.2代表用户组的MAC分流
src/des=,dns.1,代表域名群组id为1的群组
				IP段	IpRange	否	*************-***************	
				IP群组	string	否	ip-oversea
IP群组以名称作为唯一标识，agent通过名称反查ID	
				Mac群组	int	否	3	
				用户组	int	否	3	
				用户	string	否	zhangsan	
	pool		用户组Id		int	否	0:any	pool=any 为任意
	usrtype		用户类型		int	否	0: any
1:ippxy
2:nonippxy	usrtype=any为任意
usrtype=ippxy为代播用户，usrtype=nonippxy为非代播用户
服务者/服务	outip		目的地址	同源地址	mix	否	当指定多个时，使用','连接,例如：
dst=,*******/32,***********-***********00,mac.2064,pppoe.1,acct.randy	
	app		特征库协议		string	否	app=any
app=httpgroup	应用协议
floweye app showtree all 可查
	dns		域名群组		string	否	dns=domain
dns=空为任意	agent需要通过域名群组名称反查id
dns=0为任意
	atype		查询类型		int	否	0: any
1: ipv4
2: ipv6	atype=any
atype=onlya
atype=onlyaaaa
接口	inif		源接口		string	否	inif=any
inif=allposvr
inif=if.eth1	inif=any 为任意
inif=allposvr代表PPPoE服务，还可以选择以下（自己的网卡接口，Wan口，Lan口）
	bridge		路径网桥接口		int	否	1	
	vlan		vlan id		intRange	否	0-100	XX-XX表示单个数值，XX-NN表示一个范围数值，0表示任意
执行动作	action		执行策略		int	是	1: pass
2: deny
3: rdr
4: reply
5: limit
6: ippxy
7: zeroreply	放行
丢弃
牵引
解析
QPS限制
代播重定向 
无名应答
	action=pass/ippxy	ipqps	单用户qps		int	否	0	单位/s，单个IP每秒最大请求数，0为不限制
		next	是否继续匹配		bool	否	0/1	匹配后状态，0为继续匹配，1为停止匹配
	action=rdr	actarg	牵引线路		string	是	wan1	
		nosnat	不改变源地址		bool	否	0/1	1为不改变源地址
		dnslist	牵引dns		IpAddr	否	dnslist=*******,***************	多个IP用逗号分割
	action=reply	actarg	解析ip		IpAddr	是	actarg=***********,**********	多个IP用逗号分割
	action=limit	ipqps	单用户qps		int	否	0	单位/s，单个IP每秒最大请求数，0为不限制
		actarg	总qps		int	否	0	单位/s，最大请求数，0为不限制
		next	是否继续匹配		bool	否	0/1	匹配后状态，0为继续匹配，1为停止匹配

默认值格式示例：
floweye dnspolicy add id=5555 inip= outip= dns=0 vlan=0 atype=any inif=any app=any action=pass next=0 ipqps=0 actarg=null usrtype=any bridge=0 pool=0 dnslist=0.0.0.0 schtime=0

2. DNS管控策略查询
2.1 查询所有DNS管控策略
floweye dnspolicy list json=1
[root@Panabit:~]# floweye dnspolicy list json=1
{"polno":1,"iniftype":"null","inif":"null","vlan":"0","srcip":"","dstip":"","dnstype":"usr","dnsid":0,"dnsname":"any","sch_id":0,"sch_name":"NULL","active":1,"bridge":0,"action":"pass","actarg":"null","appname":"any","appcname":"任意","qps0":0,"qps1":0,"droppkts":0,"pxytype":"null","pxyactive":0,"dnslist":"","disabled":0,"next":0,"ipqps":0,"matchpkts":0,"usrtype":"any","ugroup_id":0,"ugroup_name":"any","atype":"any","nosnat":0},{"polno":50,"iniftype":"pxy","inif":"wan","vlan":"0-100","srcip":"ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy","dstip":"","dnstype":"usr","dnsid":1,"dnsname":"domain","sch_id":1,"sch_name":"time_1","active":1,"bridge":1,"action":"pass","actarg":"null","appname":"httpgroup","appcname":"HTTP协议","qps0":0,"qps1":0,"droppkts":0,"pxytype":"null","pxyactive":0,"dnslist":"","disabled":0,"next":0,"ipqps":0,"matchpkts":0,"usrtype":"any","ugroup_id":2,"ugroup_name":"new_group","atype":"onlya","nosnat":0},{"polno":100,"iniftype":"if","inif":"eth1","vlan":"0","srcip":"ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy","dstip":"","dnstype":"usr","dnsid":2,"dnsname":"domain2","sch_id":1,"sch_name":"time_1","active":1,"bridge":1,"action":"rdr","actarg":"wan ","appname":"any","appcname":"任意","qps0":0,"qps1":0,"droppkts":0,"pxytype":"pxy","pxyactive":1,"dnslist":"","disabled":0,"next":1,"ipqps":100,"matchpkts":0,"usrtype":"ippxy","ugroup_id":1,"ugroup_name":"DefaultGroup","atype":"onlyaaaa","nosnat":1}
2.2 查询指定DNS管控策略
通过Id查询：
floweye dnspolicy get <id=>
[root@Panabit:~]# floweye dnspolicy get id=100
id=100
cookie=11100
inip=ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy
outip=
dns=2
bridge=1
next=1
disable=0
ipqps=100
atype=onlyaaaa
nosnat=1
action=rdr
actarg=wan
inif=eth1
vlan=0
app=any
appid=any
appname=任意
usrtype=ippxy
pool=1
pname=DefaultGroup
schtime=1
schtime_name=time_1
dnslist=
cmpsize=96
mopoff=40
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0
通过cookie查询：
floweye dnspolicy get <cookie=>
[root@Panabit:~]# floweye dnspolicy get cookie=11100
id=100
cookie=11100
inip=ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy
outip=
dns=2
bridge=1
next=1
disable=0
ipqps=100
atype=onlyaaaa
nosnat=1
action=rdr
actarg=wan
inif=eth1
vlan=0
app=any
appid=any
appname=任意
usrtype=ippxy
pool=1
pname=DefaultGroup
schtime=1
schtime_name=time_1
dnslist=
cmpsize=96
mopoff=40
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0

3. DNS管控策略 设置
3.1 新增 DNS管控策略
floweye dnspolicy add <id=> <inip=> <outip=> <dns=> <vlan=> <atype=> <inif=> <app=> <action=> <next=> ><ipqps=> <actarg=> <usrtype=> <bridge=> <pool=> <dnslist=> <schtime=> <cookie=>

inip：源地址（支持IP、范围、网段、IP群组、用户组、账号。每个条件用英文逗号“,”前后区分。例如：条件为***********、***********/24、IP群组ID1、用户组ID2、用户账号test1，对应参数为“src=,***********,,***********/24,,1,,pppoe.2,,acct.test1,”）
[root@Panabit:~]# floweye dnspolicy add id=1 inip= outip=,***************/32,,************-**************,,acct.randy, dns=1 vlan=0 atype=onlya inif=eth0 app=any action=pass next=0 ipqps=0 actarg=null usrtype=any bridge=0 pool=2 dnslist=0.0.0.0 schtime=1 cookie=10100
[root@Panabit:~]# floweye dnspolicy get id=1
id=1
cookie=10100
inip=
outip=ip,32,***************;rng,************,**************;acct,0,randy
dns=1
bridge=0
next=0
disable=0
ipqps=0
atype=onlya
nosnat=0
action=pass
inif=eth0
vlan=0
app=any
appid=any
appname=任意
usrtype=any
pool=2
pname=new_group
schtime=1
schtime_name=time_1
dnslist=
cmpsize=0
mopoff=0
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0
dns_cnt=0
dns_cnt6=0
dns_iter=0
dns_iter6=0

3.2 修改 DNS管控策略
floweye dnspolicy set <id=> <newid=> (<inip=> <outip=> <dns=> <vlan=> <atype=> <inif=> <app=> <action=> <next=> ><ipqps=> <actarg=> <usrtype=> <bridge=> <pool=> <dnslist=> <schtime=> <cookie=>)
[root@Panabit:~]# floweye dnspolicy get id=1
id=1
cookie=10100
inip=
outip=ip,32,***************;rng,************,**************;acct,0,randy
dns=1
bridge=0
next=0
disable=0
ipqps=0
atype=onlya
nosnat=0
action=pass
inif=eth0
vlan=0
app=any
appid=any
appname=任意
usrtype=any
pool=2
pname=new_group
schtime=1
schtime_name=time_1
dnslist=
cmpsize=0
mopoff=0
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0
dns_cnt=0
dns_cnt6=0
dns_iter=0
dns_iter6=0
[root@Panabit:~]# floweye dnspolicy set id=1 newid=1 inip= outip=,*************/32,,************-**************,,acct.randy, dns=1 vlan=0 atype=onlya inif=eth0 app=any action=pass next=0 ipqps=0 actarg=null usrtype=any bridge=0 pool=2 dnslist=0.0.0.0 schtime=1
[root@Panabit:~]# floweye dnspolicy get id=1
id=1
cookie=10100
inip=
outip=ip,32,*************;rng,************,**************;acct,0,randy
dns=1
bridge=0
next=0
disable=0
ipqps=0
atype=onlya
nosnat=0
action=pass
inif=eth0
vlan=0
app=any
appid=any
appname=任意
usrtype=any
pool=2
pname=new_group
schtime=1
schtime_name=time_1
dnslist=
cmpsize=0
mopoff=0
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0
dns_cnt=0
dns_cnt6=0
dns_iter=0
dns_iter6=0
3.3 删除 DNS管控策略
floweye dnspolicy remove <id=>
[root@Panabit:~]# floweye dnspolicy list json=1
{"polno":1,"iniftype":"if","inif":"eth0","vlan":"0","srcip":"","dstip":"ip,32,*************;rng,************,**************;acct,0,randy","dnstype":"usr","dnsid":1,"dnsname":"domain","sch_id":1,"sch_name":"time_1","active":0,"bridge":0,"action":"pass","actarg":"null","appname":"any","appcname":"任意","qps0":0,"qps1":0,"droppkts":0,"pxytype":"null","pxyactive":0,"dnslist":"","disabled":0,"next":0,"ipqps":0,"matchpkts":0,"usrtype":"any","ugroup_id":2,"ugroup_name":"new_group","atype":"onlya","nosnat":0},{"polno":50,"iniftype":"pxy","inif":"wan","vlan":"0-100","srcip":"ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy","dstip":"","dnstype":"usr","dnsid":1,"dnsname":"domain","sch_id":1,"sch_name":"time_1","active":0,"bridge":1,"action":"pass","actarg":"null","appname":"httpgroup","appcname":"HTTP协议","qps0":0,"qps1":0,"droppkts":0,"pxytype":"null","pxyactive":0,"dnslist":"","disabled":0,"next":0,"ipqps":0,"matchpkts":0,"usrtype":"any","ugroup_id":2,"ugroup_name":"new_group","atype":"onlya","nosnat":0},{"polno":100,"iniftype":"if","inif":"eth1","vlan":"0","srcip":"ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy","dstip":"","dnstype":"usr","dnsid":2,"dnsname":"domain2","sch_id":1,"sch_name":"time_1","active":0,"bridge":1,"action":"rdr","actarg":"wan ","appname":"any","appcname":"任意","qps0":0,"qps1":0,"droppkts":0,"pxytype":"pxy","pxyactive":0,"dnslist":"","disabled":0,"next":1,"ipqps":100,"matchpkts":0,"usrtype":"ippxy","ugroup_id":1,"ugroup_name":"DefaultGroup","atype":"onlyaaaa","nosnat":1}[root@Panabit:~/agent]#
[root@Panabit:~]# floweye dnspolicy remove id=1
[root@Panabit:~]# floweye dnspolicy list json=1
{"polno":50,"iniftype":"pxy","inif":"wan","vlan":"0-100","srcip":"ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy","dstip":"","dnstype":"usr","dnsid":1,"dnsname":"domain","sch_id":1,"sch_name":"time_1","active":0,"bridge":1,"action":"pass","actarg":"null","appname":"httpgroup","appcname":"HTTP协议","qps0":0,"qps1":0,"droppkts":0,"pxytype":"null","pxyactive":0,"dnslist":"","disabled":0,"next":0,"ipqps":0,"matchpkts":0,"usrtype":"any","ugroup_id":2,"ugroup_name":"new_group","atype":"onlya","nosnat":0},{"polno":100,"iniftype":"if","inif":"eth1","vlan":"0","srcip":"ip,32,***************;rng,************,**************;mac,2,new_group;pool,2,new_group;acct,0,randy","dstip":"","dnstype":"usr","dnsid":2,"dnsname":"domain2","sch_id":1,"sch_name":"time_1","active":0,"bridge":1,"action":"rdr","actarg":"wan ","appname":"any","appcname":"任意","qps0":0,"qps1":0,"droppkts":0,"pxytype":"pxy","pxyactive":0,"dnslist":"","disabled":0,"next":1,"ipqps":100,"matchpkts":0,"usrtype":"ippxy","ugroup_id":1,"ugroup_name":"DefaultGroup","atype":"onlyaaaa","nosnat":1}
3.4 启用/禁用 DNS管控策略
floweye dnspolicy set <id=> <disable=0/1>
[root@Panabit:~]# floweye dnspolicy get id=1000
id=1000
cookie=10101
inip=
outip=
dns=0
bridge=0
next=0
disable=0
ipqps=0
atype=any
nosnat=0
action=pass
inif=any
vlan=0
app=any
appid=any
appname=任意
usrtype=any
pool=0
pname=any
schtime=0
dnslist=
cmpsize=40
mopoff=0
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0
[root@Panabit:~]# floweye dnspolicy set id=1000 disable=1
[root@Panabit:~]# floweye dnspolicy get id=1000
id=1000
cookie=10101
inip=
outip=
dns=0
bridge=0
next=0
disable=1
ipqps=0
atype=any
nosnat=0
action=pass
inif=any
vlan=0
app=any
appid=any
appname=任意
usrtype=any
pool=0
pname=any
schtime=0
dnslist=
cmpsize=40
mopoff=0
hasdnslist=0
pkts0=0
pkts1=0
drop_pkts=0

4. 配置处理逻辑
路由策略较其它模块稍复杂，涉及到以下几方面的处理
4.1 引用依赖对象标识映射
路由策略的配置中会引用其它模块的对象，例如IP群组，用户组，用户等等，而这些被依赖的对象在本地是以ID为唯一标识(本地生成)进行管理的，而Orch是以对象名称为唯一标识进行管理的，Agent需要做标识的转换；目前涉及到转换的被引用对象为：
1. IP群组：IP群组Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行引用和配置。
2. 域名群组：域名群组Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行引用和配置。

4.2 DNS管控策略映射
PA本地的DNS管控策略ID不仅为策略的唯一标识，还标识着本地策略 匹配的先后顺序。

4.2.1 唯一标识转换
由于Orch上管理的策略(流量控制、路由/NAT、DNS管控)涉及到跨Site间的管理，难以以单个Site的策略ID 为唯一标识进行管理，所以：
1. DNS管控策略 通过 cookie 实现唯一标识，Agent 负责将 cookie ↔ 本地策略 ID 映射。

4.2.2 排序转换
Orch 并不直接管理本地 策略ID 顺序。为了控制策略的匹配顺序，Orch 通过下发 previous 字段（即 前一个策略 cookie）来表达插入位置，Agent 需根据此字段调整顺序。

previous规则：
1. 当下发的策略的previous cookie为0时，视为排在首个
2. 当下发的策略的previous cookie 为 -1且本地不存在时，视为追加到最后

基于以上2点，DNS管控策略 新增 修改 删除 调整顺序的逻辑为
1. 新增/修改 策略
  1. ID分配；
    1. 根据本地配置查询"策略名称"是否已存在相应策略：
      1. 存在：取现有ID。
      2. 不存在：使用max(ID)+1作为新增策略ID。
  2. 修改/新增配置；
    1. 存在：更新已有策略的配置。
    2. 不存在：根据分配的ID新增策略。
  3. 调整策略顺序；
    1. 查找 previous 对应策略的 ID（若为0，则视为插入最前；若为-1或未查找到，视为最大 ID）；
    2. 若当前策略 ID ≠ previous ID + 1：
      1. 若相同，则无需调整；
      2. 若不同则需重新排序：
        1. 将当前策略 ID 设置为临时 ID（如 65536）。
        2. 根据目标插入方向：
          1. 向前插入(当前ID > 目标ID)
            1. 将 [目标 ID, 当前 ID - 1] 区间内的所有策略 ID +1（从大到小调整）。
          2. 向后移动（当前 ID < 目标 ID）：
            1. 将 [当前 ID + 1, 目标 ID] 区间内的所有策略 ID -1（从小到大调整）。
          3. 最终将当前策略 ID 设置为 previous ID + 1。
2. 删除策略
  1. 获取策略Id；根据本地配置查询"策略名称"是否已存在相应策略：
    1. 存在：获取对应策略Id。
    2. 不存在：结束删除流程。
  2. 删除对应 ID 的策略。
  3. 所有 ID > 被删除 ID 的策略 ID 全部 -1，以保持 ID 顺序连续。

