# Domain Group Implementation

This document describes the implementation of the Domain Group module in the UniSASE Agent.

## Overview

The Domain Group module allows the agent to manage domain groups on the local system. A domain group is a collection of domain names that can be used for various purposes, such as access control, traffic filtering, etc.

## Protocol Buffer Definition

The Domain Group module uses the following protocol buffer messages:

```protobuf
// 域名群组配置消息
message DomainGroupTask {
  string name = 1;                        // 域名群组名称(域名群组唯一标识)，ASCII字符 (required)
  repeated string domain = 2;             // 域名，支持前缀匹配格式：*sohu.com, @sohu.com, ^sohu.com
  optional bytes file_content = 3;        // 文件形式域名群组内容，当域名成员数量较多时使用，每行一个域名
}
```

The Domain Group task type is defined in the TaskType enum:

```protobuf
enum TaskType {
  // ... other task types ...
  TASK_DOMAIN_GROUP = 12;  // 域名群组配置任务
}
```

## Implementation

The Domain Group module consists of the following components:

1. **Domain Group Configuration (domain_group_config.go)**:
   - Defines data structures for Domain Group configuration
   - Provides functions to get and compare Domain Group configurations
   - Provides functions to verify Domain Group configurations

2. **Domain Group Processor (domain_group_processor.go)**:
   - Implements the TaskProcessor interface for Domain Group tasks
   - Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG operations
   - Supports full synchronization of Domain Group configurations

## Configuration Processing Logic

When the agent receives a Domain Group configuration:

1. The agent checks if the Domain Group exists by name
2. If the Domain Group doesn't exist, it creates a new Domain Group
3. The agent adds members to the Domain Group using a unified approach:
   - All domain data (whether from domain list, file content, or empty) is written to a temporary file
   - The temporary file is loaded using the `floweye dns loadfile` command
   - This ensures consistent behavior for all cases, including empty domain groups
4. Since we always do a full replacement, we don't need to compare the existing members with the new ones

## Domain Name Handling

The Domain Group module supports domain names with different matching patterns:

1. **Exact Match (`^domain.com`)**:
   - Matches exactly "domain.com"
   - Does not match "www.domain.com" or "subdomain.domain.com"

2. **Suffix Match (`*domain.com`)**:
   - Matches "domain.com" and any subdomain like "www.domain.com"
   - Does not match "otherdomain.com"

3. **Prefix Match (`@domain.com`)**:
   - Matches "domain.com" and "www.domain.com"
   - Does not match "adomain.com"

## File Processing

When processing Domain Group configurations from file content:

1. The agent creates a temporary file in `/tmp/` with the Domain Group name as prefix
2. The agent writes the file content to the temporary file
3. The agent uses the `floweye dns loadfile` command to load the file
4. The temporary file is automatically removed after processing

## Testing

The Domain Group module includes comprehensive unit tests:

1. **domain_group_config_test.go**:
   - Tests for Domain Group configuration functions
   - Tests for Domain Group verification functions

2. **domain_group_processor_test.go**:
   - Tests for Domain Group processor initialization
   - Tests for processing NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG operations
   - Tests for full synchronization

## Usage

To use the Domain Group module, the orchestrator sends a DeviceTask message with:

- `task_type` set to `TASK_DOMAIN_GROUP`
- `task_action` set to one of:
  - `NEW_CONFIG` (create a new Domain Group)
  - `EDIT_CONFIG` (modify an existing Domain Group)
  - `DELETE_CONFIG` (delete an existing Domain Group)
- `domain_group_task` containing the Domain Group configuration

Example:

```go
task := &pb.DeviceTask{
    TaskType:   pb.TaskType_TASK_DOMAIN_GROUP,
    TaskAction: pb.TaskAction_NEW_CONFIG,
    Payload: &pb.DeviceTask_DomainGroupTask{
        DomainGroupTask: &pb.DomainGroupTask{
            Name:   "example_group",
            Domain: []string{
                "*sohu.com",
                "@hao123.com",
                "^baidu.com",
            },
        },
    },
}
```

Or with file content:

```go
fileContent := "*sohu.com\<EMAIL>\n^baidu.com"
task := &pb.DeviceTask{
    TaskType:   pb.TaskType_TASK_DOMAIN_GROUP,
    TaskAction: pb.TaskAction_NEW_CONFIG,
    Payload: &pb.DeviceTask_DomainGroupTask{
        DomainGroupTask: &pb.DomainGroupTask{
            Name:        "example_group",
            FileContent: []byte(fileContent),
        },
    },
}
```
