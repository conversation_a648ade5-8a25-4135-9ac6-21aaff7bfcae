1. SR字段
字段		释义	字段类型	必填项	示例	备注
name		SR名称	string	是	SR-test	ASCII字符，最长15字节；SR唯一标识
links		iWAN分段标识	string	是	11110,563	多个标识','分隔，0表示删除标签
fromin		接内标记	bool	是	true/false	fromin=1；默认为0
keepalive		是否开启保huo	bool	是	true/false	keepalive=1；默认为0
mtu		mtu	int	是	1500	mtu=1500; 默认为1500
加密配置	encrypt	加密方式	int	否	0: 不加密
1: AES128
2: AES256	encrypt=NULL
encrypt=AES128
encrypt=AES256
默认为NULL
	password	加密密钥	string	否		默认为空；password=

⚠️links分段实际不存在也能添加成功。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

⚠️在SR 协议层面，SR的唯一标识即为links；不可有相同links的SR。

2. SR查询
2.1 查询所有SR
floweye nat listproxy type=srpxy json=1
[root@Panabit:~]# floweye nat listproxy type=srpxy json=1
{"id":5,"name":"sr1","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":8,"name":"111","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"}
2.2 查询指定SR
floweye nat getproxy <SR名称>
[root@Panabit:~]# floweye nat getproxy sr1
linkid=9
proxyid=5
name=sr1
myparent=NULL
hasaddr4=0
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=36796
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-40
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=srpxy
ifname=NULL
ifstatus=up
addr=0.0.0.0
vlan=0
vlan1=0
linkup=0
fromin=1
sre_id=2
sre_links=40,80,996
sre_birth=333
sre_keepalive=1
sre_ltime=-1
sre_refcnt=1
sre_curdelay=0
sre_mindelay=-1
sre_maxdelay=0
sre_pxy.sreid=2
sre_pxy.sdwpxyid=0
sre_pxy.flags=2
sre_pxy.active=0
sre_srpxy=sr1
sre_srpxy.sreid=2
sre_srpxy.sdwpxyid=0
sre_srpxy.flags=2
sre_srpxy.active=0
sre_srpxy.keepalive=1
encrypt_algo=AES128
encrypt_password=pass128

3. SR配置
3.1 新增SR
floweye nat addsrpxy <name=>  <ifname=> <mtu=> <ping_disable=0> <pingip=0.0.0.0> <pingip2=0.0.0.0> <maxdelay=0> <links=xx,xx> <fromin=> <keepalive=> <encrypt=> <password=>
[root@Panabit:~]# floweye nat addsrpxy name=sr1 ifname= mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 links=40,80,996 fromin=1 keepalive=1 encrypt=AES256 password=pass256
[root@Panabit:~]# floweye nat getproxy sr1
linkid=9
proxyid=5
name=sr1
myparent=NULL
hasaddr4=0
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=37317
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-40
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=srpxy
ifname=NULL
ifstatus=up
addr=0.0.0.0
vlan=0
vlan1=0
linkup=0
fromin=1
sre_id=2
sre_links=40,80,996
sre_birth=854
sre_keepalive=1
sre_ltime=-1
sre_refcnt=1
sre_curdelay=0
sre_mindelay=-1
sre_maxdelay=0
sre_pxy.sreid=2
sre_pxy.sdwpxyid=0
sre_pxy.flags=2
sre_pxy.active=0
sre_srpxy=sr1
sre_srpxy.sreid=2
sre_srpxy.sdwpxyid=0
sre_srpxy.flags=2
sre_srpxy.active=0
sre_srpxy.keepalive=1
encrypt_algo=AES256
encrypt_password=pass256

3.2 修改SR
floweye nat setsrpxy <name=>  <newname=> <ifname=> <mtu=> <ping_disable=0> <pingip=0.0.0.0> <pingip2=0.0.0.0> <maxdelay=0> <links=xx,xx> <fromin=> <keepalive=> <encrypt=> <password=>
[root@Panabit:~]# floweye nat setsrpxy name=sr1 newname=sr1 ifname= mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 links=40,80,996 fromin=1 keepalive=1 encrypt=AES128 password=pass128
[root@Panabit:~]# floweye nat getproxy sr1
linkid=9
proxyid=5
name=sr1
myparent=NULL
hasaddr4=0
hasaddr6=1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=1970-01-01/08:00:00
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=1
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=37368
macbase=b0-ba-f8-fc
ifmac=b0-ba-f8-fc-00-40
dnsreqs=0
dnstimeouts=0
dnsokper=0
snatdrop=0
minping=2000000
maxping=0
curping=0
gottime=0
bigpkts=0
active=0
state=0
type=srpxy
ifname=NULL
ifstatus=up
addr=0.0.0.0
vlan=0
vlan1=0
linkup=0
fromin=1
sre_id=2
sre_links=40,80,996
sre_birth=905
sre_keepalive=1
sre_ltime=-1
sre_refcnt=1
sre_curdelay=0
sre_mindelay=-1
sre_maxdelay=0
sre_pxy.sreid=2
sre_pxy.sdwpxyid=0
sre_pxy.flags=2
sre_pxy.active=0
sre_srpxy=sr1
sre_srpxy.sreid=2
sre_srpxy.sdwpxyid=0
sre_srpxy.flags=2
sre_srpxy.active=0
sre_srpxy.keepalive=1
encrypt_algo=AES128
encrypt_password=pass128

3.3 删除SR
floweye nat rmvproxy <SR名称>
[root@Panabit:~]# floweye nat listproxy type=srpxy json=1
{"id":5,"name":"sr1","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"},{"id":8,"name":"111","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"}
[root@Panabit:~]# floweye nat rmvproxy sr1
[root@Panabit:~]# floweye nat listproxy type=srpxy json=1
{"id":8,"name":"111","type":"srpxy","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"NULL","ip":"0.0.0.0","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","dns0":"0.0.0.0","dns1":"0.0.0.0"}

