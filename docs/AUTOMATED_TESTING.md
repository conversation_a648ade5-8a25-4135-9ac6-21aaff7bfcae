# 自动化测试系统

## 概述

本项目提供了一套完整的自动化测试系统，用于在真实PA环境中测试各个模块的功能。系统通过Makefile目标提供简单的命令入口，自动化执行从编译到远程测试的完整流程。

## 测试用例优化指导原则

### 1. 环境假设
- **PA远程环境上的floweye命令一定是可用的**
- 问题分析中不需要考虑floweye命令不可用的情况
- 测试脚本可以直接使用floweye命令进行验证和操作

### 2. 测试验证要求
- **测试用例编写完成之后，使用`make test-xxx`来进行验证**
- 必须确保没有任何错误，包括：
  - 脚本语法错误
  - 验证逻辑错误
  - JSON格式错误
  - 命令执行错误
- 所有测试用例必须达到100%通过率才能视为完成

### 3. 脚本执行策略
- **执行脚本不需要`set -e`**
- 避免因为过程中的预期错误导致脚本意外终止
- 使用显式的错误处理和验证逻辑
- 允许测试脚本在遇到预期的错误条件时继续执行

## 系统架构

```
本地开发环境                    PA测试环境 (*************)
┌─────────────────┐            ┌─────────────────────────┐
│ 1. 编译构建     │            │                         │
│ 2. 打包测试文件 │   SSH+     │ 4. 解码部署             │
│ 3. Base64编码   │ ────────>  │ 5. 执行测试             │
│    传输         │   Base64   │ 6. 生成结果             │
└─────────────────┘            └─────────────────────────┘
         │                                    │
         │ 7. 收集结果                        │
         └────────────────────────────────────┘
```

## 快速开始

### 1. 环境准备

确保满足以下条件：
- SSH密钥认证已配置到PA系统 (*************)
- PA系统上有floweye命令
- 本地已安装Go开发环境

### 2. 执行测试

```bash
# 检查测试环境
make test-check-env

# 执行Interface模块综合测试
make test-interface

# 执行Interface模块综合测试（详细模式）
VERBOSE=true make test-interface

# 执行WAN模块综合测试
make test-wan

# 收集测试结果（测试完成后会自动执行）
make test-collect-results

# 清理远程环境
make test-cleanup-remote
```

## 可用的测试目标

### 模块测试目标

| 目标 | 描述 | 测试脚本 |
|------|------|----------|
| `make test-interface` | Interface模块综合测试 | interface_comprehensive_test.sh |
| `make test-interface-comprehensive` | Interface模块综合测试（别名） | interface_comprehensive_test.sh |
| `make test-wan` | WAN模块综合测试 | wan_comprehensive_test.sh |
| `make test-wan-comprehensive` | WAN模块综合测试（别名） | wan_comprehensive_test.sh |
| `make test-lan` | LAN模块综合测试 | lan_comprehensive_test.sh |
| `make test-dhcp` | DHCP模块综合测试 | dhcp_comprehensive_test.sh |
| `make test-wangroup` | WAN Group模块综合测试 | wangroup_comprehensive_test.sh |
| `make test-usergroup` | User Group模块综合测试 | user_group_comprehensive_test.sh |
| `make test-user` | User模块综合测试 | user_comprehensive_test.sh |
| `make test-iwan-service` | iWAN Service模块综合测试 | iwan_service_comprehensive_test.sh |
| `make test-iwan-proxy` | iWAN Proxy模块综合测试 | iwan_proxy_comprehensive_test.sh |
| `make test-all` | 所有模块测试 | 依次执行所有模块 |

### 工具目标

| 目标 | 描述 |
|------|------|
| `make test-check-env` | 检查PA测试环境连通性 |
| `make test-collect-results` | 收集PA系统测试结果 |
| `make test-cleanup-remote` | 清理远程测试环境 |
| `make test-tools` | 显示可用测试工具 |

## 自动化流程详解

### 步骤1：编译构建
```bash
make build-linux-arm64
```
自动编译生成：
- `build/linux_arm64/agent`
- `build/linux_arm64/agent-debug-client`

### 步骤2：打包测试文件
创建包含以下内容的tar.gz包：
- 编译后的二进制文件
- 完整的test/目录
- 配置文件

### 步骤3：传输到PA系统
由于PA系统没有scp，使用base64编码传输：
```bash
# 本地编码
base64 -i test_package.tar.gz -o test_package.tar.gz.b64

# 传输到PA系统
cat test_package.tar.gz.b64 | ssh root@************* 'cat > /tmp/test_package.tar.gz.b64'

# 在PA系统解码和部署
ssh root@************* 'base64 -d /tmp/test_package.tar.gz.b64 > /tmp/test_package.tar.gz'
```

### 步骤4：清理Agent日志
测试前自动清理agent日志文件：
```bash
echo "" > /var/log/agent.log
```

### 步骤5：执行测试
在PA系统上执行对应的测试脚本，支持详细执行模式：
- 使用 `bash -x` 显示详细执行过程
- 实时显示输出和时间戳
- 记录每个步骤的执行时间

### 步骤6：收集结果
自动收集测试日志、系统信息，生成HTML报告：
- 测试成功后自动收集结果
- 测试失败时立即收集错误日志
- 生成详细的HTML测试报告

## 配置选项

### 环境变量

| 变量 | 默认值 | 描述 |
|------|--------|------|
| `PA_HOST` | ************* | PA系统IP地址 |
| `PA_USER` | root | SSH用户名 |
| `PA_DEPLOY_DIR` | /root/agent | 部署目录 |

### 命名规范和限制

#### iWAN Service命名规范
- **最大长度**: 15个字符
- **禁用字符**: 不能包含连字符(-)
- **推荐格式**: 使用字母数字组合，如 `iwantest1`, `iwansvc1`
- **测试命名**: 使用 `iwantest` 或 `iwansvc` 前缀加数字

#### 其他模块命名规范
- **Interface**: 使用系统接口名称 (eth0, eth1, eth2)
- **User Group**: 使用测试专用ID范围 (100-199)
- **User**: 使用 `testuser` 前缀加数字
- **WAN**: 使用 `testwan` 前缀加数字

#### 命名冲突避免
- 所有测试使用专用前缀，避免与生产配置冲突
- 测试结束后自动清理所有测试配置
- 使用唯一的测试标识符确保并发测试不冲突

### 使用示例
```bash
# 使用不同的PA系统
PA_HOST=************* make test-interface

# 使用不同的用户
PA_USER=admin make test-wan

# 使用不同的部署目录
PA_DEPLOY_DIR=/opt/agent make test-lan

# 启用详细执行模式
VERBOSE=true make test-interface

# 组合使用环境变量
VERBOSE=true PA_HOST=************* make test-interface
```

## 测试脚本

### 核心脚本

1. **scripts/deploy_and_test.sh**
   - 主要的部署和测试脚本
   - 处理编译、打包、传输、执行的完整流程
   - 支持参数化配置不同模块

2. **scripts/collect_test_results.sh**
   - 测试结果收集脚本
   - 下载日志文件、系统信息
   - 生成HTML测试报告

3. **scripts/test_config_template.yaml**
   - PA环境测试配置模板
   - 包含调试模式和测试特定配置

### 测试用例文件

每个模块包含以下测试文件：
- `{module}_comprehensive_test.sh` - 综合测试脚本
- `test_{module}_*.json` - 具体测试用例

**注意**：项目已统一使用综合测试，不再维护快速测试脚本。

## 目录结构

测试完成后，PA系统上的目录结构：
```
/root/agent/
├── agent                    # 编译后的主程序
├── agent-debug-client       # 编译后的调试客户端
├── config.yaml             # 配置文件
├── test/                   # 测试目录
│   ├── interface_quick_test.sh
│   ├── interface_comprehensive_test.sh
│   ├── test_interface_*.json
│   └── *.log               # 测试结果日志
└── test_package.tar.gz     # 原始压缩包
```

本地结果目录结构：
```
test_results_20240618_143022/
├── test_summary.txt         # 测试摘要
├── test_report.html        # HTML测试报告
├── system_info.txt         # 系统信息
├── remote_files.txt        # 远程文件列表
├── interface_test_results.log  # 测试日志
└── backup_*.log            # 备份日志
```

## 错误处理

### 常见错误及解决方法

1. **SSH连接失败**
   ```bash
   # 检查SSH密钥
   ssh-add -l
   
   # 测试连接
   ssh root@************* 'echo "连接成功"'
   ```

2. **编译失败**
   ```bash
   # 检查Go环境
   go version
   
   # 清理后重新编译
   make clean
   make build-linux-arm64
   ```

3. **floweye命令不可用**
   - 确保在真实PA环境中运行
   - 检查floweye命令路径

4. **权限不足**
   ```bash
   # 确保有sudo权限或root权限
   ssh root@************* 'whoami'
   ```

## 扩展性

### 添加新模块测试

1. 创建测试脚本：
   ```bash
   # 在test/目录下创建
   touch test/newmodule_quick_test.sh
   touch test/newmodule_comprehensive_test.sh
   ```

2. 在Makefile中添加目标：
   ```makefile
   .PHONY: test-newmodule
   test-newmodule: build-linux-arm64
       @$(MAKE) test-deploy-and-run MODULE=newmodule
   ```

3. 创建测试用例JSON文件：
   ```bash
   touch test/test_newmodule_*.json
   ```

### 自定义测试脚本

可以直接使用核心脚本：
```bash
# 使用自定义测试脚本
./scripts/deploy_and_test.sh mymodule my_custom_test.sh
```

## 最佳实践

1. **测试前检查环境**
   ```bash
   make test-check-env
   ```

2. **使用综合测试进行完整验证**
   ```bash
   make test-interface
   make test-wan
   ```

3. **使用详细模式进行问题诊断**
   ```bash
   VERBOSE=true make test-interface
   VERBOSE=true make test-wan
   ```

5. **测试结果自动收集**
   - 测试完成后会自动收集结果
   - 测试失败时立即收集错误日志
   - 手动收集：`make test-collect-results`

6. **定期清理远程环境**
   ```bash
   make test-cleanup-remote
   ```

7. **查看测试报告**
   ```bash
   # 测试完成后会生成HTML报告
   open test_results_*/test_report.html
   ```

## 故障排除

### 调试模式

启用详细输出：
```bash
# 使用VERBOSE环境变量启用详细模式
VERBOSE=true make test-interface

# 或直接使用脚本的调试选项
BASH_DEBUG=true ./scripts/deploy_and_test.sh interface

# 手动执行脚本调试
bash -x scripts/deploy_and_test.sh interface
```

### 手动执行步骤

如果自动化流程失败，可以手动执行各个步骤：
```bash
# 1. 编译
make build-linux-arm64

# 2. 检查文件
ls -la build/linux_arm64/

# 3. 手动传输
scp -r build/linux_arm64/* root@*************:/root/agent/

# 4. 手动执行测试
ssh root@************* 'cd /root/agent/test && ./interface_quick_test.sh'
```

## 验证方法

### 基本验证原则

1. **通过解析agent-debug-client返回的内容来判断任务成功/失败**
   - 检查输出中是否包含 `"Task failed:"` 信息
   - 检查JSON响应中是否有 `"err_code": [1-9]` 错误码
   - **不依赖agent-debug-client的退出码**，因为即使任务失败，agent-debug-client可能仍返回退出码0
2. **使用floweye命令验证配置是否正确应用**
3. **对于预期失败的测试，检查是否返回了正确的错误**

### 验证逻辑实现

```bash
# 执行测试并解析结果
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}
```

### 关键要点

- **内容解析优于退出码**: 通过解析agent-debug-client的输出内容来判断任务成功/失败，而不是依赖进程退出码
- **错误模式匹配**: 使用正则表达式匹配特定的错误模式，如`"Task failed:"`和`"err_code": [1-9]`
- **预期失败测试**: 对于预期失败的测试用例，验证是否返回了正确的错误信息

## 测试用例设计标准

### 核心测试场景

每个模块的测试用例必须包含以下核心场景：

#### 1. 基础CRUD操作
- **新增 - 新增 - 无改动**：验证幂等性
- **新增 - 修改**：分别修改单个字段，验证修改成功
- **新增 - 删除**：验证删除功能和幂等性

#### 2. 全量同步测试
- **新增 - 开启全量配置 - 结束全量配置**
- **全量配置中包含以上1-3的所有场景**
- **结束全量时，校验冗余配置是否删除**

### 全量同步测试流程

#### StartFullSync 和 EndFullSync 逻辑

```bash
# 1. 设置初始配置（增量模式）
run_test "setup_config.json" "设置初始配置"
verify_initial_state

# 2. 启动全量同步
start_full_sync || exit 1

# 3. 发送全量同步配置（只包含需要保留的配置）
run_test "full_sync_config.json" "全量同步配置"
verify_full_sync_state

# 4. 结束全量同步，触发清理逻辑
end_full_sync || exit 1

# 5. 验证清理结果：未在全量同步中的配置应被删除
verify_cleanup_state
```

#### 全量同步函数实现

```bash
# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}
```

### Interface模块测试最佳实践

#### 完整测试覆盖度

基于interface模块的实现，以下是完整的测试覆盖度要求：

#### Interface模块可选字段默认值恢复测试

##### 测试目的
验证当Interface配置中的可选字段从配置中移除时，系统是否正确恢复为默认值。

##### 可选字段及其默认值
- `mix_mode`: 默认值为 `false` (0)
- `la_group`: 默认值为 `0` (不捆绑)
- `peer`: 默认值为空字符串或 `none`
- `lacp_config`: 默认值为 `nil` (无LACP配置)

##### 测试步骤
1. **创建完整配置**：创建包含所有可选字段的Interface配置
2. **修改为基础配置**：移除所有可选字段，只保留必需字段
3. **验证默认值恢复**：确认所有可选字段恢复为系统默认值

##### 1. 基础CRUD操作测试
```bash
# 1.1 新增配置
run_test "test_interface_monitor_basic.json" "监控模式基础配置"
verify_interface "eth2" "0" "inside" "0"

# 1.2 幂等性测试 - 重复新增相同配置
run_test "test_interface_idempotent_new.json" "幂等性测试-重复新增"
verify_interface "eth2" "0" "inside" "0"

# 1.3 字段修改测试 - 分别修改单个字段
run_test "test_interface_modify_zone.json" "修改zone字段"
verify_interface "eth2" "0" "outside" "0"

run_test "test_interface_modify_mixmode.json" "修改mixmode字段"
verify_interface "eth2" "0" "outside" "1"

# 1.4 删除配置测试
run_test "test_interface_delete.json" "删除配置测试"
verify_interface "eth2" "0" "inside" "0"

# 1.5 删除不存在配置的幂等性测试
run_test "test_interface_delete_idempotent.json" "删除配置幂等性测试"
verify_interface "eth2" "0" "inside" "0"
```

##### 2. 全量同步清理测试
```bash
# 2.1 设置初始配置（增量模式）
run_test "test_interface_full_sync_setup.json" "全量同步清理初始配置"
verify_interface "eth0" "2" "outside" "1"
verify_interface "eth1" "2" "outside" "1"
verify_interface "eth2" "1" "inside" "1"

# 2.2 启动全量同步
start_full_sync || exit 1

# 2.3 发送全量同步配置（只配置eth2）
run_test "test_interface_full_sync_cleanup.json" "全量同步清理配置"
verify_interface "eth2" "0" "inside" "0"

# 2.4 结束全量同步，触发清理逻辑
end_full_sync || exit 1

# 2.5 验证清理结果：eth0和eth1应该被重置为默认配置
sleep 2  # 等待清理完成
verify_interface "eth0" "0" "inside" "0"
verify_interface "eth1" "0" "inside" "0"
verify_interface "eth2" "0" "inside" "0"
```

##### 3. 可选字段默认值恢复测试
```bash
# 3.1 创建包含所有可选字段的完整配置
run_test "test_interface_optional_fields_complete.json" "创建包含所有可选字段的完整配置"
verify_interface "eth2" "1" "outside" "1" "3"
verify_lacp "3" "1" "1" "1"

# 3.2 修改配置，移除所有可选字段，验证默认值恢复
run_test "test_interface_optional_fields_default.json" "移除可选字段验证默认值恢复"
verify_interface "eth2" "0" "inside" "0"
verify_interface_optional_defaults "eth2"
```

##### 4. 边界条件和错误处理测试
```bash
# 4.1 无效参数测试（这些应该失败）
run_test "test_interface_error_no_name.json" "缺少name字段错误测试" "false"
run_test "test_interface_error_bridge_no_peer.json" "网桥模式缺少peer错误测试" "false"
run_test "test_interface_error_nonexistent.json" "不存在接口错误测试" "false"

# 4.2 链路聚合边界测试
run_test "test_interface_error_invalid_lagroup.json" "无效lagroup错误测试" "false"
```

### WAN模块测试最佳实践

#### WAN模块特殊要求

##### 1. 前置条件
WAN配置前必须确保对应的interface zone设置为outside：
```bash
# 设置interface为接外模式（WAN配置的前置条件）
setup_interface_for_wan() {
    local interface=$1

    print_info "设置接口 $interface 为接外模式..."
    echo "设置接口 $interface 为接外模式..." >> $LOG_FILE

    local output=$(floweye if set name=$interface mode=0 zone=outside mixmode=0 2>&1)
    if [ $? -eq 0 ]; then
        print_success "接口 $interface 设置为接外模式成功"
        return 0
    else
        print_error "接口 $interface 设置为接外模式失败: $output"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}
```

##### 2. WAN类型测试覆盖
```bash
# 2.1 静态IP WAN测试
run_test "test_wan_static_new.json" "创建静态IP WAN"
verify_wan "wan-test-static" "proxy" "eth2" "1500"

run_test "test_wan_static_modify_mtu.json" "修改MTU字段"
verify_wan "wan-test-static" "proxy" "eth2" "1400"

run_test "test_wan_static_delete.json" "删除WAN配置"
verify_wan_not_exists "wan-test-static"

# 2.2 DHCP WAN测试
run_test "test_wan_dhcp_new.json" "创建DHCP WAN"
verify_wan "wan-test-dhcp" "dhcpwan" "eth2" "1500"

run_test "test_wan_dhcp_modify.json" "修改DHCP WAN"
verify_wan "wan-test-dhcp" "dhcpwan" "eth2" "1400"

# 2.3 PPPoE WAN测试
run_test "test_wan_pppoe_new.json" "创建PPPoE WAN"
verify_wan "wan-test-pppoe" "pppoe" "eth2" "1460"

run_test "test_wan_pppoe_modify.json" "修改PPPoE WAN"
verify_wan "wan-test-pppoe" "pppoe" "eth2" "1400"
```

##### 3. 清理现有配置
WAN测试前需要清理所有现有WAN配置，避免IP冲突：
```bash
# 清理所有现有的WAN配置
cleanup_all_wan_configs() {
    echo "清理所有现有WAN配置..." >> $LOG_FILE

    # 获取所有WAN配置列表
    local wan_list=$(floweye nat listproxy json=1 type=wan 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$wan_list" ] && [ "$wan_list" != "null" ]; then
        # 解析JSON并提取WAN名称
        local wan_names=$(echo "$wan_list" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

        if [ -n "$wan_names" ]; then
            echo "发现现有WAN配置: $wan_names" >> $LOG_FILE
            for wan_name in $wan_names; do
                echo "删除WAN配置: $wan_name" >> $LOG_FILE
                floweye nat rmvproxy "$wan_name" > /dev/null 2>&1
            done
            print_success "清理了现有WAN配置"
        fi
    fi
}
```

##### 4. DHCP选项验证
当前版本暂时忽略DHCP选项验证，专注于核心功能测试：
```bash
# TODO: DHCP options verification temporarily disabled
# Will be re-enabled after resolving DHCP option parsing issues
logger.Debug("DHCP options verification temporarily disabled")
```

#### 测试用例文件结构

##### 增量更新测试用例
```json
// test_interface_full_sync_setup.json
[
  {
    "tx_id": "interface-setup-for-cleanup-001",
    "device_tasks": [
      {
        "task_type": "TASK_INTERFACE",
        "task_action": "NEW_CONFIG",
        "interface_task": {
          "name": "eth0",
          "mode": "INTERFACE_MODE_BRIDGE2",
          "zone": "INTERFACE_ZONE_OUTSIDE",
          "mix_mode": true,
          "peer": "eth1"
        }
      }
    ]
  }
]
```

##### 全量同步测试用例
```json
// test_interface_full_sync_cleanup.json
[
  {
    "tx_id": "interface-full-sync-cleanup-001",
    "full_sync": true,
    "device_tasks": [
      {
        "task_type": "TASK_INTERFACE",
        "task_action": "NEW_CONFIG",
        "interface_task": {
          "name": "eth2",
          "mode": "INTERFACE_MODE_MONITOR",
          "zone": "INTERFACE_ZONE_INSIDE",
          "mix_mode": false
        }
      }
    ]
  }
]
```

#### 验证函数实现

```bash
# 验证接口配置
verify_interface() {
    local interface=$1
    local expected_mode=$2
    local expected_zone=$3
    local expected_mixmode=${4:-""}
    local expected_lagroup=${5:-""}

    echo "验证接口 $interface 配置..." >> $LOG_FILE
    local output=$(floweye if get $interface 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取接口 $interface 配置"
        return 1
    fi

    # 验证mode
    if echo "$output" | grep -q "mode=$expected_mode"; then
        print_success "接口 $interface mode=$expected_mode 验证通过"
    else
        print_error "接口 $interface mode 验证失败，期望: $expected_mode"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证zone
    if echo "$output" | grep -q "zone=$expected_zone"; then
        print_success "接口 $interface zone=$expected_zone 验证通过"
    else
        print_error "接口 $interface zone 验证失败，期望: $expected_zone"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}

# 验证接口可选字段默认值
verify_interface_optional_defaults() {
    local interface=$1

    echo "验证接口 $interface 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye if get $interface 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取接口 $interface 配置"
        return 1
    fi

    # 验证mixmode默认值为0
    if echo "$output" | grep -q "mixmode=0"; then
        print_success "接口 $interface mixmode默认值验证通过 (0)"
    else
        print_error "接口 $interface mixmode默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证lagroup默认值为0
    if echo "$output" | grep -q "lagroup=0"; then
        print_success "接口 $interface lagroup默认值验证通过 (0)"
    else
        print_error "接口 $interface lagroup默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证peer默认值为空或none
    if echo "$output" | grep -q "peer=none" || echo "$output" | grep -q "peer=\"\"" || ! echo "$output" | grep -q "peer="; then
        print_success "接口 $interface peer默认值验证通过 (none或空)"
    else
        print_error "接口 $interface peer默认值验证失败，期望: none或空"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}
```

### 关键测试原则

1. **分离增量和全量同步**：增量更新和全量同步测试必须分开执行
2. **验证清理逻辑**：全量同步结束后必须验证未配置的资源被正确清理
3. **幂等性验证**：所有操作都必须支持重复执行
4. **错误处理测试**：必须包含无效参数和边界条件测试
5. **状态验证**：每个操作后都必须验证系统状态

## 总结

自动化测试系统提供了：
- ✅ 一键式测试执行
- ✅ 完整的错误处理
- ✅ 详细的结果收集
- ✅ 灵活的配置选项
- ✅ 良好的扩展性
- ✅ 详细执行过程显示（bash -x）
- ✅ 执行时间统计和性能监控
- ✅ 自动日志清理和收集
- ✅ 测试失败时立即错误分析
- ✅ 生成详细的HTML测试报告
- ✅ 标准化的测试用例设计
- ✅ 完整的全量同步测试流程

通过简单的`make test-interface`命令，即可完成从编译到测试结果收集的完整流程。使用`VERBOSE=true make test-interface`可以获得详细的执行过程输出。

## LAN模块测试

LAN模块测试覆盖了LAN接口配置的完整生命周期，包括可选字段的默认值恢复机制。

### 测试用例覆盖

#### 基础CRUD操作测试
- **创建LAN配置**：验证基本LAN接口创建
- **幂等性测试**：验证重复创建相同配置的幂等性
- **字段修改测试**：验证MTU、IP地址、子网掩码的修改
- **可选字段默认值恢复测试**：验证可选字段的默认值恢复机制
- **删除配置测试**：验证LAN配置删除和幂等性

#### 可选字段默认值恢复测试
- **测试目的**：验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **测试步骤**：
  1. 创建包含所有可选字段的完整LAN配置（clone_mac等）
  2. 修改配置，移除所有可选字段
  3. 验证可选字段是否恢复为系统默认值
- **验证要点**：
  - `clone_mac`: 应恢复为 `00-00-00-00-00-00`（默认值）

#### 全量同步测试
- **初始配置设置**：创建多个LAN配置
- **全量同步清理**：验证未在全量同步中配置的LAN被正确删除
- **清理验证**：确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**：验证缺少name、ifname等必需字段时的错误处理
- **无效参数**：验证无效MTU、IP地址等参数的错误处理

### 执行命令
```bash
# 执行LAN模块测试
make test-lan

# 详细模式执行
VERBOSE=true make test-lan
```

## DHCP模块测试

DHCP模块测试覆盖了DHCP服务器配置的完整功能，包括可选字段的默认值恢复机制。

### 测试用例覆盖

#### 基础CRUD操作测试
- **启用DHCP配置**：验证DHCP服务器启用
- **幂等性测试**：验证重复启用相同配置的幂等性
- **字段修改测试**：验证地址池、租约时间、DNS服务器的修改
- **可选字段默认值恢复测试**：验证可选字段的默认值恢复机制
- **禁用和删除测试**：验证DHCP配置禁用、删除和幂等性

#### 可选字段默认值恢复测试
- **测试目的**：验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **测试步骤**：
  1. 创建包含所有可选字段的完整DHCP配置（dns0, dns1, dhcp_domain等）
  2. 修改配置，移除所有可选字段
  3. 验证可选字段是否恢复为系统默认值
- **验证要点**：
  - `dns0`: 应恢复为空字符串（默认值）
  - `dns1`: 应恢复为空字符串（默认值）
  - `dhcp_domain`: 应恢复为空字符串（默认值）
  - `dhcp_gateway`: 应恢复为LAN接口IP地址
  - `dhcp_mask`: 应恢复为LAN接口子网掩码

#### 全量同步测试
- **初始配置设置**：创建多个DHCP配置及其依赖的LAN配置
- **全量同步清理**：验证未在全量同步中配置的DHCP被正确删除
- **依赖关系处理**：验证LAN删除时DHCP的正确处理
- **清理验证**：确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**：验证缺少name等必需字段时的错误处理
- **不存在的LAN**：验证为不存在的LAN配置DHCP时的错误处理
- **无效参数**：验证无效地址池、租约时间等参数的错误处理

### 执行命令
```bash
# 执行DHCP模块测试
make test-dhcp

# 详细模式执行
VERBOSE=true make test-dhcp
```

### 依赖关系
DHCP模块测试会自动创建必要的LAN配置作为前置条件，确保测试的独立性和可靠性。

## User Group模块测试

User Group模块测试覆盖了用户组配置的完整功能，包括可选字段的默认值恢复机制。

### 测试用例覆盖

#### 基础CRUD操作测试
- **创建用户组**: 验证基本用户组创建功能
- **幂等性测试**: 验证重复创建相同配置的幂等性
- **字段修改测试**: 验证用户组名称、IP地址范围、带宽限制的修改
- **可选字段默认值恢复测试**: 验证可选字段的默认值恢复机制
- **删除配置测试**: 验证用户组删除功能和幂等性

#### 可选字段默认值恢复测试
- **测试目的**: 验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **测试步骤**:
  1. 创建包含所有可选字段的完整用户组配置（IP范围、带宽限制、DNS等）
  2. 修改配置，移除所有可选字段
  3. 验证可选字段是否恢复为系统默认值
- **验证要点**:
  - `start_ip/end_ip`: 应恢复为 `0.0.0.0`（默认值）
  - `ratein/rateout`: 应恢复为 `0`（无限制）
  - `ratein6/rateout6`: 应恢复为 `0`（无限制）
  - `clntepa`: 应恢复为 `reject`（默认过期账号处理方式）
  - `maxonlinetime`: 应恢复为 `0`（无限制）

#### 全量同步测试
- **初始配置设置**: 创建多个用户组配置
- **全量同步清理**: 验证未在全量同步中配置的用户组被正确删除
- **默认组保护**: 确认ID=1的默认用户组不会被清理
- **清理验证**: 确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**: 验证缺少ID、name、pid字段时的错误处理
- **无效ID范围**: 验证ID超出1-2063范围时的错误处理
- **ID冲突**: 验证相同ID不同名称的冲突处理

### 执行命令
```bash
# 执行User Group模块测试
make test-usergroup

# 详细模式执行
VERBOSE=true make test-usergroup

# 别名执行
make test-usergroup-comprehensive
```

### 特殊要求
- **ID范围限制**: 用户组ID必须在1-2063范围内
- **默认组保护**: 测试不会影响ID=1的默认用户组
- **floweye命令**: 使用`pppoeippool`命令进行用户组管理
- **测试ID范围**: 使用100-200范围的ID进行测试，避免与生产配置冲突

## User模块测试

User模块测试覆盖了用户账号配置的完整功能，包括可选字段的默认值恢复机制。

### 测试用例覆盖

#### 基础CRUD操作测试
- **创建用户**: 验证基本用户账号创建功能
- **幂等性测试**: 验证重复创建相同配置的幂等性
- **字段修改测试**: 验证密码、最大在线数、绑定IP等字段的修改
- **用户启用/禁用测试**: 验证用户账号的启用和禁用功能
- **删除配置测试**: 验证用户删除功能和幂等性

#### 完整配置测试
- **完整用户配置**: 验证包含所有字段的用户配置创建
- **用户组变更**: 验证用户在不同用户组间的迁移

#### 可选字段默认值恢复测试
- **测试目的**: 验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **测试步骤**:
  1. 创建包含所有可选字段的完整用户配置（绑定IP、MAC、VLAN等）
  2. 修改配置，移除所有可选字段
  3. 验证可选字段是否恢复为系统默认值
- **验证要点**:
  - `max_online`: 应恢复为 `0`（无限制）
  - `bind_ip`: 应恢复为 `0.0.0.0`（不绑定）
  - `bind_mac`: 应恢复为空或 `00-00-00-00-00-00`（不绑定）
  - `out_vlan`: 应恢复为 `0`（不绑定）

#### 全量同步测试
- **初始配置设置**: 创建多个用户配置
- **全量同步清理**: 验证未在全量同步中配置的用户被正确删除
- **清理验证**: 确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**: 验证缺少name、pool_id、password等必需字段时的错误处理
- **无效参数**: 验证无效用户组ID、日期格式等参数的错误处理
- **用户名长度**: 验证超长用户名的错误处理

#### 用户组依赖关系测试
- **依赖关系验证**: 验证用户与用户组的依赖关系
- **级联删除**: 验证删除用户组时其下用户的处理（根据文档，用户组删除时其下所有用户都会被删除）

### 执行命令
```bash
# 执行User模块测试
make test-user

# 详细模式执行
VERBOSE=true make test-user

# 别名执行
make test-user-comprehensive
```

### 特殊要求
- **用户组依赖**: 测试前需要创建测试用户组（ID: 100, 101）
- **floweye命令**: 使用`pppoeacct`命令进行用户管理
- **启用/禁用状态**: 通过`pppoeacct list`命令检查用户启用状态
- **测试用户组范围**: 使用100-101范围的用户组ID进行测试，避免与生产配置冲突

### 依赖关系
User模块测试会自动创建必要的用户组配置作为前置条件，确保测试的独立性和可靠性。

## iWAN Service模块测试

iWAN Service模块测试覆盖了iWAN服务配置的完整功能，包括可选字段的默认值恢复机制。

### 测试用例覆盖

#### 基础CRUD操作测试
- **创建iWAN Service**: 验证基本iWAN Service创建功能
- **幂等性测试**: 验证重复创建相同配置的幂等性
- **字段修改测试**: 验证地址、MTU、用户组字段的修改
- **删除配置测试**: 验证iWAN Service删除功能和幂等性

#### 完整配置测试
- **完整配置创建**: 验证包含所有字段的iWAN Service配置
- **完整配置修改**: 验证完整配置的修改功能

#### 可选字段默认值恢复测试
- **测试目的**: 验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **测试步骤**:
  1. 创建包含所有字段的完整iWAN Service配置
  2. 修改配置，移除可选字段
  3. 验证字段是否恢复为系统默认值
- **验证要点**:
  - `auth`: 应恢复为 `local`（默认认证方式）
  - `prefix6len`: 应恢复为 `0`（默认IPv6前缀长度）

#### 全量同步测试
- **初始配置设置**: 创建多个iWAN Service配置
- **全量同步清理**: 验证未在全量同步中配置的iWAN Service被正确删除
- **清理验证**: 确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**: 验证缺少name、addr、mtu、pool等必需字段时的错误处理
- **无效参数**: 验证无效MTU、用户组ID等参数的错误处理

#### 用户组依赖关系测试
- **依赖关系验证**: 验证iWAN Service与用户组的依赖关系
- **用户组变更**: 验证用户组变更时iWAN Service的处理

### 执行命令
```bash
# 执行iWAN Service模块测试
make test-iwan-service

# 详细模式执行
VERBOSE=true make test-iwan-service

# 别名执行
make test-iwan-service-comprehensive
```

### 特殊要求
- **用户组依赖**: 测试前需要创建测试用户组（ID: 100, 101）
- **floweye命令**: 使用`nat addiwansvc/setiwansvc/rmvproxy`命令进行iWAN Service管理
- **认证方式**: 支持local/radius/free三种认证方式
- **命名限制**: iWAN Service名称不能超过15个字符且不能包含连字符(-)
- **测试范围**: 使用测试专用的iWAN Service名称，避免与生产配置冲突

### 依赖关系
iWAN Service模块测试会自动创建必要的用户组配置作为前置条件，确保测试的独立性和可靠性。

## iWAN Proxy模块测试

iWAN Proxy模块测试覆盖了iWAN代理配置的完整功能，包括可选字段的默认值恢复机制。

### 测试用例覆盖

#### 基础CRUD操作测试
- **创建iWAN Proxy**: 验证基本iWAN Proxy创建功能
- **幂等性测试**: 验证重复创建相同配置的幂等性
- **字段修改测试**: 验证MTU、服务器地址、用户名密码等字段的修改
- **删除配置测试**: 验证iWAN Proxy删除功能和幂等性

#### 完整配置测试
- **完整配置创建**: 验证包含所有字段的iWAN Proxy配置
- **完整配置修改**: 验证完整配置的修改功能

#### 可选字段默认值恢复测试
- **测试目的**: 验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **测试步骤**:
  1. 创建包含所有可选字段的完整iWAN Proxy配置
  2. 修改配置，移除可选字段
  3. 验证字段是否恢复为系统默认值
- **验证要点**:
  - `encrypt`: 应恢复为 `false`（默认不加密）
  - `link`: 应恢复为 `0`（默认链路值）
  - `ping_ip/ping_ip2`: 应恢复为 `0.0.0.0`（默认心跳IP）
  - `max_delay`: 应恢复为 `0`（默认最大延迟）

#### 全量同步测试
- **初始配置设置**: 创建多个iWAN Proxy配置
- **全量同步清理**: 验证未在全量同步中配置的iWAN Proxy被正确删除
- **清理验证**: 确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**: 验证缺少name、ifname、svr_addr等必需字段时的错误处理
- **无效参数**: 验证无效MTU、端口号等参数的错误处理
- **不存在的接口**: 验证为不存在的WAN接口配置iWAN Proxy时的错误处理

#### WAN接口依赖关系测试
- **依赖关系验证**: 验证iWAN Proxy与WAN接口的依赖关系
- **接口变更**: 验证WAN接口变更时iWAN Proxy的处理

### 执行命令
```bash
# 执行iWAN Proxy模块测试
make test-iwan-proxy

# 详细模式执行
VERBOSE=true make test-iwan-proxy

# 别名执行
make test-iwan-proxy-comprehensive
```

### 特殊要求
- **WAN接口依赖**: 测试前需要创建测试WAN接口（wan1, wan2）
- **floweye命令**: 使用`nat addiwan/setiwan/rmvproxy`命令进行iWAN Proxy管理
- **字段映射**: 注意floweye输出中的字段映射（如`srlink_link`对应`link`字段）
- **DNS代理验证**: 由于floweye命令的已知问题，暂时跳过DNS代理字段验证
- **测试范围**: 使用测试专用的iWAN Proxy名称，避免与生产配置冲突

### 依赖关系
iWAN Proxy模块测试会自动创建必要的WAN接口配置作为前置条件，确保测试的独立性和可靠性。

### 已知问题和解决方案
- **DNS代理字段**: 由于floweye命令的bug，即使传递`dnspxy=1`参数，实际配置仍显示`dnspxy=0`。Agent内部验证暂时跳过此字段
- **字段映射**: Agent正确处理了floweye输出中的字段映射问题（如`srlink_link`字段）
- **验证方法**: 使用内容解析而非退出码来判断任务成功/失败，提高了测试的准确性

## DNS Tracking Policy模块测试

DNS Tracking Policy模块测试覆盖了DNS跟踪策略配置的完整功能，包括可选字段的默认值恢复机制和复杂的排序测试。

### 测试用例覆盖

#### 基础CRUD操作测试
- **创建DNS跟踪策略**: 验证基本DNS跟踪策略创建功能
- **幂等性测试**: 验证重复创建相同配置的幂等性
- **字段修改测试**: 验证线路、DNS服务器等字段的修改
- **启用/禁用测试**: 验证策略的启用和禁用功能
- **删除策略**: 验证策略删除功能和幂等性

#### 完整配置测试
- **完整策略配置**: 验证包含所有字段的DNS跟踪策略配置
- **完整配置修改**: 验证完整配置的修改功能

#### 可选字段默认值恢复测试
- **测试目的**: 验证当可选字段从配置中移除时，系统是否正确恢复为默认值
- **验证要点**:
  - `track_host`: 应恢复为 `false`（默认值）
  - `cache_ttl`: 应恢复为 `0`（默认值）
  - `dns_addr`: 应恢复为空（默认值）

#### 排序测试最佳实践
DNS Tracking Policy模块的排序测试参考了Flow Control Policy的最佳实践，包含以下完整场景：

##### 1. 五种策略类型设置
为了全面测试排序功能，设置五种不同类型的DNS跟踪策略：
- **完整配置策略1**: 启用跟踪，包含所有可选字段（DNS服务器、缓存TTL等）
- **基础配置策略1**: 只包含必需字段，可选字段使用默认值
- **完整配置策略2**: 禁用跟踪，使用不同域名群组和线路
- **基础配置策略2**: 使用不同域名群组，可选字段为默认值
- **禁用策略**: 策略本身被禁用，但包含完整配置

##### 2. 排序操作测试
- **保持不变**: 验证策略顺序在无变更时保持稳定
- **向前移动策略**: 将策略移动到更靠前的位置（previous指向更前面的策略）
- **向后移动策略**: 将策略移动到更靠后的位置（previous指向更后面的策略）
- **移动到首位**: 使用 `previous=0` 将策略移动到第一位
- **追加到末尾**: 使用 `previous=4294967295` (uint32 max) 将策略追加到最后
- **中间插入**: 在现有策略中间插入新策略
- **删除中间策略**: 删除中间位置的策略，验证其他策略ID自动调整

##### 3. 排序验证方法
```bash
# 验证DNS跟踪策略排序
verify_dns_tracking_policy_order() {
    local expected_order=("$@")

    # 获取策略列表
    local output=$(floweye dnrt list json=1 2>/dev/null)

    # 解析JSON输出获取策略ID顺序
    local actual_order=()
    while IFS= read -r line; do
        if [[ $line =~ \"polno\":([0-9]+) ]]; then
            actual_order+=(${BASH_REMATCH[1]})
        fi
    done <<< "$output"

    # 比较期望顺序和实际顺序
    for i in "${!expected_order[@]}"; do
        if [ "${actual_order[i]}" != "${expected_order[i]}" ]; then
            print_error "策略排序不匹配，位置$i期望: ${expected_order[i]}，实际: ${actual_order[i]}"
            return 1
        fi
    done

    print_success "DNS跟踪策略排序验证通过: ${actual_order[*]}"
    return 0
}
```

##### 4. 排序测试用例示例
```json
// 五种类型策略初始设置
{
  "tx_id": "dns-tracking-policy-five-types-setup-001",
  "device_tasks": [
    {
      "task_type": "TASK_DNS_TRACKING_POLICY",
      "task_action": "NEW_CONFIG",
      "dns_tracking_policy_task": {
        "cookie": 10100,
        "previous": 0,
        "disable": false,
        "domain_group": ["test_domain_group"],
        "pxy": "wan",
        "backup_pxy": "wan1",
        "dns_addr": {"ip": "*******"},
        "track_host": true,
        "cache_ttl": 300,
        "desc": "完整配置策略1-启用跟踪"
      }
    }
    // ... 其他四种策略类型
  ]
}
```

#### 全量同步测试
- **初始配置设置**: 创建多个DNS跟踪策略配置
- **全量同步清理**: 验证未在全量同步中配置的策略被正确删除
- **清理验证**: 确认全量同步结束后的状态正确性

#### 错误处理测试
- **缺少必需字段**: 验证缺少cookie、域名群组、线路等必需字段时的错误处理
- **无效参数**: 验证无效域名群组等参数的错误处理

### 执行命令
```bash
# 执行DNS Tracking Policy模块测试
make test-dns-tracking-policy

# 详细模式执行
VERBOSE=true make test-dns-tracking-policy
```

### 特殊要求
- **域名群组依赖**: 测试前需要创建测试域名群组（test_domain_group, test_domain_group2）
- **floweye命令**: 使用`dnrt`命令进行DNS跟踪策略管理
- **排序机制**: DNS跟踪策略使用cookie进行唯一标识，通过previous字段控制排序
- **测试范围**: 使用cookie >= 10000的策略进行测试，避免与生产配置冲突

### 依赖关系
DNS Tracking Policy模块测试会自动创建必要的域名群组配置作为前置条件，确保测试的独立性和可靠性。

### 排序测试最佳实践总结

基于Flow Control Policy和DNS Tracking Policy的实践经验，策略排序测试应包含以下核心要素：

1. **多样化策略类型**: 至少包含5种不同配置类型的策略，覆盖完整配置、基础配置、禁用策略等场景
2. **全面的移动操作**: 测试向前移动、向后移动、移动到首位、移动到末尾、中间插入等所有排序操作
3. **ID调整验证**: 验证策略删除和插入时其他策略ID的自动调整逻辑
4. **排序稳定性**: 验证无变更时策略顺序的稳定性
5. **边界条件**: 测试first position (previous=0) 和 last position (previous=uint32_max) 的边界情况

这套排序测试最佳实践可以应用到所有支持策略排序的模块中，确保排序功能的正确性和稳定性。
