# Agent调试模式使用指南

本文档介绍如何使用Agent的调试模式，通过该模式可以从配置文件读取proto消息，并模拟Orch下发配置任务执行。

## 功能概述

Agent调试模式提供以下功能：

1. 通过IPC命令控制调试服务器的启动和停止
2. 调试服务器仅允许本地连接（127.0.0.1）
3. 记录所有调试操作的访问日志
4. 支持从JSON格式的配置文件读取任务并执行
5. 返回详细的执行结果

## 使用方法

### 1. 启动调试模式

使用agent工具启动调试模式：

```bash
# 使用默认端口(8080)启动
agent debug start

# 指定端口启动
agent debug start --port=8888
```

### 2. 查看调试状态

```bash
agent debug status
```

### 3. 使用调试客户端发送配置

使用agent-debug-client工具从配置文件读取任务并发送到调试服务器：

```bash
# 使用默认服务器地址(http://127.0.0.1:8080)
agent-debug-client --config=configs/debug_examples/interface_config.json

# 指定服务器地址
agent-debug-client --config=configs/debug_examples/wan_config.json --server=http://127.0.0.1:8888
```

### 4. 停止调试模式

```bash
agent debug stop
```

## 配置文件格式

配置文件使用JSON格式，结构与Orch下发的任务相同。以下是几个示例：

### Interface配置示例

```json
[
  {
    "tx_id": "test-interface-1",
    "device_tasks": [
      {
        "task_type": "CPE_INTERFACE",
        "task_action": "NEW_CONFIG",
        "interface_task": {
          "name": "eth0",
          "mode": "INTERFACE_MODE_MONITOR",
          "zone": "INTERFACE_ZONE_INSIDE"
        }
      }
    ]
  }
]
```

### WAN配置示例

```json
[
  {
    "tx_id": "test-wan-1",
    "device_tasks": [
      {
        "task_type": "CPE_WAN",
        "task_action": "NEW_CONFIG",
        "wan_task": {
          "name": "wan1",
          "ifname": "eth1",
          "mtu": 1500,
          "static_ip": {
            "addr": {
              "ip_string": "*************"
            },
            "gateway": {
              "ip_string": "***********"
            },
            "dns": {
              "ip_string": "*******"
            }
          }
        }
      }
    ]
  }
]
```

### LAN配置示例

```json
[
  {
    "tx_id": "test-lan-1",
    "device_tasks": [
      {
        "task_type": "CPE_LAN",
        "task_action": "NEW_CONFIG",
        "lan_task": {
          "name": "lan1",
          "ifname": "eth2",
          "mtu": 1500,
          "addr": {
            "ip_string": "***********"
          },
          "mask": {
            "ip_string": "*************"
          }
        }
      }
    ]
  }
]
```

### DHCP配置示例

```json
[
  {
    "tx_id": "test-dhcp-1",
    "device_tasks": [
      {
        "task_type": "CPE_DHCP",
        "task_action": "NEW_CONFIG",
        "dhcp_task": {
          "name": "lan1",
          "dhcp_enable": true,
          "dhcp_pool": {
            "start_ip": {
              "ip_string": "***********00"
            },
            "end_ip": {
              "ip_string": "*************"
            }
          },
          "lease_ttl": 86400
        }
      }
    ]
  }
]
```

## 日志记录

调试模式会记录所有操作到日志文件：

- 日志文件路径：`/var/log/agent/debug.log`
- 日志内容包括：请求时间、请求方法、请求路径、客户端地址、用户代理、处理时间等

## 安全注意事项

1. 调试模式仅允许本地连接（127.0.0.1），不接受远程连接
2. 调试模式应仅在开发和测试环境中使用，不应在生产环境中启用
3. 调试操作会被详细记录，便于审计和问题排查

## 故障排除

1. 如果无法启动调试服务器，请检查：
   - Agent是否正在运行
   - 指定的端口是否已被占用
   - 是否有足够的权限

2. 如果无法连接到调试服务器，请检查：
   - 调试服务器是否已启动（使用`agent-ctl debug status`命令）
   - 服务器地址和端口是否正确
   - 防火墙设置是否阻止了连接

3. 如果配置执行失败，请检查：
   - 配置文件格式是否正确
   - 配置内容是否合法
   - 查看调试日志获取详细错误信息
