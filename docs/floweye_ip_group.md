1. IP群组字段
字段		释义	字段类型	必填项	示例	备注
name		ip群组名称	string	是	ip-overseas	唯一标识；
ip成员	ip	ip	IpAddr	是	***********	格式: ip,ip/mask,ip-ip
	info	备注	string	否	ip-google	默认为空 info=
文件	content	文件形式IP群组	byte	否	*********** ip-gw
***********/24 ip-hr
***********-***********00 ip-iot	每行一项，每项为
ip,ip/mask,ip-ip 备注

⚠️IP群组删除时，相关策略也会一并删除。
⚠️以名称作为唯一标识，agent通过名称反查ID，路由等策略引用时同理。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. IP群组查询
2.1 查询IP群组列表
floweye table list
[root@Panabit:~]# floweye table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233

2.2 查询指定IP群组
floweye table get <name=ip群组名称>
[root@Panabit:~]# floweye table get name=orch11233
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw

3. IP群组配置
3.1 新增IP群组
floweye table add <name=ip-group-test>
[root@Panabit:~]# floweye table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233

[root@Panabit:~]# floweye table add name=ip-group-test
[root@Panabit:~]# floweye table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233
7 ip-group-test
<ID> <ip群组名称>

3.2 新增IP群组成员
3.2.1 添加单条IP群组成员
floweye table addip <id=> <ip> <info=>
[root@Panabit:~]# floweye table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233
7 ip-group-test
8 test123

[root@Panabit:~]# floweye table get id=7
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw
*********** ip-gw
[root@Panabit:~]# floweye table addip id=7 ************* info=ip-gw
[root@Panabit:~]# floweye table get id=7
************* ip-gw
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw
*********** ip-gw
⚠️需要根据id 进行添加。

3.2.2 文件形式添加IP群组成员
floweye table loadfile <tid=IP群组ID> <file=文件路径> <clear=0追加|1覆盖更新>
[root@Panabit:~]# floweye table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233
7 ip-group-test
8 test123
[root@Panabit:~]# floweye table get id=7
************* ip-gw
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw
*********** ip-gw
[root@Panabit:~]# cat ip.txt
*********** ip-gw
***********/24 ip-hr
***********-***********00 ip-iot
[root@Panabit:~]# floweye table loadfile tid=7 file=/root/agent/ip.txt clear=1
[root@Panabit:~]# floweye table get id=7
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw
⚠️需要根据id 进行添加。

3.3 删除IP群组成员
floweye table rmvip <ip群组ID> <ip>
[root@Panabit:~]# floweye table get id=7
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw
[root@Panabit:~]# floweye table rmvip 7 ***********-***********00
[root@Panabit:~]# floweye table get id=7
***********/24 ip-hr
*********** ip-gw

3.4 清空IP群组成员
floweye table clear <ip群组ID>
[root@Panabit:~]# floweye table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233
7 ip-group-test
8 test123
[root@Panabit:~]# floweye table get id=7
***********-***********00 ip-iot
***********/24 ip-hr
*********** ip-gw
[root@Panabit:~]# floweye table clear 7
[root@Panabit:~]# floweye table get id=7
[root@Panabit:~]#

3.5 删除IP群组
floweye table remove <id=ip群组ID>
[root@Panabit:~]# ./floweye_tool table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233
7 ip-group-test
8 test123
[root@Panabit:~]# floweye table remove id=7
[root@Panabit:~]# ./floweye_tool table list
1 免认证IP
2 orch1
3 ipgroup-test
4 orch11
5 orch125
6 orch11233
8 test123

4. 配置处理逻辑
- Orch 以"ip群组名称" 作为唯一标识，每次单个ip群组的 新增/变更 皆以全量配置下发。
- 一个ip群组的全量配置，当ip成员多时以 byte形式的内容进行下发，少时以repeated ip字段进行下发；择一；

Agent处理：
1. 接收ip群组对象时，通过查询ip群组列表，获取"ip群组名称"对应的"ip群组ID"，后续皆以ID进行操作。
2. ip群组ID若不存在，则新建IP群组(3.1)
3. 清空对应IP群组成员: floweye table clear <ip群组ID>
4. 根据配置内容，选择 单条ip 添加 或 文件形式 添加IP群组成员。

全量配置删除冗余项：仅删除冗余ip群组对象，IP群组成员皆为全量覆盖添加。

