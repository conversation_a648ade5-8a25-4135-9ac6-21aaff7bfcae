1. 域名群组字段
字段		释义	字段类型	必填项	示例	备注
name		域名群组名称	string	是	domian-overseas	唯一标识；
domain成员	domain	域名	string	是	*sohu.com	系统采取后缀匹配算法，*sohu.com相当于sohu.com，^sohu.com精确匹配sohu.com，
@sohu.com可匹配www.sohu.com和sohu.com，不可匹配asohu.com
文件	content	文件形式域名群组	byte	否	*sohu.com
@hao123.com
^baidu.com	每行一项
⚠️域名群组删除时，相关策略也会一并删除。
⚠️以名称作为唯一标识，agent通过名称反查ID，路由等策略引用时同理。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. 域名群组查询
2.1 域名群组列表
floweye dns listgrp
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 2
usr 13 domain-test 1
<域名组类型，usr为用户创建，sys标识系统创建> <域名组ID> <域名组名称> <域名组成员个数>
⚠️我们仅管理user创建的域名组。

2.2 查询指定域名群组成员
floweye dns dumpgrp <域名组ID>
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 2
usr 13 domain-test 1

[root@Panabit:~]# floweye dns dumpgrp 13
domain-test1.com

3. 域名群组配置
3.1 新增域名群组
floweye dns addgrp <域名群组名称>
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 2
[root@Panabit:~]# floweye dns addgrp domain-test
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 2
usr 13 domain-test 0

3.2 新增域名群组成员
3.2.1 添加单条域名群组成员
floweye dns add <id> <domain>
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 2
usr 13 domain-test 0
[root@Panabit:~]# floweye dns dumpgrp 13
[root@Panabit:~]# floweye dns add 13 domain-test1.com
[root@Panabit:~]# floweye dns dumpgrp 13
domain-test1.com
⚠️需要根据id 进行添加。

3.2.2 文件形式添加域名群组成员
floweye dns loadfile <域名群组ID> <文件绝对路径>
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 2
usr 13 domain-test 1
[root@Panabit:~]# floweye dns dumpgrp 13
domain-test1.com
[root@Panabit:~]# cat domain.txt
*sohu.com
@hao123.com
^baidu.com
[root@Panabit:~]# floweye dns loadfile 13 /root/agent/domain.txt
[root@Panabit:~]# floweye dns dumpgrp 13
^baidu.com
@hao123.com
sohu.com
[root@Panabit:~]# cat domain.txt
test-domain.com
test-domain1.com
test-domain2.com
[root@Panabit:~]# floweye dns dumpgrp 13
^baidu.com
@hao123.com
sohu.com
[root@Panabit:~]# floweye dns loadfile 13 /root/agent/domain.txt
[root@Panabit:~]# floweye dns dumpgrp 13
test-domain2.com
test-domain1.com
test-domain.com
⚠️需要根据id 进行添加。
⚠️全量替换添加。

3.3 删除域名群组成员
floweye dns remove <域名群组ID> <域名>
[root@Panabit:~]# floweye dns dumpgrp 13
@hao123.com
sohu.com
test-domain.com
test-domain1.com
test-domain2.com
[root@Panabit:~]# floweye dns remove 13 @hao123.com
[root@Panabit:~]# floweye dns dumpgrp 13
sohu.com
test-domain.com
test-domain1.com
test-domain2.com
3.5 删除域名群组
floweye dns rmvgrp <域名群组ID>
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 5
usr 13 domain-test 4
[root@Panabit:~]# floweye dns rmvgrp 13
[root@Panabit:~]# floweye dns listgrp
usr 1 whitelist 3511
usr 2 openai 65
usr 3 谷歌play 13
usr 4 log.unisase.com 2
usr 5 自定义加速域名 16
usr 6 KPHZ内网 1
usr 7 google 544
usr 8 develop 11
usr 9 本地认证 1
usr 10 vpn 2
usr 11 gemini 8
usr 12 JetBrain 5

4. 配置处理逻辑
- Orch 以"域名群组名称" 作为唯一标识，每次单个域名群组的 新增/变更 皆以全量配置下发。
- 一个域名群组的全量配置，当域名成员多时以 byte形式的内容进行下发，少时以repeated domian字段进行下发；择一；

Agent处理：
1. 接收域名群组对象时，通过查询域名群组列表，获取"域名群组名称"对应的"域名群组ID"，后续皆以ID进行操作。
2. 域名群组ID若不存在，则新建域名群组(3.1)
3. 由于没有清空的floweye命令，无论orch是以单域名形式下发，还是文件形式下发，都通过floweye 文件形式下发给 PA，以达到全量配置覆盖。

全量配置删除冗余项：仅删除user管理的冗余域名群组对象，域名群组成员皆为全量覆盖添加。

