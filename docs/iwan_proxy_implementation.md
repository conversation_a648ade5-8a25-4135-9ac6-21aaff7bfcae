# iWAN Proxy模块配置实现文档

## 概述

iWAN Proxy模块负责处理iWAN线路的配置，包括服务器地址、端口、用户名、密码、加密选项、心跳配置等设置。本文档描述了iWAN Proxy模块的实现方案和关键代码。

## 配置项

根据`floweye_iwan_proxy_service.md`文档和`message.proto`定义，iWAN Proxy模块支持以下配置项：

1. **基本配置**
   - 名称(name) - 必填，iWAN线路名称，ASCII字符，最长15字节
   - 承载的线路名称(ifname) - 必填，如wan1
   - MTU(mtu) - 必填，最大传输单元，范围: 500-4700

2. **iWAN参数**
   - 服务器IP/域名(svraddr) - 必填
   - 服务器端口(svrport) - 必填
   - iWAN账号(username) - 必填
   - iWAN密码(password) - 必填
   - 是否加密(encrypt) - 可选
   - 分段表示同意(link) - 可选

3. **心跳服务(Heartbeat)**
   - 心跳服务器1(ping_ip) - 可选
   - 心跳服务器2(ping_ip2) - 可选
   - 最大延迟(max_delay) - 可选，单位ms

4. **其他配置**
   - DNS代理开关(dns_pxy) - 可选

## 配置一致性和同步机制

根据项目的配置一致性和同步机制，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye nat listproxy`和`floweye nat getproxy`命令）
   - 将配置缓存在内存中

2. **配置处理策略**
   - 对象不在全量配置中：执行创建流程
   - 对象已在全量配置中：
     - 配置一致：忽略，无需修改
     - 配置不一致：替换配置
   - 每处理完一个对象后，从全量配置缓存中移除

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 处理剩余的本地配置（这些是需要删除的对象）
   - 清理资源

### 增量同步

1. **配置处理**
   - 获取本地全量配置
   - 按与全量配置一致的逻辑处理对象
   - 不在每次配置成功后重新获取全量配置，而是在下次配置时再获取

## 实现方案

### 关键数据结构

1. **IwanProxyConfig**
   - 表示本地iWAN Proxy配置
   - 包含名称、承载的线路名称、MTU、服务器地址、端口、用户名、密码、加密选项、心跳配置等信息

2. **IwanProxyProcessor**
   - 处理iWAN Proxy配置任务
   - 维护本地配置缓存
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 实现全量同步和增量同步逻辑

### 关键方法

1. **refreshLocalConfigs()**
   - 获取本地全量配置
   - 更新配置缓存

2. **StartFullSync()**
   - 开始全量同步
   - 获取本地全量配置

3. **EndFullSync()**
   - 结束全量同步
   - 处理剩余的本地配置
   - 清理资源

4. **handleConfigChange()**
   - 处理新建和编辑配置任务
   - 实现配置一致性检查
   - 构建命令参数
   - 执行配置命令并验证结果

5. **handleDeleteConfig()**
   - 处理删除配置任务
   - 构建删除命令
   - 执行删除命令并验证结果

6. **GetLocalIwanProxyConfigs()**
   - 获取所有本地iWAN Proxy配置
   - 使用结构化的方式解析命令输出
   - 将结果转换为易于处理的结构化数据

7. **GetIwanProxyConfig()**
   - 获取指定iWAN Proxy的配置
   - 使用结构化的方式解析key-value格式的命令输出
   - 将结果转换为易于处理的结构化数据

8. **VerifyIwanProxyConfig()**
   - 验证配置是否成功应用
   - 确保命令执行成功后配置确实生效

9. **CompareIwanProxyConfig()**
   - 比较请求的配置与本地配置
   - 决定是否需要修改配置

## 命令行交互

iWAN Proxy模块通过以下命令与系统交互：

1. **查询所有iWAN Proxy**
   ```
   floweye nat listproxy type=wan json=1
   ```

2. **查询特定iWAN Proxy**
   ```
   floweye nat getproxy <iWAN名称>
   ```

3. **新增iWAN Proxy**
   ```
   floweye nat addiwan name=<iWAN名称> ifname=<承载的线路名称> mtu=<MTU> svraddr=<服务器地址> svrport=<服务器端口> username=<用户名> password=<密码> encrypt=<是否加密> link=<分段表示同意> pingip=<心跳服务器1> pingip2=<心跳服务器2> maxdelay=<最大延迟> dnspxy=<DNS代理开关>
   ```

4. **修改iWAN Proxy**
   ```
   floweye nat setiwan name=<iWAN名称> newname=<新iWAN名称> ifname=<承载的线路名称> mtu=<MTU> svraddr=<服务器地址> svrport=<服务器端口> username=<用户名> password=<密码> encrypt=<是否加密> link=<分段表示同意> pingip=<心跳服务器1> pingip2=<心跳服务器2> maxdelay=<最大延迟> dnspxy=<DNS代理开关>
   ```

5. **删除iWAN Proxy**
   ```
   floweye nat rmvproxy <iWAN名称>
   ```

## 实现特点

1. **结构化配置解析**
   - 使用map来解析key-value格式的命令输出
   - 提高了解析效率和可读性

2. **统一的全量同步接口**
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 支持全量同步和增量同步

3. **配置验证机制**
   - 在应用配置后验证是否成功
   - 确保命令执行成功后配置确实生效

4. **配置比对机制**
   - 在应用配置前先检查是否与本地配置一致
   - 如果一致则无需修改，提高效率

5. **依赖检查**
   - 添加iWAN Proxy时，需确保其承载的线路已添加
   - 在测试中先创建必要的WAN配置，再测试iWAN Proxy功能

## 注意事项

1. 必须指定iWAN Proxy的名称、承载的线路名称、MTU、服务器地址、端口、用户名和密码
2. 添加iWAN Proxy时，需确保其承载的线路已添加
3. 删除iWAN Proxy时，PA可能默认会删除与其关联的策略，例如策略路由等等
4. 命令返回0不一定代表成功，需要再次查询确认配置是否生效

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 缺少必要参数：验证并返回明确的错误信息

## 测试方案

1. 单元测试：验证处理器的基本功能和错误处理
2. 集成测试：验证与floweye命令的交互
3. 系统测试：验证配置在实际环境中的生效情况
4. 全量同步测试：验证全量同步机制
5. 增量同步测试：验证增量同步机制
6. 依赖测试：验证在创建iWAN Proxy前先创建必要的WAN配置

## 后续优化

1. 优化本地配置缓存的更新策略
2. 增加配置批量处理能力
3. 增加对iWAN Proxy状态的监控和上报
4. 实现更细粒度的错误处理和恢复机制
5. 优化JSON解析逻辑，提高解析效率和稳定性
