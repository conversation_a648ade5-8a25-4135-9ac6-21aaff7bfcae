1. WAN GROUP字段
字段	释义	字段类型	必填项	示例	备注
id	群组ID	int	是	1-127	范围: 1-127；wan 群组唯一标识
name	群组名称	string	是	group1	ASCII字符，最长15字节
type	群组类型	string	是	srcdst	见下方类型说明
proxy	WAN成员名称	string	否	wan1	可以为多个


类型	描述	备注
srcdst	源地址+目的地址	基于源地址和目的地址进行负载均衡
spdp	源目地址+源目端口	基于源目地址和源目端口进行负载均衡
src	源地址	仅基于源地址进行负载均衡
srcsport	源地址+源端口	基于源地址和源端口进行负载均衡
dst	目的地址	仅基于目的地址进行负载均衡
dstdport	目的地址+目的端口	基于目的地址和目的端口进行负载均衡
leftbw	最大下行空闲带宽	基于最大下行空闲带宽进行负载均衡
leftbwup	最大上行空闲带宽	基于最大上行空闲带宽进行负载均衡
minflow	最小连接数	基于最小连接数进行负载均衡
failover	主备模式	基于failover进行负载均衡

⚠️id 唯一；id一致，name不一致，后下发的group name为替换，相当于变更群组名称。
⚠️name唯一；name一致，id不一致，后下发的配置失败。
保证name和id 对应的唯一性。

⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. WAN GROUP 查询
2.1 查询所有WAN群组
floweye wangroup list
<ID> <名称> <成员数量> <类型> <未知字段> <未知字段> [成员列表]
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 orch 3 srcdst 0 0 wan1 wan2 wan3
22 group1 0 srcdst 0 0
128 group224 0 srcdst 0 0
2.2 查询特定WAN群组
floweye wangroup get id=<群组ID> showproxy=1
<WAN类型> <WAN ID> <WAN名称> <未知字段> <权重> <未知字段> <未知字段> <未知字段> <未知字段> <IP地址>
[root@Panabit:~]# floweye wangroup get id=<群组ID> showproxy=1
proxy 12 wan1 0 1 0 0 0 0.00 ************
pppoe 13 wan2 0 1 0 0 0 0.00 0.0.0.0
dhcpwan 15 wan3 1 1 0 0 0 0.00 ************
3. WAN GROUP配置
3.1 创建WAN群组
floweye wangroup add id=<群组ID> name=<群组名> type=<群组类型>
[root@Panabit:~/agent/test]# floweye wangroup add name=group224 id=128 type=srcdst
[root@Panabit:~/agent/test]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 orch 3 srcdst 0 0 wan1 wan2 wan3
22 group1 0 srcdst 0 0
128 group224 0 srcdst 0 0
3.2 修改WAN群组
floweye wangroup set id=<群组ID> name=<新群组名> type=<新群组类型>
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 group1234 4 spdp 0 0 iwan1 wan2 iwan-SH-POP SR-SH-JP
22 group1 0 srcdst 0 0
128 group224 0 srcdst 0 0
[root@Panabit:~]# floweye wangroup set id=128 name=group224 type=spdp
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 group1234 4 spdp 0 0 iwan1 wan2 iwan-SH-POP SR-SH-JP
22 group1 0 srcdst 0 0
128 group224 0 spdp 0 0
3.3 WAN群组成员管理
添加成员：
floweye wangroup set <id=> <proxy=> <weight=1>
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 group1234 4 spdp 0 0 iwan1 wan2 iwan-SH-POP SR-SH-JP
22 group1 0 srcdst 0 0
128 group224 0 spdp 0 0
[root@Panabit:~]# floweye wangroup set id=128 proxy=wan1 weight=1
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 group1234 4 spdp 0 0 iwan1 wan2 iwan-SH-POP SR-SH-JP
22 group1 0 srcdst 0 0
128 group224 1 spdp 0 0 wan1
添加多个时，需要分别添加

删除成员：
floweye wangroup set id=<群组ID> proxy=<WAN名称> weight=-1
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 group1234 4 spdp 0 0 iwan1 wan2 iwan-SH-POP SR-SH-JP
22 group1 0 srcdst 0 0
128 group224 1 spdp 0 0 wan1
[root@Panabit:~]# floweye wangroup set id=128 proxy=wan1 weight=-1
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 group1234 4 spdp 0 0 iwan1 wan2 iwan-SH-POP SR-SH-JP
22 group1 0 srcdst 0 0
128 group224 0 spdp 0 0
删除多个时，需要分别删除

3.4 删除WAN群组
floweye wangroup remove id=<群组ID>
[root@Panabit:~/agent]# floweye wangroup add name=group127 id=127 type=srcdst
[root@Panabit:~/agent]# floweye wangroup list
1 aaa 0 srcdst 0 0
127 group127 0 srcdst 0 0
128 group224 2 spdp 0 0 wan1 wan2
[root@Panabit:~/agent]# floweye wangroup remove id=127
[root@Panabit:~/agent]# floweye wangroup list
1 aaa 0 srcdst 0 0
128 group224 2 spdp 0 0 wan1 wan2
WAN GROUP群组内成员无需提前删除

4. 注意事项
4.1 ID冲突示例
当使用相同ID但不同name创建群组时，name将被替换：
[root@Panabit:~]# floweye wangroup add name=group223 id=128 type=srcdst
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 orch 3 srcdst 0 0 wan1 wan2 wan3
22 group1 0 srcdst 0 0
128 group223 0 srcdst 0 0
[root@Panabit:~]# floweye wangroup add name=group224 id=128 type=srcdst
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 orch 3 srcdst 0 0 wan1 wan2 wan3
22 group1 0 srcdst 0 0
128 group224 0 srcdst 0 0

4.2 名称冲突示例
当使用相同name但不同ID创建群组时，配置将失败：
[root@Panabit:~]# floweye wangroup add name=group224 id=128 type=srcdst
[root@Panabit:~]# floweye wangroup list
1 aaa 0 srcdst 0 0
2 orch 3 srcdst 0 0 wan1 wan2 wan3
22 group1 0 srcdst 0 0
128 group224 0 srcdst 0 0
[root@Panabit:~]# floweye wangroup add name=group224 id=23 type=srcdst
EXIST

