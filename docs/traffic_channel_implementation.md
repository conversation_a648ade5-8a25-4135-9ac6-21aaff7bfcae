# Traffic Channel模块配置实现文档

## 概述

Traffic Channel模块负责处理流量通道的配置，包括通道带宽、限额和优先级设置。本文档描述了Traffic Channel模块的实现方案和关键代码。

## 配置项

根据`floweye_traffic_channel.md`文档和`message.proto`定义，Traffic Channel模块支持以下配置项：

1. **基本配置**
   - 名称(name) - 必填，流量通道名称，唯一标识
   - 带宽(rate) - 必填，流量通道带宽，单位kbits/s，范围: 1-16000000
   - 限额(quota) - 可选，当日限额，单位Mbytes，0表示不限额

2. **优先级配置(Priorities)**
   - 优先级(pri) - 必填，优先级1-16，1优先级最高
   - 最大带宽(maxrate) - 必填，单位kbps
   - 保证带宽(gbw) - 必填，单位kbps
   - 描述(desc) - 可选，优先级描述

3. **约束条件**
   - 通道数量上限192
   - 最多16个优先级
   - 所有优先级保证带宽之和不能大于通道带宽
   - 保证带宽不能超过最大带宽

## 配置一致性和同步机制

根据`Configuration_Consistency_and_Synchronization_Mechanism.md`文档，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye policy listbwo`和`floweye policy getbwo`命令）
   - 将配置缓存在内存中

2. **配置处理策略**
   - 对象不在全量配置中：执行创建流程
   - 对象已在全量配置中：
     - 配置一致：忽略，无需修改
     - 配置不一致：替换配置
   - 每处理完一个对象后，从全量配置缓存中移除

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 处理剩余的本地配置（这些是需要删除的对象）
   - 清理资源

### 增量同步

1. **配置处理**
   - 获取本地全量配置
   - 按与全量配置一致的逻辑处理对象
   - 不在每次配置成功后重新获取全量配置，而是在下次配置时再获取

## 实现方案

### 关键数据结构

1. **TrafficChannelPriority**
   - 表示流量通道优先级配置
   - 包含优先级、最大带宽、保证带宽、描述等信息

2. **TrafficChannelConfig**
   - 表示本地流量通道配置
   - 包含名称、带宽、限额、优先级列表等信息

3. **TrafficChannelProcessor**
   - 处理流量通道配置任务
   - 维护本地配置缓存
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 实现全量同步和增量同步逻辑

### 关键方法

1. **refreshLocalConfigs()**
   - 获取本地全量配置
   - 更新配置缓存

2. **StartFullSync()**
   - 开始全量同步
   - 获取本地全量配置

3. **EndFullSync()**
   - 结束全量同步
   - 处理剩余的本地配置
   - 清理资源

4. **handleConfigChange()**
   - 处理新建和修改配置任务
   - 实现配置一致性检查
   - 根据配置状态构建不同的命令
   - 执行配置命令并验证结果

5. **handleDeleteConfig()**
   - 处理删除配置任务
   - 验证配置存在性
   - 执行删除命令并验证结果

6. **validatePriorities()**
   - 验证优先级配置
   - 检查业务规则约束

7. **configureBasicChannel()**
   - 配置基本通道属性
   - 使用floweye policy addbwo/setbwo命令

8. **configurePriorities()**
   - 配置优先级设置
   - 使用floweye policy sethtb命令

9. **verifyChannelConfig()**
   - 验证配置是否正确应用
   - 比较期望配置与实际配置

## Floweye命令映射

### 查询命令
- `floweye policy listbwo` - 查询所有流量通道
- `floweye policy getbwo name=<name>` - 查询指定流量通道
- `floweye policy gethtb <name>` - 查询流量通道带宽优先级

### 配置命令
- `floweye policy addbwo name=<name> rate=<rate> quota=<quota>` - 新增流量通道
- `floweye policy setbwo name=<name> rate=<rate> quota=<quota>` - 修改流量通道
- `floweye policy sethtb name=<name> pri=<pri> maxrate=<maxrate> gbw=<gbw> desc=<desc>` - 修改流量通道带宽
- `floweye policy rmvbwo name=<name>` - 删除流量通道

## 错误处理

1. **输入验证**
   - 验证必填字段
   - 验证数值范围
   - 验证业务规则约束

2. **命令执行错误**
   - 记录详细错误信息
   - 返回用户友好的错误消息

3. **配置验证错误**
   - 验证配置是否正确应用
   - 提供详细的不匹配信息

## 测试

### 单元测试
- 配置解析功能测试
- 配置比较功能测试
- 优先级验证功能测试

### 集成测试
- 完整的配置流程测试
- 全量同步和增量同步测试
- 错误场景测试

## 使用示例

### 创建流量通道
```json
{
  "task_type": "TASK_TRAFFIC_CHANNEL",
  "task_action": "NEW_CONFIG",
  "traffic_channel_task": {
    "name": "channel_1",
    "rate": 20000,
    "quota": 10000,
    "priorities": [
      {
        "pri": 1,
        "maxrate": 15000,
        "gbw": 5000,
        "desc": "High priority traffic"
      }
    ]
  }
}
```

### 修改流量通道
```json
{
  "task_type": "TASK_TRAFFIC_CHANNEL",
  "task_action": "EDIT_CONFIG",
  "traffic_channel_task": {
    "name": "channel_1",
    "rate": 30000,
    "quota": 15000,
    "priorities": [
      {
        "pri": 1,
        "maxrate": 25000,
        "gbw": 8000,
        "desc": "Updated high priority"
      }
    ]
  }
}
```

### 删除流量通道
```json
{
  "task_type": "TASK_TRAFFIC_CHANNEL",
  "task_action": "DELETE_CONFIG",
  "traffic_channel_task": {
    "name": "channel_1"
  }
}
```
