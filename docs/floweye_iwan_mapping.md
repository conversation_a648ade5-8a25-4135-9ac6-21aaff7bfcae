1. iWAN MAPPING字段
字段	释义	字段类型	必填项	示例	备注
proxy	映射线路	string	是	wan1	proxy+port作为唯一标识；
一个wan的唯一 port 对应一个iwan service，对应service 改变时会被替换。
port	映射端口	string	是	8080	
server	iWAN服务	string	是	iwan-service	

⚠️一个wan的唯一 port 对应一个iwan service，对应service 改变时会被替换。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. iWAN MAPPING 查询
floweye iwanmap list
查询为全量查询，没有具体查询指定映射的方法。
[root@Panabit:~]# floweye iwanmap list
wan1 16 666 iwan-service 12 0
wan1 16 7788 iwan-service 12 0
wan1 16 7797 aaa 14 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0
wan 1 8000 aaa 14 0
<映射线路名称> <映射线路ID> <映射端口> <iWAN服务> <iWAN服务ID> <未知字段>

3. iWAN MAPPING 配置
3.1 设置 iWAN MAPPING 
floweye iwanmap set <proxy=映射线路名称> <port=映射线路端口> <server=iWAN服务名称>
[root@Panabit:~]# floweye iwanmap list
wan1 16 7788 iwan-service 12 0
wan1 16 7797 iwan-service 12 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0
wan 1 8000 aaa 14 0
[root@Panabit:~]#
[root@Panabit:~]#
[root@Panabit:~]# floweye iwanmap set proxy=wan1 port=666 server=iwan-service
[root@Panabit:~]# floweye iwanmap list
wan1 16 666 iwan-service 12 0
wan1 16 7788 iwan-service 12 0
wan1 16 7797 iwan-service 12 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0
wan 1 8000 aaa 14 0
⚠️新增方法，修改方法相同；
⚠️一个wan的唯一 port 对应一个iwan service，对应service 改变时会被替换，例如：
[root@Panabit:~/agent]# floweye iwanmap list
wan1 16 666 iwan-service 12 0
wan1 16 7788 iwan-service 12 0
wan1 16 7797 aaa 14 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0
wan 1 8000 aaa 14 0
[root@Panabit:~/agent]# floweye iwanmap set proxy=wan1 port=666 server=aaa
[root@Panabit:~/agent]# floweye iwanmap list
wan1 16 666 aaa 14 0
wan1 16 7788 iwan-service 12 0
wan1 16 7797 aaa 14 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0
wan 1 8000 aaa 14 0

3.2 删除iWAN MAPPING
floweye iwanmap set <proxy=映射线路名称或id> <port=映射线路端口> <server=NULL>
删除方法与设置方法相同，server置空。
[root@Panabit:~]# floweye iwanmap list
wan1 16 666 iwan-service 12 0
wan1 16 7788 iwan-service 12 0
wan1 16 7797 aaa 14 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0
[root@Panabit:~]# floweye iwanmap set proxy=wan1 port=666 server=NULL
[root@Panabit:~]# floweye iwanmap list
wan1 16 7788 iwan-service 12 0
wan1 16 7797 aaa 14 0
wan 1 7799 iwan-service 12 0
wan1 16 8000 iwan-service 12 0

