# WAN模块配置实现文档

## 概述

WAN模块负责处理广域网接口的配置，包括静态IP、DHCP和PPPoE等连接方式的设置。本文档描述了WAN模块的实现方案和关键代码。

## 配置项

根据`floweye_wan.md`文档和`message.proto`定义，WAN模块支持以下配置项：

1. **基本配置**
   - 名称(name) - 必填，WAN接口名称
   - 网卡名称(ifname) - 必填，如eth0
   - MTU(mtu) - 必填，最大传输单元

2. **连接类型**
   - 静态IP(StaticIp)
   - DHCP(Dhcp)
   - PPPoE(Pppoe)

3. **心跳配置(Heartbeat)**
   - 心跳服务器IP(ping_ip)
   - 备用心跳服务器IP(ping_ip2)
   - 最大延迟(max_delay)

4. **通用配置(Common)**
   - DNS代理(dns_pxy)
   - 禁用Ping响应(ping_disable)
   - MAC地址克隆(clone_mac)

## 配置一致性和同步机制

根据`Configuration_Consistency_and_Synchronization_Mechanism.md`文档，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye nat listproxy`和`floweye nat getproxy`命令）
   - 将配置缓存在内存中

2. **配置处理策略**
   - 对象不在全量配置中：执行创建流程
   - 对象已在全量配置中：
     - 配置一致：忽略，无需修改
     - 配置不一致：替换配置
   - 每处理完一个对象后，从全量配置缓存中移除

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 处理剩余的本地配置（这些是需要删除的对象）
   - 清理资源

### 增量同步

1. **配置处理**
   - 获取本地全量配置
   - 按与全量配置一致的逻辑处理对象
   - 不在每次配置成功后重新获取全量配置，而是在下次配置时再获取

## 实现方案

### 关键数据结构

1. **WanConfig**
   - 表示本地WAN配置
   - 包含名称、类型、接口名称、MTU、IP地址、网关等信息

2. **WanProcessor**
   - 处理WAN配置任务
   - 维护本地配置缓存
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 实现全量同步和增量同步逻辑

### 关键方法

1. **refreshLocalConfigs()**
   - 获取本地全量配置
   - 更新配置缓存

2. **StartFullSync()**
   - 开始全量同步
   - 获取本地全量配置

3. **EndFullSync()**
   - 结束全量同步
   - 处理剩余的本地配置
   - 清理资源

4. **handleNewConfig()**
   - 处理新建配置任务
   - 实现配置一致性检查
   - 根据WAN类型构建不同的命令
   - 执行配置命令并验证结果

5. **handleEditConfig()**
   - 处理编辑配置任务
   - 如果WAN不存在，则创建
   - 如果WAN类型发生变化（如从静态IP变为DHCP），先删除再创建
   - 如果配置一致，则无需修改
   - 根据WAN类型构建不同的命令
   - 执行配置命令并验证结果

6. **handleDeleteConfig()**
   - 处理删除配置任务
   - 根据WAN类型构建不同的删除命令
   - 执行删除命令并验证结果

7. **GetWanConfig()**
   - 获取指定WAN的配置
   - 使用结构化的方式解析key-value格式的命令输出
   - 将结果转换为易于处理的结构化数据

8. **VerifyWanConfig()**
   - 验证配置是否成功应用
   - 确保命令执行成功后配置确实生效

9. **CompareWanConfig()**
   - 比较请求的配置与本地配置
   - 决定是否需要修改配置

## 命令行交互

WAN模块通过以下命令与系统交互：

1. **查询所有WAN**
   ```
   floweye nat listproxy type=wan json=1
   ```

2. **查询特定WAN**
   ```
   floweye nat getproxy <WAN名>
   ```

3. **配置静态IP WAN**
   ```
   floweye nat addproxy name=<WAN名> ifname=<网卡名> mtu=<MTU> addr=<IP地址> gateway=<网关> ...
   ```

4. **配置DHCP WAN**
   ```
   floweye nat adddhcpwan name=<WAN名> ifname=<网卡名> mtu=<MTU> ...
   ```

5. **配置PPPoE WAN**
   ```
   floweye nat addpppoe name=<WAN名> ifname=<网卡名> mtu=<MTU> username=<用户名> password=<密码> ...
   ```

6. **修改WAN配置**
   ```
   floweye nat setproxy name=<WAN名> newname=<新WAN名> ...
   floweye nat setdhcpwan name=<WAN名> newname=<新WAN名> ...
   floweye nat setpppoe name=<WAN名> newname=<新WAN名> ...
   ```

7. **删除WAN配置**
   ```
   floweye nat delproxy <WAN名>
   floweye nat deldhcpwan <WAN名>
   floweye nat delpppoe <WAN名>
   ```

## 实现特点

1. **结构化配置解析**
   - 使用map来解析key-value格式的命令输出
   - 提高了解析效率和可读性

2. **统一的全量同步接口**
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 支持全量同步和增量同步

3. **配置验证机制**
   - 在应用配置后验证是否成功
   - 确保命令执行成功后配置确实生效

4. **配置比对机制**
   - 在应用配置前先检查是否与本地配置一致
   - 如果一致则无需修改，提高效率

5. **WAN类型自适应**
   - 根据不同的WAN类型（静态IP、DHCP、PPPoE）自动选择不同的命令
   - 支持不同类型WAN的创建、修改和删除
   - 当WAN类型变化时（如从静态IP变为DHCP），自动先删除再创建

## 注意事项

1. 必须指定WAN类型（静态IP、DHCP或PPPoE）
2. 静态IP配置必须提供IP地址和网关
3. PPPoE配置必须提供用户名和密码
4. 命令返回0不一定代表成功，需要再次查询确认配置是否生效
5. 当WAN类型发生变化时（如从静态IP变为DHCP），系统会先删除现有WAN配置，然后创建新的WAN配置

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 缺少必要参数：验证并返回明确的错误信息

## 测试方案

1. 单元测试：验证处理器的基本功能和错误处理
2. 集成测试：验证与floweye命令的交互
3. 系统测试：验证配置在实际环境中的生效情况
4. 全量同步测试：验证全量同步机制
5. 增量同步测试：验证增量同步机制

## 后续优化

1. 优化本地配置缓存的更新策略
2. 增加配置批量处理能力
3. 增加对WAN状态的监控和上报
4. 实现更细粒度的错误处理和恢复机制
5. 优化JSON解析逻辑，提高解析效率和稳定性
