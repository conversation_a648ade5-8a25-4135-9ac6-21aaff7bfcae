# SR 路径监控系统集成指南

## 概述

SR 路径监控系统为 SDWAN CPE 提供了自动路径监控和切换功能。当控制器下发多个 SR 路径时，系统会监控当前活跃路径的状态，并在路径失效时自动切换到备用路径。

## 核心特性

- **自动路径监控**：定期检查当前 SR 路径的活跃状态
- **智能路径切换**：当路径连续失败达到阈值时自动切换
- **并发安全**：使用读写锁保证多线程安全
- **可配置参数**：支持自定义检查间隔和失败阈值
- **幂等操作**：注册、注销等操作支持重复调用
- **与现有系统集成**：无缝集成到现有的 SR Proxy 处理器

## 架构设计

### 核心组件

1. **SRPathMonitor 接口**：定义监控操作的标准接口
2. **srPathMonitorImpl**：监控系统的具体实现
3. **SRPathMonitorManager**：高级管理器，提供便捷的集成接口
4. **SRMonitorConfig**：监控配置结构

### 数据结构

```go
// SR 路径定义
type SRPath struct {
    Links []int32 // iWAN Proxy分段标识列表
}

// SR 实例监控信息
type SRPathInfo struct {
    Name       string    // SR实例名称
    Paths      []SRPath  // 所有可用的路径
    ActivePath int       // 当前使用的路径索引
    FailCount  int       // 当前路径连续失败次数
    LastCheck  time.Time // 最后检查时间
}

// 监控配置
type SRMonitorConfig struct {
    CheckInterval  time.Duration                        // 检测间隔
    MaxFailCount   int                                  // 最大失败次数
    IsPathActiveFn func(srName string, path SRPath) bool // 路径活跃检查函数
    SetPathFn      func(srName string, path SRPath) error // 路径设置函数
}
```

## 使用方法

### 1. 基本使用

```go
// 创建 logger
logger := logger.NewLogger("sr-monitor", "info")

// 创建监控管理器
manager := NewSRPathMonitorManager(logger)

// 启动监控
ctx := context.Background()
err := manager.Start(ctx)
if err != nil {
    log.Fatal("Failed to start monitor:", err)
}
defer manager.Stop()

// 注册 SR 实例进行监控
pbPaths := []*pb.SRpath{
    {Links: []int32{443, 999}},
    {Links: []int32{444, 1000}},
}
err = manager.RegisterFromProtobuf("sr1", pbPaths)
if err != nil {
    log.Fatal("Failed to register SR:", err)
}
```

### 2. 与 SR Proxy 处理器集成

在 `SrProxyProcessor` 中集成监控系统：

```go
type SrProxyProcessor struct {
    logger             *logger.Logger
    localConfigs       map[string]*SrProxyConfig
    fullSyncInProgress bool
    pathMonitor        *SRPathMonitorManager // 新增监控管理器
}

func NewSrProxyProcessor(log *logger.Logger) *SrProxyProcessor {
    processor := &SrProxyProcessor{
        logger:             log.WithModule("sr-proxy-processor"),
        localConfigs:       make(map[string]*SrProxyConfig),
        fullSyncInProgress: false,
        pathMonitor:        NewSRPathMonitorManager(log), // 初始化监控管理器
    }
    
    // 启动监控系统
    ctx := context.Background()
    if err := processor.pathMonitor.Start(ctx); err != nil {
        log.Error("Failed to start SR path monitor", zap.Error(err))
    }
    
    return processor
}
```

### 3. 处理控制器下发的多路径配置

当控制器下发包含多个路径的 SR 配置时：

```go
func (p *SrProxyProcessor) handleConfigChange(ctx context.Context, srProxyTask *pb.SrProxyTask, taskAction pb.TaskAction) (string, error) {
    // 现有的配置处理逻辑...
    
    // 如果任务包含多个路径，注册到监控系统
    if len(srProxyTask.GetPaths()) > 1 {
        err := p.pathMonitor.RegisterFromProtobuf(srProxyTask.GetName(), srProxyTask.GetPaths())
        if err != nil {
            p.logger.Warn("Failed to register SR for path monitoring",
                zap.String("name", srProxyTask.GetName()),
                zap.Error(err))
        } else {
            p.logger.Info("SR registered for path monitoring",
                zap.String("name", srProxyTask.GetName()),
                zap.Int("path_count", len(srProxyTask.GetPaths())))
        }
    }
    
    // 继续现有的配置逻辑...
}
```

### 4. 处理删除配置

当删除 SR 配置时，同时从监控系统中注销：

```go
func (p *SrProxyProcessor) handleDeleteConfig(ctx context.Context, srProxyTask *pb.SrProxyTask) (string, error) {
    // 从监控系统中注销
    err := p.pathMonitor.Unregister(srProxyTask.GetName())
    if err != nil {
        p.logger.Warn("Failed to unregister SR from path monitoring",
            zap.String("name", srProxyTask.GetName()),
            zap.Error(err))
    }
    
    // 继续现有的删除逻辑...
}
```

## 配置参数

### 默认配置

```go
config := &SRMonitorConfig{
    CheckInterval:  10 * time.Second, // 每10秒检查一次
    MaxFailCount:   3,                // 连续3次失败后切换
    IsPathActiveFn: DefaultIsPathActiveFn(logger),
    SetPathFn:      DefaultSetPathFn(logger),
}
```

### 自定义配置

```go
// 创建自定义配置
config := &SRMonitorConfig{
    CheckInterval:  10 * time.Second, // 更频繁的检查
    MaxFailCount:   5,                // 更高的失败容忍度
    IsPathActiveFn: customPathChecker,
    SetPathFn:      customPathSetter,
}

monitor := NewSRPathMonitor(logger, config)
```

## 监控逻辑

### 路径检查流程

1. **定时检查**：每隔 `CheckInterval` 检查所有注册的 SR 实例
2. **状态判断**：调用 `IsPathActiveFn` 检查当前路径是否活跃
3. **失败计数**：如果路径不活跃，增加失败计数；如果活跃，重置计数
4. **路径切换**：当失败计数达到 `MaxFailCount` 时，切换到下一个路径
5. **循环切换**：按顺序循环使用所有可用路径

### 默认路径检查函数

```go
func DefaultIsPathActiveFn(logger *logger.Logger) func(string, SRPath) bool {
    return func(srName string, path SRPath) bool {
        // 使用 floweye 命令检查 SR proxy 状态
        // floweye nat getproxy sr1 | grep sre_srpxy.active
        output, err := utils.ExecuteCommand(logger, 5, "floweye", "nat", "getproxy", srName)
        if err != nil {
            return false
        }

        configMap := parseSRKeyValueOutput(output)
        activeStatus, exists := configMap["sre_srpxy.active"]
        if !exists {
            return false
        }

        // sre_srpxy.active=1 表示 active
        return activeStatus == "1"
    }
}
```

### 默认路径设置函数

```go
func DefaultSetPathFn(logger *logger.Logger) func(string, SRPath) error {
    return func(srName string, path SRPath) error {
        // 构建 links 参数
        linkStrs := make([]string, len(path.Links))
        for i, link := range path.Links {
            linkStrs[i] = strconv.Itoa(int(link))
        }
        linksParam := strings.Join(linkStrs, ",")
        
        // 执行 floweye 命令设置新路径
        cmdArgs := []string{"nat", "setsrpxy", srName, "links=" + linksParam}
        
        output, err := utils.ExecuteCommand(logger, 10, "floweye", cmdArgs...)
        if err != nil {
            return fmt.Errorf("failed to set SR path: %w", err)
        }
        
        return nil
    }
}
```

## API 接口

### SRPathMonitor 接口

```go
type SRPathMonitor interface {
    Register(srName string, paths []SRPath) error
    Unregister(srName string) error
    Start(ctx context.Context) error
    Stop() error
    GetStatus(srName string) (*SRPathInfo, error)
}
```

### SRPathMonitorManager 方法

```go
// 启动和停止
func (m *SRPathMonitorManager) Start(ctx context.Context) error
func (m *SRPathMonitorManager) Stop() error

// 注册和注销
func (m *SRPathMonitorManager) RegisterFromProtobuf(srName string, pbPaths []*pb.SRpath) error
func (m *SRPathMonitorManager) RegisterFromConfig(config *SrProxyConfig) error
func (m *SRPathMonitorManager) Unregister(srName string) error

// 状态查询
func (m *SRPathMonitorManager) GetStatus(srName string) (*SRPathInfo, error)
```

## 错误处理

### 常见错误

1. **注册错误**：
   - 空的 SR 名称
   - 空的路径列表
   - 路径格式错误

2. **运行时错误**：
   - 路径检查失败
   - 路径设置失败
   - 网络连接问题

3. **状态错误**：
   - 重复启动监控
   - 查询不存在的 SR 实例

### 错误恢复

- **路径检查失败**：继续监控，等待下次检查
- **路径设置失败**：记录错误，保持当前路径
- **网络问题**：自动重试，使用指数退避

## 性能考虑

### 资源使用

- **内存**：每个 SR 实例约占用 1KB 内存
- **CPU**：检查操作轻量级，对系统影响最小
- **网络**：仅在路径切换时产生网络调用

### 优化建议

1. **合理设置检查间隔**：平衡响应速度和资源消耗
2. **适当的失败阈值**：避免频繁切换造成的抖动
3. **批量操作**：在可能的情况下批量处理多个 SR 实例

## 测试

### 单元测试

运行单元测试：

```bash
go test -v ./internal/client/task -run TestSRPathMonitor
```

### 集成测试

```bash
# 测试与 floweye 命令的集成
go test -v ./internal/client/task -run TestSRPathMonitor_Integration
```

## 故障排除

### 常见问题

1. **路径不切换**：
   - 检查 `IsPathActiveFn` 是否正确实现
   - 验证失败计数是否达到阈值
   - 确认有多个可用路径

2. **频繁切换**：
   - 增加 `MaxFailCount` 值
   - 延长 `CheckInterval` 间隔
   - 检查路径状态检查逻辑

3. **监控不工作**：
   - 确认监控已启动
   - 检查 SR 实例是否已注册
   - 验证日志输出

### 调试方法

1. **启用调试日志**：设置日志级别为 `debug`
2. **查看监控状态**：使用 `GetStatus` 方法
3. **监控日志输出**：关注路径检查和切换日志

## 总结

SR 路径监控系统提供了一个完整的解决方案，用于自动监控和切换 SR 路径。通过合理的配置和集成，可以显著提高 SDWAN CPE 的可靠性和自动化水平。
