1. 策略时段字段
字段		释义	字段类型	必填项	示例	备注
id		策略时段ID	int	是	1	唯一标识；
范围 1 - 65535
name		策略时段名称	string	是	work-time	
策略时段起始	startwday	星期几	int	是	1	startwday=1 endwday=7 start=08:00:00 end=20:30:00
	starthour	小时	int	是	0	
	startmin	分钟	int	是	0	
	startsec	秒	int	是	0	
策略时段终止	endwday	星期几	int	是	0	
	endhour	小时	int	是	0	
	endmin	分钟	int	是	0	
	endsec	秒	int	是	0	

⚠️策略时段对路由/NAT 及 DNS管控生效。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. 策略时段查询
2.1 查询所有策略时段
floweye rtptime list
[root@Panabit:~/agent]# floweye rtptime list
1 strg1 1 05 00 00 1 23 59 59
2 tet-time 1 05 00 00 1 23 59 59
<策略时段ID> <策略时段名称> <startwday> <starthour> <startmin> <startsec> <endwday> <endhour> <endmin> <endsec>

2.2 查询指定策略时段
floweye rtptime get <id=>
[root@Panabit:~/agent]# floweye rtptime get id=2
id=2
name=tet-time
startwday=1
starthour=5
startmin=0
startsec=0
endwday=1
endhour=23
endmin=59
endsec=59

3. 策略时段配置
3.1 新增策略时段
floweye rtptime add <id=> <name=> <startwday=> <endwday=> <start=> <end=>
[root@Panabit:~/agent]# floweye rtptime list
1 strg1 1 05 00 00 1 23 59 59
2 tet-time 1 05 00 00 1 23 59 59
[root@Panabit:~/agent]# floweye rtptime add id=10 name=effective-time-test startwday=1 endwday=7 start=08:00:00 end=20:30:00
[root@Panabit:~/agent]# floweye rtptime list
1 strg1 1 05 00 00 1 23 59 59
2 tet-time 1 05 00 00 1 23 59 59
10 effective-time-test 1 08 00 00 7 20 30 00

3.2 修改策略时段
floweye rtptime set <id=> <name=> <startwday=> <endwday=> <start=> <end=>
[root@Panabit:~/agent]# floweye rtptime get id=10
id=10
name=effective-time-test
startwday=1
starthour=8
startmin=0
startsec=0
endwday=7
endhour=20
endmin=30
endsec=0
[root@Panabit:~/agent]# floweye rtptime set id=10 name=tet-time startwday=2 endwday=5 start=10:00:00 end=23:59:59
[root@Panabit:~/agent]# floweye rtptime get id=10
id=10
name=tet-time
startwday=2
starthour=10
startmin=0
startsec=0
endwday=5
endhour=23
endmin=59
endsec=59

3.3 删除策略时段
floweye rtptime remove <id=>
[root@Panabit:~/agent]# floweye rtptime list
1 strg1 1 05 00 00 1 23 59 59
10 tet-time 2 10 00 00 5 23 59 59
[root@Panabit:~/agent]# floweye rtptime remove id=10
[root@Panabit:~/agent]# floweye rtptime list
1 strg1 1 05 00 00 1 23 59 59

