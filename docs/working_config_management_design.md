# 通用工作配置管理设计方案

## 背景问题

在全量配置期间，所有模块都不能通过`refreshLocalConfigs`来更新本地缓存，因为这会破坏全量同步逻辑。但是操作过程中，可能会涉及到使用最新的configs来进行各种操作：

- **排序操作**：需要最新的配置列表来确定正确的排序位置
- **启用/禁用操作**：需要最新的配置来获取正确的ID和状态
- **修改操作**：需要最新的配置来验证和更新
- **ID获取**：新增配置后需要获取分配的ID
- **验证操作**：需要最新的配置来进行状态验证

### 核心冲突

1. **全量同步期间**：`localConfigs`用于冗余配置删除，不能被刷新
2. **操作需求**：各种操作都需要最新的配置信息
3. **非全量同步期间**：可以正常使用`refreshLocalConfigs`

### 影响的模块类型

1. **排序相关模块**：flow control policy, flow control group policy, route policy, dns policy, dns tracking policy
2. **配置管理模块**：lan, wan, iwan service, dhcp, interface等
3. **策略管理模块**：所有需要动态获取配置状态的模块
4. **服务管理模块**：需要启用/禁用功能的服务模块

## 解决方案设计

### 核心思路

创建独立的**工作配置（Working Configs）**，与用于全量同步冗余删除的**本地配置（Local Configs）**分离：

- **Local Configs**：仅用于全量同步的冗余配置删除
- **Working Configs**：用于所有操作（排序、启用/禁用、获取ID等）

### 通用架构设计

```go
// 通用模式：任何Processor都可以采用这种设计
type GenericProcessor struct {
    logger               *logger.Logger

    // 用于全量同步冗余删除的配置（只在StartFullSync填充，EndFullSync清理）
    localConfigs         map[string]*ConfigType
    localNameToID        map[string]int

    // 用于所有操作的工作配置（随时可刷新，用于所有操作）
    workingConfigs       map[string]*ConfigType
    workingNameToID      map[string]int

    fullSyncInProgress   bool
}

// 具体示例：FlowControlProcessor
type FlowControlProcessor struct {
    logger               *logger.Logger

    // 全量同步专用配置
    localPolicyGroups    map[string]*PolicyGroupConfig
    localPolicies        map[uint32]*PolicyConfig
    groupNameToID        map[string]int

    // 操作专用工作配置
    workingPolicyGroups  map[string]*PolicyGroupConfig
    workingPolicies      map[uint32]*PolicyConfig
    workingGroupNameToID map[string]int

    fullSyncInProgress   bool
}

// 具体示例：LANProcessor
type LANProcessor struct {
    logger               *logger.Logger

    // 全量同步专用配置
    localConfigs         map[string]*LANConfig
    localNameToID        map[string]int

    // 操作专用工作配置
    workingConfigs       map[string]*LANConfig
    workingNameToID      map[string]int

    fullSyncInProgress   bool
}
```

## 实现细节

### 1. 配置获取逻辑重构

#### 通用配置获取模式

```go
// 通用模式：提取配置获取逻辑
func (p *GenericProcessor) fetchConfigs() (map[string]*ConfigType, map[string]int, error) {
    configs := make(map[string]*ConfigType)
    nameToID := make(map[string]int)

    // 1. 执行相应的floweye命令获取列表
    // 2. 解析配置IDs
    // 3. 获取每个配置的详细信息
    // 4. 构建配置映射

    return configs, nameToID, nil
}

// Flow Control示例
func (p *FlowControlProcessor) fetchFlowControlConfigs() (
    map[string]*PolicyGroupConfig,
    map[uint32]*PolicyConfig,
    map[string]int,
    error) {

    // 统一的配置获取逻辑
    // 1. 执行 floweye policygroup2 list
    // 2. 解析 policy group IDs
    // 3. 获取每个 group 的详细配置
    // 4. 获取每个 group 的 policies
    // 5. 获取每个 policy 的详细配置

    return policyGroups, policies, groupNameToID, nil
}

// LAN模块示例
func (p *LANProcessor) fetchLANConfigs() (map[string]*LANConfig, map[string]int, error) {
    configs := make(map[string]*LANConfig)
    nameToID := make(map[string]int)

    // 1. 执行 floweye lan list
    // 2. 解析 LAN IDs
    // 3. 获取每个 LAN 的详细配置

    return configs, nameToID, nil
}

// Route Policy示例
func (p *RoutePolicyProcessor) fetchRoutePolicyConfigs() (map[string]*RoutePolicyConfig, map[string]int, error) {
    configs := make(map[string]*RoutePolicyConfig)
    nameToID := make(map[string]int)

    // 1. 执行 floweye route list
    // 2. 解析 route policy IDs
    // 3. 获取每个 policy 的详细配置

    return configs, nameToID, nil
}
```

#### 通用刷新方法模式

```go
// 通用模式：本地配置刷新（仅用于全量同步）
func (p *GenericProcessor) refreshLocalConfigs() error {
    configs, nameToID, err := p.fetchConfigs()
    if err != nil {
        return err
    }

    p.localConfigs = configs
    p.localNameToID = nameToID
    return nil
}

// 通用模式：工作配置刷新（用于所有操作）
func (p *GenericProcessor) refreshWorkingConfigs() error {
    configs, nameToID, err := p.fetchConfigs()
    if err != nil {
        return err
    }

    p.workingConfigs = configs
    p.workingNameToID = nameToID
    return nil
}

// Flow Control具体实现
func (p *FlowControlProcessor) refreshLocalConfigs() error {
    policyGroups, policies, groupNameToID, err := p.fetchFlowControlConfigs()
    if err != nil {
        return err
    }

    p.localPolicyGroups = policyGroups
    p.localPolicies = policies
    p.groupNameToID = groupNameToID
    return nil
}

func (p *FlowControlProcessor) refreshWorkingConfigs() error {
    policyGroups, policies, groupNameToID, err := p.fetchFlowControlConfigs()
    if err != nil {
        return err
    }

    p.workingPolicyGroups = policyGroups
    p.workingPolicies = policies
    p.workingGroupNameToID = groupNameToID
    return nil
}

// LAN模块具体实现
func (p *LANProcessor) refreshLocalConfigs() error {
    configs, nameToID, err := p.fetchLANConfigs()
    if err != nil {
        return err
    }

    p.localConfigs = configs
    p.localNameToID = nameToID
    return nil
}

func (p *LANProcessor) refreshWorkingConfigs() error {
    configs, nameToID, err := p.fetchLANConfigs()
    if err != nil {
        return err
    }

    p.workingConfigs = configs
    p.workingNameToID = nameToID
    return nil
}
```

### 2. 通用的操作配置获取

```go
// 通用模式：统一的操作配置获取入口
func (p *GenericProcessor) getConfigsForOperation() error {
    // 始终使用 working configs，简化逻辑
    return p.refreshWorkingConfigs()
}

// 所有模块都使用相同的模式
func (p *FlowControlProcessor) getConfigsForOperation() error {
    return p.refreshWorkingConfigs()
}

func (p *LANProcessor) getConfigsForOperation() error {
    return p.refreshWorkingConfigs()
}

func (p *RoutePolicyProcessor) getConfigsForOperation() error {
    return p.refreshWorkingConfigs()
}

func (p *DNSPolicyProcessor) getConfigsForOperation() error {
    return p.refreshWorkingConfigs()
}
```

### 3. 配置使用模式

#### 全量同步期间
- **Local Configs**：在 `StartFullSync` 时填充，在 `EndFullSync` 时清理
- **Working Configs**：随时可以刷新，用于所有操作

#### 非全量同步期间  
- **Local Configs**：不使用
- **Working Configs**：用于所有操作

## 最佳实践

### 1. 配置获取时机

```go
// ✅ 正确：在需要最新配置的操作前调用
func (p *FlowControlProcessor) handlePolicyGroupConfigChange(...) {
    // 获取最新配置用于后续操作
    if err := p.getConfigsForOperation(); err != nil {
        return fmt.Sprintf("Failed to get configurations: %v", err), err
    }
    
    // 执行创建/更新操作
    // ...
    
    // 再次获取配置以包含新创建/更新的对象
    if err := p.getConfigsForOperation(); err != nil {
        return fmt.Sprintf("Failed to refresh configs: %v", err), err
    }
    
    // 执行排序、启用/禁用等操作
    // ...
}
```

### 2. 排序操作中的配置使用

```go
// ✅ 正确：使用 working configs
func (p *FlowControlProcessor) handlePolicyGroupOrderingWithConfig(...) {
    var currentGroups []PolicyGroupOrderInfo
    for name, config := range p.workingPolicyGroups {
        currentGroups = append(currentGroups, PolicyGroupOrderInfo{
            ID:   config.ID,
            Name: name,
        })
    }
    // 执行排序逻辑...
}
```

### 3. 启用/禁用操作中的配置使用

```go
// ✅ 正确：使用 working configs
func (p *FlowControlProcessor) handlePolicyGroupEnableDisable(groupName string, disable bool) error {
    groupID, exists := p.workingGroupNameToID[groupName]
    if !exists {
        return fmt.Errorf("policy group '%s' not found in working cache", groupName)
    }
    // 执行启用/禁用逻辑...
}
```

### 4. 全量同步中的配置管理

```go
func (p *FlowControlProcessor) StartFullSync() error {
    p.fullSyncInProgress = true
    
    // 填充 local configs 用于冗余删除
    if err := p.refreshLocalConfigs(); err != nil {
        p.fullSyncInProgress = false
        return err
    }
    return nil
}

func (p *FlowControlProcessor) EndFullSync() {
    // 清理剩余的 local configs
    for groupName := range p.localPolicyGroups {
        // 删除冗余配置...
    }
    
    // 重置状态
    p.fullSyncInProgress = false
    p.localPolicyGroups = make(map[string]*PolicyGroupConfig)
    p.localPolicies = make(map[uint32]*PolicyConfig)
    p.groupNameToID = make(map[string]int)
    p.workingPolicyGroups = make(map[string]*PolicyGroupConfig)
    p.workingPolicies = make(map[uint32]*PolicyConfig)
    p.workingGroupNameToID = make(map[string]int)
}
```

## 适用模块

这个方案适用于**所有模块**，确保统一的配置管理模式：

### 排序相关模块
1. **Flow Control Policy Group** ✅ 已实现
2. **Flow Control Policy**
3. **Route Policy**
4. **DNS Policy**
5. **DNS Tracking Policy**

### 配置管理模块
6. **LAN Processor**
7. **WAN Processor**
8. **iWAN Service Processor**
9. **DHCP Processor**
10. **Interface Processor**

### 策略管理模块
11. **Traffic Channel Processor**
12. **Traffic Stat Processor**
13. **QoS Policy Processor**
14. **Security Policy Processor**

### 服务管理模块
15. **VPN Service Processor**
16. **Load Balancer Processor**
17. **Firewall Rule Processor**
18. **其他所有需要动态配置获取的模块**

### 统一性的好处
- **一致的代码模式**：所有模块使用相同的配置管理方式
- **降低学习成本**：开发者只需学习一种模式
- **简化维护**：统一的错误处理和调试方式
- **提高可靠性**：经过验证的模式减少bug风险

## 优势

1. **逻辑简化**：始终使用 working configs，无需复杂的条件判断
2. **功能完整**：支持排序、启用/禁用、获取ID等所有操作场景
3. **安全性**：全量同步期间不会破坏 local configs 的冗余删除逻辑
4. **性能**：避免不必要的配置复制操作
5. **可维护性**：统一的配置获取和使用模式
6. **可扩展性**：新的操作类型可以直接使用 working configs

## 注意事项

1. **及时刷新**：在创建/更新操作后，需要刷新 working configs 以获取最新状态
2. **错误处理**：配置获取失败时要有适当的错误处理和回退机制
3. **日志记录**：在配置刷新时添加适当的调试日志
4. **测试覆盖**：确保全量同步和非全量同步场景都有测试覆盖

## 技术实现细节

### 通用数据结构设计模式

```go
// 通用模式：所有Processor都应该遵循这个结构
type GenericProcessor struct {
    logger               *logger.Logger

    // 全量同步冗余删除专用配置（只在 StartFullSync 填充，EndFullSync 清理）
    localConfigs         map[string]*ConfigType
    localNameToID        map[string]int
    // 可能还有其他特定的local映射

    // 操作专用工作配置（随时可刷新，用于所有操作）
    workingConfigs       map[string]*ConfigType
    workingNameToID      map[string]int
    // 可能还有其他特定的working映射

    fullSyncInProgress   bool
}

// 具体实现示例
type FlowControlProcessor struct {
    logger               *logger.Logger

    // 全量同步专用配置
    localPolicyGroups    map[string]*PolicyGroupConfig
    localPolicies        map[uint32]*PolicyConfig
    groupNameToID        map[string]int

    // 操作专用工作配置
    workingPolicyGroups  map[string]*PolicyGroupConfig
    workingPolicies      map[uint32]*PolicyConfig
    workingGroupNameToID map[string]int

    fullSyncInProgress   bool
}

type LANProcessor struct {
    logger               *logger.Logger

    // 全量同步专用配置
    localConfigs         map[string]*LANConfig
    localNameToID        map[string]int

    // 操作专用工作配置
    workingConfigs       map[string]*LANConfig
    workingNameToID      map[string]int

    fullSyncInProgress   bool
}

type RoutePolicyProcessor struct {
    logger               *logger.Logger

    // 全量同步专用配置
    localZoneConfigs     map[string]map[string]*RoutePolicyConfig  // zone -> name -> config
    localPolicyNameToID  map[string]int

    // 操作专用工作配置
    workingZoneConfigs   map[string]map[string]*RoutePolicyConfig
    workingPolicyNameToID map[string]int

    fullSyncInProgress   bool
}
```

### 配置获取流程图

```
操作请求
    ↓
调用 getConfigsForOperation()
    ↓
调用 refreshWorkingConfigs()
    ↓
调用 fetchFlowControlConfigs()
    ↓
执行 floweye 命令获取最新配置
    ↓
解析并填充 workingConfigs
    ↓
操作使用 workingConfigs
```

### 错误处理策略

```go
func (p *FlowControlProcessor) getConfigsForOperation() error {
    if err := p.refreshWorkingConfigs(); err != nil {
        p.logger.Error("failed to refresh working configs", zap.Error(err))
        return fmt.Errorf("failed to get latest configurations: %w", err)
    }
    return nil
}

func (p *FlowControlProcessor) handlePolicyGroupConfigChange(...) {
    // 操作前获取配置
    if err := p.getConfigsForOperation(); err != nil {
        return fmt.Sprintf("Failed to get configurations: %v", err), err
    }

    // 执行操作...

    // 操作后刷新配置（用于后续排序等操作）
    if err := p.getConfigsForOperation(); err != nil {
        p.logger.Warn("failed to refresh configs after operation", zap.Error(err))
        // 不返回错误，因为主要操作已成功
    }
}
```

### 性能优化考虑

1. **按需刷新**：只在需要最新配置时才调用 `getConfigsForOperation()`
2. **缓存复用**：同一操作流程中避免重复刷新
3. **错误恢复**：配置获取失败时的降级策略

```go
// 示例：批量操作中的配置管理
func (p *FlowControlProcessor) handleBatchOperations(operations []Operation) error {
    // 批量操作开始前获取一次配置
    if err := p.getConfigsForOperation(); err != nil {
        return err
    }

    for _, op := range operations {
        // 执行操作，使用已缓存的 working configs
        if err := p.executeOperation(op); err != nil {
            return err
        }
    }

    // 批量操作结束后刷新配置
    return p.getConfigsForOperation()
}
```

## 测试策略

### 单元测试

```go
func TestWorkingConfigManagement(t *testing.T) {
    processor := NewFlowControlProcessor(logger)

    // 测试非全量同步期间的配置获取
    err := processor.getConfigsForOperation()
    assert.NoError(t, err)
    assert.NotEmpty(t, processor.workingPolicyGroups)

    // 测试全量同步期间的配置隔离
    processor.fullSyncInProgress = true
    processor.localPolicyGroups["test"] = &PolicyGroupConfig{}

    err = processor.getConfigsForOperation()
    assert.NoError(t, err)
    // working configs 应该独立更新，不影响 local configs
    assert.Contains(t, processor.localPolicyGroups, "test")
}
```

### 集成测试

```go
func TestFullSyncWithOperations(t *testing.T) {
    // 1. 启动全量同步
    err := processor.StartFullSync()
    assert.NoError(t, err)

    // 2. 在全量同步期间执行操作
    task := &pb.FlowControlPolicyGroupTask{Name: "test_group"}
    result, err := processor.handlePolicyGroupConfigChange(ctx, task, pb.TaskAction_NEW_CONFIG)
    assert.NoError(t, err)

    // 3. 验证 local configs 和 working configs 的独立性
    assert.Contains(t, processor.workingPolicyGroups, "test_group")
    // local configs 应该在 EndFullSync 时用于冗余删除

    // 4. 结束全量同步
    processor.EndFullSync()
}
```

## 迁移指南

### 现有代码迁移步骤

1. **添加 working configs 字段**
```go
// 在 processor 结构体中添加
workingPolicyGroups  map[string]*PolicyGroupConfig
workingPolicies      map[uint32]*PolicyConfig
workingGroupNameToID map[string]int
```

2. **提取共同逻辑**
```go
// 将 refreshLocalConfigs 中的逻辑提取到 fetchFlowControlConfigs
func (p *Processor) fetchFlowControlConfigs() (configs..., error) {
    // 原有的 floweye 查询和解析逻辑
}
```

3. **更新操作方法**
```go
// 将所有使用 localConfigs 的操作改为使用 workingConfigs
// 将所有 refreshLocalConfigs 调用改为 getConfigsForOperation
```

4. **更新构造函数**
```go
func NewProcessor(logger *logger.Logger) *Processor {
    return &Processor{
        // 初始化所有 working configs
        workingPolicyGroups:  make(map[string]*PolicyGroupConfig),
        workingPolicies:      make(map[uint32]*PolicyConfig),
        workingGroupNameToID: make(map[string]int),
    }
}
```

## 实施步骤

1. **第一阶段**：Flow Control Group Policy（已完成）
2. **第二阶段**：Flow Control Policy
3. **第三阶段**：Route Policy
4. **第四阶段**：DNS Policy
5. **第五阶段**：DNS Tracking Policy
6. **第六阶段**：统一测试和验证

## 总结

这个工作配置管理方案通过分离关注点，解决了全量同步期间的配置获取冲突问题：

- **简化逻辑**：统一使用 working configs
- **保证安全**：不影响全量同步的冗余删除逻辑
- **提升性能**：避免不必要的配置复制
- **增强可维护性**：清晰的职责分离和统一的使用模式

该方案已在 Flow Control Group Policy 模块中成功实现，可以作为其他排序模块的参考模板。

## 快速参考

### 关键方法签名

```go
// 统一配置获取逻辑
func (p *Processor) fetchFlowControlConfigs() (
    map[string]*PolicyGroupConfig,
    map[uint32]*PolicyConfig,
    map[string]int,
    error)

// 刷新本地配置（仅用于全量同步）
func (p *Processor) refreshLocalConfigs() error

// 刷新工作配置（用于所有操作）
func (p *Processor) refreshWorkingConfigs() error

// 获取操作配置（统一入口）
func (p *Processor) getConfigsForOperation() error
```

### 使用模式检查清单

- [ ] 操作前调用 `getConfigsForOperation()`
- [ ] 使用 `workingConfigs` 而不是 `localConfigs`
- [ ] 创建/更新后再次调用 `getConfigsForOperation()`
- [ ] 在构造函数中初始化所有 working configs
- [ ] 在 `EndFullSync` 中清理所有 configs

### 常见错误避免

❌ **错误**：在全量同步期间调用 `refreshLocalConfigs`
```go
if !p.fullSyncInProgress {
    p.refreshLocalConfigs() // 条件判断容易出错
}
```

✅ **正确**：始终使用 `getConfigsForOperation`
```go
p.getConfigsForOperation() // 内部处理所有逻辑
```

❌ **错误**：混用 local 和 working configs
```go
groupID := p.groupNameToID[name]        // local config
config := p.workingPolicyGroups[name]   // working config
```

✅ **正确**：统一使用 working configs
```go
groupID := p.workingGroupNameToID[name]  // working config
config := p.workingPolicyGroups[name]    // working config
```
