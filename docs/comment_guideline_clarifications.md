# 注释规范澄清备忘录

本文档记录了在代码注释整改过程中，从 `coding_guideline.md` 延伸出来的，经过讨论确认的具体实践细则。所有后续的注释修改都应严格遵守此备忘录。

## 1. 函数注释 (Godoc)

所有导出的函数、方法都必须有符合 `gofmt` 规范的 godoc 注释。

### 1.1 描述与返回值

- **返回值应在描述中说明**：函数的返回值说明必须自然地融入到函数描述的最后一句话中，而不是使用一个单独的 `Returns:` 列表。

  **✅ 正确示例:**
  ```go
  // MyFunction does something interesting and returns a boolean indicating success.
  ```

  **❌ 错误示例:**
  ```go
  // MyFunction does something interesting.
  //
  // Returns:
  //   - bool: true if successful, false otherwise.
  ```

### 1.2 参数列表

- **格式**: 如果需要列出参数，必须使用 `Parameters:` 作为标题，且每个参数以 `-` 开头，与标题之间有两个空格的缩进。

  **✅ 正确示例:**
  ```go
  // MyFunction does something interesting and returns a boolean indicating success.
  //
  // Parameters:
  //   - param1: The first parameter, which is a string.
  //   - param2: The second parameter, an integer.
  ```

### 1.3 注释风格

- **必须使用 `//`**：所有的 godoc 注释（包括多行）都必须使用 `//` 单行注释风格。
- **禁止使用 `/* ... */`**：块注释仅用于 `main` 包的命令介绍，绝不能用于函数或类型的 godoc 注释。

## 2. 类型注释 (Structs, Interfaces)

- **字段注释**: 对于 `struct` 等复合类型，如果原始注释中包含 `FIELDS` 列表，必须将这些描述转换为每个字段后面的行尾注释。修改时绝不能破坏类型原有的字段定义和顺序。

  **✅ 正确示例:**
  ```go
  // MyStruct represents a configuration.
  type MyStruct {
      FieldA string // The name of the configuration.
      FieldB int    // The value of the configuration.
  }
  ```

  **❌ 错误示例 (破坏了原始定义):**
  ```go
  // MyStruct represents a configuration.
  // FIELDS:
  //     FieldA - The name of the configuration.
  //     FieldB - The value of the configuration.
  type MyStruct {
      FieldA string
      FieldB int
  }
  ```

## 3. 函数内部注释

- **必须使用 `//`**: 函数实现内部的所有注释，无论是单行还是多行，都必须使用 `//` 风格。
- **保留有效信息**: 如果函数内部有作者留下的、对理解代码逻辑有帮助的注释（例如 `Workflow`），应将其保留，并转换为 `//` 风格。

## 4. 语言和翻译

- **翻译而非删除**: 对于代码中原有的中文注释，应将其翻译成准确的英文，而不是直接删除。
