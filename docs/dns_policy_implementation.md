# DNS策略模块实现文档

## 概述

DNS策略模块是Agent系统中用于管理DNS管控策略的核心组件。该模块实现了完整的DNS策略生命周期管理，包括策略的创建、修改、删除、排序和验证。

## 实现架构

### 核心组件

1. **DnsPolicyProcessor** - 主要处理器，实现TaskProcessor接口
2. **DnsPolicyConfig** - DNS策略配置结构体
3. **解析函数** - 用于解析floweye命令输出
4. **比较函数** - 用于配置一致性检查
5. **排序逻辑** - 实现策略排序管理

### 文件结构

```
internal/client/task/
├── dns_policy_processor.go    # 主处理器实现
├── dns_policy_config.go       # 配置结构体和解析函数
├── dns_policy_helper.go       # 辅助函数和命令构建
└── dns_policy_ordering.go     # 排序和验证逻辑
```

## 功能特性

### 1. 策略管理
- **创建策略**: 支持新建DNS策略配置
- **修改策略**: 支持编辑现有策略
- **删除策略**: 支持删除策略并维护连续排序
- **配置验证**: 确保策略配置正确应用

### 2. 排序管理
- **基于previous字段的排序**: 实现策略插入位置控制
- **连续ID维护**: 删除后自动调整后续策略ID
- **双向移动**: 支持前向和后向插入

### 3. 同步模式
- **全量同步**: StartFullSync/EndFullSync支持
- **增量同步**: 单个策略的增量更新
- **配置清理**: 全量同步结束时清理未使用配置

### 4. 动作类型支持
- **pass**: 放行动作，支持QPS限制和继续匹配配置
- **deny**: 丢弃动作
- **rdr**: 牵引动作，支持线路配置和DNS列表
- **reply**: 解析动作，支持自定义解析IP
- **limit**: QPS限制动作
- **ippxy**: 代播重定向动作
- **zeroreply**: 无名应答动作

## 技术实现

### 1. Protobuf定义

在`docs/message.proto`中添加了完整的DNS策略定义：

```protobuf
// DNS策略任务类型
TASK_DNS_POLICY = 18;

// DNS查询类型枚举
enum DnsQueryType {
  DNS_QUERY_TYPE_ANY = 0;
  DNS_QUERY_TYPE_IPV4 = 1;
  DNS_QUERY_TYPE_IPV6 = 2;
}

// DNS策略动作类型枚举
enum DnsPolicyAction {
  DNS_ACTION_PASS = 0;
  DNS_ACTION_DENY = 1;
  DNS_ACTION_RDR = 2;
  DNS_ACTION_REPLY = 3;
  DNS_ACTION_LIMIT = 4;
  DNS_ACTION_IPPXY = 5;
  DNS_ACTION_ZEROREPLY = 6;
}

// DNS策略配置消息
message DnsPolicyTask {
  uint32 cookie = 1;
  optional uint32 previous = 2;
  bool disable = 3;
  // ... 其他字段
}
```

### 2. Floweye命令集成

实现了完整的floweye dnspolicy命令集成：

- `floweye dnspolicy list json=1` - 获取策略列表
- `floweye dnspolicy get id=<id>` - 获取详细配置
- `floweye dnspolicy add` - 添加新策略
- `floweye dnspolicy set` - 修改策略
- `floweye dnspolicy remove` - 删除策略

### 3. 配置解析

实现了两种解析模式：

1. **JSON解析**: 解析list命令的JSON输出
2. **键值对解析**: 解析get命令的key=value输出

### 4. 配置比较

实现了完整的配置比较功能，包括：

1. **基本字段比较**: cookie、disable、schtime等
2. **IP地址比较**: 支持多种IP格式的逐一对比
   - IPv4地址（uint32格式）
   - IPv6地址（[]byte格式）
   - IP字符串格式
   - CIDR格式（v4_cidr/v6_cidr）
   - 地址范围比较
   - IP群组名称比较
3. **VLAN配置比较**: 支持单个VLAN和VLAN范围
4. **应用协议比较**: 应用协议规范比较
5. **动作配置比较**: 不同动作类型的参数比较

### 4. 排序算法

实现了基于previous字段的智能排序：

- **前向插入**: 目标位置 < 当前位置
- **后向移动**: 目标位置 > 当前位置
- **位置计算**: previous cookie + 1 = 目标位置
- **ID调整**: 删除后自动递减后续策略ID

## 配置示例

### 基本DNS策略配置

```json
{
  "dns_policy_task": {
    "cookie": 30001,
    "disable": false,
    "schtime": 0,
    "in_ip": [
      {
        "ip": {
          "ipv4": "***********/24"
        }
      }
    ],
    "domain_group": ["malware_domains"],
    "atype": "DNS_QUERY_TYPE_ANY",
    "action": "DNS_ACTION_DENY"
  }
}
```

### 牵引动作配置

```json
{
  "dns_policy_task": {
    "cookie": 30002,
    "action": "DNS_ACTION_RDR",
    "action_rdr": {
      "act_arg": "line1",
      "no_snat": false,
      "dns_list": [
        {"ipv4": "*******"},
        {"ipv4": "*******"}
      ]
    }
  }
}
```

## 测试覆盖

实现了完整的单元测试：

1. **处理器测试**: 验证TaskProcessor接口实现
2. **解析测试**: 验证JSON和键值对解析功能
3. **比较测试**: 验证配置一致性检查，包括IP地址逐一对比
4. **依赖解析测试**: 验证IP群组和域名群组名称解析
5. **IP地址比较测试**: 验证多种IP格式的比较功能
6. **错误处理测试**: 验证各种错误场景

### 测试用例详情

#### IP地址比较测试
- **匹配的IP地址**: 验证相同IP配置的正确匹配
- **不匹配的源IP**: 验证源IP不同时的正确检测
- **空IP地址(any)**: 验证空IP配置被正确处理为"any"

#### 依赖解析测试
- **IP群组解析**: 验证调用`GetIpGroupIdByName()`接口
- **域名群组解析**: 验证调用`GetDomainGroupIdByName()`接口

## 集成测试

在`test/run_module_tests.sh`中添加了DNS策略的集成测试：

- 新建策略测试
- 编辑策略测试
- 排序测试
- 动作配置测试
- 全量同步测试
- 错误处理测试
- 删除策略测试

## 性能优化

1. **单对象检索**: 使用`floweye dnspolicy get`进行单个策略验证
2. **缓存机制**: 本地配置缓存减少重复查询
3. **批量操作**: 全量同步时的批量处理
4. **智能排序**: 最小化ID调整操作

## 错误处理

实现了完善的错误处理机制：

1. **NEXIST错误**: 删除不存在的策略视为成功
2. **命令失败**: 详细的错误日志和堆栈跟踪
3. **配置验证**: 严格的字段验证和类型检查
4. **依赖解析**: IP群组和域名群组的名称解析

## 日志记录

使用结构化日志记录关键操作：

- 策略创建/修改/删除
- 排序操作详情
- 配置比较结果
- 错误和警告信息

## 依赖解析

DNS策略模块现在完全集成了IP群组和域名群组模块的接口：

### IP群组解析
- 使用 `GetIpGroupIdByName()` 函数从 `ip_group_config.go`
- 执行 `floweye table list` 命令获取IP群组列表
- 支持完整的错误处理和日志记录

### 域名群组解析
- 使用 `GetDomainGroupIdByName()` 函数从 `domain_group_config.go`
- 执行 `floweye dns listgrp` 命令获取域名群组列表
- 支持完整的错误处理和日志记录

### 集成测试
```go
// 测试IP群组名称解析
_, err := processor.ResolveIPGroupNameToID("test_ip_group")

// 测试域名群组名称解析
_, err := processor.ResolveDomainGroupNameToID("test_domain_group")
```

## 未来扩展

1. **性能监控**: 添加操作耗时统计
2. **配置模板**: 支持策略模板功能
3. **批量操作**: 支持批量策略操作
4. **缓存优化**: 群组名称到ID的缓存机制

## 总结

DNS策略模块成功实现了完整的策略管理功能，遵循了项目的设计模式和最佳实践。该模块具有良好的可扩展性和维护性，为DNS管控提供了强大的支持。
