# WAN群组模块配置实现文档

## 概述

WAN群组模块负责处理WAN群组的配置，包括创建、修改、删除WAN群组以及管理WAN群组成员。本文档描述了WAN群组模块的实现方案和关键代码。

## 配置项

根据`floweye_wangroup.md`文档和`message.proto`定义，WAN群组模块支持以下配置项：

1. **基本配置**
   - 名称(name) - 必填，WAN群组名称，ASCII字符，最长15字节
   - ID(id) - 必填，群组ID，范围: 1-128
   - 类型(type) - 必填，群组类型

2. **群组类型**
   - srcdst - 源地址+目的地址
   - spdp - 源目地址+源目端口
   - src - 源地址
   - srcsport - 源地址+源端口
   - dst - 目的地址
   - dstdport - 目的地址+目的端口
   - leftbw - 最大空闲带宽

3. **成员配置**
   - 成员列表 - WAN成员名称列表

## 配置一致性和同步机制

根据`Configuration_Consistency_and_Synchronization_Mechanism.md`文档，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye wangroup list`和`floweye wangroup get`命令）
   - 将配置缓存在内存中

2. **配置处理策略**
   - 对象不在全量配置中：执行创建流程
   - 对象已在全量配置中：
     - 配置一致：忽略，无需修改
     - 配置不一致：
       - 更新名称和类型（如果有变化）
       - 比较成员列表，添加缺少的成员，删除多余的成员
   - 每处理完一个对象后，从全量配置缓存中移除

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 处理剩余的本地配置（这些是需要删除的对象）
   - 清理资源

### 增量同步

1. **配置处理**
   - 获取本地全量配置
   - 按与全量配置一致的逻辑处理对象
   - 不再在每次配置成功后重新获取全量配置，而是在下次配置时再获取

## 实现方案

### 关键数据结构

1. **WanGroupConfig**
   - 表示本地WAN群组配置
   - 包含ID、名称、类型和成员列表等信息

2. **WanGroupProcessor**
   - 处理WAN群组配置任务
   - 维护本地配置缓存
   - 实现全量同步和增量同步逻辑
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法

### 关键方法

1. **GetAllWanGroups()**
   - 获取本地所有WAN群组配置
   - 解析`floweye wangroup list`命令输出
   - 获取每个群组的成员信息

2. **GetWanGroupMember()**
   - 获取特定WAN群组的成员信息
   - 解析`floweye wangroup get`命令输出
   - 提取成员名称和权重

3. **VerifyWanGroupConfig()**
   - 验证WAN群组配置是否成功应用
   - 确保命令执行成功后配置确实生效
   - 验证名称、类型和成员列表是否与期望一致

4. **CompareWanGroupConfig()**
   - 比较请求的配置与本地配置
   - 检查名称、类型和成员列表是否一致
   - 决定是否需要修改配置

5. **handleNewConfig()**
   - 处理新建WAN群组配置任务
   - 验证必填字段
   - 检查ID和名称是否已存在
   - 执行创建命令并添加成员
   - 验证配置是否成功应用

6. **handleEditConfig()**
   - 处理编辑WAN群组配置任务
   - 如果ID不存在，作为新增处理
   - 如果名称或类型发生变化，更新配置
   - 比较成员列表，添加缺少的成员，删除多余的成员
   - 验证配置是否成功应用

7. **handleDeleteConfig()**
   - 处理删除WAN群组配置任务
   - 检查ID是否存在
   - 执行删除命令
   - 从本地配置中移除

## 命令行交互

WAN群组模块通过以下命令与系统交互：

1. **查询所有WAN群组**
   ```
   floweye wangroup list
   ```

2. **查询特定WAN群组**
   ```
   floweye wangroup get id=<群组ID> showproxy=1
   ```

3. **创建WAN群组**
   ```
   floweye wangroup add name=<群组名> id=<群组ID> type=<群组类型>
   ```

4. **修改WAN群组**
   ```
   floweye wangroup set id=<群组ID> name=<新群组名> type=<新群组类型>
   ```

5. **添加WAN群组成员**
   ```
   floweye wangroup set id=<群组ID> proxy=<WAN名称> weight=1
   ```

6. **删除WAN群组成员**
   ```
   floweye wangroup set id=<群组ID> proxy=<WAN名称> weight=-1
   ```

7. **删除WAN群组**
   ```
   floweye wangroup remove id=<群组ID>
   ```

## 实现特点

1. **结构化配置解析**
   - 使用map来解析命令输出
   - 提高了解析效率和可读性

2. **统一的全量同步接口**
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 支持全量同步和增量同步

3. **配置验证机制**
   - 在应用配置后验证是否成功
   - 确保命令执行成功后配置确实生效

4. **配置比对机制**
   - 在应用配置前先检查是否与本地配置一致
   - 如果一致则无需修改，提高效率

5. **成员管理**
   - 比较成员列表，添加缺少的成员，删除多余的成员
   - 支持添加和删除成员

## 注意事项

1. ID范围必须在1-128之间
2. ID和name都必须唯一
3. 如果ID一致但name不一致，后下发的group name将替换原有name
4. 如果name一致但ID不一致，后下发的配置将失败
5. 创建群组后可以修改名称和类型，但不能修改ID
6. 添加成员时，指定的WAN必须已存在
7. 删除成员时，使用负权重值
8. 删除群组前，建议先移除所有成员

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 缺少必要参数：验证并返回明确的错误信息
5. ID或名称冲突：检查并返回明确的错误信息

## 测试方案

1. 单元测试：验证处理器的基本功能和错误处理
2. 集成测试：验证与floweye命令的交互
3. 系统测试：验证配置在实际环境中的生效情况
4. 全量同步测试：验证全量同步机制
5. 增量同步测试：验证增量同步机制

## 后续优化

1. 优化本地配置缓存的更新策略
2. 增加配置批量处理能力
3. 增加对WAN群组状态的监控和上报
4. 实现更细粒度的错误处理和恢复机制
5. 优化命令输出解析逻辑，提高解析效率和稳定性
