# IP Group Implementation

This document describes the implementation of the IP Group module in the UniSASE Agent.

## Overview

The IP Group module allows the agent to manage IP groups on the local system. An IP group is a collection of IP addresses that can be used for various purposes, such as access control, traffic filtering, etc.

## Protocol Buffer Definition

The IP Group module uses the following protocol buffer messages:

```protobuf
// IP群组成员
// 表示单个IP成员配置
message IpGroupMember {
  oneof ip_addr {
    IpAddress ip = 1;                 // IP地址，格式: ip,ip/mask
    IpRange ip_range = 2;             // IP地址范围,ip-ip
  }
  optional string info = 3;           // 备注信息
}

// IP群组配置消息
message IpGroupTask {
  string name = 1;                      // IP群组名称(IP群组唯一标识)，ASCII字符，最长15字节 (required)
  repeated IpGroupMember members = 2;   // IP成员列表，当IP成员数量较少时使用
  optional bytes file_content = 3;      // 文件形式IP群组内容，当IP成员数量较多时使用
}
```

The IP Group task type is defined in the TaskType enum:

```protobuf
enum TaskType {
  // ... other task types ...
  TASK_IP_GROUP = 11;   // IP群组配置任务
}
```

## Implementation

The IP Group module consists of the following components:

1. **IP Group Configuration (ip_group_config.go)**:
   - Defines data structures for IP Group configuration
   - Provides functions to get and compare IP Group configurations
   - Provides functions to verify IP Group configurations

2. **IP Group Processor (ip_group_processor.go)**:
   - Implements the TaskProcessor interface for IP Group tasks
   - Handles NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG operations
   - Supports full synchronization of IP Group configurations

## Configuration Processing Logic

When the agent receives an IP Group configuration:

1. The agent checks if the IP Group exists by name
2. If the IP Group doesn't exist, it creates a new IP Group
3. If the IP Group exists, it clears all existing members
4. The agent adds members to the IP Group using one of two methods:
   - From a member list (when the number of members is small)
   - From file content (when the number of members is large)
5. Since we always do a full replacement, we don't need to compare the existing members with the new ones

## IP Address Handling

The IP Group module supports both single IP addresses and IP ranges through the `oneof ip_addr` field in the `IpGroupMember` message:

1. **Single IP Address**:
   - Can be in various formats: IPv4, IPv6, CIDR notation
   - Stored in the `ip` field of the `ip_addr` oneof
   - Uses `utils.GetIpString()` to convert the IP address to a string representation

2. **IP Range**:
   - Represents a range of IP addresses (start_ip-end_ip)
   - Stored in the `ip_range` field of the `ip_addr` oneof
   - Uses `utils.GetIpRangeString()` to convert the IP range to a string representation

## File Processing

When processing IP Group configurations from file content:

1. The agent creates a temporary file in `/tmp/` with the IP Group name as prefix
2. The agent writes the file content to the temporary file
3. The agent uses the `floweye table loadfile` command to load the file
4. The temporary file is automatically removed after processing

## Testing

The IP Group module includes comprehensive unit tests:

1. **ip_group_config_test.go**:
   - Tests for IP Group configuration functions
   - Tests for IP Group verification functions

2. **ip_group_processor_test.go**:
   - Tests for IP Group processor initialization
   - Tests for processing NEW_CONFIG, EDIT_CONFIG, and DELETE_CONFIG operations
   - Tests for full synchronization

## Usage

To use the IP Group module, the orchestrator sends a DeviceTask message with:

- `task_type` set to `TASK_IP_GROUP`
- `task_action` set to one of:
  - `NEW_CONFIG` (create a new IP Group)
  - `EDIT_CONFIG` (modify an existing IP Group)
  - `DELETE_CONFIG` (delete an existing IP Group)
- `ip_group_task` containing the IP Group configuration

Example:

```go
task := &pb.DeviceTask{
    TaskType:   pb.TaskType_TASK_IP_GROUP,
    TaskAction: pb.TaskAction_NEW_CONFIG,
    Payload: &pb.DeviceTask_IpGroupTask{
        IpGroupTask: &pb.IpGroupTask{
            Name: "example_group",
            Members: []*pb.IpGroupMember{
                {
                    IpAddr: &pb.IpGroupMember_Ip{
                        Ip: &pb.IpAddress{
                            Ip: &pb.IpAddress_IpString{
                                IpString: "***********",
                            },
                        },
                    },
                    Info: "Example IP",
                },
                {
                    IpAddr: &pb.IpGroupMember_IpRange{
                        IpRange: &pb.IpRange{
                            StartIp: &pb.IpAddress{
                                Ip: &pb.IpAddress_IpString{
                                    IpString: "********",
                                },
                            },
                            EndIp: &pb.IpAddress{
                                Ip: &pb.IpAddress_IpString{
                                    IpString: "*********",
                                },
                            },
                        },
                    },
                    Info: "Example IP Range",
                },
            },
        },
    },
}
```
