1. 路由/NAT 策略字段
字段			释义		字段类型	必填项	示例	备注
cookie			策略cookie		int	是	1	唯一标识; uint32
zone			策略所属区		int	是	CTRL_TIER_T1 = 0;
CUST_TIER_T2 = 1; 
LPM_TIER_T3 = 2;  
DEF_WAN_TIER_T4 = 3;	平台连接策略层 (优先级1-5000)
自定义策略层 (优先级5001-50000)
最长前缀策略层 (优先级50001-60000)
默认路由策略层 (优先级60001-65535)
desc			策略描述		string	是	policy_group1_p1	
previous			前一个策略cookie		int	是	0	zone首个策略为0
zone追加策略为-1
disable			是否启用		int	是	0/1	0为启用，1位禁用
schtime			策略时段Id		int	否		0为任意
用户/访问者	src		源地址	IP	IpAddr	否	*************99/32	src和dst分别代表源地址和目的地址src=,4代表IP群组id为4的群组代表原地址dst=,2,代表IP群组id为2的群组代表目的地址
src/des=mac.1代表MAC组的DefaultGroup
src/des=mac.2代表MAC组的MAC分流
src/des=,pppoe.1代表用户组的DefaultGroup
src/des=,pppoe.2代表用户组的MAC分流
src/des=,dns.1,代表域名群组id为1的群组
				IP段	IpRange	否	*************-*************99	
				IP群组	string	否	ip-oversea
IP群组以名称作为唯一标识，agent通过名称反查ID	
				Mac群组	int	否	3	
				用户组	int	否	3	
				用户	string	否	zhangsan	
	usrtype		用户类型		int	否	0: any
1:ippxy
2:nonippxy	usrtype=any为任意
usrtype=ippxy为代播用户，usrtype=nonippxy为非代播用户
	pool		用户组Id		int	否	0:any	pool=any 为任意
	srcport		内网端口		mix	否	80,443,100-120	srcport=80,443,100-120
服务者/服务	dst		目的地址	同源地址	mix	否	当指定多个时，使用','连接,例如：
dst=,*******/32,***********-***********00,mac.2064,pppoe.1,acct.randy	
	dstport		外网端口		mix	否	80,443,100-120	dstport=80,443,100-120
	proto		协议		string	否	any
cmp
tcp
udp	any/tcp/udp/icmp
	app		特征库协议		string	否	app=any
app=httpgroup	应用协议
floweye app showtree all 可查
接口	inif		源接口		string	否	inif=any
inif=allposvr
inif=if.eth1	inif=any 为任意
inif=allposvr代表PPPoE服务，还可以选择以下（自己的网卡接口，Wan口，Lan口）
	wanbw		上行带宽		int	否	0	如果参数不为 0，表示当目标线路上下行流量超过设定的最大带宽参数时
该策略路由自动失效，会继续匹配下一条路由策略。
	wanbwout		下行带宽		int	否	0	
	vlan		vlan id		intRange	否	0-100	XX-XX表示单个数值，XX-NN表示一个范围数值，0表示任意
	TTL		匹配数据包的TTL值		intRange	否	200-200	
	DSCP		匹配DSCP值		intRange	否	200-200	
执行动作	action		执行策略		int	是	1: 路由
2: NAT
3: DNAT
4: CGNAT
5:代播	xxxx-xxx,”-”前代表执行动作，后代表NAT路线。“-”前可以选择dnat/cgnat/route等。“-”后可选NAT的线路，可选以下，其中空线路为_NULL_,动态路由线路为_DYNROUTE_
例如action=nat-wan，表示动作为NAT，NAT线路为wan，action=route-wan，表示动作为路由，路由线路为wan
	action=路由	proxy	线路		string	是	wan1	
		nexthop	下一跳		IpAddr	否	nexthop=***********	
	action=NAT/DNAT	proxy	线路		string	是	wan1	匹配后状态，0为继续匹配，1为停止匹配
		newdstip	DNAT地址		IpAddr	否	newdstip=**************	
		natip	SNAT地址池		IpAddr	否	natip=**************-***************,**************-**************	多端IP用逗号分割
		nexthop	下一跳		string	否	wan1	nexthop=wan1，表示下一跳线路为wan1
空线路为_NULL_
		fullconenat	全锥形NAT		bool	否	FALSE	NAT时有效
		nosnat	不改变源地址		bool	否	FALSE	DNAT时有效

默认值格式：
floweye route add id=6555 inif=any sport=0 dport=0 src= dst= proto=any app=any vlan=0-0 dscp=0 action=route-_wg.wan_group1 nexthop=0.0.0.0 usrtype=any desc=NULL schtime=0 pool=0 newdstip=0.0.0.0 wanbw=0 wanbwout=0 natip= disable=0

2. 路由/NAT 查询
2.1 查询所有路由/NAT
floweye route list json=1
[root@Panabit:~]# floweye route list json=1
{"polno":1,"iniftype":"any","inif":"any","proto":"any","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2064,TempAccounts;pool,1,DefaultGroup;acct,0,randy","srcport":"any","dstip":"ip,32,***************","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"dnat","nhtype":"pxy","nexthop":"wan","gwtype":"pxy","gateway":"wan","state":1,"pkts":0,"rtptimeid":1,"rtptimename":"time_1","active":1,"standby":"no","usrtype":"any","pid":2,"pname":"new_group","newdstip":"***********00","fullconenat":1,"natip":"rng,*********,*********00","desc":"route_policy1","cgnatid":0,"cgnatname":"NULL"},{"polno":65535,"iniftype":"any","inif":"any","proto":"any","appname":"any","appcname":"Э"srcip":"","srcport":"any","dstip":"","dstport":"any","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"nat","nhtype":"pxy","nexthop":"wan","gwtype":"NULL","gateway":"NULL","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"any","pid":0,"pname":"any","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"NULL","cgnatid":0,"cgnatname":"NULL"}
2.2 查询指定路由/NAT
通过Id查询：
floweye rtpolicy get <id=>
[root@Panabit:~]# floweye rtpolicy get id=100
group=ses
id=100
cookie=11100
iniftype=if
inif=eth1
proto=tcp
appid=any
appname=Эsrc=ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy
sport=80
dst=
dport=80,443
dscp=0
disable=0
vlan=0-0
ttl=0-255
action=nat
dynrt=0
actpxyname=wan
actpxyid=1
nexthop=wan
nexthopid=1
newdstip=0.0.0.0
cgnatid=0
cgnatname=NULL
pkts=0
schtime=0
desc=restrict
usrtype=ippxy
pool=2
pname=new_group
fullconenat=0
nomapback=0
nosnat=0
rtp_isactive=1
natip=
wanbw=0
wanbwout=0
bwfullflag=0
inbps=0
outbps=0
policy_size=108
policy_mopoff=32
policy_natipcnt=0
policy_bwfull=0
policy_fromwan=0
policy_standby=0
policy_disabled=0
policy_inactive=0
通过cookie查询：
floweye rtpolicy get <cookie=>
[root@Panabit:~]# floweye rtpolicy get cookie=11100
group=ses
id=100
cookie=11100
iniftype=if
inif=eth1
proto=tcp
appid=any
appname=Эsrc=ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy
sport=80
dst=
dport=80,443
dscp=0
disable=0
vlan=0-0
ttl=0-255
action=nat
dynrt=0
actpxyname=wan
actpxyid=1
nexthop=wan
nexthopid=1
newdstip=0.0.0.0
cgnatid=0
cgnatname=NULL
pkts=0
schtime=0
desc=restrict
usrtype=ippxy
pool=2
pname=new_group
fullconenat=0
nomapback=0
nosnat=0
rtp_isactive=1
natip=
wanbw=0
wanbwout=0
bwfullflag=0
inbps=0
outbps=0
policy_size=108
policy_mopoff=32
policy_natipcnt=0
policy_bwfull=0
policy_fromwan=0
policy_standby=0
policy_disabled=0
policy_inactive=0

3. 路由/NAT 设置
3.1 新增 路由/NAT 
floweye route add <id=> <inif=> <sport=> <dport=> <src=> <dst=> <proto=> <app=> <vlan=> <dscp=> <action=> <nexthop=> <usrtype=> <desc=> <schtime=> <pool=> <newdstip=> <wanbw=> <wanbwout=> <natip=> <disable=> <cookie=>

src：源地址（支持IP、范围、网段、IP群组、用户组、账号。每个条件用英文逗号“,”前后区分。例如：条件为***********、***********/24、IP群组ID1、用户组ID2、用户账号test1，对应参数为“src=,***********,,***********/24,,1,,pppoe.2,,acct.test1,”）
[root@Panabit:~]# floweye route add id=101 inif=if.eth1 sport=80 dport=80,443 src=,*******/32,,***********-***********00,,mac.2,,pppoe.2,,acct.randy, dst= proto=tcp app=any vlan=0-0 dscp=0 action=route-wan nexthop=************* usrtype=ippxy desc=restrict schtime=0 pool=2 newdstip=0.0.0.0 wanbw=0 wanbwout=0 natip= disable=0 cookie=10100
[root@Panabit:~]# floweye rtpolicy get id=101
group=ses
id=101
cookie=10100
iniftype=if
inif=eth1
proto=tcp
appid=any
appname=Эsrc=ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy
sport=80
dst=
dport=80,443
dscp=0
disable=0
vlan=0-0
ttl=0-255
action=route
dynrt=0
actpxyname=wan
actpxyid=1
nexthop=*************
nexthopid=0
newdstip=0.0.0.0
cgnatid=0
cgnatname=NULL
pkts=0
schtime=0
desc=restrict
usrtype=ippxy
pool=2
pname=new_group
fullconenat=0
nomapback=0
nosnat=0
rtp_isactive=1
natip=
wanbw=0
wanbwout=0
bwfullflag=0
inbps=0
outbps=0
policy_size=108
policy_mopoff=32
policy_natipcnt=0
policy_bwfull=0
policy_fromwan=0
policy_standby=0
policy_disabled=0
policy_inactive=0

3.2 修改 路由/NAT
floweye route set <id=>  <newid=> (<inif=> <sport=> <dport=> <src=> <dst=> <proto=> <app=> <vlan=> <dscp=> <action=> <nexthop=> <usrtype=> <desc=> <schtime=> <pool=> <newdstip=> <wanbw=> <wanbwout=> <natip=> <disable=> <cookie=>)
[root@Panabit:~]# floweye route set id=101 newid=101 inif=if.eth1 sport=80 dport=80,443 src=,*******/32,***********-***********00,mac.2,pppoe.2,acct.randy dst= proto=any app=any vlan=0-0 dscp=0 action=route-wan nexthop=************* usrtype=ippxy desc=restrict schtime=0 pool=2 newdstip=0.0.0.0 wanbw=0 wanbwout=0 natip= disable=0 cookie=10100
[root@Panabit:~]# floweye rtpolicy get id=101 
group=ses
id=101
cookie=10100
iniftype=if
inif=eth1
proto=any
appid=any
appname=Эsrc=ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy
sport=80
dst=
dport=80,443
dscp=0
disable=0
vlan=0-0
ttl=0-255
action=route
dynrt=0
actpxyname=wan
actpxyid=1
nexthop=*************
nexthopid=0
newdstip=0.0.0.0
cgnatid=0
cgnatname=NULL
pkts=0
schtime=0
desc=restrict
usrtype=ippxy
pool=2
pname=new_group
fullconenat=0
nomapback=0
nosnat=0
rtp_isactive=1
natip=
wanbw=0
wanbwout=0
bwfullflag=0
inbps=0
outbps=0
policy_size=104
policy_mopoff=32
policy_natipcnt=0
policy_bwfull=0
policy_fromwan=0
policy_standby=0
policy_disabled=0
policy_inactive=0
3.3 删除 路由/NAT
floweye route remove <id=>
[root@Panabit:~]# floweye route list json=1
{"polno":1,"iniftype":"any","inif":"any","proto":"any","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2064,TempAccounts;pool,1,DefaultGroup;acct,0,randy","srcport":"any","dstip":"ip,32,***************","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"dnat","nhtype":"pxy","nexthop":"wan","gwtype":"pxy","gateway":"wan","state":1,"pkts":0,"rtptimeid":1,"rtptimename":"time_1","active":1,"standby":"no","usrtype":"any","pid":2,"pname":"new_group","newdstip":"***********00","fullconenat":1,"natip":"rng,*********,*********00","desc":"route_policy1","cgnatid":0,"cgnatname":"NULL"},{"polno":100,"iniftype":"if","inif":"eth1","proto":"tcp","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy","srcport":"80","dstip":"","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"nat","nhtype":"pxy","nexthop":"wan","gwtype":"pxy","gateway":"wan","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"ippxy","pid":2,"pname":"new_group","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"restrict","cgnatid":0,"cgnatname":"NULL"},{"polno":101,"iniftype":"if","inif":"eth1","proto":"any","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy","srcport":"80-90,80,443","dstip":"","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"nat","nhtype":"pxy","nexthop":"wan","gwtype":"pxy","gateway":"wan","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"ippxy","pid":2,"pname":"new_group","newdstip":"***************","fullconenat":0,"natip":"rng,**************,***************;rng,**************,**************0","desc":"restrict","cgnatid":0,"cgnatname":"NULL"},{"polno":1200,"iniftype":"if","inif":"eth1","proto":"tcp","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy","srcport":"80","dstip":"","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"route","nhtype":"pxy","nexthop":"wan","gwtype":"ip","gateway":"*************","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"ippxy","pid":2,"pname":"new_group","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"restrict","cgnatid":0,"cgnatname":"NULL"},{"polno":65535,"iniftype":"any","inif":"any","proto":"any","appname":"any","appcname":"Э"srcip":"","srcport":"any","dstip":"","dstport":"any","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"nat","nhtype":"pxy","nexthop":"wan","gwtype":"NULL","gateway":"NULL","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"any","pid":0,"pname":"any","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"NULL","cgnatid":0,"cgnatname":"NULL"}
[root@Panabit:~]# floweye route remove id=101
[root@Panabit:~]# floweye route list json=1  
{"polno":1,"iniftype":"any","inif":"any","proto":"any","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2064,TempAccounts;pool,1,DefaultGroup;acct,0,randy","srcport":"any","dstip":"ip,32,***************","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"dnat","nhtype":"pxy","nexthop":"wan","gwtype":"pxy","gateway":"wan","state":1,"pkts":0,"rtptimeid":1,"rtptimename":"time_1","active":1,"standby":"no","usrtype":"any","pid":2,"pname":"new_group","newdstip":"***********00","fullconenat":1,"natip":"rng,*********,*********00","desc":"route_policy1","cgnatid":0,"cgnatname":"NULL"},{"polno":100,"iniftype":"if","inif":"eth1","proto":"tcp","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy","srcport":"80","dstip":"","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"nat","nhtype":"pxy","nexthop":"wan","gwtype":"pxy","gateway":"wan","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"ippxy","pid":2,"pname":"new_group","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"restrict","cgnatid":0,"cgnatname":"NULL"},{"polno":1200,"iniftype":"if","inif":"eth1","proto":"tcp","appname":"any","appcname":"Э"srcip":"ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy","srcport":"80","dstip":"","dstport":"80,443","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"route","nhtype":"pxy","nexthop":"wan","gwtype":"ip","gateway":"*************","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"ippxy","pid":2,"pname":"new_group","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"restrict","cgnatid":0,"cgnatname":"NULL"},{"polno":65535,"iniftype":"any","inif":"any","proto":"any","appname":"any","appcname":"Э"srcip":"","srcport":"any","dstip":"","dstport":"any","dscp":0,"disabled":0,"vlan":"0/0","ttl":"0-255","action":"nat","nhtype":"pxy","nexthop":"wan","gwtype":"NULL","gateway":"NULL","state":1,"pkts":0,"rtptimeid":0,"rtptimename":"NULL","active":1,"standby":"no","usrtype":"any","pid":0,"pname":"any","newdstip":"0.0.0.0","fullconenat":0,"natip":" 0.0.0.0","desc":"NULL","cgnatid":0,"cgnatname":"NULL"}
3.4 启用/禁用 路由/NAT
floweye route disable <id=>
[root@Panabit:~]# floweye rtpolicy get id=100
group=ses
id=100
cookie=11100
iniftype=if
inif=eth1
proto=tcp
appid=any
appname=任意协议
src=ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy
sport=80
dst=
dport=80,443
dscp=0
disable=0
vlan=0-0
ttl=0-255
action=nat
dynrt=0
actpxyname=wan
actpxyid=1
nexthop=wan
nexthopid=1
newdstip=0.0.0.0
host_newdstip=0.0.0.0
cgnatid=0
cgnatname=NULL
pkts=0
schtime=0
desc=restrict
usrtype=ippxy
pool=2
pname=new_group
fullconenat=0
nomapback=0
nosnat=0
rtp_isactive=0
natip=
wanbw=0
wanbwout=0
bwfullflag=0
inbps=0
outbps=0
policy_size=108
policy_mopoff=32
policy_natipcnt=0
policy_bwfull=0
policy_fromwan=0
policy_standby=0
policy_disabled=0
policy_inactive=1
[root@Panabit:~]# floweye route disable id=100
[root@Panabit:~]# floweye rtpolicy get id=100
group=ses
id=100
cookie=11100
iniftype=if
inif=eth1
proto=tcp
appid=any
appname=任意协议
src=ip,32,*******;rng,***********,***********00;mac,2,new_group;pool,2,new_group;acct,0,randy
sport=80
dst=
dport=80,443
dscp=0
disable=1
vlan=0-0
ttl=0-255
action=nat
dynrt=0
actpxyname=wan
actpxyid=1
nexthop=wan
nexthopid=1
newdstip=0.0.0.0
host_newdstip=0.0.0.0
cgnatid=0
cgnatname=NULL
pkts=0
schtime=0
desc=restrict
usrtype=ippxy
pool=2
pname=new_group
fullconenat=0
nomapback=0
nosnat=0
rtp_isactive=0
natip=
wanbw=0
wanbwout=0
bwfullflag=0
inbps=0
outbps=0
policy_size=108
policy_mopoff=32
policy_natipcnt=0
policy_bwfull=0
policy_fromwan=0
policy_standby=0
policy_disabled=1
policy_inactive=1

4. 配置处理逻辑
路由策略较其它模块稍复杂，涉及到以下几方面的处理
4.1 引用依赖对象标识映射
路由策略的配置中会引用其它模块的对象，例如IP群组，用户组，用户等等，而这些被依赖的对象在本地是以ID为唯一标识(本地生成)进行管理的，而Orch是以对象名称为唯一标识进行管理的，Agent需要做标识的转换；目前涉及到转换的被引用对象为：
1. IP群组：IP群组Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行引用和配置。

4.2 路由NAT策略映射
PA本地的路由NAT策略ID不仅为策略的唯一标识，还标识着本地策略 匹配的先后顺序。

4.2.1 唯一标识转换
由于Orch上管理的策略(流量控制、路由/NAT、DNS管控)涉及到跨Site间的管理，难以以单个Site的策略ID 为唯一标识进行管理，所以：
1. 路由/NAT策略 通过 cookie 实现唯一标识，Agent 负责将 cookie ↔ 本地策略 ID 映射。

4.2.2 排序转换
Orch 并不直接管理本地 策略ID 顺序。为了控制策略的匹配顺序，Orch 通过下发 previous 字段（即 前一个策略 cookie）来表达插入位置，Agent 需根据此字段调整顺序。

zone规则：
Panabit cpe的策略优先级为从1到65535. 每个site创建后，控制器针对不同类型策略分为四个组
  1. 平台连接策略层（CTRL Tier-T1）：优先级较高，用于访问控制器和平台等服务。每个wan单独创建一条到平台服务的策略。预留1-5000优先级的策略为平台使用，这些策略状态（启用或关闭）可部分关闭，不可全部关闭（专线wan也自动创建策略，但是创建后默认为关闭状态），优先级不可调整，不可修改访问目标，此部分控制器上不可见，由site的wan配置决定是下发此策略配置内容。
  2. 自定义策略层 （CUST Tier-T2）：用户自己创建的策略在这个组中，优先级从5001到50000，用户可在控制器界面通过拖动顺序调整优先级，但是优先级具体值由控制器填写，不需要客户管理优先级的值.
  3. 最长前缀策略层（LPM Tier-T3）：默认lan策略--创建源any（此策略为创建lan口时，系统自动创建），目的为lan subnet，lan路由线路为lan接口，下一跳使用站点lan配置处的下一跳信息，多个lan接口时创建多条策略。组网最长前缀匹配路由是采用动态路由还是agent实现还未定，优先级从50001到60000，用户不可调整优先级顺序及任何配置.
  4. 默认路由策略层（DEF WAN Tier-T4）：优先级较低的默认策略用于站点本地访问，预留策略60001-65535，用户不可调整优先级顺序，但是策略状态和出口线路可编辑。默认wan策略--创建一个默认wan群组，将所有的wan口加入到wan群组中，源any，目的any，wan路由&nat出口为默认wan群组。

zone为LPM Tier-T3 的策略，下发时不指定previous，agent根据本地当前已有策略进行排序，排序规则为

previous规则：
1. 当下发的策略的previous cookie为0时，视为排在zone首个
2. 当下发的策略的previous cookie 为 -1且本地不存在时，视为追加zone最后

基于以上2点，路由NAT/策略 新增 修改 删除 调整顺序的逻辑为
1. 新增/修改 策略
  1. ID分配；
    1. 根据本地配置查询"策略名称"是否已存在相应策略：
      1. 存在：取现有ID。
      2. 不存在：使用该zone max(ID)+1作为新增策略ID。
  2. 修改/新增配置；
    1. 存在：更新已有策略的配置。
    2. 不存在：根据分配的ID新增策略。
  3. 调整策略顺序；
    1. 查找 previous 对应策略的 ID（若为null，则视为插入最前；若未查找到或为-1，视为最大 ID）；
    2. 若当前策略 ID ≠ previous ID + 1：
      1. 若相同，则无需调整；
      2. 若不同则需重新排序：
        1. 将当前策略 ID 设置为临时 ID（如 65536）。
        2. 根据目标插入方向：
          1. 向前插入(当前ID > 目标ID)
            1. 将 [目标 ID, 当前 ID - 1] 区间内的所有策略 ID +1（从大到小调整）。
          2. 向后移动（当前 ID < 目标 ID）：
            1. 将 [当前 ID + 1, 目标 ID] 区间内的所有策略 ID -1（从小到大调整）。
          3. 最终将当前策略 ID 设置为 previous ID + 1。
2. 删除策略
  1. 获取策略Id；根据本地配置查询"策略名称"是否已存在相应策略：
    1. 存在：获取对应策略Id。
    2. 不存在：结束删除流程。
  2. 删除对应 ID 的策略。
  3. 所有 ID > 被删除 ID 的策略 ID 全部 -1，以保持 ID 顺序连续。

