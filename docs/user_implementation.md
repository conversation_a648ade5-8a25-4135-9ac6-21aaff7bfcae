# 用户模块配置实现文档

## 概述

用户模块负责处理用户的配置，包括创建、修改、删除用户，以及启用/禁用用户。本文档描述了用户模块的实现方案和关键代码。

## 配置项

根据`floweye_usergroup_user.md`文档和`message.proto`定义，用户模块支持以下配置项：

1. **基本配置**
   - 用户名(name) - 必填，用户账号名称
   - 用户组ID(pool_id) - 必填，用户所属的用户组ID
   - 密码(password) - 必填，用户密码
   - 启用状态(enable) - 可选，是否启用用户账号

2. **账号有效期**
   - 开始日期(start) - 必填，账号生效日期，格式为YYYY-MM-DD
   - 过期日期(expire) - 必填，账号过期日期，格式为YYYY-MM-DD

3. **用户限制**
   - 最大在线数(max_online) - 可选，最大并发登录数，0表示不限制
   - 绑定IP(bind_ip) - 可选，绑定的IP地址，0.0.0.0表示不绑定
   - 绑定MAC(bind_mac) - 可选，绑定的MAC地址列表，空表示不绑定
   - 绑定VLAN(out_vlan) - 可选，绑定的VLAN ID，0表示不绑定

4. **用户身份信息**
   - 真实姓名(cname) - 可选，用户真实姓名
   - 证件号码(card) - 可选，用户证件号码
   - 电话号码(phone) - 可选，用户电话号码
   - 其他信息(other) - 可选，其他用户信息

## 配置一致性和同步机制

根据项目的配置一致性和同步机制，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye pppoeacct list`和`floweye pppoeacct get`命令）
   - 将配置缓存在内存中

2. **同步过程**
   - 处理来自Orchestrator的用户配置
   - 对于每个配置，检查是否与本地配置一致
   - 如果不一致，应用新配置
   - 从本地缓存中移除已处理的配置

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 删除本地缓存中剩余的配置（这些配置在Orchestrator中已不存在）
   - 清理资源

### 增量同步

1. **获取本地配置**
   - 获取特定用户的本地配置
   - 将配置与请求的配置进行比较

2. **应用配置**
   - 如果配置不一致，应用新配置
   - 验证配置是否成功应用

## 用户启用/禁用处理

用户模块的一个特殊之处是需要处理用户的启用/禁用状态。这需要执行两条命令：

1. **创建/修改用户**：使用`floweye pppoeacct add/set`命令
2. **启用/禁用用户**：使用`floweye pppoeacct config accten/acctdis`命令

为了简化处理，我们采用了以下策略：

1. 无论配置是否变更，都会根据`enable`字段执行`EnableUser()`或`DisableUser()`函数
2. 使用单独的函数`EnableUser()`和`DisableUser()`封装了启用/禁用用户的命令
3. 如果第一条命令成功但第二条命令失败，会返回相应的错误信息

## 关键函数

1. **GetLocalUserConfigs()**
   - 获取所有本地用户配置
   - 首先使用`floweye pppoeacct list`获取所有用户名
   - 然后对每个用户名使用`floweye pppoeacct get`获取详细配置
   - 返回用户配置的映射

2. **GetUserConfig()**
   - 获取特定用户的配置
   - 使用`floweye pppoeacct get`命令
   - 解析命令输出，构建用户配置对象

3. **VerifyUserConfig()**
   - 验证用户配置是否成功应用
   - 获取本地配置并与期望配置比较
   - 检查名称和用户组ID是否匹配

4. **CompareUserConfig()**
   - 比较请求的配置与本地配置
   - 检查所有相关字段是否一致
   - 决定是否需要修改配置

5. **EnableUser()**
   - 启用用户账号
   - 执行`floweye pppoeacct config accten=<用户名>`命令

6. **DisableUser()**
   - 禁用用户账号
   - 执行`floweye pppoeacct config acctdis=<用户名>`命令

7. **handleConfigChange()**
   - 处理新建和修改用户配置任务
   - 验证必填字段
   - 检查用户名是否已存在
   - 构建并执行floweye命令
   - 处理用户启用/禁用状态
   - 验证配置是否成功应用

8. **handleDeleteConfig()**
   - 处理删除用户配置任务
   - 检查用户名是否存在
   - 执行删除命令
   - 从本地配置中移除

## 命令行交互

用户模块通过以下命令与系统交互：

1. **查询所有用户**
   ```
   floweye pppoeacct list
   ```

2. **查询特定用户**
   ```
   floweye pppoeacct get name=<用户名>
   ```

3. **创建用户**
   ```
   floweye pppoeacct add name=<用户名> poolid=<用户组ID> passwd=<密码> start=<开始日期> expire=<过期日期> maxonline=<最大在线数> bindip=<绑定IP> bindmac=<绑定MAC> outvlan=<绑定VLAN> other=<用户信息>
   ```

4. **修改用户**
   ```
   floweye pppoeacct set name=<用户名> poolid=<用户组ID> passwd=<密码> start=<开始日期> expire=<过期日期> maxonline=<最大在线数> bindip=<绑定IP> bindmac=<绑定MAC> outvlan=<绑定VLAN> other=<用户信息>
   ```

5. **删除用户**
   ```
   floweye pppoeacct remove name=<用户名>
   ```

6. **启用用户**
   ```
   floweye pppoeacct config accten=<用户名>
   ```

7. **禁用用户**
   ```
   floweye pppoeacct config acctdis=<用户名>
   ```

## 实现特点

1. **结构化配置解析**
   - 使用map来解析命令输出
   - 提高了解析效率和可读性

2. **统一的全量同步接口**
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 支持全量同步和增量同步

3. **配置验证机制**
   - 在应用配置后验证是否成功
   - 确保命令执行成功后配置确实生效

4. **配置比对机制**
   - 在应用配置前先检查是否与本地配置一致
   - 如果一致则无需修改，提高效率

5. **IP地址处理**
   - 使用utils/ip.go中的通用函数处理IP地址
   - 简化了IP地址处理代码

6. **用户启用/禁用处理**
   - 单独处理用户的启用/禁用状态
   - 无论配置是否变更，都会根据enable字段执行相应命令

7. **日期处理**
   - 实现了formatDate()函数，将pb.Date转换为YYYY-MM-DD格式的字符串
   - 实现了parseDate()函数，将YYYY-MM-DD格式的字符串转换为pb.Date

## 注意事项

1. 用户名必须唯一
2. 用户必须属于一个有效的用户组
3. 密码不能为空
4. 开始日期和过期日期必须有效
5. 用户身份信息以分号分隔的字符串存储
6. 用户启用/禁用状态需要单独处理

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 缺少必要参数：验证并返回明确的错误信息
5. 用户名冲突：检查并返回明确的错误信息

## 测试方案

1. **单元测试**
   - 测试GetLocalUserConfigs函数
   - 测试GetUserConfig函数
   - 测试VerifyUserConfig函数
   - 测试CompareUserConfig函数
   - 测试EnableUser和DisableUser函数
   - 测试ProcessTask方法（各种任务类型和边界情况）
   - 测试StartFullSync和EndFullSync方法

2. **集成测试**
   - 测试用户的创建、修改和删除
   - 测试用户的启用和禁用
   - 测试全量同步
   - 测试边界情况和错误处理

## 后续优化

1. 优化本地配置缓存的更新策略
2. 增加配置批量处理能力
3. 增加对用户状态的监控和上报
4. 实现更细粒度的错误处理和恢复机制
5. 优化命令输出解析逻辑，提高解析效率和稳定性
