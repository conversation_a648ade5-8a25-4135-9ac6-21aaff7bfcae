1. 配置字段
1.1 LAN 配置字段
字段	释义	字段类型	必填项	示例	备注
name	LAN接口名称	string	是	lan1	ASCII字符，最长15字节，LAN & DHCP唯一标识
ifname	网卡名称	string	是	eth0	ifname=eth0
mtu	最大传输单元	int	是	1500	范围: 500-4700
addr	接口ip	IpAddr	是	************	addr=************
mask	掩码	IpAddr	是	*************	mask=*************
clonemac	克隆MAC	string	否	00-00-00-00-00-00	不使用自身携带的 MAC 地址，而是使用自定义手工输入的 MAC 地址。
格式：00-00-00-00-00-00，前 4 字节不能为空。

1.2 DHCP 配置字段
字段	释义	字段类型	必填项	示例	备注
name	LAN接口名称	string	是	lan1	ASCII字符，最长15字节
dhcp_pool	dhcp地址范围	string	是	***************-***************	x.x.x.x-y.y.y.y
leasettl	租约时间	int	是	3600	单位 s；默认为leasettl=86400
dhcp_enable	开启/关闭dhcp	bool	是	0/1	dhcp_enable=1
dns0	dhcp主DNS	IpAddr	否	*******	默认为空 dns0=
dns1	dhcp次DNS	IpAddr	否	***************	默认为空 dns1=
dhcp_gateway	dhcp网关	IpAddr	否	*************	默认为0.0.0.0，则使用接口IP地址作为网关
dhcp_mask	dhcp掩码	IpAddr	否	0.0.0.0	默认为0.0.0.0，则使用接口的掩码
dhcp_acaddr	dhcp分配给客户端的AC地址	IpAddr	否	*************11	默认为dhcp_acaddr=0.0.0.0
dhcp_domain	dhcp分配给客户端的域名	string	否	aaa.com	默认为dhcp_domain=
option12	DHCP HostName	string/hex	否	host-123	单个options的payload长度不应该超过255字节
总options的长度应该控制在312字节内
TODO: 验证派网能配置的长度
默认为NULL
dhcp_option=12,NULL, dhcp_option=61,NULL, dhcp_option=60,NULL,
option61	DHCP Vendor class ID	string/hex	否	MSFT 5.0	
option60	DHCP Client ID	string/hex	否	01080027c1766275	
⚠️暂先仅列出可能使用部分。
⚠️所有操作以“LAN 接口名称” 为索引进行操作，暂不涉及到ID转换。
⚠️DHCP在LAN创建完成后可单独进行配置/修改。

⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. LAN查询
2.1 查询所有LAN
floweye nat listproxy type=lan json=1
[root@Panabit:~]# floweye nat listproxy type=lan json=1
{"id":2,"name":"lan","type":"rtif","state":1,"standby":0,"disable":0,"inbps":33728,"outbps":4783896,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth1","ip":"*************","gw":"0.0.0.0","mask":"*************","vlan":"0"},{"id":13,"name":"lan1","type":"rtif","state":0,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth2","ip":"*************","gw":"0.0.0.0","mask":"*************","vlan":"0"},{"id":14,"name":"aaa","type":"iwansvc","state":1,"standby":0,"disable":0,"inbps":0,"outbps":0,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1436,"group":"","consecs":0,"if":"NULL","ip":"*************","gw":"0.0.0.0","mask":"0.0.0.0","vlan":"0","pid":2064,"pname":"TempAccounts","dns0":"0.0.0.0","dns1":"0.0.0.0","clntcnt":0,"auth":"local","radid":0,"radname":"DefaultRadius"}

type为rtif的为lan
{"id":2,"name":"lan","type":"rtif","state":1,"standby":0,"disable":0,"inbps":1384,"outbps":1157,"dnsreq":0,"dnsfail":"0.00","flowcnt":0,"mtu":1500,"group":"","consecs":0,"if":"eth1","ip":"*************","gw":"0.0.0.0","mask":"*************","vlan":"0"}

2.2 查询指定LAN
floweye nat getproxy <name>
DHCP 信息也包含在指定LAN信息中。
[root@Panabit:~]# floweye nat getproxy lan1
linkid=16
proxyid=12
name=lan1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=2025-04-14/15:51:48
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=53
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
active=0
state=0
type=rtif
ifname=eth2
ifstatus=down
addr=************
vlan=0
vlan1=0
linkup=0
standby=NULL
standby_state=0
netmask=*************
dhcp_enable=1
dns0=***************
dns1=*******
dhcp_gateway=************
leasettl=3600
dhcp_pool=**************-************00
dhcp_vlan=0
dhcp_mask=0.0.0.0
dhcp_acaddr=0.0.0.0
dhcp_domain=aaa.com
dhcp_option=61,str,MSFT
dhcp_option=12,str,host-123
arpreflex=0
3. LAN 配置
3.1 新增LAN
floweye nat addrtif <name=> <ifname=> <mtu=> <ping_disable=> <pingip=> <pingip2=> <maxdelay=> <vlan=> <mtu=> <clonemac=> <standby=> <addr=> <mask=>
[root@Panabit:~]# floweye nat listproxy type=lan
rtif 2 lan 1 eth1 ************* ************* 0/1500 49.09K 33.92K 0 0 1 enable
iwansvc 14 aaa NULL ************* 0 1436 NULL 0.0.0.0 0.0.0.0 0 0 0 2064 TempAccounts 0 local enable 0 DefaultRadius
[root@Panabit:~]# floweye nat addrtif name=lan1 ifname=eth2 mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 vlan=0 mtu=1500 clonemac=00-00-00-00-00-00 standby=NULL addr=************* mask=*************
[root@Panabit:~]# floweye nat listproxy type=lan
rtif 2 lan 1 eth1 ************* ************* 0/1500 1.22K 2.95K 0 0 1 enable
rtif 13 lan1 0 eth2 ************* ************* 0/1500 0 0 0 0 0 enable
iwansvc 14 aaa NULL ************* 0 1436 NULL 0.0.0.0 0.0.0.0 0 0 0 2064 TempAccounts 0 local enable 0 DefaultRadius
3.2 修改LAN
floweye nat setrtif <name=> <newname=> <ifname=> <mtu=> <ping_disable=> <pingip=> <pingip2=> <maxdelay=> <vlan=> <mtu=> <clonemac=> <standby=> <addr=> <mask=>
[root@Panabit:~]# floweye nat listproxy type=lan
rtif 2 lan 1 eth1 ************* ************* 0/1500 88.82K 121.28K 0 0 1 enable
rtif 13 lan1 0 eth2 ************* ************* 0/1500 0 0 0 0 0 enable
iwansvc 14 aaa NULL ************* 0 1436 NULL 0.0.0.0 0.0.0.0 0 0 0 2064 TempAccounts 0 local enable 0 DefaultRadius
[root@Panabit:~]# floweye nat setrtif name=lan1 newname=lan1 ifname=eth2 mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 vlan=0 mtu=1500 clonemac=00-00-00-00-00-00 standby=NULL addr=************* mask=*************
[root@Panabit:~]# floweye nat listproxy type=lan
rtif 2 lan 1 eth1 ************* ************* 0/1500 98.91K 51.23K 0 0 1 enable
rtif 13 lan1 0 eth2 ************* ************* 0/1500 0 0 0 0 0 enable
iwansvc 14 aaa NULL ************* 0 1436 NULL 0.0.0.0 0.0.0.0 0 0 0 2064 TempAccounts 0 local enable 0 DefaultRadius
3.3 删除LAN
floweye nat rmvproxy <name>
[root@Panabit:~]# floweye nat listproxy type=lan
rtif 2 lan 1 eth1 ************* ************* 0/1500 20.12K 159.52K 0 0 1 enable
rtif 13 lan1 0 eth2 ************* ************* 0/1500 0 0 0 0 0 enable
iwansvc 14 aaa NULL ************* 0 1436 NULL 0.0.0.0 0.0.0.0 0 0 0 2064 TempAccounts 0 local enable 0 DefaultRadius
[root@Panabit:~]# floweye nat rmvproxy lan1
[root@Panabit:~]# floweye nat listproxy type=lan
rtif 2 lan 1 eth1 ************* ************* 0/1500 49.09K 33.92K 0 0 1 enable
iwansvc 14 aaa NULL ************* 0 1436 NULL 0.0.0.0 0.0.0.0 0 0 0 2064 TempAccounts 0 local enable 0 DefaultRadius

4. DHCP 配置
DHCP 开启/关闭 与 配置修改 方法皆相同：
floweye nat setrtif <name=> <dhcp_pool=> <dns0=> <dns1=> <leasettl=> <dhcp_enable=> <dhcp_gateway=> <dhcp_vlan=> <dhcp_mask=> <dhcp_acaddr=> <dhcp_domain=> <dhcp_option=> <dhcp_option=> <dhcp_option=>
floweye nat setrtif name=lan1 id=3 dhcp_pool=*************-************* dns0= dns1= leasettl=86400 dhcp_enable=1 dhcp_gateway=*********** dhcp_vlan=0 dhcp_mask=0.0.0.0 dhcp_acaddr=0.0.0.0 dhcp_domain= dhcp_option=12,NULL, dhcp_option=61,NULL, dhcp_option=60,NULL,
[root@Panabit:~]# floweye nat setrtif name=lan1 dhcp_pool=***************-*************** dns0=*************** dns1=******* leasettl=3600 dhcp_enable=1 dhcp_gateway=************* dhcp_vlan=0 dhcp_mask=0.0.0.0 dhcp_acaddr=0.0.0.0 dhcp_domain=aaa.com dhcp_option=12,str,host-123 dhcp_option=61,str,MSFT 5.0 dhcp_option=60,hex,010800279a4f
[root@Panabit:~]# floweye nat getproxy lan1
linkid=16
proxyid=12
name=lan1
drop_dummyif=0
drop_dummyvlan=0
drop_nostate=0
drop_standby=0
dummypkt_stat=0/0/0[if/vlan/arp]
gwpxy=0
clonemac=00-00-00-00-00-00
inbps=0
outbps=0
lastdowntime=2025-04-14/15:51:48
maxping_ms=0.00
minping_ms=0.00
curping_ms=0.00
ifup=0
standby=NULL
standby_state=0
natip=0.0.0.0
natip_count=0
natip_deadcnt=0
mtu=1500
nextipid=0
disable=0
ping_disable=0
datattl=0
hbfail=0
arpttl=53
macbase=b0-ef-a7-37
ifmac=b0-ef-a7-37-00-b0
active=0
state=0
type=rtif
ifname=eth2
ifstatus=down
addr=************
vlan=0
vlan1=0
linkup=0
standby=NULL
standby_state=0
netmask=*************
dhcp_enable=1
dns0=***************
dns1=*******
dhcp_gateway=************
leasettl=3600
dhcp_pool=***************-***************
dhcp_vlan=0
dhcp_mask=0.0.0.0
dhcp_acaddr=0.0.0.0
dhcp_domain=aaa.com
dhcp_option=61,str,MSFT
dhcp_option=12,str,host-123
dhcp_option=60,hex,010800279a4f
arpreflex=0
dhcp_enable=1 为开启
dhcp_enable=0 为关闭
