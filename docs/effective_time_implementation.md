# Effective Time Implementation

## Overview

The Effective Time module manages time-based policy configurations in the UniSASE Agent. It handles the creation, modification, and deletion of time period settings that can be used by other modules (such as policy rules) to apply time-based restrictions or behaviors.

## Key Components

### Data Structures

#### EffectiveTimeConfig

This structure represents an effective time configuration in the local system:

```go
type EffectiveTimeConfig struct {
    ID        int    // Effective time ID (unique identifier)
    Name      string // Effective time name
    StartWDay int    // Start day of week
    StartHour int    // Start hour
    StartMin  int    // Start minute
    StartSec  int    // Start second
    EndWDay   int    // End day of week
    EndHour   int    // End hour
    EndMin    int    // End minute
    EndSec    int    // End second
}
```

### Core Functions

#### GetEffectiveTimeConfigById

Retrieves an effective time configuration by its ID:

```go
func GetEffectiveTimeConfigById(logger *logger.Logger, id int) (*EffectiveTimeConfig, error)
```

- **Parameters**:
  - `logger`: Logger instance for logging operations
  - `id`: Effective time ID to retrieve

- **Returns**:
  - `*EffectiveTimeConfig`: The retrieved configuration
  - `error`: Error if retrieval fails

#### GetAllEffectiveTimeConfigs

Retrieves all effective time configurations from the system:

```go
func GetAllEffectiveTimeConfigs(logger *logger.Logger) (map[int]*EffectiveTimeConfig, error)
```

- **Parameters**:
  - `logger`: Logger instance for logging operations

- **Returns**:
  - `map[int]*EffectiveTimeConfig`: Map of configurations indexed by ID
  - `error`: Error if retrieval fails

#### VerifyEffectiveTimeConfig

Verifies if an effective time configuration matches expected values:

```go
func VerifyEffectiveTimeConfig(ctx context.Context, logger *logger.Logger, task *pb.EffectiveTimeTask) (bool, error)
```

- **Parameters**:
  - `ctx`: Context for the operation
  - `logger`: Logger instance for logging operations
  - `task`: Effective time task containing expected values

- **Returns**:
  - `bool`: True if configuration matches, false otherwise
  - `error`: Error if verification fails

### Task Processor

The `EffectiveTimeProcessor` implements the `TaskProcessor` interface and handles effective time configuration tasks:

```go
type EffectiveTimeProcessor struct {
    logger             *logger.Logger
    localConfigs       map[int]*EffectiveTimeConfig
    fullSyncInProgress bool
    mu                 sync.Mutex
}
```

#### Key Methods

- **GetTaskType()**: Returns `pb.TaskType_TASK_EFFECTIVE_TIME`
- **ProcessTask(ctx, task)**: Processes effective time tasks based on task action
- **handleConfigChange(ctx, effectiveTimeTask, taskAction)**: Handles creation and modification of effective time configurations
- **handleDeleteConfig(ctx, effectiveTimeTask)**: Handles deletion of effective time configurations
- **refreshLocalConfigs()**: Refreshes the local cache of effective time configurations
- **StartFullSync()**: Prepares for a full synchronization operation
- **EndFullSync()**: Completes a full synchronization operation, removing any configurations not included in the sync

## Implementation Details

### Configuration Management

The effective time processor uses ID as the unique identifier for effective time configurations. This approach ensures consistent tracking and management of configurations across the system.

### Command Execution

The processor uses the `floweye rtptime` command to interact with the local PA system:

- **Add**: `floweye rtptime add id=<id> name=<name> startwday=<wday> endwday=<wday> start=<HH:MM:SS> end=<HH:MM:SS>`
- **Set**: `floweye rtptime set id=<id> name=<name> startwday=<wday> endwday=<wday> start=<HH:MM:SS> end=<HH:MM:SS>`
- **Remove**: `floweye rtptime remove id=<id>`
- **List**: `floweye rtptime list`
- **Get**: `floweye rtptime get id=<id>`

### Synchronization Mechanism

The effective time processor implements the standard synchronization mechanism used throughout the agent:

1. **Full Sync**:
   - Fetches all local configurations
   - Processes configurations from the orchestrator
   - Removes any local configurations not included in the sync

2. **Incremental Sync**:
   - Fetches current local state
   - Applies specific changes from the orchestrator
   - Verifies changes were applied correctly

### Error Handling

The processor implements robust error handling:

- Validates required fields (ID, name, start/end times)
- Handles command execution errors
- Verifies configuration changes were applied correctly
- Reports detailed error information back to the orchestrator

## Integration with Other Modules

The effective time configurations can be referenced by other modules, such as policy rules, to implement time-based restrictions or behaviors. The effective time ID is used as the reference key.

## Testing

The effective time module includes both unit tests and integration tests:

- **Unit Tests**: Test the processor's logic in isolation
- **Integration Tests**: Test the interaction with the actual PA system using the `floweye` command

Integration tests cover:
- Creating new effective time configurations
- Modifying existing configurations
- Deleting configurations
- Error handling scenarios
