1. 配置字段
1.1 组织架构 配置字段
字段		释义	字段类型	必填项	示例	备注
id		用户组id	int	是	1-2063	唯一标识；
范围1-2063；
id=2063 默认作为_reserved_for_seg_srv_poo组织架构占用
pid		上级组id	int	是	0	
name		用户组名称	string	是	group1	
v4地址范围	start	ipv4地址范围起始	string	否	0.0.0.0	默认0.0.0.0
	end	ipv4地址范围结束	string	否	0.0.0.0	默认0.0.0.0
v6地址	prefix	前缀地址	string	否	2001:0db8:85a3::	默认::
	pfxlen	前缀地址	string	否	64	默认为空
账号带宽限制	ratein	v4账号入向带宽限制	int	否	0	单位 kbps，0 表示不限制。当 V6 账号带宽限制值是 0 时，V4 和 V6 的
用户共享 1 个限速。当 V6 账号带宽限制值不等于 0 时，V4 和 V6 用户
分别共享对应的限速值。
默认为0
	rateout	v4账号出向带宽限制	int	否	0	
	ratein6	v6账号入向带宽限制	int	否	0	
	rateout6	v6账号入向带宽限制	int	否	0	
dns		dns	string	否	*******	***************,******* 可配置多个；默认为0.0.0.0
maxonlinetime		在线时间	int	否	0	小时,在线时间超过时，系统会主动踢用户下线,0表示不控制；默认为0
clntepa		过期账号	string	否	reject/login/pass	reject禁止登录
login允许登录，禁止上网 
pass允许登录及上网
默认reject

⚠️当前节点被其他节点引用时，当前节点可以删除
⚠️当前节点引用其他节点时，依赖的节点被删除时，当前节点仍存在，引用关系仍存在；当引用的节点加回时，引用关系(pid)仍存在，pid对应的name可以改变。
⚠️当前节点被其它节点引用时，无法变更上级节点。
⚠️当前节点删除时，其组下所有账号都会被删除。

1.2 本地账号 配置字段
	字段	释义	字段类型	必填项	示例	备注
name		用户账号	string	是	zhangsan	"唯一标识；
创建后无法修改；
不超过 30 个英文字符或者 15 个中文字符。"
poolid		用户组id	int	是	1	
password		用户密码	string	是	password	不超过 30 个英文字符。
start		开通日期	string	是	"2025-1-9"	年-月-日
expire		截止日期	string	是	"2025-2-9"	
enable		账号启用/禁用	bool	是	true/false	需要根据此字段进行启用/禁用
用户限制	maxonline	最大在线数	int	否	0	最大在线用户数，在线时间超过时，系统会主动让用户下线，0 表示不控制。默认为1；
	bindip	绑定ip	string	否	0.0.0.0	0.0.0.0或为空表示不绑定；默认为0.0.0.0
	bindmac	绑定mac	string	否	00-00-00-00-00-00	默认00-00-00-00-00-00表示不绑定，多个MAC用逗号隔开
	outvlan	绑定vlan	int	否	0	默认不绑定 ，绑定后不仅认证用户名和密码 ，还会认证用户的 VLAN。
身份信息	cname	用户姓名	string	否	zhangsan	默认为空
	card	用户身份证	string	否		默认为空
	phone	用户联系电话	string	否		默认为空
	other	用户其他信息	string	否		默认为空
⚠️是否禁用 作为用户的属性由orch下发，agent内部处理。
⚠️暂先仅列出可能使用部分。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

2. 查询
2.1 查询组织架构
2.1.1 查询全部用户组
floweye pppoeippool list
[root@Panabit:~]# floweye pppoeippool list
1 DefaultGroup 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2 test 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3 test1 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
129 test129 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2.1.2 查询特定用户组
floweye pppoeippool get <id=>
[root@Panabit:~]# floweye pppoeippool get id=129
id=129
pid=0
parent=NULL
name=test1299
nameutf8=NULL
start=0.0.0.0
end=0.0.0.0
size=0
bitmapsz=0
ratein=0
rateout=0
ratein6=0
rateout6=0
dns=
ipleft=0
nextip=0
acctcnt=0
rebuild_enable=0
maxonlinetime=0
clntepa=reject
ifname1=NULL
ifvlan1=0
ifname2=NULL
ifvlan2=0
svcname=NULL
maxip=0
maxdial=0
vlan_first=0
prefix=::
pfxlen=0

2.2 查询本地账号
2.2.1 查询所有账号
floweye pppoeacct list
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91703420 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29236220 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126263420 1970-01-01/08:00:00 0 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126263420 1970-01-01/08:00:00 0 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
⚠️经过验证:
禁用标志：1970-01-01/08:00:00 日志后第一个为 账号禁用状态，0为启用，1为禁用

2.2.2 查询特定账号
floweye pppoeacct get <name=>
[root@Panabit:~]# floweye pppoeacct get name=testa
name=testa
passwd=password1
mac=00:00:00:00:00:00
ip=0.0.0.0
poolid=4
pool=测试12
expire=2029-04-17
start=2025-04-17
birth=2025-04-17/14:00:58
lastofftime=1970-01-01/08:00:00
bindmac=
bindip=0.0.0.0
maxonline=100
onlinecnt=0
outif=
outvlan=0
outvlan1=0
cname=abc
card=aaa
phone=aaa
other=aaa
⚠️ 只有list才能看到是否禁用。

3. 配置
3.1 配置 组织架构
3.1.1 新建用户组
floweye pppoeippool add <id=> <pid=> <name=> <start=> <end=> <prefix=::> <pfxlen=> <ratein=> <rateout=> <ratein6=> <rateout6=> <dns=> <clntepa=> <maxonlinetime=> <vlan_first=>
[root@Panabit:~]# floweye pppoeippool add id=55 pid=129 name=test55 start=0.0.0.0 end=0.0.0.0 prefix=:: pfxlen= ratein=0 rateout=0 ratein6=0 rateout6=0 dns=0.0.0.0 clntepa=reject maxonlinetime=0 vlan_first=0
[root@Panabit:~]# floweye pppoeippool list
1 DefaultGroup 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2 test 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3 test123 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
129 te 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
1299 test1299 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 2001:db8:85a3::/64
4 测试12 1299 test1299 0.0.0.0 0.0.0.0 0 0 2 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
15 娴嬭瘯 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
55 test55 129 te 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3.1.2 修改用户组
floweye pppoeippool set <id=> <pid=> <name=> <start=> <end=> <prefix=::> <pfxlen=> <ratein=> <rateout=> <ratein6=> <rateout6=> <dns=> <clntepa=> <maxonlinetime=> <vlan_first=>
[root@Panabit:~]# floweye pppoeippool list
1 DefaultGroup 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2 test 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3 test123 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
129 te 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
1299 test1299 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 2001:db8:85a3::/64
4 测试12 1299 test1299 0.0.0.0 0.0.0.0 0 0 2 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
15 娴嬭瘯 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
55 test55 129 te 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
[root@Panabit:~]#
[root@Panabit:~]# floweye pppoeippool set id=55 name=test55 pid=129 start=0.0.0.0 end=0.0.0.0 prefix=:: pfxlen=0 ratein=0 rateout=0 ratein6=0 rateout6=0 dns=******* clntepa=reject maxonlinetime=0 vlan_first=0
[root@Panabit:~]# floweye pppoeippool list
1 DefaultGroup 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2 test 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3 test123 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
129 te 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
1299 test1299 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 2001:db8:85a3::/64
4 测试12 1299 test1299 0.0.0.0 0.0.0.0 0 0 2 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
15 娴嬭瘯 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
55 test55 129 te 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 ::/0
⚠️修改用户组只能通过id操作。

3.1.3 删除用户组
floweye pppoeippool remove <id=>
[root@Panabit:~]# floweye pppoeippool list
1 DefaultGroup 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2 test 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3 test123 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
129 te 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
1299 test1299 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 2001:db8:85a3::/64
4 测试12 1299 test1299 0.0.0.0 0.0.0.0 0 0 2 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
15 娴嬭瘯 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
55 test55 129 te 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 ::/0
[root@Panabit:~]# floweye pppoeippool remove id=55
[root@Panabit:~]# floweye pppoeippool list
1 DefaultGroup 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
2 test 0 NULL 0.0.0.0 0.0.0.0 0 0 1 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
3 test123 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
129 te 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
1299 test1299 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 ******* 0.0.0.0 0 reject NULL 0 2001:db8:85a3::/64
4 测试12 1299 test1299 0.0.0.0 0.0.0.0 0 0 2 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0
15 娴嬭瘯 0 NULL 0.0.0.0 0.0.0.0 0 0 0 0 0 0 0 0.0.0.0 0.0.0.0 0 reject NULL 0 ::/0

3.2 配置 本地账号
3.2.1 创建用户
floweye pppoeacct add <poolid=> <name=> <start=> <expire=> <maxonline=> <password=> <bindmac=> <bindip=> <outvlan=> <phone=> <card=> <cname=> <other=>
floweye pppoeacct add poolid=2 name=new_user start=2025-06-15 expire=2026-06-15 maxonline=1 password=12345 bindmac=00-00-00-00-00-00 bindip=0.0.0.0 outvlan=0 phone= card= cname= other=
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91702035 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234835 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126262035 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126262035 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
[root@Panabit:~]# floweye pppoeacct add poolid=4 name=testuser start=2025-04-17 expire=2029-04-17 maxonline=100 password=userpass bindmac=00-e0-4c-10-00-48,00-e0-4c-00-00-46,00-e0-4c-00-00-47 bindip=*********** outvlan=0 phone=1321111 card=65552311 cname=zhangsan other=others
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701958 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234758 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261958 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261958 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261958 1970-01-01/08:00:00 0 0 00-e0-4c-10-00-48 0 100 *********** NULL 0 0 zhangsan;65552311;1321111;others
[root@Panabit:~]# floweye pppoeacct get name=testuser
name=testuser
passwd=userpass
mac=00:00:00:00:00:00
ip=0.0.0.0
poolid=4
pool=测试12
expire=2029-04-17
start=2025-04-17
birth=2025-04-17/15:13:59
lastofftime=1970-01-01/08:00:00
bindmac=00-e0-4c-10-00-48,00-e0-4c-00-00-47,00-e0-4c-00-00-46
bindip=***********
maxonline=100
onlinecnt=0
outif=
outvlan=0
outvlan1=0
cname=zhangsan
card=65552311
phone=1321111
other=others
3.2.2 修改用户
pppoeacct set <poolid=> <name=> <start=> <expire=> <maxonline=> <password=> <bindmac=> <bindip=> <outvlan=> <phone=> <card=> <cname=> <other=>
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701823 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234623 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261823 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261823 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261823 1970-01-01/08:00:00 0 0 00-e0-4c-10-00-48 0 100 *********** NULL 0 0 zhangsan;65552311;1321111;others

[root@Panabit:~]# pppoeacct set poolid=4 name=testuser start=2025-04-17 expire=2029-04-17 maxonline=20 password=userpass bindmac=00-e0-4c-10-00-48,00-e0-4c-00-00-47,00-e0-4c-00-00-46 bindip=*********** outvlan=0 phone=1321111 card=65552311 cname=zhangsan other=others

[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701770 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234570 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261770 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261770 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261770 1970-01-01/08:00:00 0 0 00-e0-4c-10-00-48 0 20 *********** NULL 0 0 zhangsan;65552311;1321111;others
[root@Panabit:~]# floweye pppoeacct get name=testuser
name=testuser
passwd=userpass
mac=00:00:00:00:00:00
ip=0.0.0.0
poolid=4
pool=测试12
expire=2029-04-17
start=2025-04-17
birth=2025-04-17/15:13:59
lastofftime=1970-01-01/08:00:00
bindmac=00-e0-4c-10-00-48,00-e0-4c-00-00-47,00-e0-4c-00-00-46
bindip=***********
maxonline=20
onlinecnt=0
outif=
outvlan=0
outvlan1=0
cname=zhangsan
card=65552311
phone=1321111
other=others
3.2.3 禁用用户
floweye pppoeacct config acctdis=<用户名>
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701632 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234432 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261632 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261632 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261632 1970-01-01/08:00:00 0 0 00-e0-4c-10-00-48 0 20 *********** NULL 0 0 zhangsan;65552311;1321111;others
[root@Panabit:~]# pppoeacct config acctdis=testuser
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701583 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234383 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261583 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261583 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261583 1970-01-01/08:00:00 1 0 00-e0-4c-10-00-48 0 20 *********** NULL 0 0 zhangsan;65552311;1321111;others
3.2.4 启用用户
floweye pppoeacct config  accten=<用户名>
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701583 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234383 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261583 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261583 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261583 1970-01-01/08:00:00 1 0 00-e0-4c-10-00-48 0 20 *********** NULL 0 0 zhangsan;65552311;1321111;others
[root@Panabit:~]# floweye pppoeacct config  accten=testuser
[root@Panabit:~]# floweye pppoeacct list
2 test Leo Panabit@123 0.0.0.0 00-00-00-00-00-00 2025-03-14 2028-03-13 91701515 1970-01-01/08:00:00 0 0 NULL 0 3 0.0.0.0 NULL 0 0 NULL
1 DefaultGroup Printer xxxxxx 0.0.0.0 00-00-00-00-00-00 2025-03-21 2026-03-21 29234315 1970-01-01/08:00:00 0 0 NULL 0 1 ************** NULL 0 0 NULL
4 测试12 testa password1 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261515 1970-01-01/08:00:00 1 0 NULL 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testb testa 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261515 1970-01-01/08:00:00 0 0 00-e0-4c-00-00-48 0 100 0.0.0.0 NULL 0 0 abc;aaa;aaa;aaa
4 测试12 testuser userpass 0.0.0.0 00-00-00-00-00-00 2025-04-17 2029-04-17 126261515 1970-01-01/08:00:00 0 0 00-e0-4c-10-00-48 0 20 *********** NULL 0 0 zhangsan;65552311;1321111;others

