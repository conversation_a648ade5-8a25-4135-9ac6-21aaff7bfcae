# LAN和DHCP模块配置实现文档

## 概述

LAN模块负责处理局域网接口的配置，包括IP地址、子网掩码等设置。DHCP模块负责处理DHCP服务器的配置，包括地址池、租约时间等设置。本文档描述了LAN和DHCP模块的实现方案和关键代码。

## 配置项

### LAN配置项

根据`floweye_lan_dhcp.md`文档和`message.proto`定义，LAN模块支持以下配置项：

1. **基本配置**
   - 名称(name) - 必填，LAN接口名称
   - 网卡名称(ifname) - 必填，如eth0
   - MTU(mtu) - 必填，最大传输单元
   - IP地址(addr) - 必填，LAN接口IP地址
   - 子网掩码(mask) - 必填，LAN接口子网掩码
   - MAC地址克隆(clone_mac) - 可选，MAC地址

### DHCP配置项

DHCP模块支持以下配置项：

1. **基本配置**
   - 名称(name) - 必填，对应LAN接口名称
   - DHCP启用(dhcp_enable) - 必填，是否启用DHCP服务

2. **DHCP服务器配置**
   - 地址池(dhcp_pool) - 必填，DHCP地址分配范围
   - 租约时间(lease_ttl) - 可选，DHCP租约时间
   - DNS服务器(dns0, dns1) - 可选，主备DNS服务器
   - 网关(dhcp_gateway) - 可选，DHCP分配的网关
   - 子网掩码(dhcp_mask) - 可选，DHCP分配的子网掩码
   - AC地址(dhcp_ac_addr) - 可选，DHCP AC地址
   - 域名(dhcp_domain) - 可选，DHCP分配的域名

## 配置一致性和同步机制

根据`Configuration_Consistency_and_Synchronization_Mechanism.md`文档，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye nat listproxy`和`floweye nat getproxy`命令）
   - 将配置缓存在内存中

2. **配置处理策略**
   - 对象不在全量配置中：执行创建流程
   - 对象已在全量配置中：
     - 配置一致：忽略，无需修改
     - 配置不一致：替换配置
   - 每处理完一个对象后，从全量配置缓存中移除

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 对于LAN模块：处理剩余的本地配置（这些是需要删除的对象）
   - 对于DHCP模块：处理剩余的本地配置（这些是需要禁用DHCP的对象）
   - 清理资源

### 增量同步

1. **配置处理**
   - 获取本地全量配置
   - 按与全量配置一致的逻辑处理对象
   - 不在每次配置成功后重新获取全量配置，而是在下次配置时再获取

## 实现方案

### 关键数据结构

1. **LanConfig**
   - 表示本地LAN配置
   - 包含名称、接口名称、MTU、IP地址、子网掩码等信息
   - 同时包含DHCP相关配置

2. **LanProcessor**
   - 处理LAN配置任务
   - 维护本地配置缓存
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 实现全量同步和增量同步逻辑

3. **DhcpProcessor**
   - 处理DHCP配置任务
   - 维护本地配置缓存（复用LanConfig）
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 实现全量同步和增量同步逻辑

### 关键方法

#### LAN模块

1. **refreshLocalConfigs()**
   - 获取本地全量配置
   - 更新配置缓存

2. **StartFullSync()**
   - 开始全量同步
   - 获取本地全量配置

3. **EndFullSync()**
   - 结束全量同步
   - 处理剩余的本地配置（删除）
   - 清理资源

4. **handleNewConfig()**
   - 处理新建配置任务
   - 实现配置一致性检查
   - 执行配置命令并验证结果

5. **handleEditConfig()**
   - 处理编辑配置任务
   - 如果LAN不存在，则创建
   - 如果配置一致，则无需修改
   - 执行配置命令并验证结果

6. **handleDeleteConfig()**
   - 处理删除配置任务
   - 执行删除命令并验证结果

#### DHCP模块

1. **refreshLocalConfigs()**
   - 获取本地全量配置
   - 更新配置缓存

2. **StartFullSync()**
   - 开始全量同步
   - 获取本地全量配置

3. **EndFullSync()**
   - 结束全量同步
   - 处理剩余的本地配置（禁用DHCP）
   - 清理资源

4. **handleNewConfig()**
   - 处理新建配置任务
   - 实现配置一致性检查
   - 执行配置命令并验证结果

5. **handleEditConfig()**
   - 处理编辑配置任务
   - 如果LAN不存在，则报错
   - 如果配置一致，则无需修改
   - 执行配置命令并验证结果

6. **handleDeleteConfig()**
   - 处理删除配置任务
   - 禁用DHCP而不是删除LAN
   - 执行配置命令并验证结果

### 共享方法

1. **GetLocalLanConfigs()**
   - 获取所有本地LAN配置
   - 使用结构化的方式解析JSON格式的命令输出
   - 将结果转换为易于处理的结构化数据

2. **GetLanConfig()**
   - 获取指定LAN的配置
   - 使用结构化的方式解析key-value格式的命令输出
   - 将结果转换为易于处理的结构化数据

3. **VerifyLanConfig()**
   - 验证LAN配置是否成功应用
   - 确保命令执行成功后配置确实生效

4. **VerifyDhcpConfig()**
   - 验证DHCP配置是否成功应用
   - 确保命令执行成功后配置确实生效

5. **CompareLanConfig()**
   - 比较请求的LAN配置与本地配置
   - 决定是否需要修改配置

6. **CompareDhcpConfig()**
   - 比较请求的DHCP配置与本地配置
   - 决定是否需要修改配置

## 命令行交互

LAN和DHCP模块通过以下命令与系统交互：

1. **查询所有LAN**
   ```
   floweye nat listproxy type=lan json=1
   ```

2. **查询特定LAN**
   ```
   floweye nat getproxy <LAN名>
   ```

3. **创建LAN**
   ```
   floweye nat addproxy type=rtif name=<LAN名> ifname=<网卡名> mtu=<MTU> addr=<IP地址> netmask=<子网掩码> ...
   ```

4. **修改LAN**
   ```
   floweye nat setrtif name=<LAN名> newname=<LAN名> [ifname=<网卡名>] [mtu=<MTU>] [addr=<IP地址>] [netmask=<子网掩码>] ...
   ```

5. **删除LAN**
   ```
   floweye nat delproxy <LAN名>
   ```

6. **配置DHCP**
   ```
   floweye nat setrtif name=<LAN名> newname=<LAN名> dhcp_enable=<0|1> [dhcp_pool=<起始IP-结束IP>] [leasettl=<租约时间>] ...
   ```

## 实现特点

1. **结构化配置解析**
   - 使用map来解析key-value格式的命令输出
   - 使用JSON解析来处理列表输出
   - 提高了解析效率和可读性

2. **统一的全量同步接口**
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 支持全量同步和增量同步

3. **配置验证机制**
   - 在应用配置后验证是否成功
   - 确保命令执行成功后配置确实生效

4. **配置比对机制**
   - 在应用配置前先检查是否与本地配置一致
   - 如果一致则无需修改，提高效率

5. **DHCP与LAN的关联处理**
   - DHCP配置是LAN配置的一部分
   - DHCP删除操作只是禁用DHCP，不删除LAN
   - 全量同步结束时，对于未包含的DHCP配置，只禁用DHCP而不删除LAN

## 注意事项

1. LAN配置必须提供名称、接口名称、MTU、IP地址和子网掩码
2. DHCP配置必须提供名称和DHCP启用状态
3. 如果启用DHCP，必须提供地址池
4. DHCP配置依赖于LAN配置，如果LAN不存在，则无法配置DHCP
5. 删除DHCP配置只是禁用DHCP，不会删除LAN
6. 命令返回0不一定代表成功，需要再次查询确认配置是否生效

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 缺少必要参数：验证并返回明确的错误信息
5. LAN不存在但尝试配置DHCP：返回错误

## 测试方案

1. 单元测试：验证处理器的基本功能和错误处理
2. 集成测试：验证与floweye命令的交互
3. 系统测试：验证配置在实际环境中的生效情况
4. 全量同步测试：验证全量同步机制
5. 增量同步测试：验证增量同步机制

## 后续优化

1. 优化本地配置缓存的更新策略
2. 增加配置批量处理能力
3. 增加对LAN和DHCP状态的监控和上报
4. 实现更细粒度的错误处理和恢复机制
5. 优化JSON解析逻辑，提高解析效率和稳定性
6. 实现DHCP选项的完整支持
