# Interface模块网桥和链路捆绑功能实现文档

## 概述

本文档描述了Interface模块新增的网桥（Bridge）和链路捆绑（Link Aggregation/Bonding）功能的实现。

## 功能特性

### 1. 网桥（Bridge）功能
- 支持网桥模式1-6（INTERFACE_MODE_BRIDGE1 到 INTERFACE_MODE_BRIDGE6）
- 支持配置网桥对端接口（peer参数）
- 支持网桥接入位置配置（inside/outside）
- 支持混合模式配置

### 2. 链路捆绑（Link Aggregation）功能
- 支持链路捆绑组配置（lagroup 0-6，0为不捆绑）
- 支持LACP协议配置：
  - 静态模式（LACP_PROTOCOL_STATIC）
  - LACP协议模式（LACP_PROTOCOL_LACP）
- 支持LACP参数配置：
  - 老化模式：慢速（LACP_TIMEOUT_SLOW）/快速（LACP_TIMEOUT_FAST）
  - 被动模式：主动（false）/被动（true）

## 实现细节

### 1. Protocol Buffer定义

#### 新增枚举类型
```protobuf
// 链路捆绑协议类型枚举
enum LacpProtocol {
  LACP_PROTOCOL_STATIC = 0; // 静态模式
  LACP_PROTOCOL_LACP = 1;   // LACP协议模式
}

// 链路捆绑老化模式枚举
enum LacpTimeout {
  LACP_TIMEOUT_SLOW = 0; // 慢速模式
  LACP_TIMEOUT_FAST = 1; // 快速模式
}
```

#### 新增消息类型
```protobuf
// LACP配置消息
message LacpConfig {
  LacpProtocol protocol = 1;                // 捆绑协议：static/LACP (required)
  optional LacpTimeout timeout = 2;         // 老化模式：slow/fast
  optional bool passive = 3;                // 被动模式：true/false
}
```

#### 扩展InterfaceTask消息
```protobuf
message InterfaceTask {
  string name = 1;                          // 网卡名称，如 eth0 (required)
  InterfaceMode mode = 2;                   // 接口类型：监控模式或网桥模式 (required)
  InterfaceZone zone = 3;                   // 接入位置：inside/outside (required)
  optional bool mix_mode = 4;               // 混合模式：true/false
  optional uint32 la_group = 5;             // 链路捆绑组：0-6，0为不捆绑，1-6为链路组
  optional string peer = 6;                 // 网桥对端网卡名称（网桥模式时必填）
  optional LacpConfig lacp_config = 7;      // LACP配置（链路捆绑时使用）
}
```

### 2. 代码实现

#### 配置结构体更新
- `InterfaceConfig`：添加了`Lagroup`和`Peer`字段
- 新增`LacpConfig`结构体用于LACP配置管理

#### 处理器更新
- 合并`handleNewConfig`和`handleEditConfig`为统一的`handleConfigChange`方法
- 新增`configureLacp`方法处理LACP配置
- 更新floweye命令构建逻辑，支持新参数
- 更新配置验证和比较逻辑

#### 新增功能函数
- `GetLacpConfig`：获取LACP配置
- 更新的配置解析、比较和验证函数

### 3. Floweye命令支持

#### 网卡配置命令
```bash
floweye if set name=<interface> mode=<0-6> zone=<inside/outside> lagroup=<0-6> mixmode=<0/1> peer=<peer_interface>
```

#### LACP配置命令
```bash
floweye lacp set lag=<1-6> enable=<0/1> timeout=<0/1> passive=<0/1>
```

### 4. 测试覆盖

#### 单元测试
- 更新了`interface_processor_test.go`，支持新功能测试
- 添加了网桥配置测试
- 添加了链路捆绑配置测试

#### 集成测试
- `interface_bridge_config.json`：网桥配置测试
- `interface_link_aggregation_config.json`：链路捆绑配置测试
- `interface_comprehensive_config.json`：综合功能测试

## 使用示例

### 1. 网桥配置示例
```json
{
  "task_type": "TASK_INTERFACE",
  "task_action": "NEW_CONFIG",
  "interface_task": {
    "name": "eth2",
    "mode": "INTERFACE_MODE_BRIDGE1",
    "zone": "INTERFACE_ZONE_INSIDE",
    "mix_mode": true,
    "la_group": 0,
    "peer": "eth0"
  }
}
```

### 2. 链路捆绑配置示例
```json
{
  "task_type": "TASK_INTERFACE",
  "task_action": "NEW_CONFIG",
  "interface_task": {
    "name": "eth2",
    "mode": "INTERFACE_MODE_MONITOR",
    "zone": "INTERFACE_ZONE_INSIDE",
    "mix_mode": false,
    "la_group": 3,
    "lacp_config": {
      "protocol": "LACP_PROTOCOL_LACP",
      "timeout": "LACP_TIMEOUT_FAST",
      "passive": true
    }
  }
}
```

## 兼容性说明

- 新功能完全向后兼容，现有的interface配置不受影响
- 新增字段均为可选字段，不会破坏现有API
- 遵循项目现有的代码规范和架构模式

## 文件修改清单

### 核心实现文件
- `docs/message.proto`：添加新的protobuf定义
- `internal/client/task/interface_config.go`：更新配置结构和处理函数
- `internal/client/task/interface_processor.go`：更新处理器实现

### 测试文件
- `internal/client/task/interface_processor_test.go`：更新单元测试
- `test/interface_bridge_config.json`：网桥功能测试
- `test/interface_link_aggregation_config.json`：链路捆绑功能测试
- `test/interface_comprehensive_config.json`：综合功能测试
- `test/run_module_tests.sh`：更新测试脚本

### 生成文件
- `internal/pb/message.pb.go`：protobuf生成的Go代码（自动生成）

## 总结

本次实现完整支持了floweye_interface.md文档中描述的网桥和链路捆绑功能，包括：
- 完整的protobuf定义
- 完善的代码实现
- 全面的测试覆盖
- 良好的向后兼容性

所有实现都遵循了项目的编码规范和架构模式，确保了代码质量和可维护性。
