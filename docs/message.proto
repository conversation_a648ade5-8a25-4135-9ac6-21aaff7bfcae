syntax = "proto3";

option go_package = "grpc/proto;grpc";
option java_package = "com.unisase.rpc.proto";
option java_multiple_files = true;

// 同步消息逻辑中，任务类型枚举
// 这是站在设备（CPE/POP）视角的类型定义
// 控制器上的一次操作，可能会产生设备内部多个模块的业务操作
enum TaskType {
  TASK_INTERFACE = 0;
  TASK_WAN = 1;
  TASK_LAN = 2;
  TASK_DHCP = 3;
  TASK_WAN_GROUP = 4;
  TASK_USER_GROUP = 5;                // 用户组配置任务
  TASK_USER = 6;                      // 用户配置任务
  TASK_IWAN_PROXY = 7;                // iWAN Proxy线路配置任务
  TASK_IWAN_SERVICE = 8;              // iWAN Service服务配置任务
  TASK_IWAN_MAPPING = 9;              // iWAN Mapping配置任务
  TASK_SR_PROXY = 10;                 // SR Proxy配置任务
  TASK_IP_GROUP = 11;                 // IP群组配置任务
  TASK_DOMAIN_GROUP = 12;             // 域名群组配置任务
  TASK_EFFECTIVE_TIME = 13;           // 策略时段配置任务
  TASK_TRAFFIC_CHANNEL = 14;          // 流量通道配置任务
  TASK_TRAFFIC_STAT = 15;             // 流量统计配置任务
  TASK_FLOW_CONTROL = 16;             // 流量控制配置任务
  TASK_ROUTE_POLICY = 17;             // 路由策略配置任务
  TASK_DNS_POLICY = 18;               // DNS管控策略配置任务
  TASK_DNS_TRACKING_POLICY = 19;      // DNS跟踪策略配置任务
}

// 同步消息逻辑中，任务动作枚举
// 对于大部分模块来说（目前所有的模块），Agent 在处理 NEW_CONFIG 和 EDIT_CONFIG 时行为是一致的；
enum TaskAction {
  NEW_CONFIG = 0;
  EDIT_CONFIG = 1;
  DELETE_CONFIG = 2;
}

// 同步类型枚举
enum SyncType {
  INCREMENTAL_SYNC = 0;     // 增量同步
  FULL_SYNC = 1;            // 全量同步
}

// 客户端（CPE 或 POP）请求消息
message ClientSyncRequest {
  int32 customer_id = 1;                    // 客户ID
  int32 client_id = 2;                      // 客户端ID
  string uuid = 3;                          // 请求 UUID
  SyncType sync_type = 4;                   // 同步类型：增量或全量
}

// 服务器响应消息
// 一次可以响应多个任务
message ServerSyncResponse {
  int32 customer_id = 1;                    // 客户ID
  int32 client_id = 2;                      // 客户端ID
  string uuid = 3;                          // 返回 ClientSyncRequest 中的 uuid
  repeated TaskTx task_txs = 4;             // 子消息集合
}

// 相当于一个事务，内部可以包含多个任务
// 整体成功或者失败，通过 uuid 反馈状态给控制器
// Tx = Transaction
message TaskTx {
  string tx_id = 1;                     // 来自 redis 中每个配置变更的 uuid
  repeated DeviceTask device_tasks = 2; // 一个事物中包含的多条消息
}

// CPE/POP 内部对一个模块的一次操作
message DeviceTask {
  TaskType task_type = 1;                                   // 任务类型
  TaskAction task_action = 2;                               // 任务动作
  oneof payload {                                           // 具体业务消息
    WanTask wan_task = 3;                                   // WAN配置任务
    LanTask lan_task = 4;                                   // LAN配置任务
    InterfaceTask interface_task = 5;                       // 网卡配置任务
    DhcpServerTask dhcp_task = 6;                           // DHCP配置任务
    WanGroupTask wan_group_task = 7;                        // WAN群组配置任务
    UserGroupTask user_group_task = 8;                      // 用户组配置任务
    UserTask user_task = 9;                                 // 用户配置任务
    IwanProxyTask iwan_proxy_task = 10;                     // iWAN Proxy线路配置任务
    IwanServiceTask iwan_service_task = 11;                 // iWAN Service服务配置任务
    IwanMappingTask iwan_mapping_task = 12;                 // iWAN Mapping配置任务
    SrProxyTask sr_proxy_task = 13;                         // SR Proxy配置任务
    IpGroupTask ip_group_task = 14;                         // IP群组配置任务
    DomainGroupTask domain_group_task = 15;                 // 域名群组配置任务
    EffectiveTimeTask effective_time_task = 16;             // 策略时段配置任务
    TrafficChannelTask traffic_channel_task = 17;           // 流量通道配置任务
    TrafficStatTask traffic_stat_task = 18;                 // 流量统计配置任务
    FlowControlTask flow_control_task = 19;                 // 流量控制配置任务
    RoutePolicyTask route_policy_task = 20;                 // 路由策略配置任务
    DnsPolicyTask dns_policy_task = 21;                     // DNS管控策略配置任务
    DnsTrackingPolicyTask dns_tracking_policy_task = 22;    // DNS跟踪策略配置任务
  }
}

message TaskTxStatus {
  string tx_id = 1;                         // 事务ID
  int32 err_code = 2;                       // 0 表示成功，其他的错误场景控制和 Agent 一起定义
  string desc = 3;                          // 描述信息
}

// 客户端执行情况反馈
message ClientSyncAck {
  int32 customer_id = 1;                    // 客户ID
  int32 client_id = 2;                      // 客户端ID
  string uuid = 3;                          // 对应 ClientSyncRequest 中的 uuid
  repeated TaskTxStatus task_tx_status = 4; // 任务执行状态
}

// 网卡接口类型枚举
enum InterfaceMode {
  INTERFACE_MODE_MONITOR = 0; // 监控模式，网卡的普通接入模式
  INTERFACE_MODE_BRIDGE1 = 1; // 网桥模式1
  INTERFACE_MODE_BRIDGE2 = 2; // 网桥模式2
  INTERFACE_MODE_BRIDGE3 = 3; // 网桥模式3
  INTERFACE_MODE_BRIDGE4 = 4; // 网桥模式4
  INTERFACE_MODE_BRIDGE5 = 5; // 网桥模式5
  INTERFACE_MODE_BRIDGE6 = 6; // 网桥模式6
}

// 网卡接入位置枚举
enum InterfaceZone {
  INTERFACE_ZONE_INSIDE = 0;  // 接内
  INTERFACE_ZONE_OUTSIDE = 1; // 接外
}

// 链路捆绑协议类型枚举
enum LacpProtocol {
  LACP_PROTOCOL_STATIC = 0; // 静态模式
  LACP_PROTOCOL_LACP = 1;   // LACP协议模式
}

// 链路捆绑老化模式枚举
enum LacpTimeout {
  LACP_TIMEOUT_SLOW = 0; // 慢速模式
  LACP_TIMEOUT_FAST = 1; // 快速模式
}

// LACP配置消息
message LacpConfig {
  LacpProtocol protocol = 1;                // 捆绑协议：static/LACP (required)
  optional LacpTimeout timeout = 2;         // 老化模式：slow/fast
  optional bool passive = 3;                // 被动模式：true/false
}

// 网卡配置消息
message InterfaceTask {
  string name = 1;                          // 网卡名称，如 eth0 (required)
  InterfaceMode mode = 2;                   // 接口类型：监控模式或网桥模式 (required)
  InterfaceZone zone = 3;                   // 接入位置：inside/outside (required)
  optional bool mix_mode = 4;               // 混合模式：true/false
  optional uint32 la_group = 5;              // 链路捆绑组：0-6，0为不捆绑，1-6为链路组
  optional string peer = 6;                 // 网桥对端网卡名称（网桥模式时必填）
  optional LacpConfig lacp_config = 7;      // LACP配置（链路捆绑时使用）
}

// 心跳请求消息
message HeartbeatRequest {
  int32 customer_id = 1;                    // 客户ID
  int32 client_id = 2;                      // 客户端 ID
  bool check_new_config = 3;                // 是否检查新配置
}

// 心跳响应消息
message HeartbeatResponse {
  bool has_new_config = 1;                  // 是否有新配置
}

message ErrorCode {
  int32 code = 1;                           // 错误码
}

// CPE/POP 拉取配置服务
service ConfigSyncService {
  rpc HandleConfigSyncRequest(ClientSyncRequest) returns (ServerSyncResponse);
  rpc HandleClientSyncAck(ClientSyncAck) returns (ErrorCode);
}

// CPE/POP 发送心跳服务
service HeartbeatService {
  rpc SendHeartbeat(HeartbeatRequest) returns (HeartbeatResponse);
}

// CPE/POP 设备上报服务
service DeviceReportService {
  rpc SendDeviceReport(DeviceReportRequest) returns (DeviceReportResponse);
}

// WAN 网关类型枚举
enum WanGatewayType {
  WAN_GATEWAY_TYPE_NORMAL = 0;    // 正常网关
  WAN_GATEWAY_TYPE_INTERNET = 1;  // 互联网关
}

// IPv4 CIDR 表示
message V4Cidr {
  uint32 ip = 1;                            // IPv4 地址（32位整数）(required)
  uint32 prefix_length = 2;                 // 前缀长度（0-32）(required)
}

// IPv6 CIDR 表示
message V6Cidr {
  bytes ip = 1;                             // IPv6 地址（16字节）(required)
  uint32 prefix_length = 2;                 // 前缀长度（0-128）(required)
}

// IP 地址通用表示
message IpAddress {
  oneof ip {
    uint32 ipv4 = 1;                        // IPv4 地址（32位整数）
    bytes ipv6 = 2;                         // IPv6 地址（16字节）
    string ip_string = 3;                   // 字符串形式的 IP 地址
    V4Cidr v4_cidr = 4;                     // IPv4 CIDR 表示
    V6Cidr v6_cidr = 5;                     // IPv6 CIDR 表示
  }
}

// DHCP 选项值类型枚举
enum DhcpOptionValueType {
  DHCP_OPTION_TYPE_STRING = 0;  // 普通字符串
  DHCP_OPTION_TYPE_HEX = 1;     // 十六进制字符串
}

// DHCP 选项值
message DhcpOptionValue {
  string value = 1;                         // 选项值 (required)
  DhcpOptionValueType value_type = 2;       // 值类型：普通字符串或十六进制 (required)
}

// DHCP-V4配置
message DhcpOptionConfig {
  optional DhcpOptionValue option12 = 1;    // DHCP HostName
  optional DhcpOptionValue option61 = 2;    // DHCP Vendor class ID
  optional DhcpOptionValue option60 = 3;    // DHCP Client ID
}

message NatIp {
  optional IpAddress ip = 1;                // NAT IP地址
  optional IpRange ip_range = 2;            // NAT IP地址范围
}

// 心跳服务配置
message HeartbeatConfig {
  optional IpAddress ping_ip = 1;           // 心跳服务器1
  optional IpAddress ping_ip2 = 2;          // 心跳服务器2
  optional int32 max_delay = 3;             // 最大延迟，单位 ms
}

// WAN 配置消息
message WanTask {
  string name = 1;                          // WAN接口名称(wan唯一标识)，ASCII字符，最长15字节 (required)
  string ifname = 2;                        // 网卡名称 (添加修改时必填)
  int32 mtu = 3;                            // 最大传输单元，范围: 500-4700 (添加修改时必填)

  // 固定IP-V4配置
  message StaticIpConfig {
    WanGatewayType gw_pxy = 1;              // 网关类型 (required)
    IpAddress addr = 2;                     // 接口ip (required)
    IpAddress gateway = 3;                  // 网关地址 (required)
    optional IpAddress dns = 4;             // dns服务器地址
    optional NatIp nat_ip = 5;              // NAT地址池
  }

  // PPPoE-V4配置
  message PppoeConfig {
    string username = 1;                    // PPPoE账号，ASCII字符，max 255字节 (required)
    string password = 2;                    // PPPoE密码 (required)
    optional string ac_name = 3;            // BRAS 名称，ASCII字符，max 64字节
    optional string svc_name = 4;           // Service名称
    optional int32 wait_time = 5;           // 等待时间，单位秒
  }

  // 其它通用设置
  message CommonConfig {
    optional bool dns_pxy = 1;              // DNS线路开关
    optional string clone_mac = 2;          // 克隆MAC，格式：00-00-00-00-00-00
    optional bool ping_disable = 3;         // 外网ping不应答
  }

  oneof wan_type {
    StaticIpConfig static_ip = 4;           // 固定IP配置
    DhcpOptionConfig dhcp = 5;              // DHCP配置
    PppoeConfig pppoe = 6;                  // PPPoE配置
  }

  optional HeartbeatConfig heartbeat = 7;   // 心跳服务配置
  optional CommonConfig common = 8;         // 其它通用设置
}

// LAN 配置消息
message LanTask {
  string name = 1;                          // LAN接口名称(lan唯一标识)，ASCII字符，最长15字节 (required)
  string ifname = 2;                        // 网卡名称 (添加修改时必填)
  int32 mtu = 3;                            // 最大传输单元，范围: 500-4700 (添加修改时必填)
  IpAddress addr = 4;                       // 接口ip (添加修改时必填)
  IpAddress mask = 5;                       // 掩码 (添加修改时必填)
  optional string clone_mac = 6;            // 克隆MAC，格式：00-00-00-00-00-00，前4字节不能为空
}

message IpRange {
  IpAddress start_ip = 1;                   // 起始IP地址
  IpAddress end_ip = 2;                     // 结束IP地址
}

// DHCP 配置消息
message DhcpServerTask {
  string name = 1;                              // LAN接口名称(dhcp唯一标识)，ASCII字符，最长15字节 (required)
  IpRange dhcp_pool = 2;                        // dhcp地址范围 (添加修改时必填)
  int32 lease_ttl = 3;                          // 租约时间，单位 s (添加修改时必填)
  bool dhcp_enable = 4;                         // 开启/关闭dhcp (添加修改时必填)
  optional IpAddress dns0 = 5;                  // dhcp主DNS
  optional IpAddress dns1 = 6;                  // dhcp次DNS
  optional IpAddress dhcp_gateway = 7;          // dhcp网关，如果为0.0.0.0或不填，则使用接口IP地址作为网关
  optional IpAddress dhcp_mask = 8;             // dhcp掩码，如果为0.0.0.0或不填，则使用接口的掩码
  optional string dhcp_domain = 9;              // dhcp分配给客户端的域名
  optional DhcpOptionConfig dhcp_options = 10;  // DHCP options
  optional IpAddress dhcp_ac_addr = 11;         // dhcp分配给客户端的AC地址
}

// WAN群组类型枚举
enum WanGroupType {
  WAN_GROUP_TYPE_SRCDST = 0;    // 源地址+目的地址
  WAN_GROUP_TYPE_SPDP = 1;      // 源目地址+源目端口
  WAN_GROUP_TYPE_SRC = 2;       // 源地址
  WAN_GROUP_TYPE_SRCSPORT = 3;  // 源地址+源端口
  WAN_GROUP_TYPE_DST = 4;       // 目的地址
  WAN_GROUP_TYPE_DSTDPORT = 5;  // 目的地址+目的端口
  WAN_GROUP_TYPE_RX_LEFTBW = 6; // 最大下行空闲带宽
  WAN_GROUP_TYPE_TX_LEFTBW = 7; // 最大上行空闲带宽
  WAN_GROUP_TYPE_SESSION = 8;   // 最小连接数
  WAN_GROUP_TYPE_FAILOVER = 9;  // 主备模式
}

// WAN群组成员配置
message WanGroupMember {
  string proxy_name = 1;                // WAN成员名称 (required)
}

// WAN群组配置消息
message WanGroupTask {
  int32 id = 1;                         // 群组ID(wan group唯一标识)，范围: 1-128 (required)
  string name = 2;                      // 群组名称，ASCII字符，最长15字节 (添加修改时必填)
  WanGroupType type = 3;                // 群组类型 (添加修改时必填)
  repeated WanGroupMember members = 4;  // 群组成员列表
}

// 过期账号处理方式枚举
enum UserExpiredPolicy {
  USER_EXPIRED_POLICY_REJECT = 0; // 禁止登录
  USER_EXPIRED_POLICY_LOGIN = 1;  // 允许登录，禁止上网
  USER_EXPIRED_POLICY_PASS = 2;   // 允许登录及上网
}

message UserBwLimit {
  optional int32 rate_in = 1;               // kbps, 0表示不限制
  optional int32 rate_out = 2;              // kbps，0表示不限制
}

// 用户组配置消息
message UserGroupTask {
  int32 id = 1;                             // 用户组ID(用户组唯一标识)，范围: 1-2063 (required)
  int32 pid = 2;                            // 上级组ID (添加修改时必填)
  string name = 3;                          // 用户组名称 (添加修改时必填)

  // IPv4地址范围
  optional IpRange v4_range = 4;            // IPv4地址范围起始

  // IPv6地址
  optional IpAddress v6_range = 5;          // IPv6地址范围

  // 账号带宽限制
  optional UserBwLimit v4_rate = 6;         // IPv4带宽限制
  optional UserBwLimit v6_rate = 7;         // IPv6带宽限制

  repeated IpAddress dns = 8;               // DNS服务器，可配置多个，用逗号分隔
  optional int32 max_online_time = 9;       // 在线时间，单位小时，0表示不控制
  optional UserExpiredPolicy clnt_epa = 10; // 过期账号处理方式
}

// 用户限制配置
message UserRestriction {
  optional int32 max_online = 1;           // 最大在线数，0表示不控制
  optional IpAddress bind_ip = 2;          // 绑定IP，0.0.0.0或为空表示不绑定
  repeated string bind_mac = 3;            // 绑定MAC，00-00-00-00-00-00表示不绑定，可以为多个
  optional int32 out_vlan = 4;             // 绑定VLAN，默认不绑定
}

// 用户身份信息
message UserIdentity {
  optional string cname = 1;               // 用户姓名
  optional string card = 2;                // 用户身份证
  optional string phone = 3;               // 用户联系电话
  optional string other = 4;               // 用户其他信息
}

message Date {
  int32 year = 1;                          // 年
  int32 month = 2;                         // 月
  int32 day = 3;                           // 日
}

// 用户配置消息
message UserTask {
  string name = 1;                          // 用户账号(用户唯一标识)，不超过30个英文字符或者15个中文字符 (required)
  int32 pool_id = 2;                        // 用户组ID (添加修改时必填)
  string password = 3;                      // 用户密码，不超过30个英文字符 (添加修改时必填)
  Date start = 4;                           // 开通日期，格式：年-月-日 (添加修改时必填)
  Date expire = 5;                          // 截止日期，格式：年-月-日 (添加修改时必填)
  bool enable = 6;                          // 账号是否启用 (添加修改时必填)

  optional UserRestriction restriction = 7; // 用户限制配置
  optional UserIdentity identity = 8;       // 用户身份信息
}

// iWAN Proxy线路配置消息
message IwanProxyTask {
  string name = 1;                          // iWAN线路名称(iWAN线路唯一标识)，ASCII字符，最長15字节 (required)
  string ifname = 2;                        // 承载的线路名称 (添加修改时必填)
  int32 mtu = 3;                            // 最大传输单元，范围: 500-4700 (添加修改时必填)
  string svr_addr = 4;                      // 服务器ip/域名 (添加修改时必填)
  int32 svr_port = 5;                       // 服务器端口 (添加修改时必填)
  string username = 6;                      // iWAN账号 (添加修改时必填)
  string password = 7;                      // iWAN密码 (添加修改时必填)
  optional bool encrypt = 8;                // 是否加密
  optional int32 link = 9;                  // 分段标识
  optional HeartbeatConfig heartbeat = 10;  // 心跳服务配置
  optional bool dns_pxy = 11;               // DNS线路开关
  string ifname2 = 12;                      // 备份承载的线路名称，可与ifname相同
}

// iWAN Service服务配置消息
message IwanServiceTask {
  string name = 1;                          // iWAN服务名称(iWAN服务唯一标识)，ASCII字符，最長15字节 (required)
  IpAddress addr = 2;                       // 服务器网关 (添加修改时必填)
  int32 mtu = 3;                            // 最大传输单元，范围: 500-4700 (添加修改时必填)
  int32 pool = 4;                           // 地址池(用户组)ID (添加修改时必填)
}

message IwanMappingTask {
  string proxy = 1;                         // 映射线路 (required) proxy+port作为唯一标识
  int32 port = 2;                           // 映射端口 (required)
  optional string server = 3;               // iWAN服务名称, NULL for delete
}

// SR加密类型枚举
enum SrEncryptType {
  SR_ENCRYPT_NONE = 0;    // 不加密
  SR_ENCRYPT_AES128 = 1;  // AES128加密
  SR_ENCRYPT_AES256 = 2;  // AES256加密
}

// SR加密配置消息
message SrEncryptConfig {
  SrEncryptType encrypt_type = 1;           // 加密方式 (required)
  optional string password = 2;             // 加密密钥，当encrypt_type不为NONE时使用
}

// SR路径配置消息
message SrPath {
  repeated int32 links = 1;                 // iWAN Proxy分段标识，多个标识用','分隔，0表示删除
}

// SR Proxy配置消息
message SrProxyTask {
  string name = 1;                          // SR名称(SR唯一标识)，ASCII字符，最长15字节 (required)
  repeated SrPath paths = 2;                // SR路径配置列表 (添加修改时必填)
  bool from_in = 3;                         // 接内标记 (添加修改时必填)
  int32 mtu = 4;                            // 最大传输单元，范围: 500-4700 (添加修改时必填)
  bool keepalive = 5;                       // 是否开启保活，true为开启，false为关闭 (添加修改时必填)
  optional SrEncryptConfig encrypt_config = 6; // 加密配置，可选
}

// IP群组成员
// 表示单个IP成员配置
message IpGroupMember {
  oneof ip_addr {
    IpAddress ip = 1;                       // IP地址，格式: ip,ip/mask
    IpRange ip_range = 2;                   // IP地址范围,ip-ip
  }
  optional string info = 3;                 // 备注信息
}

// IP群组配置消息
message IpGroupTask {
  string name = 1;                          // IP群组名称(IP群组唯一标识)，ASCII字符，最长15字节 (required)
  repeated IpGroupMember members = 2;       // IP成员列表，当IP成员数量较少时使用
  optional bytes file_content = 3;          // 文件形式IP群组内容，当IP成员数量较多时使用
}

// 域名群组配置消息
message DomainGroupTask {
  string name = 1;                          // 域名群组名称(域名群组唯一标识)，ASCII字符 (required)
  repeated string domain = 2;               // 域名，支持前缀匹配格式：*sohu.com, @sohu.com, ^sohu.com
  optional bytes file_content = 3;          // 文件形式域名群组内容，当域名成员数量较多时使用，每行一个域名
}

// 每日时间点定义消息
message DailyTime {
  int32 hour = 1;                           // 小时，范围: 0-23
  int32 min = 2;                            // 分钟，范围: 0-59
  int32 sec = 3;                            // 秒，范围: 0-59
}

// 时间规格定义消息 - 表示时间段
// 用于定义"每周某几天的某个时间段内生效"的时间规则
// 示例：
//   - 每周一到周五的9:00-18:00: start_day=1, end_day=5, start_time=9:00:00, end_time=18:00:00
message TimeSpec {
  int32 start_day = 1;                      // 起始星期几，范围: 1-7 (1=周一, 2=周二, ..., 7=周日)
  int32 end_day = 2;                        // 结束星期几，范围: 1-7 (1=周一, 2=周二, ..., 7=周日)
  DailyTime start_time = 3;                 // 每日起始时间 (required)
  DailyTime end_time = 4;                   // 每日结束时间 (required)
}

// 策略时段配置消息 用于路由/NAT & DNS 管控
message EffectiveTimeTask {
  int32 id = 1;                             // 策略时段ID(策略时段唯一标识)，范围: 1-128 (required)
  string name = 2;                          // 策略时段名称，ASCII字符 (添加修改时必填)
  TimeSpec time_range = 3;                  // 策略时段时间范围 (添加修改时必填)
}

// 流量通道优先级配置
message TrafficChannelPriority {
  int32 pri = 1;                            // 优先级，范围: 1-16，1优先级最高 (required)
  int32 max_rate = 2;                       // 最大带宽，单位kbps (required)
  int32 gbw = 3;                            // 保证带宽，单位kbps (required)
  optional string desc = 4;                 // 描述
}

// 流量通道配置消息
message TrafficChannelTask {
  string name = 1;                          // 流量通道名称(流量通道唯一标识)，ASCII字符 (required)
  int32 rate = 2;                           // 流量通道带宽，单位kbits/s，范围: 1-16000000 (添加修改时必填)
  optional int32 quota = 3;                 // 当日限额，单位Mbytes，0表示不限额
  repeated TrafficChannelPriority priorities = 4; // 优先级设置列表，最多16个优先级
}

// 流量统计配置消息
message TrafficStatTask {
  string name = 1;                          // 流量统计名称(流量统计唯一标识)，ASCII字符 (required)
  bool track_ip = 2;                        // 跟踪IP标志，true表示跟踪IP，false表示不跟踪 (添加修改时必填)
}

// 流量控制策略动作类型枚举
enum FlowControlAction {
  FLOW_CONTROL_ACTION_PERMIT = 0;   // 允许通过
  FLOW_CONTROL_ACTION_DENY = 1;     // 阻断
  FLOW_CONTROL_ACTION_CHANNEL = 2;  // 流量通道限速
}

// 流量方向枚举
enum FlowDirection {
  FLOW_DIRECTION_BOTH = 0;  // 双向
  FLOW_DIRECTION_IN = 1;    // 入方向
  FLOW_DIRECTION_OUT = 2;   // 出方向
}

// 路由策略动作类型枚举
enum RoutePolicyAction {
  ROUTE_ACTION_ROUTE = 0;   // 路由
  ROUTE_ACTION_NAT = 1;     // NAT
  ROUTE_ACTION_DNAT = 2;    // DNAT
  ROUTE_ACTION_PROXY = 3;   // 代播
}

// 路由策略Zone枚举 - 用于分层排序管理
enum RoutePolicyZone {
  CTRL_TIER_T1 = 0;     // 平台连接策略层 (优先级1-5000)
  CUST_TIER_T2 = 1;     // 自定义策略层 (优先级5001-50000)
  LPM_TIER_T3 = 2;      // 最长前缀策略层 (优先级50001-60000)
  DEF_WAN_TIER_T4 = 3;  // 默认路由策略层 (优先级60001-65535)
}

// 用户类型枚举
enum UserType {
  USER_TYPE_ANY = 0;      // 任意
  USER_TYPE_IPPXY = 1;    // 代播用户
  USER_TYPE_NONIPPXY = 2; // 非代播用户
}

message AddressSelector {
  oneof selector {
    IpAddress ip = 1;                       // IP地址
    IpRange ip_range = 2;                   // IP地址范围
    string ip_group_name = 3;               // IP群组名称
    int32 mac_group_id = 4;                 // MAC群组ID
    int32 user_group_id = 5;                // 用户群组ID
    string username = 6;                    // 用户名
    string domain_group_name = 7;           // 域名群组名称
  }
}

// 整数范围配置（用于TTL、DSCP、VLAN等）
message IntRange {
  uint32 start = 1;                        // 起始值
  uint32 end = 2;                          // 结束值，若 start == end 表示单个值
}

// 端口范围配置
message PortRange {
  uint32 start = 1;                        // 起始端口
  uint32 end = 2;                          // 结束端口，若 start == end 表示单个端口
}

// 端口规格配置
message PortSpec {
  repeated PortRange ports = 1;             // 端口范围列表，支持单端口和端口范围
}

// 应用协议配置
message AppProtocolSpec {
  string app_name = 1;                      // 应用名称，如 "any", "httpgroup", "ftpgroup" (required)
  string app_protocol = 2;                  // 协议类型，如 "tcp", "udp", "icmp" 等 (required)
}

// VLAN范围配置
message VlanRange {
  uint32 start = 1;                        // 起始VLAN ID
  uint32 end = 2;                          // 结束VLAN ID，若 start == end 表示单个VLAN
}

// 接口配置
message InterfaceSpec {
  optional string bridge = 1;              // 线路名称，支持线路群组格式：_wg.线路群组名称
  optional FlowDirection dir = 2;          // 流向：both/in/out
  optional string ifname = 3;              // 首包接口：网卡名/线路名称
  optional string in_if = 4;               // 源接口：网卡名/线路名称
  optional VlanRange vlan = 5;             // VLAN配置，支持单VLAN和VLAN范围
}

// 策略组配置消息
message FlowControlPolicyGroupTask {
  string name = 1;                         // 策略组名称(策略组唯一标识)，ASCII字符 (required)
  TimeSpec time_range = 2;                 // 策略组时间范围 (添加修改时必填)
  bool disable = 3;                        // 是否启用：false为启用，true为禁用 (添加修改时必填)
  bool stop = 4;                           // 是否继续匹配：false为继续匹配，true为停止 (添加修改时必填)
  optional string previous = 5;            // 前一个策略组名称，用于排序，null表示首个策略组
}

// 策略执行动作配置
message ActionChannelConfig {
  bool next = 1;                           // 匹配后状态：false为继续匹配，true为停止匹配 (required)
  string channel = 2;                      // 流量通道名称（当action为CHANNEL时必填）
  int32 pri = 3;                           // 通道优先级（当action为CHANNEL时必填）
  optional int32 ip_rate = 4;              // 内网IP限速，单位kbits/s，0为不限速
  optional string so_id = 5;               // 流量统计名称
}

// 策略执行动作配置
message ActionAcceptConfig {
  bool next = 1;                           // 匹配后状态：false为继续匹配，true为停止匹配 (required)
  optional int32 ip_rate = 3;              // 内网IP限速，单位kbits/s，0为不限速
  optional int32 tos = 4;                  // 修改DSCP，值为1~63
  optional string so_id = 5;               // 流量统计名称
}

// 策略配置消息
message FlowControlPolicyTask {
  uint32 cookie = 1;                       // 策略cookie(策略唯一标识)，uint32 (required)
  string desc = 2;                         // 策略描述 (添加修改时必填)
  string group_name = 3;                   // 所属策略组名称 (required)
  bool disable = 4;                        // 是否启用：false为启用，true为禁用 (添加修改时必填)
  optional uint32 previous = 5;            // 前一个策略cookie，用于排序，0表示首个策略

  // 用户/访问者配置
  repeated AddressSelector in_ip = 6;      // 内网IP配置列表
  optional PortSpec in_port = 7;           // 内网端口配置

  // 服务者/服务配置
  repeated AddressSelector out_ip = 8;     // 外网IP配置列表
  optional PortSpec out_port = 9;          // 外网端口配置
  optional AppProtocolSpec app = 10;       // 协议配置

  // 接口配置
  optional InterfaceSpec interface = 11;   // 接口配置

  // 执行动作
  FlowControlAction action = 12;           // 执行动作类型 (required)
  oneof action_config {
    ActionAcceptConfig action_accept = 13; // 允许动作配置
    ActionChannelConfig action_channel = 14; // 通道动作配置
  }
}

// 流量控制任务配置消息
message FlowControlTask {
  oneof task_config {
    FlowControlPolicyGroupTask policy_group = 1; // 策略组配置任务
    FlowControlPolicyTask policy = 2;            // 策略配置任务
  }
}

// NAT IP地址池配置
message NatIpPool {
  repeated IpAddress ip = 1;                // 单个IP地址列表
  repeated IpRange ip_ranges = 2;           // IP地址范围列表
}

// 路由策略动作配置
message RouteActionConfig {
  string proxy = 1;                         // 线路名称 (required)
  optional IpAddress next_hop = 2;          // 下一跳地址（路由动作时使用）
}

// 路由策略动作配置
message NatActionConfig {
  string proxy = 1;                         // 线路名称 (required)
  optional IpAddress new_dst_ip = 2;        // DNAT目标地址（DNAT动作时使用）
  optional NatIpPool nat_ip = 3;            // SNAT地址池（NAT/DNAT动作时使用）
  optional IpAddress next_hop = 4;          // 下一跳地址（路由动作时使用）
  optional bool full_cone_nat = 5;          // 全锥形NAT（NAT时有效）
  optional bool no_snat = 6;                // 不改变源地址（DNAT时有效）
}

// 路由策略配置消息
message RoutePolicyTask {
  uint32 cookie = 1;                        // 策略cookie(策略唯一标识)，uint32 (required)
  string desc = 2;                          // 策略描述 (可选)
  optional uint32 previous = 3;             // 前一个策略cookie，用于排序; zone首个策略为0;zone追加策略为-1
  bool disable = 4;                         // 是否启用：false为启用，true为禁用 (添加修改时必填)
  optional int32 sch_time = 5;              // 策略时段ID，0为任意
  optional RoutePolicyZone zone = 6;        // 策略所属Zone，用于分层排序管理

  // 用户/访问者配置
  repeated AddressSelector src = 7;         // 源地址配置列表
  optional PortSpec src_port = 8;           // 源端口配置
  optional UserType usr_type = 9;           // 用户类型
  optional int32 pool = 10;                 // 用户组ID，0为任意

  // 服务者/服务配置
  repeated AddressSelector dst = 11;        // 目的地址配置列表
  optional PortSpec dst_port = 12;          // 目的端口配置
  optional AppProtocolSpec proto = 13;      // 协议类型

  // 接口配置
  optional string in_if = 15;               // 源接口，any为任意
  optional int32 wan_bw = 16;               // 上行带宽，0为不限制
  optional int32 wan_bw_out = 17;           // 下行带宽，0为不限制
  optional IntRange vlan = 18;              // VLAN ID范围
  optional IntRange ttl = 19;               // TTL值范围
  optional IntRange dscp = 20;              // DSCP值范围

  // 执行动作
  RoutePolicyAction action = 21;            // 执行动作类型 (required)
  optional RouteActionConfig route_config = 22; // 路由动作配置 (required)
  optional NatActionConfig nat_config = 23; // NAT动作配置
}

// DNS查询类型枚举
enum DnsQueryType {
  DNS_QUERY_TYPE_ANY = 0;     // 任意
  DNS_QUERY_TYPE_IPV4 = 1;    // IPv4 (A记录)
  DNS_QUERY_TYPE_IPV6 = 2;    // IPv6 (AAAA记录)
}

// DNS策略动作类型枚举
enum DnsPolicyAction {
  DNS_ACTION_PASS = 0;        // 放行
  DNS_ACTION_DENY = 1;        // 丢弃
  DNS_ACTION_RDR = 2;         // 牵引
  DNS_ACTION_REPLY = 3;       // 解析
  DNS_ACTION_LIMIT = 4;       // QPS限制
  DNS_ACTION_IPPXY = 5;       // 代播重定向
  DNS_ACTION_ZEROREPLY = 6;   // 无名应答
}

// DNS策略动作配置 - 放行/代播重定向
message DnsPolicyActionPassConfig {
  optional uint32 ip_qps = 1;               // 单用户QPS，单位/s，0为不限制
  bool next = 2;                            // 是否继续匹配：false为继续匹配，true为停止匹配
}

// DNS策略动作配置 - 牵引
message DnsPolicyActionRdrConfig {
  string act_arg = 1;                       // 牵引线路 (required)
  optional bool no_snat = 2;                // 不改变源地址：false为改变，true为不改变
  repeated IpAddress dns_list = 3;          // 牵引DNS列表
}

// DNS策略动作配置 - 解析
message DnsPolicyActionReplyConfig {
  repeated IpAddress act_arg = 1;           // 解析IP列表 (required)
}

// DNS策略动作配置 - QPS限制
message DnsPolicyActionLimitConfig {
  optional uint32 ip_qps = 1;               // 单用户QPS，单位/s，0为不限制
  optional uint32 act_arg = 2;              // 总QPS，单位/s，0为不限制
  bool next = 3;                            // 是否继续匹配：false为继续匹配，true为停止匹配
}

// DNS管控策略配置消息
message DnsPolicyTask {
  uint32 cookie = 1;                        // 策略cookie(策略唯一标识)，uint32 (required)
  optional uint32 previous = 2;             // 前一个策略cookie，用于排序，0表示首个策略，-1表示追加到最后
  bool disable = 3;                         // 是否启用：false为启用，true为禁用 (添加修改时必填)
  optional int32 sch_time = 4;              // 策略时段ID，0为任意

  // 用户/访问者配置
  repeated AddressSelector in_ip = 5;       // 源地址配置列表
  optional int32 pool = 6;                  // 用户组ID，0为任意
  optional UserType usr_type = 7;           // 用户类型

  // 服务者/服务配置
  repeated AddressSelector out_ip = 8;      // 目的地址配置列表
  optional AppProtocolSpec app = 9;         // 应用协议配置
  repeated string domain_group = 10;        // 域名群组名称，空为任意
  optional DnsQueryType a_type = 11;        // 查询类型

  // 接口配置
  optional string in_if = 12;               // 源接口，any为任意
  optional int32 bridge = 13;               // 路径网桥接口
  optional IntRange vlan = 14;              // VLAN ID范围

  // 执行动作
  DnsPolicyAction action = 15;              // 执行动作类型 (required)
  oneof action_config {
    DnsPolicyActionPassConfig action_pass = 16;     // 放行/代播重定向动作配置
    DnsPolicyActionRdrConfig action_rdr = 17;       // 牵引动作配置
    DnsPolicyActionReplyConfig action_reply = 18;   // 解析动作配置
    DnsPolicyActionLimitConfig action_limit = 19;   // QPS限制动作配置
  }
}

// DNS跟踪策略配置消息
message DnsTrackingPolicyTask {
  uint32 cookie = 1;                        // 策略cookie(策略唯一标识)，uint32 (required)
  optional uint32 previous = 2;             // 前一个策略cookie，用于排序，0表示首个策略，-1表示追加到最后
  bool disable = 3;                         // 是否启用：false为启用，true为禁用 (添加修改时必填)
  repeated string domain_group = 4;         // 域名群组名称列表，agent需要通过域名群组名称反查ID (required)
  string pxy = 5;                           // 线路名称 (required)
  string backup_pxy = 6;                    // 备份线路名称 (required)
  optional IpAddress dns_addr = 7;          // DNS服务器地址，为空时表示使用线路DNS服务器
  optional bool track_host = 8;             // 跟踪host标志，true表示跟踪，false表示不跟踪
  optional uint32 cache_ttl = 9;            // DNS缓存应答TTL，单位秒，0表示关闭
  optional string desc = 10;                // 备注描述
}

// 设备上报类型枚举
enum ReportType {
  REPORT_TYPE_PROXY_STATUS = 0;     // 线路状态上报
  // 后续可扩展其他上报类型
}

// 上报触发类型枚举
enum ReportTriggerType {
  REPORT_TRIGGER_EVENT = 0;         // 事件触发上报
  REPORT_TRIGGER_PERIODIC = 1;      // 周期性全量上报
}

// 线路类型枚举
enum ProxyType {
  PROXY_TYPE_NONE = 0;
  PROXY_TYPE_IWAN = 1;      // iWAN线路
  PROXY_TYPE_SRPXY = 2;     // SR线路
}

// 线路状态枚举
enum ProxyState {
  PROXY_STATE_DOWN = 0;     // 断线状态
  PROXY_STATE_UP = 1;       // 连通状态
}

// 单个线路状态信息
message ProxyStatusInfo {
  string name = 1;                          // 线路名称 (required)
  ProxyType type = 2;                       // 线路类型 (required)
  ProxyState state = 3;                     // 线路状态 (required)
}

// 线路状态上报数据
message ProxyStatusReport {
  ReportTriggerType trigger_type = 1;         // 触发类型：事件触发或周期性 (required)
  repeated ProxyStatusInfo proxy_status = 2;  // 线路状态信息列表 (required)
}

// 设备上报消息（类似于 DeviceTask）
message DeviceReport {
  ReportType report_type = 1;                   // 上报类型 (required)
  oneof payload {                               // 具体上报内容
    ProxyStatusReport proxy_status_report = 2;  // 线路状态上报
    // 后续可扩展其他上报类型
  }
}

// 设备上报请求消息
message DeviceReportRequest {
  int32 customer_id = 1;                    // 客户ID
  int32 client_id = 2;                      // 客户端ID
  string uuid = 3;                          // 请求 UUID
  repeated DeviceReport device_reports = 4; // 设备上报列表
  int64 report_timestamp = 5;               // 上报时间戳，Unix时间戳（秒） (required)
}

// 设备上报响应消息
message DeviceReportResponse {
  string uuid = 1;                          // 返回 DeviceReportRequest 中的 uuid
  int32 err_code = 2;                       // 0 表示成功，其他表示错误
}
