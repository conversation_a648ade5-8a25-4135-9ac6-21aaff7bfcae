1. 流量统计字段
字段		释义	字段类型	必填项	示例	备注
name		流量统计名称	string	是	stats_1	唯一标识；
trackip		跟踪ip	bool	是	0	

⚠️以名称作为唯一标识，agent通过名称反查ID。

2. 流量统计查询
2.1 查询所有流量统计
floweye ntmso list
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
2.2 查询指定流量统计
floweye ntmso get <id=>
[root@Panabit:~]# floweye ntmso get id=1
id=1
name=stat_inner
trackip=1
upbps=0
dnbps=0
upbytes=0
dnbytes=0
thread_flow1_trackip=1

3. 流量统计设置
3.1 新增流量统计
floweye ntmso add <name=> <trackip=>  
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
[root@Panabit:~]# floweye ntmso add name=stat_new trackip=1     
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":3,"name":"stat_new","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}

3.2 修改流量统计
floweye ntmso set <id=> <trackip=>
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":3,"name":"stat_new","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
[root@Panabit:~]# floweye ntmso set id=3 trackip=0
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":3,"name":"stat_new","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
[root@Panabit:~]# floweye ntmso get id=3
id=3
name=stat_new
trackip=0
upbps=0
dnbps=0
upbytes=0
dnbytes=0
thread_flow1_trackip=0

3.3 删除流量统计
floweye ntmso remove <id=>
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":3,"name":"stat_new","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}
[root@Panabit:~]# floweye ntmso remove id=3
[root@Panabit:~]# floweye ntmso list
{"id":1,"name":"stat_inner","trackip":1,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0},{"id":2,"name":"stat_inner2","trackip":0,"upbps":0,"dnbps":0,"upbytes":0,"dnbytes":0}

