1. 配置字段
1.1 策略组字段
字段		释义		字段类型	必填项	示例	备注
name		策略组名称		string	是	policy_group1	唯一标识
调度日期	month	调度日期		int	是	0	0为每周、1~12为对应月份
	startday	开始日期		int	是	0	当month为0时，1~7对应周一到周日；month为1~12时，值为匹配月份。
	endday	结束日期		int	是	0	
	start	开始时刻		string	是	0:00:00	时:分:秒24小时计时
	end	结束时刻		string	是	0:00:00	
disable		是否启用		int	是	0/1	0为启用，1位禁用
stop		是否继续匹配		int	是	0/1	匹配后状态，0为继续匹配，1为停止
previous		前一个策略组名称		string	是	null/policy_group1	为"null"时，视为排序在”_subscription_group”后面
未"append"且本地不存在时，视为追加

⚠️策略组以名称作为唯一标识，agent通过名称反查ID。
⚠️全量配置下发时，按照先后匹配顺序下发。
⚠️策略组在orch上用来匹配具体的stite/site-tag，选择是否下发给具体site，策略组中不包含具体inip&outip策略信息，策略组中具体策略才进行下发。

默认值：
floweye policygroup2 add name=flow_control_policy_group1 inip= outip= ifname=NULL ifbps=0 month=0 startday=1 endday=7 start=00:00:00 end=23:59:59 disable=0 stop=0


1.2 策略字段
字段			释义		字段类型	必填项	示例	备注
cookie			策略cookie		int	是	1	唯一标识; uint32
desc			策略描述		string	是	policy_1	
所属策略组	groupname		策略组名称		string	是	policy_group1	策略组唯一标识
disable			是否启用		int	是	0/1	0为启用，1位禁用
previous			前一个策略cookie		int	是	0	首个策略为0
追加策略为-1
用户/访问者	inip		内网ip	IP	IpAddr	否	***************/32	xxx.xxx.xxx.xxx/nn
inip=***************/32
				IP段	IpRange	否	*************-***************	n.n.n.n-m.m.m.m
inip=*************-***************
				IP群组	string	否	ip-oversea	inip=6
IP群组以名称作为唯一标识，agent通过名称反查ID
				Mac群组	int	否	3	inip=mac.3
				用户组	int	否	3	inip=pppoe.3
				用户	string	否	zhangsan	inip=acct.Leo
	inport		内网端口		mix	否	80,443,100-120	inport=80,443,100-120
服务者/服务	outip		外网ip	同内网ip	mix	否	当指定多个时，使用','连接,例如：
outip=***************/32,*************-***************,6,mac.3,pppoe.3,acct.Leo outip=,***************/32	
	outport		外网端口		int	否	80,443,100-120	outport=80,443,100-120
	proto		协议		string	否	app=any
app=any.tcp
app=httpgroup.tcp	协议（“特征库协议英文名.协议”，“协议”可设置tcp、udp、ipv4、tcp/ipv4、udp/ipv4、ipv6、tcp/ipv6、udp/ipv6、ppp
	app		特征库协议		string	否		
接口	bridge		线路名称		string	否	线路（设置线路群组时_wg.线路群组名称。例如“bridge=_wg.WANGroup1”）	
	dir		流向		int	否	0: both
1: in
2: out	dir=both
	ifname		首包接口		string	否	ifname=eth0/iwan-svc-test1
ifname=eth0/wan	"网卡名/线路名称”any为任意
	inif		源接口		string	否	inif=eth0/wan	"网卡名/线路名称”any为任意
	vlan		vlan id		intRange	否	0-100	0代表任意，1-4094，格式：1 或者1-100
执行动作	action		执行策略	permit
deny
流量通道ID	int	是	1: permit
2: deny
3: 通道限速	参数设置“permit”允许、“deny”阻断、“数据通道ID”走数据通道floweyepolicylistbwo查看数据通道、“route-线路ID”包转发到对应线路、“iwanmir-Iwan客户端线路ID/vlan”，Iwan镜像/镜像后vlan、“dup-网卡名”端口镜像、“portfwd-网卡名/vlan”端口转发
	action=放行	next	匹配后状态		int	是	0/1	匹配后状态，0为继续匹配，1为停止匹配
		iprate	内网ip限速		int	否	0	单位为kbits/s，0为不限速
		tos	修改dscp		int	否		修改DSCP（值为1~63）
		soid	流量统计名称		string	否		流量统计（floweye ntmso list策略流量统计对象ID）
	action=通道限速	next	匹配后状态		int	是	0/1	匹配后状态，0为继续匹配，1为停止匹配
		channel	流量通道名称		string	是	action=channel_2	
		pri	通道优先级		int	是		
		iprate	内网ip限速		int	否		单位为kbits/s，0为不限速
		soid	流量统计名称		string	否		流量统计（floweye ntmso list策略流量统计对象ID）

默认值：
floweye newpolicy add id=555 group=1 bridge=any dir=both macgid=0 inport=0 outport=0 inip= outip= app=any action=permit iprate=0 next=0 inif=any pktno= pri=0 natip=0 appnot=0 hasms=0 qqcnt=0 vlan= ttl=0-255 soid=0 disable=0 ifname=any/any desc= tos=0

2. 查询
2.1 策略组查询
2.1.1 查询所有策略组
floweye policygroup2 list
策略组ID；策略组名称；策略条数；当前是否生效0为不生效1为生效；是否禁用0为启用1为禁用；是否继续匹配0继续版匹配1停止匹配；策略前速率；策略后速率；内网IP；外网IP；调度日期；开始日期；结束日期；开始时刻；结束时刻
[root@Panabit:~]# floweye policygroup2 list
2 test_1 3 1 0 0 0 0 any any 0 1 7 0 0 0 23 59 59
1 test 3 1 0 0 0 0 any any 0 1 7 0 0 0 23 59 59
3 policy1 0 0 0 0 0 0 ip,32,***************;rng,************,**************;tbl,3,ipgroup-test;pool,1,DefaultGroup;acct,0,Printer;acct,0,Leo;mac,1,DefaultGroup tbl,3,ipgroup-test 0 6 7 0 0 0 23 59 59
4 policy2 0 0 0 0 0 0 any ip,32,*************** 0 6 7 0 0 0 23 59 59
5 policy-test 0 0 0 0 0 0 ip,32,***************;rng,*************,***************;tbl,6,orch11233;mac,3,test1234;pool,3,test1234;acct,0,Leo ip,32,*************** 0 6 7 0 0 0 23 59 59
6 policy-test1 0 0 0 0 0 0 any ip,32,*************** 0 6 7 0 0 0 23 59 59
7 policy-test2 0 0 0 0 0 0 any ip,32,*************** 0 6 7 0 0 0 23 59 59
8 policy-test5 0 0 0 0 0 0 ip,32,***************;rng,*************,***************;tbl,6,orch11233;mac,3,test1234;pool,3,test1234;acct,0,Leo ip,32,*************** 0 6 7 0 0 0 23 59 59
2.1.2 查询指定策略组
floweye policygroup2 get <id=>
[root@Panabit:~]# floweye policygroup2 list
2 test_1 3 1 0 0 0 0 any any 0 1 7 0 0 0 23 59 59
1 test 3 1 0 0 0 0 any any 0 1 7 0 0 0 23 59 59
3 policy1 0 0 0 0 0 0 ip,32,***************;rng,************,**************;tbl,3,ipgroup-test;pool,1,DefaultGroup;acct,0,Printer;acct,0,Leo;mac,1,DefaultGroup tbl,3,ipgroup-test 0 6 7 0 0 0 23 59 59
4 policy2 0 0 0 0 0 0 any ip,32,*************** 0 6 7 0 0 0 23 59 59
5 policy-test 0 0 0 0 0 0 ip,32,***************;rng,*************,***************;tbl,6,orch11233;mac,3,test1234;pool,3,test1234;acct,0,Leo ip,32,*************** 0 6 7 0 0 0 23 59 59
6 policy-test1 0 0 0 0 0 0 any ip,32,*************** 0 6 7 0 0 0 23 59 59
7 policy-test2 0 0 0 0 0 0 any ip,32,*************** 0 6 7 0 0 0 23 59 59
8 policy-test5 0 0 0 0 0 0 ip,32,***************;rng,*************,***************;tbl,6,orch11233;mac,3,test1234;pool,3,test1234;acct,0,Leo ip,32,*************** 0 6 7 0 0 0 23 59 59

[root@Panabit:~]# floweye policygroup2 get id=5
id=5
name=policy-test
inip=ip,32,***************;rng,*************,***************;tbl,6,orch11233;mac,3,test1234;pool,3,test1234;acct,0,Leo
outip=ip,32,***************
month=0
startday=6
endday=7
starthour=0
startmin=0
startsec=0
endhour=23
endmin=59
endsec=59
active=0
disable=0
stop=0
ifname=NULL
ifbps=0
ifcurbps=0

2.2 策略查询
2.2.1 查询策略组所有策略
floweye newpolicy list <group=group_id> json=1
[root@Panabit:~/agent]# floweye newpolicy list group=4 json=1
{"id":1,"link":"any","fistif":"any","fistpxy":"NULL","inif":"any","inpxy":"NULL","dir":"both","appnot":0,"appname":"any","appcname":"",proto":"any","inip":"","inport":"any","outip":"","outport":"any","action":"permit","pktno":0,"stop":1,"iprate":"0","bps":8664,"bps2":8664,"tos":0,"pri":0,"pridesc":"","natip":0,"bridge":0,"disable":0,"mscnt":0,"qqcnt":0,"vlan":"0","ttl":"0-255","macgid":0,"macgname":"NULL","soid":0,"soname":"NULL","desc":"policy-group3-p1"},{"id":100,"link":"wan","fistif":"eth0","fistpxy":"wan","inif":"eth0","inpxy":"wan","dir":"both","appnot":0,"appname":"any","appcname":"",proto":"tcp","inip":"ip,24,*******;rng,***********,*************;mac,2064,TempAccounts;pool,1,DefaultGroup;acct,0,randy","inport":"any","outip":"","outport":"80,443","action":"permit","pktno":0,"stop":1,"iprate":"0","bps":0,"bps2":0,"tos":0,"pri":0,"pridesc":"","natip":0,"bridge":5,"disable":0,"mscnt":0,"qqcnt":0,"vlan":"12","ttl":"0-255","macgid":0,"macgname":"NULL","soid":0,"soname":"NULL","desc":"policy-group3-p100"}
2.2.2 查询策略组中指定策略
根据ID查询：
floweye newpolicy get <group=group_id> <id=policy_id>
[root@Panabit:~]# floweye newpolicy get group=3 id=1   
group=3
polno=1
cookie=100
ifname=eth0
firstpxy=wan
inif=eth0
inpxy=wan
bridge=wan
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00;acct,0,randy
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
根据cookie查询：
[root@Panabit:~]# floweye newpolicy get group=3 cookie=100
group=3
polno=1
cookie=100
ifname=eth0
firstpxy=wan
inif=eth0
inpxy=wan
bridge=wan
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00;acct,0,randy
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
3. 配置
3.1 策略组配置
3.1.1 新增策略组
floweye policygroup2 add
floweye policygroup2 add name= inip= outip= month= startday= endday= start= end= ifname= ifbps= disable= stop=
[root@Panabit:/var/log]# floweye policygroup2 list
2 policy-group1 2 1 0 0 7757 7757 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 7757 7757 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 2 1 0 0 7757 7757 any any 0 1 7 0 0 0 23 59 59
[root@Panabit:/var/log]# floweye policygroup2 add name=policy-group4 inip= outip= ifname=NULL ifbps=0 month=0 startday=1 endday=7 start=08:00:00 end=23:59:59 disable=0 stop=0
[root@Panabit:/var/log]# floweye policygroup2 list
2 policy-group1 2 1 0 0 22106 22106 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 22106 22106 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 2 1 0 0 22106 22106 any any 0 1 7 0 0 0 23 59 59
3 policy-group4 0 1 0 0 0 0 any any 0 1 7 8 0 0 23 59 59
[root@Panabit:/var/log]# floweye policygroup2 get id=3
id=3
name=policy-group4
inip=
outip=
month=0
startday=1
endday=7
starthour=8
startmin=0
startsec=0
endhour=23
endmin=59
endsec=59
active=1
disable=0
stop=0
ifname=NULL
ifbps=0
ifcurbps=0
3.1.2 修改策略组
floweye policygroup2 set <id=>
floweye policygroup2 set id= name= inip= outip= month= startday= endday= start= end= ifname= ifbps= disable= stop=（moveto=）
[root@Panabit:/var/log]# floweye policygroup2 get id=3
id=3
name=policy-group4
inip=
outip=
month=0
startday=1
endday=7
starthour=8
startmin=0
startsec=0
endhour=23
endmin=59
endsec=59
active=1
disable=0
stop=0
ifname=NULL
ifbps=0
ifcurbps=0
[root@Panabit:/var/log]#floweye policygroup2 set id=3 newname=policy-group4 name=policy-group4 inip= outip= ifname=NULL ifbps=0 month=1 startday=5 endday=10 start=8:0:0 end=23:59:59 disable=0 stop=0
[root@Panabit:/var/log]# floweye policygroup2 get id=3
id=3
name=policy-group4
inip=
outip=
month=1
startday=5
endday=10
starthour=8
startmin=0
startsec=0
endhour=23
endmin=59
endsec=59
active=0
disable=0
stop=0
ifname=NULL
ifbps=0
ifcurbps=0

3.1.3 删除策略组
floweye policygroup2 remove <id=>
[root@Panabit:/var/log]# floweye policygroup2 list    
2 policy-group1 2 1 0 0 6347 6347 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 6347 6347 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 2 1 0 0 6347 6347 any any 0 1 7 0 0 0 23 59 59
3 policy-group4 0 0 0 0 0 0 any any 1 5 10 8 0 0 23 59 59
[root@Panabit:/var/log]# floweye policygroup2 remove id=3
[root@Panabit:/var/log]# floweye policygroup2 list       
2 policy-group1 2 1 0 0 9254 9254 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 9254 9254 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 2 1 0 0 9254 9254 any any 0 1 7 0 0 0 23 59 59

3.1.4 启用/禁用策略组
floweye policygroup2 set <id=策略组Id> <disable=0 启用 1禁用>
[root@Panabit:/var/log]# floweye policygroup2 get id=1
id=1
name=policy-group2
inip=
outip=
month=0
startday=1
endday=7
starthour=0
startmin=0
startsec=0
endhour=23
endmin=59
endsec=59
active=1
disable=0
stop=0
ifname=NULL
ifbps=0
ifcurbps=0
[root@Panabit:/var/log]# floweye policygroup2 set id=1 disable=1 
[root@Panabit:/var/log]# floweye policygroup2 get id=1          
id=1
name=policy-group2
inip=
outip=
month=0
startday=1
endday=7
starthour=0
startmin=0
startsec=0
endhour=23
endmin=59
endsec=59
active=0
disable=1
stop=0
ifname=NULL
ifbps=0
ifcurbps=0
[root@Panabit:/var/log]# floweye policygroup2 list              
2 policy-group1 2 1 0 0 8930 8930 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 0 1 0 0 0 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 2 1 0 0 8930 8930 any any 0 1 7 0 0 0 23 59 59

3.1.5 调整策略组顺序
floweye policygroup2 set id=1 moveto=2
floweye的命令是将id=x的策略组插入到movetoid=x 的位置；挪到最后一个为 -1
2 policy-group1 2 1 0 0 27918 27918 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 1 1 0 0 27918 27918 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 27918 27918 any any 0 1 7 0 0 0 23 59 59

floweye policygroup2 set id=4 moveto=2

4 policy-group3 1 1 0 0 10730 10730 any any 0 1 7 0 0 0 23 59 59
2 policy-group1 2 1 0 0 10730 10730 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 10730 10730 any any 0 1 7 0 0 0 23 59 59


floweye policygroup2 set id=1 moveto=4

1 policy-group2 1 1 0 0 10190 10190 any any 0 1 7 0 0 0 23 59 59
4 policy-group3 1 1 0 0 10190 10190 any any 0 1 7 0 0 0 23 59 59
2 policy-group1 2 1 0 0 10190 10190 any any 0 1 7 0 0 0 23 59 59

floweye policygroup2 set id=1 moveto=-1

4 policy-group3 1 1 0 0 3107 3107 any any 0 1 7 0 0 0 23 59 59
2 policy-group1 2 1 0 0 3107 3107 any any 0 1 7 0 0 0 23 59 59
1 policy-group2 1 1 0 0 3107 3107 any any 0 1 7 0 0 0 23 59 59

3.2 策略配置
3.2.1 新增策略
floweye newpolicy add <id=>
floweye newpolicy add id= group= bridge= dir= macgid= inport= outport= inip= outip= app= action= iprate= next= ifname= inif= pktno= pri= natip= hasms= qqcnt= vlan= ttl= soid= disable= desc= tos=  cookie=

inip：内网地址（支持IP、范围、网段、IP群组、用户组、账号。每个条件用英文逗号“,”前后区分。例如：条件为***********、***********/24、IP群组ID1、用户组ID2、用户账号test1，对应参数为“inip=,***********,,***********/24,,1,,pppoe.2,,acct.test1,”）
[root@Panabit:~]# floweye newpolicy add id=1 group=3 bridge=wan dir=both macgid=0 inport=80 outport=80,443 inip=,*******/32,,***********-*************,,pppoe.2064,,acct.randy,,mac.2064, outip=,***************/32,,***********-***********00,,acct.randy, app=any.tcp action=permit iprate=10000 next=0 ifname=eth0/wan inif=eth0/wan pktno= pri=0 natip=0 appnot=0 hasms=0 qqcnt=0 vlan=11 ttl=0-255 soid=0 disable=0 desc=policy-group4-p1 tos=0 cookie=100
[root@Panabit:~]# floweye newpolicy get group=3 cookie=100
group=3
polno=1
cookie=100
ifname=eth0
firstpxy=wan
inif=eth0
inpxy=wan
bridge=wan
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00;acct,0,randy
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
3.2.2 修改策略
floweye newpolicy set <group=> <id=> 
floweye newpolicy set group= id= newid=（bridge= dir= macgid= inport= outport= inip= outip= app= action= iprate= next= ifname= inif= pktno= pri= natip= hasms= qqcnt= vlan= ttl= soid= disable= desc= tos=）
[root@Panabit:~]# floweye newpolicy get group=3 id=1
group=3
polno=1
cookie=100
ifname=eth0
firstpxy=wan
inif=eth0
inpxy=wan
bridge=wan
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00;acct,0,randy
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
[root@Panabit:~]# floweye newpolicy set id=1 newid=1 group=3 bridge=any dir=both macgid=0 inport=80 outport=80,443 inip=,*******/32,***********-*************,pppoe.2064,acct.randy,mac.2064 outip=,***************/32,***********-***********00 app=any.tcp action=permit iprate=10000 next=0 ifname=eth1/iwan-svc-test1 inif=eth1/iwan-svc-test1 pktno=0 pri=0 natip=0 appnot=0 hasms=0 qqcnt=0 vlan=11 ttl=0-255 soid=0 disable=0 desc=policy-group4-p1 tos=0 cookie=100
[root@Panabit:~]# floweye newpolicy get group=3 id=1      
group=3
polno=1
cookie=100
ifname=eth1
firstpxy=iwan-svc-test1
inif=eth1
inpxy=iwan-svc-test1
bridge=any
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
3.2.3 启用/禁用 策略
禁用：floweye newpolicy config group= id= disable=1
启用：floweye newpolicy config group= id= enable=1
[root@Panabit:~/agent]# floweye newpolicy get group=3 id=1
group=3
polno=1
cookie=0
ifname=eth1
firstpxy=iwan-svc-test1
inif=eth1
inpxy=iwan-svc-test1
bridge=any
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
[root@Panabit:~/agent]# ^C
[root@Panabit:~/agent]# floweye newpolicy config group=3 id=1 disable=1
[root@Panabit:~/agent]# floweye newpolicy get group=3 id=1             
group=3
polno=1
cookie=0
ifname=eth1
firstpxy=iwan-svc-test1
inif=eth1
inpxy=iwan-svc-test1
bridge=any
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=1
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
[root@Panabit:~/agent]# floweye newpolicy config group=3 id=1 enable=1             
[root@Panabit:~/agent]# floweye newpolicy get group=3 id=1            
group=3
polno=1
cookie=0
ifname=eth1
firstpxy=iwan-svc-test1
inif=eth1
inpxy=iwan-svc-test1
bridge=any
dir=both
appnot=0
appid=any
appname=Эproto=tcp
inip=ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts
inport=80
outip=ip,32,***************;rng,***********,***********00
outport=80,443
action=permit
matchact=stop
iprate=10000
prclevel=0
tos=0
priority=0
natip=0
disable=0
hasms=0
qqcnt=0
vlan=11
ttl=0-255
inbps=0
outbps=0
desc=policy-group4-p1
macgid=0
macpool=NULL
pktno=0
soid=0
soname=NULL
3.2.4 删除策略
floweye newpolicy remove <group=> <id=>
[root@Panabit:~/agent]# floweye newpolicy list group=3 json=1
{"id":1,"link":"any","fistif":"eth1","fistpxy":"iwan-svc-test1","inif":"eth1","inpxy":"iwan-svc-test1","dir":"both","appnot":0,"appname":"any","appcname":"Э"proto":"tcp","inip":"ip,32,*******;rng,***********,*************;pool,2064,TempAccounts;acct,0,randy;mac,2064,TempAccounts","inport":"80","outip":"ip,32,***************;rng,***********,***********00","outport":"80,443","action":"permit","pktno":0,"stop":1,"iprate":"10000","bps":0,"bps2":0,"tos":0,"pri":0,"pridesc":"","natip":0,"bridge":0,"disable":0,"mscnt":0,"qqcnt":0,"vlan":"11","ttl":"0-255","macgid":0,"macgname":"NULL","soid":0,"soname":"NULL","desc":"policy-group4-p1"}[root@Panabit:~/agent]# 
[root@Panabit:~/agent]# floweye newpolicy remove group=3 id=1
[root@Panabit:~/agent]# floweye newpolicy list group=3 json=1
[root@Panabit:~/agent]# 

4. 配置处理逻辑
流量控制策略较其它模块稍复杂，涉及到以下几方面的处理
4.1 引用依赖对象标识映射
策略的配置中会引用其它模块的对象，例如IP群组，用户组，用户等等，而这些被依赖的对象在本地是以ID为唯一标识(本地生成)进行管理的，而Orch是以对象名称为唯一标识进行管理的，Agent需要做标识的转换；目前涉及到转换的被引用对象为：
1. 策略组：策略组Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行策略引用和配置。
2. IP群组：IP群组Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行策略引用和配置。
3. 流量统计对象：流量统计Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行策略引用和配置。

4.2 策略映射
PA本地的策略/策略组 ID不仅为策略的唯一标识，还标识着本地策略/策略组 匹配的先后顺序。

4.2.1 唯一标识转换
由于Orch上管理的策略(流量控制、路由、DNS管控)涉及到跨Site间的管理，难以以单个Site的策略/策略组ID 为唯一标识进行管理，所以：
1. 策略组 以策略组名称为唯一标识进行管理，Agent 负责将策略组名称 ↔ 本地策略组 ID 映射。
2. 策略 通过 cookie 实现唯一标识，Agent 负责将 cookie ↔ 本地策略 ID 映射。

4.2.2 本地默认策略组

1. 新增两个默认的 flow control policy group，默认调度日期为每周1到周7 00:00:00-23:59:59，默认启用，默认继续匹配，其策略组名称 分别为：
  1. _traffic_mirroring_group，为第一个默认策略组，用于做端口镜像
  2. _subscription_group，为第二个默认策略组，用于订阅限速
2. 以上两个策略组，在flow control policy group全量配置开始时，判断是否存在，如果不存在，则本地新建
3. 当下发的策略组的previous策略组名称为字符串”null”时，视为排序在”_subscription_group”后面
4. 当下发的策略组的previous策略组名称为字符串”append”时且本地不存在时，视为排序在当前的最后，新建即可，新建的pa本身处理就是排在最后；如果本地存在，则不修改其顺序

4.2.3 排序转换
Orch 并不直接管理本地 ID 顺序。为了控制策略组或策略的匹配顺序，Orch 通过下发 previous 字段（即 前一个策略组名称/策略 cookie）来表达插入位置，Agent 需根据此字段调整顺序。
规则：
流量控制-策略组
1. 当下发的策略组的previous策略组名称为字符串”null”时，视为排序在”_subscription_group”后面
2. 当下发的策略组的previous策略组名称为字符串”append”且本地不存在时，视为追加

其它含cookie策略
1. 当下发的策略的previous cookie为0时，视为排在首个(路由/NAT策略为zone首个)
2. 当下发的策略的previous cookie 为 -1且本地不存在时，视为追加

基于以上3点，策略组 新增 修改 删除 调整顺序的逻辑为

1.  新增/修改 策略组
  1. ID 分配
    1. 根据本地配置查询“策略组名称”是否已存在相应策略组：
      1. 存在：取现有 ID。
      2. 不存在：floweye add 命令会自动分配新 ID。
  2. 修改/新增配置
    1. 策略组已存在：更新已有策略组的配置。
    2. 策略组不存在：使用 floweye add 命令新增策略组（不指定 ID 参数）。
  3. 调整策略组顺序
    1. 使用 floweye moveto 命令进行位置调整，根据 previous 字段确定目标位置：
      1. 若 previous 为 null（移动到第一位置）：
        1. 找到当前第一个策略组的 ID。执行：floweye policygroup2 set id=<当前策略组ID> moveto=<第一个策略组ID>
      2. 若 previous 指定策略组名称（移动到该策略组之后）：
        1. 找到 previous 策略组的 ID。
        2. 找到其后的下一个策略组 ID。执行：floweye policygroup2 set id=<当前策略组ID> moveto=<下一个策略组ID>
      3. 若 previous 是最后一个。执行floweye policygroup2 set id=<当前策略组ID> moveto=-1
      4. 若 previous 不存在（移动到最后）：floweye policygroup2 set id=<当前策略组ID> moveto=-1
2. 删除策略组
  1. 获取策略组 ID，根据本地配置查询“策略组名称”：
    1. 存在：获取对应 ID。
    2. 不存在：结束删除流程。
  2. 删除对应 ID 的策略组。
    1. floweye 会自动维护 ID 的连续性，无需手动调整其他策略组 ID。

3. 新增/修改 策略
  1. 确认策略组存在
    1. 根据策略组名称查找本地策略组 ID：
    2. 存在：获取其 ID。
    3. 不存在：终止流程（异常配置）。
  2. ID 分配
    1. 根据 cookie 在指定策略组中查找策略：
      1. 存在：取现有 ID。
      2. 不存在：使用该组中 max(ID)+1 作为新增策略 ID。
    2. 修改/新增配置
      1. 存在：更新策略配置。
      2. 不存在：使用 floweye add 命令新增策略（指定 ID）。
  3. 调整策略顺序
    1. 使用复杂的 ID 调整逻辑，根据 previous 字段确定目标位置：
      1. 若 previous 为 null 或 0（移动到第一位置）：
        1. 目标位置为 1。
        2. 使用临时 ID 和区间调整逻辑移动。
      2. 若 previous 指定 cookie（移动到该策略之后）：
        1. 找到 previous 策略的 ID。
        2. 目标位置为 previous ID + 1。
          1. 操作步骤：
            1. 将当前策略设置为临时 ID（65536）。
            2. 根据移动方向调整区间内其他策略的 ID。
            3. 设置当前策略为目标 ID。
      3. 若 previous 策略不存在或为append（移动到最后）：
        1. 找到该组最大策略 ID。
        2. 目标位置为 max ID + 1。
        3. 使用临时 ID 和区间调整逻辑移动。
4.  删除策略
  1. 确认策略组存在：
    1. 存在：获取策略组 ID。
    2. 不存在：视为删除成功。
  2. 获取策略 ID（根据 cookie 查找）。
    1. 删除该策略。
    2. 手动调整该组中其他策略的 ID，维护连续性：
      1. 找出所有 ID 大于被删除策略的策略。
      2. 将这些策略的 ID 全部减 1。
      3. 使用 floweye newpolicy set 的 newid 参数逐个调整。
