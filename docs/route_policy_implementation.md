# Route Policy Module Implementation

## Overview

The route policy module provides comprehensive management of routing policies in the UniSASE Agent system. It implements the `TaskProcessor` interface to handle `TASK_ROUTE_POLICY` type tasks and integrates with the floweye command-line interface for policy configuration.

## Architecture

### Core Components

1. **RoutePolicyProcessor** - Main processor implementing the TaskProcessor interface
2. **RoutePolicyConfig** - Configuration data structure and parsing functions
3. **Protocol Buffer Definitions** - Message definitions in `docs/message.proto`

### File Structure

```
internal/client/task/
├── route_policy_processor.go      # Main processor implementation
├── route_policy_config.go         # Configuration parsing and comparison
└── route_policy_processor_test.go # Unit tests

test/
├── route_policy_integration_test.go # Integration tests
└── run_module_tests.sh             # Updated with route policy tests

docs/
├── message.proto                   # Updated with route policy messages
├── floweye_route_policy.md        # Floweye command documentation
└── route_policy_implementation.md # This document
```

## Protocol Buffer Definitions

### New Message Types

- `RoutePolicyTask` - Main task configuration message
- `RouteActionConfig` - Route action configuration
- `NatActionConfig` - NAT action configuration
- `IntRange` - Integer range specification
- `NatIpPool` - NAT IP address pool

### New Enumerations

- `RouteAction` - Policy action types (route/nat/dnat/proxy)
- `UserType` - User type specification (any/ippxy/nonippxy)

## Floweye Command Integration

### Supported Commands

1. **List Policies**: `floweye route list json=1`
2. **Get Policy Details**: `floweye rtpolicy get id=<id>` or `floweye rtpolicy get cookie=<cookie>`
3. **Add Policy**: `floweye route add <parameters>`
4. **Update Policy**: `floweye route set <parameters>`
5. **Remove Policy**: `floweye route remove id=<id>`

### Command Parameters

The module supports all floweye route policy parameters including:

- Basic configuration: cookie, desc, disable, schtime, previous
- Source criteria: src, sport, usrtype, pool
- Destination criteria: dst, dport, proto, appname
- Interface and QoS: inif, wanbw, wanbwout, vlan, ttl, dscp
- Action configuration: action, proxy, nexthop, newdstip, natip, fullconenat, nosnat

## Key Features

### 1. Full Synchronization Support

- **StartFullSync()**: Initializes full sync mode and refreshes local cache
- **EndFullSync()**: Cleans up remaining policies not in sync set
- Maintains `fullSyncInProgress` flag for proper sync behavior

### 2. Configuration Comparison

- Compares task configurations with local system state
- Supports incremental updates when configurations differ
- Implements idempotent operations

### 3. Policy Ordering

- Supports policy ordering through `previous` field
- Handles complex ordering scenarios with cookie-based references
- Maintains policy sequence integrity

### 4. Dependency Resolution

- Resolves IP group names to IDs (placeholder implementation)
- Handles address selectors for various object types
- Supports complex address specifications

### 5. Error Handling

- Comprehensive error handling for floweye command failures
- Graceful degradation in test environments
- Detailed logging for troubleshooting

### 6. Performance Optimization

- Single-object retrieval for verification operations
- Efficient local configuration caching
- Minimal floweye command execution

## Implementation Details

### Configuration Parsing

The module implements two parsing functions:

1. **ParseRoutePolicyFromList()** - Parses JSON output from `floweye route list json=1`
2. **ParseRoutePolicyFromGet()** - Parses key=value output from `floweye rtpolicy get`

### Address String Building

Supports multiple address selector types:
- IP addresses and ranges
- IP groups (with name-to-ID resolution)
- MAC groups
- User groups
- Individual users
- Domain groups

### Action Configuration

Handles different action types:
- **Route**: Requires proxy and optional nexthop
- **NAT**: Requires proxy and optional NAT IP pool
- **DNAT**: Requires proxy, newdstip, and optional NAT configuration
- **Proxy**: Requires proxy line specification

## Testing

### Unit Tests

Located in `route_policy_processor_test.go`:
- Processor initialization
- Configuration parsing
- Comparison logic
- Error handling
- Helper function validation

### Integration Tests

Located in `test/route_policy_integration_test.go`:
- End-to-end workflow testing
- Full synchronization testing
- Performance benchmarking
- Error scenario validation

### Module Tests

Integrated into `test/run_module_tests.sh`:
- Create, update, delete operations
- Policy ordering scenarios
- Full synchronization workflows
- Error handling validation

## Usage Examples

### Creating a Route Policy

```go
routePolicyTask := &pb.RoutePolicyTask{
    Cookie:  12345,
    Desc:    "Test Route Policy",
    Disable: false,
    Action:  pb.RouteAction_ROUTE_ACTION_ROUTE,
    RouteConfig: &pb.RouteActionConfig{
        Proxy: "wan1",
        NextHop: &pb.IpAddress{Ip: "***********"},
    },
}

deviceTask := &pb.DeviceTask{
    TaskType:        pb.TaskType_TASK_ROUTE_POLICY,
    TaskAction:      pb.TaskAction_NEW_CONFIG,
    RoutePolicyTask: routePolicyTask,
}

result, err := processor.ProcessTask(ctx, deviceTask)
```

### Full Synchronization

```go
// Start full sync
err := processor.StartFullSync()
if err != nil {
    return err
}

// Process sync tasks...
for _, task := range syncTasks {
    _, err := processor.ProcessTask(ctx, task)
    if err != nil {
        log.Error("Failed to process sync task", zap.Error(err))
    }
}

// End full sync (cleans up remaining policies)
processor.EndFullSync()
```

## Configuration Validation

The module performs comprehensive validation:

1. **Required Fields**: cookie, action, proxy (desc is optional)
2. **Field Constraints**: Valid action types, proper address formats
3. **Dependency Checks**: Referenced objects exist
4. **Ordering Validation**: Previous policy references are valid

## Error Scenarios

Common error scenarios and handling:

1. **Missing Required Fields**: Returns descriptive error messages
2. **Invalid Floweye Commands**: Logs command output and returns error
3. **Dependency Resolution Failures**: Warns and continues with fallback
4. **Ordering Conflicts**: Attempts resolution or reports conflict

## Performance Considerations

1. **Local Caching**: Maintains local configuration cache to minimize floweye calls
2. **Single-Object Retrieval**: Uses specific queries instead of full refreshes
3. **Batch Operations**: Groups related operations when possible
4. **Lazy Loading**: Loads detailed configurations only when needed

## Future Enhancements

1. **IP Group Integration**: Complete implementation of IP group name resolution
2. **Advanced Ordering**: More sophisticated policy ordering algorithms
3. **Validation Enhancement**: More comprehensive configuration validation
4. **Performance Optimization**: Further reduce floweye command overhead
5. **Monitoring Integration**: Add metrics and monitoring capabilities

## Dependencies

- **floweye**: Command-line interface for policy management
- **Protocol Buffers**: Message serialization
- **Zap Logger**: Structured logging
- **Utils Package**: Command execution utilities

## Compliance

The implementation follows:
- Project coding standards defined in `.airules`
- Architecture patterns from existing modules
- Error handling conventions
- Testing requirements
- Documentation standards
