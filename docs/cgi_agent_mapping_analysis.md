# CGI 脚本与 Agent 模块映射分析文档

## 概述

本文档分析了 `pa_ui_a_script` 目录下的 CGI 脚本与 UniSASE Agent 项目中各个模块的映射关系，以及配置参数的对应关系。这些 CGI 脚本展示了从 UI 界面配置后，通过 CGI 接口向 floweye 下发配置到 PA 系统的标准方法。

## 映射关系总览

### 1. 网络模块 (network)

| CGI 脚本文件 | Agent 模块 | 功能描述 | floweye 命令 |
|-------------|-----------|----------|-------------|
| `ajax_route_policy` | `route_policy_processor.go` | 路由策略管理 | `floweye route` |
| `ajax_dnspolicy` | `dns_policy_processor.go` | DNS策略管理 | `floweye dnspolicy` |
| `ajax_interface` | `interface_processor.go` | 网络接口配置 | `floweye interface` |
| `ajax_dhcpsvr` | `dhcp_processor.go` | DHCP服务器配置 | `floweye dhcp` |
| `ajax_wan_group` | `wan_group_processor.go` | WAN组管理 | `floweye wangroup` |
| `ajax_proxy` | `iwan_service_processor.go` | iWAN代理服务 | `floweye proxy` |
| `ajax_cgnat` | - | CGNAT配置 | `floweye cgnat` |
| `ajax_static_nat` | - | 静态NAT配置 | `floweye staticnat` |

### 2. 流控模块 (flowcontrol)

| CGI 脚本文件 | Agent 模块 | 功能描述 | floweye 命令 |
|-------------|-----------|----------|-------------|
| `ajax_flowct` | `flow_control_processor.go` | 流控策略管理 | `floweye policy` |
| `ajax_nflowct` | `flow_control_group_processor.go` | 新流控策略组 | `floweye policygroup2` |
| `ajax_plink` | `lan_processor.go` | LAN链路配置 | `floweye plink` |
| `ajax_conlimit` | - | 连接限制 | `floweye conlimit` |
| `ajax_urlfilter` | - | URL过滤 | `floweye urlfilter` |

### 3. 对象管理模块 (object)

| CGI 脚本文件 | Agent 模块 | 功能描述 | floweye 命令 |
|-------------|-----------|----------|-------------|
| `ajax_user_account` | `user_processor.go` | 用户账户管理 | `floweye pppoeacct` |
| `ajax_user_group` | `user_group_processor.go` | 用户组管理 | `floweye pppoeippool` |
| `ajax_iptable` | `ip_group_processor.go` | IP表/IP组管理 | `floweye table` |
| `ajax_urlgrp` | `domain_group_processor.go` | 域名组管理 | `floweye urlgrp` |
| `ajax_rtptime` | `effective_time_processor.go` | 有效时间管理 | `floweye rtptime` |

### 4. 系统模块 (system)

| CGI 脚本文件 | Agent 模块 | 功能描述 | floweye 命令 |
|-------------|-----------|----------|-------------|
| `ajax_sys_config` | - | 系统配置 | 多种系统命令 |
| `ajax_sys_setting` | - | 系统设置 | 多种系统命令 |
| `ajax_pingmonitor` | - | Ping监控 | `floweye pingmonitor` |

## 详细参数映射分析

### 1. 路由策略模块 (Route Policy)

#### CGI 参数与 Protobuf 字段映射

| CGI 参数 | Protobuf 字段 | 类型 | 说明 |
|---------|--------------|------|------|
| `CGI_id` | `id` | int32 | 策略ID |
| `CGI_inif` | `in_interface` | string | 入接口 |
| `CGI_sport` | `src_port` | string | 源端口 |
| `CGI_dport` | `dst_port` | string | 目标端口 |
| `CGI_submit_srcip` | `src_ip` | string | 源IP地址 |
| `CGI_submit_dstip` | `dst_ip` | string | 目标IP地址 |
| `CGI_proto` | `protocol` | string | 协议类型 |
| `CGI_appid` | `app_id` | string | 应用ID |
| `CGI_vlan` | `vlan` | string | VLAN标签 |
| `CGI_dscp` | `dscp` | string | DSCP标记 |
| `CGI_act` | `action` | string | 动作类型 |
| `CGI_disable` | `disable` | bool | 是否禁用 |

#### floweye 命令构建逻辑

```bash
# 添加路由策略
floweye route add id=${id} inif=${inif} sport=${sport} dport=${dport} \
    src=${srcip} dst=${dstip} proto=${proto} app=${appid} vlan=${vlan} \
    dscp=${dscp} action=${action} disable=${disable}

# 修改路由策略  
floweye route set id=${old_id} newid=${id} [其他参数...]

# 删除路由策略
floweye route remove id=${id}

# 启用/禁用策略
floweye route enable id=${id}
floweye route disable id=${id}
```

### 2. 流控策略模块 (Flow Control)

#### CGI 参数与 Protobuf 字段映射

| CGI 参数 | Protobuf 字段 | 类型 | 说明 |
|---------|--------------|------|------|
| `CGI_group` | `group_id` | int32 | 策略组ID |
| `CGI_polno` | `policy_id` | int32 | 策略ID |
| `CGI_bridge` | `bridge` | string | 桥接接口 |
| `CGI_dir` | `direction` | string | 流量方向 |
| `CGI_inip` | `src_ip` | string | 内网IP |
| `CGI_outip` | `dst_ip` | string | 外网IP |
| `CGI_inport` | `src_port` | string | 内网端口 |
| `CGI_outport` | `dst_port` | string | 外网端口 |
| `CGI_pact` | `action` | string | 策略动作 |
| `CGI_iprate` | `rate_limit` | string | 速率限制 |
| `CGI_priority` | `priority` | int32 | 优先级 |

#### floweye 命令构建逻辑

```bash
# 添加流控策略
floweye policy addrule group=${group} id=${id} bridge=${bridge} \
    dir=${dir} inip=${inip} outip=${outip} inport=${inport} \
    outport=${outport} action=${action} iprate=${iprate} \
    pri=${priority} disable=${disable}

# 策略组管理
floweye policy addgrp ${name}
floweye policy rmvgrp ${id}
floweye policygroup2 add name=${name} [其他参数...]
```

### 3. DNS策略模块 (DNS Policy)

#### CGI 参数与 Protobuf 字段映射

| CGI 参数 | Protobuf 字段 | 类型 | 说明 |
|---------|--------------|------|------|
| `CGI_id` | `id` | int32 | 策略ID |
| `CGI_submit_srcip` | `src_ip` | string | 源IP地址 |
| `CGI_submit_dstip` | `dst_ip` | string | 目标IP地址 |
| `CGI_dns` | `dns_domain` | string | DNS域名 |
| `CGI_atype` | `query_type` | string | 查询类型 |
| `CGI_act` | `action` | string | 动作类型 |
| `CGI_actarg` | `action_arg` | string | 动作参数 |
| `CGI_ipqps` | `qps_limit` | string | QPS限制 |

#### floweye 命令构建逻辑

```bash
# 添加DNS策略
floweye dnspolicy add id=${id} inip=${srcip} outip=${dstip} \
    dns=${dns} atype=${atype} action=${action} actarg=${actarg} \
    ipqps=${ipqps} [其他参数...]

# 启用/禁用DNS策略
floweye dnspolicy set id=${id} disable=0
floweye dnspolicy set id=${id} disable=1
```

### 4. 用户账户模块 (User Account)

#### CGI 参数与 Protobuf 字段映射

| CGI 参数 | Protobuf 字段 | 类型 | 说明 |
|---------|--------------|------|------|
| `CGI_poolid` | `pool_id` | int32 | 用户池ID |
| `CGI_name` | `name` | string | 用户名 |
| `CGI_passwd` | `password` | string | 密码 |
| `CGI_start` | `start_time` | string | 开始时间 |
| `CGI_expire` | `expire_time` | string | 过期时间 |
| `CGI_maxonline` | `max_online` | int32 | 最大在线数 |
| `CGI_bindmac` | `bind_mac` | string | 绑定MAC |
| `CGI_bindip` | `bind_ip` | string | 绑定IP |
| `CGI_cname` | `real_name` | string | 真实姓名 |
| `CGI_phone` | `phone` | string | 电话号码 |

#### floweye 命令构建逻辑

```bash
# 添加用户账户
floweye pppoeacct add poolid=${poolid} name=${name} \
    password=${password} start=${start} expire=${expire} \
    maxonline=${maxonline} bindmac=${bindmac} bindip=${bindip} \
    cname=${cname} phone=${phone} [其他参数...]

# 修改用户账户
floweye pppoeacct set name=${name} [其他参数...]

# 删除用户账户
floweye pppoeacct remove name=${name}
```

## 配置验证要点

### 1. 参数类型一致性
- 确保 CGI 参数类型与 Protobuf 字段类型匹配
- 注意字符串编码问题（CGI 使用 %20 替换空格）
- 数值类型的默认值处理

### 2. 命令构建逻辑
- 参数顺序和格式必须与 floweye 命令规范一致
- 可选参数的默认值设置
- 特殊字符的转义处理

### 3. 错误处理机制
- CGI 脚本的错误返回格式
- Agent 模块的错误处理逻辑
- 日志记录的一致性

## 路由策略模块详细验证结果

### 1. 命令构建对比分析

#### CGI 脚本 (ajax_route_policy) 命令构建逻辑：
```bash
# 基础命令结构
cmdargs="add id=${CGI_id} inif=${CGI_inif}"  # 或 "set id=${CGI_old_id} newid=${CGI_id} inif=${CGI_inif}"
cmdargs="${cmdargs} sport=${CGI_sport} dport=${CGI_dport}"
cmdargs="${cmdargs} src=${CGI_submit_srcip} dst=${CGI_submit_dstip}"
cmdargs="${cmdargs} proto=${CGI_proto} app=${CGI_appid} vlan=${CGI_vlan} dscp=${CGI_dscp}"

# 动作配置
if [ "${CGI_act}" = "nat" -o "${CGI_act}" = "cgnat" -o "${CGI_act}" = "dnat" ]; then
    cmdargs="${cmdargs} action=${CGI_act}-${CGI_actpxyname}"
    cmdargs="${cmdargs} nexthop=${CGI_nexthop}"
    if [ "${CGI_act}" = "nat" ]; then
        cmdargs="${cmdargs} fullconenat=${fullconenat}"
    fi
    if [ "${CGI_act}" = "cgnat" ]; then
        cmdargs="${cmdargs} cgnatid=${CGI_cgnatid}"
    fi
else
    cmdargs="${cmdargs} action=route-${CGI_route_pxyname}"
    cmdargs="${cmdargs} nexthop=${CGI_route_nexthop}"
fi

# 其他参数
cmdargs="${cmdargs} usrtype=${CGI_usrtype}"
cmdargs="${cmdargs} desc=${CGI_desc}"
cmdargs="${cmdargs} schtime=${CGI_schtime}"
cmdargs="${cmdargs} pool=${CGI_pool}"
cmdargs="${cmdargs} newdstip=${CGI_newdstip}"
cmdargs="${cmdargs} wanbw=${CGI_wanbw} wanbwout=${CGI_wanbwout} natip=${CGI_natip}"

# 最终执行
${FLOWEYE} route ${cmdargs} disable=${CGI_disable}
```

#### Agent 模块 (buildRoutePolicyArgs) 命令构建逻辑：
```go
// 基础参数
*args = append(*args, "id="+strconv.Itoa(policyID))
*args = append(*args, "cookie="+strconv.FormatUint(uint64(configData.Cookie), 10))
*args = append(*args, "desc="+configData.Desc)
*args = append(*args, "schtime="+strconv.Itoa(int(configData.SchTime)))

// 源地址和端口
if configData.Src == "any" || configData.Src == "" {
    *args = append(*args, "src=")
} else {
    *args = append(*args, "src="+configData.Src)
}
*args = append(*args, "sport="+configData.SrcPort)

// 目标地址和端口
if configData.Dst == "any" || configData.Dst == "" {
    *args = append(*args, "dst=")
} else {
    *args = append(*args, "dst="+configData.Dst)
}
*args = append(*args, "dport="+configData.DstPort)

// 协议和应用
*args = append(*args, "proto="+configData.Proto)

// 接口配置
if configData.InIf == "any" {
    *args = append(*args, "inif=any")
} else {
    *args = append(*args, "inif=if."+configData.InIf)  // 注意：添加了 "if." 前缀
}

// QoS配置
*args = append(*args, "wanbw="+strconv.Itoa(int(configData.WanBw)))
*args = append(*args, "wanbwout="+strconv.Itoa(int(configData.WanBwOut)))
*args = append(*args, "vlan="+configData.VLAN)
*args = append(*args, "ttl="+configData.TTL)
*args = append(*args, "dscp="+configData.DSCP)

// 动作配置
actionStr := configData.Action
if configData.Proxy != "" {
    actionStr = configData.Action + "-" + configData.Proxy
}
*args = append(*args, "action="+actionStr)

// 下一跳配置
if configData.NextHop == "" {
    if configData.Action == "nat" || configData.Action == "dnat" || configData.Action == "cgnat" {
        *args = append(*args, "nexthop=_NULL_")
    } else {
        *args = append(*args, "nexthop=0.0.0.0")
    }
} else {
    *args = append(*args, "nexthop="+configData.NextHop)
}
```

### 2. 发现的关键差异

#### 差异1：接口参数格式
- **CGI脚本**：`inif=${CGI_inif}` (直接使用接口名)
- **Agent模块**：`inif=if.${configData.InIf}` (添加了 "if." 前缀)

**影响**：这可能导致接口配置不正确，需要验证 floweye 是否真的需要 "if." 前缀。

#### 差异2：参数顺序和完整性
- **CGI脚本**：参数顺序为 `sport/dport` → `src/dst` → `proto/app`
- **Agent模块**：参数顺序为 `src/sport` → `dst/dport` → `proto`

**影响**：虽然参数顺序通常不影响功能，但保持一致性更好。

#### 差异3：缺失的参数
Agent模块中缺少以下CGI脚本中的参数：
- `usrtype` (用户类型)
- `pool` (用户池ID)
- `app` (应用ID，与协议分开)
- `ttl` 参数的处理方式不同

#### 差异4：默认值处理
- **CGI脚本**：
  ```bash
  [ "${CGI_dscp}" = "" ] && CGI_dscp="0"
  [ "${CGI_vlan}" = "" ] && CGI_vlan="0-0"
  [ "${CGI_nexthop}" = "" ] && CGI_nexthop="_NULL_"
  ```
- **Agent模块**：使用不同的默认值逻辑

### 3. 需要修复的问题

#### 问题1：接口参数格式需要验证
需要确认 floweye 命令是否真的需要 "if." 前缀，或者这是一个错误。

#### 问题2：缺失的关键参数
Agent模块需要添加以下参数：
- `usrtype` 参数处理
- `pool` 参数处理
- `app` 参数的正确处理（与协议分开）

#### 问题3：TTL参数处理不一致
CGI脚本中TTL有特殊处理逻辑，Agent模块需要对齐。

## DHCP 模块详细验证结果

### 1. 命令构建对比分析

#### CGI 脚本 (ajax_dhcpsvr) 命令构建逻辑：
```bash
# set_dhcpsvr() 函数 (第31-66行)
cmdargs="name=${CGI_name} id=${CGI_id} dhcp_pool=${CGI_dhcp_pool}"
cmdargs="${cmdargs} dns0=${CGI_dns0} dns1=${CGI_dns1}"
cmdargs="${cmdargs} leasettl=${CGI_leasettl} dhcp_enable=${enable}"

# 条件性添加参数
if [ "${CGI_dhcp_gateway}" != "" -a "${CGI_dhcp_gateway}" != "0.0.0.0" ]; then
    cmdargs="${cmdargs} dhcp_gateway=${CGI_dhcp_gateway}"
fi

if [ "${CGI_dhcp_vlan}" != "" ]; then
    cmdargs="${cmdargs} dhcp_vlan=${CGI_dhcp_vlan}"
else
    cmdargs="${cmdargs} dhcp_vlan=0"
fi

dhcpmask="${CGI_dhcp_mask}"
[ "${dhcpmask}" = "" ] && dhcpmask="0.0.0.0"
cmdargs="${cmdargs} dhcp_mask=${dhcpmask}"

[ "${CGI_dhcp_acaddr}" = "" ] && CGI_dhcp_acaddr="0.0.0.0"
cmdargs="${cmdargs} dhcp_acaddr=${CGI_dhcp_acaddr} dhcp_domain=${CGI_dhcp_domain}"

# 最终执行
${FLOWEYE} nat setrtif ${cmdargs}
```

#### Agent 模块 (buildDhcpCommand) 命令构建逻辑：
```go
// dhcp_processor.go buildDhcpCommand() 函数
cmdArgs := []string{
    "nat", "setrtif",
    "name=" + dhcpConfig.Name,
    "dhcp_pool=" + dhcpConfig.DhcpPool,
    "dns0=" + dhcpConfig.Dns0,
    "dns1=" + dhcpConfig.Dns1,
    "leasettl=" + strconv.Itoa(dhcpConfig.LeaseTtl),
    "dhcp_enable=" + strconv.Itoa(dhcpConfig.DhcpEnable),
}

// 条件性添加 dhcp_gateway
if dhcpConfig.DhcpGateway != "" && dhcpConfig.DhcpGateway != "0.0.0.0" {
    cmdArgs = append(cmdArgs, "dhcp_gateway="+dhcpConfig.DhcpGateway)
} else {
    // 从LAN配置获取网关
    lanConfig, err := GetLanConfig(p.logger, dhcpConfig.Name)
    if err == nil && lanConfig.Addr != "" {
        cmdArgs = append(cmdArgs, "dhcp_gateway="+lanConfig.Addr)
    } else {
        cmdArgs = append(cmdArgs, "dhcp_gateway=0.0.0.0")
    }
}

// 硬编码参数
cmdArgs = append(cmdArgs, "dhcp_vlan=0")
cmdArgs = append(cmdArgs, "dhcp_mask="+dhcpConfig.DhcpMask)
cmdArgs = append(cmdArgs, "dhcp_acaddr="+dhcpConfig.DhcpAcAddr)
cmdArgs = append(cmdArgs, "dhcp_domain="+dhcpConfig.DhcpDomain)
```

### 2. 发现的关键差异

#### 差异1：缺失的 ID 参数
- **CGI脚本**：`id=${CGI_id}` (包含LAN接口ID)
- **Agent模块**：没有包含 id 参数
- **影响**：可能导致 DHCP 配置无法正确关联到特定的 LAN 接口

#### 差异2：dhcp_vlan 处理不一致
- **CGI脚本**：支持配置 `dhcp_vlan=${CGI_dhcp_vlan}`，默认为0
- **Agent模块**：硬编码为 `"dhcp_vlan=0"`
- **protobuf定义**：没有 dhcp_vlan 字段
- **影响**：无法支持 VLAN 配置功能

#### 差异3：参数顺序不同
- **CGI脚本**：`name` → `id` → `dhcp_pool` → `dns0/dns1` → `leasettl` → `dhcp_enable`
- **Agent模块**：`name` → `dhcp_pool` → `dns0/dns1` → `leasettl` → `dhcp_enable`
- **影响**：虽然通常不影响功能，但保持一致性更好

#### 差异4：DHCP Options 处理
- **CGI脚本**：没有明显的 DHCP options 处理逻辑
- **Agent模块**：正确实现了 DHCP options 转换（option12, option61, option60）
- **影响**：Agent 模块在这方面比 CGI 脚本更完善

### 3. protobuf 定义验证

#### 支持的字段（message.proto 第305-317行）：
```protobuf
message DhcpServerTask {
  string name = 1;                              // ✓ 支持
  IpRange dhcp_pool = 2;                        // ✓ 支持
  int32 lease_ttl = 3;                          // ✓ 支持
  bool dhcp_enable = 4;                         // ✓ 支持
  optional IpAddress dns0 = 5;                  // ✓ 支持
  optional IpAddress dns1 = 6;                  // ✓ 支持
  optional IpAddress dhcp_gateway = 7;          // ✓ 支持
  optional IpAddress dhcp_mask = 8;             // ✓ 支持
  optional string dhcp_domain = 9;              // ✓ 支持
  optional DhcpOptionConfig dhcp_options = 10;  // ✓ 支持
  optional IpAddress dhcp_ac_addr = 11;         // ✓ 支持
}
```

#### 缺失的字段：
- `id` 字段（CGI 脚本中使用）
- `dhcp_vlan` 字段（CGI 脚本中使用）

### 4. 需要修复的问题

#### 问题1：添加缺失的 ID 参数支持
需要在 protobuf 定义中添加 `id` 字段，并在 Agent 模块中处理：
```protobuf
message DhcpServerTask {
  // ... 现有字段 ...
  optional int32 id = 12;                       // LAN接口ID
}
```

#### 问题2：添加 DHCP VLAN 支持
需要在 protobuf 定义中添加 `dhcp_vlan` 字段：
```protobuf
message DhcpServerTask {
  // ... 现有字段 ...
  optional int32 dhcp_vlan = 13;                // DHCP VLAN配置，默认为0
}
```

#### 问题3：对齐参数顺序
建议 Agent 模块按照 CGI 脚本的参数顺序构建命令，以保持一致性。

### 5. 环境验证结果

执行验证命令：
```bash
ssh root@************* 'floweye nat rmvproxy test-netmask && floweye nat rmvproxy test-mask'
```
结果：`PXY_NEXIST` - 表示测试代理不存在，环境正常。

## Domain Group 模块详细验证结果

### 1. 命令构建对比分析

#### CGI 脚本 (ajax_urlgrp) 命令构建逻辑：
```bash
# 新增域名群组
${FLOWEYE} dns addgrp ${CGI_name}

# 删除域名群组
${FLOWEYE} dns rmvgrp ${CGI_id}

# 添加单个域名成员
${FLOWEYE} dns add ${CGI_id} "${CGI_url}"

# 文件形式添加域名成员（支持追加和替换模式）
if [ "${CGI_type}" = "0" ]; then
    ${FLOWEYE} dns dumpgrp ${CGI_id} > ${ttfile}  # 追加模式：先导出现有内容
fi
cat ${CGI_file} | tr -ds '\r' '' >> ${ttfile}    # 添加新内容
${FLOWEYE} dns loadfile ${CGI_id} ${ttfile}      # 加载文件

# 删除域名成员
${FLOWEYE} dns remove ${CGI_id} "${CGI_name}"

# 查询域名群组列表
${FLOWEYE} dns listgrp

# 查询域名群组成员
${FLOWEYE} dns dumpgrp ${CGI_id}
```

#### Agent 模块 (domain_group_processor.go) 命令构建逻辑：
```go
// 新增域名群组
cmdArgs := []string{
    "dns", "addgrp",
    configData.Name,
}

// 删除域名群组
cmdArgs := []string{
    "dns", "rmvgrp",
    fmt.Sprintf("%d", groupId),
}

// 文件形式添加域名成员（仅支持全量替换模式）
cmdArgs := []string{
    "dns", "loadfile",
    fmt.Sprintf("%d", groupId),
    tempFileName,
}

// 查询域名群组列表（在GetDomainGroupConfigs中）
utils.ExecuteCommand(logger, 10, "floweye", "dns", "listgrp")

// 查询域名群组成员（在GetDomainGroupConfig中）
utils.ExecuteCommand(logger, 10, "floweye", "dns", "dumpgrp", fmt.Sprintf("%d", id))
```

### 2. 发现的关键差异

#### 差异1：配置策略不同（符合设计要求）
- **CGI脚本**：支持增量操作（单个域名添加/删除、追加模式）
- **Agent模块**：仅支持全量替换操作
- **影响**：这是符合设计要求的差异，Agent模块按照文档要求采用全量替换策略

#### 差异2：不支持的操作（符合设计要求）
Agent模块不支持以下CGI脚本中的操作：
- 单个域名添加：`dns add ${id} ${domain}`
- 单个域名删除：`dns remove ${id} ${domain}`
- 追加模式的文件加载

**影响**：这些差异是有意设计的，符合文档中"全量配置覆盖"的要求。

### 3. protobuf 定义验证

#### 支持的字段（message.proto 第490-494行）：
```protobuf
message DomainGroupTask {
  string name = 1;                          // 域名群组名称(域名群组唯一标识)，ASCII字符 (required)
  repeated string domain = 2;               // 域名，支持前缀匹配格式：*sohu.com, @sohu.com, ^sohu.com
  optional bytes file_content = 3;          // 文件形式域名群组内容，当域名成员数量较多时使用，每行一个域名
}
```

#### 参数映射验证：
| CGI 参数 | Protobuf 字段 | Agent 处理 | 说明 |
|---------|--------------|-----------|------|
| `CGI_name` | `name` | ✓ 支持 | 域名群组名称 |
| `CGI_url` | `domain` | ✓ 支持 | 单个域名（转换为domain数组） |
| `CGI_file` | `file_content` | ✓ 支持 | 文件内容（字节数组） |
| `CGI_id` | - | ✓ 通过名称查询 | Agent通过名称反查ID |
| `CGI_type` | - | ✗ 不支持 | Agent仅支持替换模式 |

### 4. 配置处理逻辑验证

#### Agent模块处理逻辑（符合文档要求）：
1. **名称到ID映射**：通过`GetDomainGroupIdByName`函数查询域名群组ID
2. **创建逻辑**：如果域名群组不存在，先创建再配置成员
3. **全量替换**：无论输入是domain数组还是file_content，都转换为文件内容进行全量替换
4. **验证机制**：通过`VerifyDomainGroupConfig`验证配置是否正确应用

#### 与文档要求的对比：
- ✓ "以域名群组名称作为唯一标识"
- ✓ "通过查询域名群组列表，获取域名群组名称对应的域名群组ID"
- ✓ "都通过floweye文件形式下发给PA，以达到全量配置覆盖"
- ✓ "仅删除user管理的冗余域名群组对象"

### 5. 验证和错误处理

#### 验证逻辑：
```go
// CompareDomainGroupConfig 处理域名格式标准化
func CompareDomainGroupConfig(logger *logger.Logger, configData *DomainGroupConfig, localConfig *DomainGroupConfig) bool {
    // 处理floweye的域名格式标准化（*.domain.com -> .domain.com）
    // 比较域名列表
}

// VerifyDomainGroupConfig 验证配置应用结果
func VerifyDomainGroupConfig(logger *logger.Logger, configData *DomainGroupConfig, groupId int) (bool, error) {
    // 获取当前配置并比较
}
```

#### 错误处理：
- ✓ 正确处理NEXIST错误（幂等删除）
- ✓ 临时文件清理机制
- ✓ 详细的日志记录

### 6. Working Config Management 模式

Agent模块正确实现了working config management设计模式：
- `localConfigs`：用于全同步冗余删除
- `workingConfigs`：用于操作期间的配置刷新
- `getConfigsForOperation()`：获取最新配置用于操作

### 7. 环境验证结果

执行验证命令：
```bash
ssh root@************* 'floweye nat rmvproxy test-netmask && floweye nat rmvproxy test-mask'
```
结果：`PXY_NEXIST` - 表示测试代理不存在，环境正常。

### 8. 总结

Domain Group模块的代码实现**完全正确**，主要特点：

#### 优点：
1. **命令构建一致**：与CGI脚本的floweye命令完全一致
2. **设计符合要求**：正确实现了全量替换策略
3. **参数映射正确**：protobuf字段与CGI参数映射合理
4. **验证机制完善**：包含配置验证和域名格式标准化处理
5. **错误处理得当**：正确处理各种异常情况
6. **架构模式正确**：实现了working config management模式

#### 设计差异（符合要求）：
1. **不支持增量操作**：这是有意设计，符合"全量配置覆盖"要求
2. **简化配置管理**：避免了增量更新可能导致的状态不一致问题

**结论**：Domain Group模块无需修复，代码实现质量良好，完全符合设计要求。

## 下一步验证计划

1. **修复 protobuf 定义**：添加缺失的 `id` 和 `dhcp_vlan` 字段
2. **更新 Agent 模块**：在 buildDhcpCommand 中添加 id 和 dhcp_vlan 参数处理
3. **对齐参数顺序**：确保命令构建顺序与CGI脚本一致
4. **验证其他模块**：继续验证流控策略、DNS策略等其他模块

