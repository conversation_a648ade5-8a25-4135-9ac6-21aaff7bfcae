1. 域名跟踪策略字段
字段			释义		字段类型	必填项	示例	备注
cookie			策略cookie		int	是	1	唯一标识; uint32
previous			前一个策略cookie		int	是	0	首个策略为0
追加策略为-1
disable			是否启用		int	否	0/1	0为启用，1位禁用
domain_group			域名群组		string	是	dns=domain,domain2	agent需要通过域名群组名称反查id
多个时以,分离
pxy			线路		string	是	pxy=wan	
bkuppxy			备份线路		string	是	bkuppxy=wan	
dnsaddr			dns服务器		IpAddr	否	dnsaddr=*******	为空时表示使用线路DNS服务器
trackhost			跟踪host		bool	否	0/1	
cachettl			DNS缓存应答TTL		int	否	cachettl=0	0表示关闭
desc			备注		string	否	desc=	

⚠️对于同一个域名群组，只能有一条域名跟踪策略。
⚠️TODO: 验证派网各参数能配置的字符、长度、范围等等。

默认字段示例：
floweye dnrt add id=1000 dns=1 pxy=wan dnsaddr= desc= trackhost=0 cachettl=0 bkuppxy=wan

2. 域名跟踪策略查询
2.1 查询所有域名跟踪策略
floweye dnrt list json=1
[root@Panabit:~]# floweye dnrt list json=1
{"polno":2,"cookie":2100,"enable":1,"dnsid":1,"dnsname":"1","pxy":"wan1","bkuppxy":"wan1","active":0,"trackhost":0,"cachettl":0,"desc":"","dnscnt":0,"dnsreqs":0,"dnshits":0,"dnsaddr":"0.0.0.0"}
2.2 查询指定域名跟踪
通过Id查询：
floweye dnrt get <id=>
[root@Panabit:~]# floweye dnrt get id=100
id=100
id=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=wan
bkuppxy=wan1
dnsaddr=*******
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa
通过cookie查询：
floweye dnrt get <cookie=>
[root@Panabit:~]# floweye dnrt get cookie=11100
id=100
id=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=wan
bkuppxy=wan1
dnsaddr=*******
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa
3. 域名跟踪 设置
3.1 新增 域名跟踪
floweye dnrt add <id=> <dns=> <pxy=> <dnsaddr=> <desc=> <trackhost=> <cachettl=> <bkuppxy=> <cookie=>
[root@Panabit:~]# floweye dnrt add id=100 dns=1 pxy=wan dnsaddr=******* desc=aaaa trackhost=1 cachettl=0 bkuppxy=wan1 cookie=11100
[root@Panabit:~]# floweye dnrt get id=100
id=100
id=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=wan
bkuppxy=wan1
dnsaddr=*******
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa

3.2 修改 域名跟踪
floweye dnrt set <id=> <newid=> (<dns=> <pxy=> <dnsaddr=> <desc=> <trackhost=> <cachettl=> <bkuppxy=> <cookie=>)
[root@Panabit:~]# floweye dnrt get id=100
id=100
id=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=wan
bkuppxy=wan1
dnsaddr=*******
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa
[root@Panabit:~]# floweye dnrt set id=100 newid=100 dns=1 pxy=sr1 dnsaddr=*************** desc=aaaa trackhost=1 cachettl=0 bkuppxy=sr1 cookie=11100
[root@Panabit:~]# floweye dnrt get id=100
id=100
cookie=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=sr1
bkuppxy=sr1
dnsaddr=***************
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa
3.3 删除 域名跟踪
floweye dnrt remove <id=>
[root@Panabit:~]# floweye dnrt list json=1
{"polno":2,"cookie":2100,"enable":1,"dnsid":1,"dnsname":"1","pxy":"wan1","bkuppxy":"wan1","active":0,"trackhost":0,"cachettl":0,"desc":"","dnscnt":0,"dnsreqs":0,"dnshits":0,"dnsaddr":"0.0.0.0"}[root@Panabit:~]#
[root@Panabit:~]#
[root@Panabit:~]#
[root@Panabit:~]# floweye dnrt remove id=2
[root@Panabit:~]# floweye dnrt list json=1
[root@Panabit:~]#
3.4 禁用/启用 域名跟踪
floweye dnrt set <id=> <disable=1/0>
[root@Panabit:~]# floweye dnrt get id=100
id=100
id=11100
enable=1
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=sr1
bkuppxy=sr1
dnsaddr=***************
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa
[root@Panabit:~]# floweye dnrt set id=100 disable=1
[root@Panabit:~]# floweye dnrt get id=100
id=100
id=11100
enable=0
trackhost=1
cachettl=0
dnsid=1
dnsname=domain
pxy=sr1
bkuppxy=sr1
dnsaddr=***************
dnscnt=0
dnsreqs=0
dnshits=0
desc=aaaa

4. 配置处理逻辑
域名跟踪策略较其它模块稍复杂，涉及到以下几方面的处理
4.1 引用依赖对象标识映射
域名跟踪策略的配置中会引用其它模块的对象，例如域名群组，而这些被依赖的对象在本地是以ID为唯一标识(本地生成)进行管理的，而Orch是以对象名称为唯一标识进行管理的，Agent需要做标识的转换；目前涉及到转换的被引用对象为：
1. 域名群组：域名群组Orch以名称作为唯一标识，agent接收时需要根据名称反查ID进行引用和配置。

4.2 域名跟踪策略映射
PA本地的域名跟踪策略ID不仅为策略的唯一标识，还标识着本地策略 匹配的先后顺序。

4.2.1 唯一标识转换
由于Orch上管理的策略(流量控制、路由/NAT、DNS管控、域名跟踪)涉及到跨Site间的管理，难以以单个Site的策略ID 为唯一标识进行管理，所以：
1. 域名跟踪策略 通过 cookie 实现唯一标识，Agent 负责将 cookie ↔ 本地策略 ID 映射。

4.2.2 排序转换
Orch 并不直接管理本地 策略ID 顺序。为了控制策略的匹配顺序，Orch 通过下发 previous 字段（即 前一个策略 cookie）来表达插入位置，Agent 需根据此字段调整顺序。

previous规则：
1. 当下发的策略的previous cookie为0时，视为排在首个
2. 当下发的策略的previous cookie 为 -1且本地不存在时，视为追加到最后

基于以上2点，域名跟踪策略 新增 修改 删除 调整顺序的逻辑为
1. 新增/修改 策略
  1. ID分配；
    1. 根据本地配置查询"策略名称"是否已存在相应策略：
      1. 存在：取现有ID。
      2. 不存在：使用max(ID)+1作为新增策略ID。
  2. 修改/新增配置；
    1. 存在：更新已有策略的配置。
    2. 不存在：根据分配的ID新增策略。
  3. 调整策略顺序；
    1. 查找 previous 对应策略的 ID（若为0，则视为插入最前；若为-1或未查找到，视为最大 ID）；
    2. 若当前策略 ID ≠ previous ID + 1：
      1. 若相同，则无需调整；
      2. 若不同则需重新排序：
        1. 将当前策略 ID 设置为临时 ID（如 65536）。
        2. 根据目标插入方向：
          1. 向前插入(当前ID > 目标ID)
            1. 将 [目标 ID, 当前 ID - 1] 区间内的所有策略 ID +1（从大到小调整）。
          2. 向后移动（当前 ID < 目标 ID）：
            1. 将 [当前 ID + 1, 目标 ID] 区间内的所有策略 ID -1（从小到大调整）。
          3. 最终将当前策略 ID 设置为 previous ID + 1。
2. 删除策略
  1. 获取策略Id；根据本地配置查询"策略名称"是否已存在相应策略：
    1. 存在：获取对应策略Id。
    2. 不存在：结束删除流程。
  2. 删除对应 ID 的策略。
  3. 所有 ID > 被删除 ID 的策略 ID 全部 -1，以保持 ID 顺序连续。

