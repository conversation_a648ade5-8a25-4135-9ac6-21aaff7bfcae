# 用户组模块配置实现文档

## 概述

用户组模块负责处理用户组的配置，包括创建、修改和删除用户组。本文档描述了用户组模块的实现方案和关键代码。

## 配置项

根据`floweye_usergroup_user.md`文档和`message.proto`定义，用户组模块支持以下配置项：

1. **基本配置**
   - ID(id) - 必填，用户组ID，范围: 1-2063
   - 上级组ID(pid) - 必填，上级组ID
   - 名称(name) - 必填，用户组名称

2. **IPv4地址范围**
   - 起始IP(start) - 可选，IPv4地址范围起始
   - 结束IP(end) - 可选，IPv4地址范围结束

3. **IPv6地址**
   - 前缀地址(prefix) - 可选，IPv6前缀地址
   - 前缀长度(pfxlen) - 可选，IPv6前缀长度

4. **账号带宽限制**
   - v4账号入向带宽限制(ratein) - 可选，单位kbps，0表示不限制
   - v4账号出向带宽限制(rateout) - 可选，单位kbps，0表示不限制
   - v6账号入向带宽限制(ratein6) - 可选，单位kbps，0表示不限制
   - v6账号出向带宽限制(rateout6) - 可选，单位kbps，0表示不限制

5. **其他设置**
   - DNS服务器(dns) - 可选，可配置多个，用逗号分隔
   - 在线时间(maxonlinetime) - 可选，单位小时，0表示不控制
   - 过期账号处理方式(clntepa) - 可选，reject/login/pass

## 配置一致性和同步机制

根据项目的配置一致性和同步机制，我们实现了以下同步机制：

### 全量同步

1. **同步开始**
   - 调用`StartFullSync()`方法
   - 获取本地全量配置（使用`floweye pppoeippool list`和`floweye pppoeippool get`命令）
   - 将配置缓存在内存中

2. **同步过程**
   - 处理来自Orchestrator的用户组配置
   - 对于每个配置，检查是否与本地配置一致
   - 如果不一致，应用新配置
   - 从本地缓存中移除已处理的配置

3. **同步结束**
   - 调用`EndFullSync()`方法
   - 删除本地缓存中剩余的配置（这些配置在Orchestrator中已不存在）
   - 清理资源

### 增量同步

1. **获取本地配置**
   - 获取特定用户组的本地配置
   - 将配置与请求的配置进行比较

2. **应用配置**
   - 如果配置不一致，应用新配置
   - 验证配置是否成功应用

## 关键函数

1. **GetLocalUserGroupConfigs()**
   - 获取所有本地用户组配置
   - 首先使用`floweye pppoeippool list`获取所有用户组ID
   - 然后对每个ID使用`floweye pppoeippool get`获取详细配置
   - 返回用户组配置的映射

2. **GetUserGroupConfig()**
   - 获取特定用户组的配置
   - 使用`floweye pppoeippool get`命令
   - 解析命令输出，构建用户组配置对象

3. **VerifyUserGroupConfig()**
   - 验证用户组配置是否成功应用
   - 获取本地配置并与期望配置比较
   - 检查ID、名称和上级组ID是否匹配

4. **CompareUserGroupConfig()**
   - 比较请求的配置与本地配置
   - 检查所有相关字段是否一致
   - 决定是否需要修改配置

5. **handleConfigChange()**
   - 处理新建和修改用户组配置任务
   - 验证必填字段
   - 检查ID和名称是否已存在
   - 构建并执行floweye命令
   - 验证配置是否成功应用

6. **handleDeleteConfig()**
   - 处理删除用户组配置任务
   - 检查ID是否存在
   - 执行删除命令
   - 从本地配置中移除

## 命令行交互

用户组模块通过以下命令与系统交互：

1. **查询所有用户组**
   ```
   floweye pppoeippool list
   ```

2. **查询特定用户组**
   ```
   floweye pppoeippool get id=<用户组ID>
   ```

3. **创建用户组**
   ```
   floweye pppoeippool add id=<用户组ID> pid=<上级组ID> name=<用户组名称> start=<起始IP> end=<结束IP> prefix=<前缀地址> pfxlen=<前缀长度> ratein=<入向带宽> rateout=<出向带宽> ratein6=<v6入向带宽> rateout6=<v6出向带宽> dns=<DNS服务器> clntepa=<过期账号处理> maxonlinetime=<在线时间> vlan_first=<VLAN优先>
   ```

4. **修改用户组**
   ```
   floweye pppoeippool set id=<用户组ID> pid=<上级组ID> name=<用户组名称> start=<起始IP> end=<结束IP> prefix=<前缀地址> pfxlen=<前缀长度> ratein=<入向带宽> rateout=<出向带宽> ratein6=<v6入向带宽> rateout6=<v6出向带宽> dns=<DNS服务器> clntepa=<过期账号处理> maxonlinetime=<在线时间> vlan_first=<VLAN优先>
   ```

5. **删除用户组**
   ```
   floweye pppoeippool remove id=<用户组ID>
   ```

## 实现特点

1. **结构化配置解析**
   - 使用map来解析命令输出
   - 提高了解析效率和可读性

2. **统一的全量同步接口**
   - 实现TaskProcessor接口定义的StartFullSync和EndFullSync方法
   - 支持全量同步和增量同步

3. **配置验证机制**
   - 在应用配置后验证是否成功
   - 确保命令执行成功后配置确实生效

4. **配置比对机制**
   - 在应用配置前先检查是否与本地配置一致
   - 如果一致则无需修改，提高效率

5. **IP地址处理**
   - 使用utils/ip.go中的通用函数处理IP地址
   - 简化了IP地址处理代码

## 注意事项

1. ID范围必须在1-2063之间
2. ID和name都必须唯一
3. 如果ID一致但name不一致，后下发的group name将替换原有name
4. 如果name一致但ID不一致，后下发的配置将失败
5. 创建用户组后可以修改名称和上级组ID，但不能修改ID
6. ID=2063默认作为_reserved_for_seg_srv_poo组织架构占用

## 错误处理

1. 命令执行失败：记录错误并返回
2. 配置验证失败：记录错误并返回
3. 本地配置获取失败：记录错误并返回
4. 缺少必要参数：验证并返回明确的错误信息
5. ID或名称冲突：检查并返回明确的错误信息

## 测试方案

1. **单元测试**
   - 测试GetLocalUserGroupConfigs函数
   - 测试GetUserGroupConfig函数
   - 测试VerifyUserGroupConfig函数
   - 测试CompareUserGroupConfig函数
   - 测试ProcessTask方法（各种任务类型和边界情况）
   - 测试StartFullSync和EndFullSync方法

2. **集成测试**
   - 测试用户组的创建、修改和删除
   - 测试全量同步
   - 测试边界情况和错误处理

## 后续优化

1. 优化本地配置缓存的更新策略
2. 增加配置批量处理能力
3. 增加对用户组状态的监控和上报
4. 实现更细粒度的错误处理和恢复机制
5. 优化命令输出解析逻辑，提高解析效率和稳定性
