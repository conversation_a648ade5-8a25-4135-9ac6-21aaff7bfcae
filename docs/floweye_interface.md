1. 网卡字段
字段		释义	字段类型	必填项	示例	备注
name		网卡名称	string	是	eth0	
mode		接口类型	int	是	0-6	0:监控模式，网卡的普通接入模式；1-6:网桥；
当前默认为监控模式，不支持配置。
zone		接入位置	string	是	inside/outside	方向: 接内/接外

LAN 接口、DHCP 等的接口应设置为“接内”。
WAN 线路、对外网提供服务（NAT，DNS 管控等）的接口，应设置为“接外”。

设置为“接内”：那么流入这个网卡的流量，将被统计为上行流量。
设置为“接外”：那么流入这个网卡的流量，将被统计为下行流量。
lagroup		链路捆绑	int	否	0-6	0:不捆绑；1-6:链路组；
mixmode		混合模式	bool	否	true/false	mixmode=0/1
网桥参数	peer	对端	string	是	eth0	peer=eth0
链路捆绑参数	protocol	捆绑协议	int	是	0/1	enable=0 为static；
enable=1 为LACP;
	timeout	老化模式	int	否	0/1	timeout=0 为慢速模式；
timeout=1 为跨素模式；
	passive	被动模式	bool	否	true/false	passive=0/1

2. 网卡查询
1.1 查看所有网卡信息
floweye if list
[root@Panabit:~]# floweye if list
eth0 1 inside down PANAOS 00-e0-3c-00-0b-99 PAENIC 0 0 0 0 ����1 NONE eth1 1 0 down
eth1 1 inside down PANAOS 00-e0-3c-00-0b-9a PAENIC 0 0 0 0 ����1 NONE eth0 0 0 down
eth2 0 inside down PANAOS 00-e0-3c-00-0b-9b PAENIC 0 0 0 0 0 NONE NULL 2 0 down
1.2 查询特定网卡信息
floweye if get <网卡名>
[root@Panabit:~]# floweye if get eth0
index=1
quota=16
bypass=0
lagroup=1
bdgid=0
mtu=1500
numqs=1
zone=inside
mode=1
mixmode=1
ifdesc=PAENIC
peer=eth1
driver=PANAOS
link-state=down
lacp-state=down
vf=0
backplane=0
speed=NONE
physpeed=1000M
media=AUTO
macaddr=00-e0-3c-00-0b-99
rx-byte=0
rx-pkt=0
tx-byte=0
tx-pkt=0
tx-drop=177527
lastdowntime=1970-01-01/08:00:00
bps-in=0
bps-out=0
pps-in=0
pps-out=0
1.3 查看网卡统计
floweye if stat
[root@Panabit:~]# floweye if stat
name bps-in    bps-out   pps-in    pps-out   bps-tcpin bps-tcpout bps-udpin bps-udpout pps-tcpin pps-tcpout pps-udpin pps-udpout pps-syn   pps-synack
eth0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
eth1 0 0 0 0 0 0 0 0 0 0 0 0 0 0
eth2 0 0 0 0 0 0 0 0 0 0 0 0 0 0
bgp 0 0 0 0 0 0 0 0 0 0 0 0 0 0
1.4 查看网卡内参
floweye if stat <网卡名>
[root@Panabit:~]# floweye if stat eth0
MAC Ctrl = 0x8078203
IO Address = 0x7fb6535000
IO PHY = 0xfe010000
Mode = 0
Chain Mode = 0
Received Packets = 0
Received Bytes = 0
Received No Buff = 0
Transmit Packets = 0
Transmit Bytes = 0
Transmit Error = 0
----------------- RX QUEUES ---------------------
DMA rx = 0x7fb674e000
DMA rx phy = 0x7f078000
DMA rx size = 256
Cur rx = 0
DMA buf size = 1536
----------------- TX QUEUES ---------------------
DMA tx = 0x7fb6531000
DMA tx PHY = 0x7f07c000
DMA tx size = 256
Cur tx = 0
tx pending = 0
tx next_to_clean = 0
tx errors = 0
----------------- MAC Management Counters ---------------------
tx_octetcount_gb = 0
tx_framecount_gb = 0
tx_broadcastframe_g = 0
tx_multicastframe_g = 0
tx_unicast_gb = 0
tx_multicast_gb = 0
tx_broadcast_gb = 0
tx_underflow_error = 0
tx_singlecol_g = 0
tx_multicol_g = 0
tx_deferred = 0
tx_latecol = 0
tx_exesscol = 0
tx_carrier_error = 0
tx_octetcount_g = 0
tx_framecount_g = 0
tx_excessdef = 0
tx_pause_frame = 0
tx_vlan_frame_g = 0
rx_framecount_gb = 0
rx_octetcount_gb = 0
rx_octetcount_g = 0
rx_broadcastframe_g = 0
rx_multicastframe_g = 0
rx_crc_error = 0
rx_align_error = 0
rx_run_error = 0
rx_jabber_error = 0
rx_undersize_g = 0
rx_oversize_g = 0
rx_unicast_g = 0
rx_length_error = 0
rx_autofrangetype = 0
rx_pause_frames = 0
rx_fifo_overflow = 0
rx_vlan_frames_gb = 0
rx_watchdog_error = 0
rx_ipc_intr_mask = 0
rx_ipc_intr = 0
rx_ipv4_gd = 0
rx_ipv4_hderr = 0
rx_ipv4_nopay = 0
rx_ipv4_frag = 0
rx_ipv4_udsbl = 0
rx_ipv4_gd_octets = 0
rx_ipv4_hderr_octets = 0
rx_ipv4_nopay_octets = 0
rx_ipv4_frag_octets = 0
rx_ipv4_udsbl_octets = 0
rx_ipv6_gd_octets = 0
rx_ipv6_hderr_octets = 0
rx_ipv6_nopay_octets = 0
1.5 查看网卡LACP状态
floweye lacp get if=网卡名
[root@Panabit:~]# floweye lacp get if=eth0
portpri=32768
linkup=0
stateup=0
lagroup=1
lagup=0
1.6 查看LACP组状态
floweye lacp get lag=(1-6)
[root@Panabit:~]# floweye lacp get lag=3
enable=0
timeout=1
passive=1
actagg=0
strict=1
suppress_distributing=0
flapping=0
3. 网卡配置
3.1 配置网卡接入模式/接入位置/混合模式
3.1.1 配置 监控模式/接入位置/混合模式
floweye if set <name=> <mode=0> <zone=inside/outside> <lagroup=> <mixmode=>
[root@Panabit:~/agent]# floweye if set name=eth2 mode=0 zone=inside lagroup=0 mixmode=1
[root@Panabit:~/agent]# floweye if get eth2
index=3
quota=16
bypass=0
lagroup=0
bdgid=0
mtu=1500
numqs=1
zone=inside
mode=0
mixmode=1
peer=none
3.1.2 配置 网桥
floweye if set <name=> <mode=1-6> <zone=inside/outside> <lagroup=> <mixmode=> <peer=>
[root@Panabit:~]# floweye if set name=eth2 mode=4 zone=inside lagroup=0 mixmode=1 peer=eth0
[root@Panabit:~/agent]# floweye if get eth2
index=3
quota=16
bypass=0
lagroup=0
bdgid=0
mtu=1500
numqs=1
zone=inside
mode=4
mixmode=1
peer=eth0
3.2 配置网卡链路聚合
1. 设置网卡lagroup
floweye if set <name=> <mode=1-6> <zone=inside/outside> <lagroup=> <mixmode=> <peer=>
[root@Panabit:~]# floweye if set name=eth2 mode=1 zone=inside lagroup=3 mixmode=1 peer=eth0
[root@Panabit:~/agent]# floweye if get eth2
index=3
quota=16
bypass=0
lagroup=3
bdgid=0
mtu=1500
numqs=1
zone=inside
mode=1
mixmode=1
ifdesc=PAENIC
peer=eth0
2. 设置对应lagroup配置
floweye lacp set <lag=> <enable=> <timeout=> <passive=>
[root@Panabit:~]# floweye lacp set lag=3 enable=1 timeout=1 passive=1
[root@Panabit:~/agent]# floweye lacp get lag=3
enable=1
timeout=1
passive=1
actagg=0
strict=1
suppress_distributing=0
flapping=0
3.5 配置网卡调度策略
⚠️暂不实现

