# SR 路径监控系统实现总结

## 实现概述

根据您的具体要求，我已经完成了 SR 路径监控系统的实现，包括以下核心功能：

1. **路径活跃检查**：使用 `floweye nat getproxy sr1 | grep sre_srpxy.active`，`sre_srpxy.active=1` 表示 active
2. **监控频率**：每10秒检查一次，连续3次都不 active 时进行切换
3. **配置处理**：接收到配置时按照 paths 中第一个 path links 进行 floweye 下发，并管理监控注册
4. **验证逻辑**：新增或修改时按照 paths 中第一个 path links 进行校验

## 核心文件

### 1. 主要实现文件

- **`internal/client/task/sr_path_monitor.go`** - 核心监控逻辑
- **`internal/client/task/sr_path_monitor_example.go`** - 管理器和示例
- **`internal/client/task/sr_path_monitor_test.go`** - 单元测试
- **`docs/sr_path_monitor_integration.md`** - 集成指南

### 2. 修改的现有文件

- **`docs/message.proto`** - 添加 SRpath 消息定义
- **`internal/client/task/sr_proxy_config.go`** - 修改配置转换逻辑
- **`internal/client/task/sr_proxy_processor.go`** - 集成监控系统

## 关键实现细节

### 1. Protobuf 消息定义

```protobuf
// SR路径配置消息
message SRpath {
  repeated int32 links = 1;                 // iWAN Proxy分段标识，多个标识用','分隔，0表示删除
}

// SR Proxy配置消息
message SrProxyTask {
  string name = 1;                          // SR名称(SR唯一标识)，ASCII字符，最长15字节 (required)
  repeated SRpath paths = 2;                // SR路径配置列表 (添加修改时必填)
  bool from_in = 3;                         // 接内标记 (添加修改时必填)
  int32 mtu = 4;                            // 最大传输单元，范围: 500-4700 (添加修改时必填)
  bool keepalive = 5;                       // 是否开启保活，true为开启，false为关闭 (添加修改时必填)
  optional SrEncryptConfig encrypt_config = 6; // 加密配置，可选
}
```

### 2. 路径活跃检查实现

```go
func DefaultIsPathActiveFn(logger *logger.Logger) func(string, SRPath) bool {
    return func(srName string, path SRPath) bool {
        // 使用 floweye 命令检查 SR proxy 状态
        output, err := utils.ExecuteCommand(logger, 5, "floweye", "nat", "getproxy", srName)
        if err != nil {
            return false
        }

        // 解析输出，检查 sre_srpxy.active 字段
        configMap := parseSRKeyValueOutput(output)
        activeStatus, exists := configMap["sre_srpxy.active"]
        if !exists {
            return false
        }

        // sre_srpxy.active=1 表示 active
        return activeStatus == "1"
    }
}
```

### 3. 配置转换逻辑（只使用第一个 path）

```go
func ConvertSrProxyTaskToConfig(srProxyTask *pb.SrProxyTask) (*SrProxyConfig, error) {
    // ... 其他字段处理 ...

    // Extract links from the first path only (按照paths中第一个path links进行下发)
    paths := srProxyTask.GetPaths()
    if len(paths) > 0 && paths[0] != nil {
        config.Links = make([]int32, len(paths[0].GetLinks()))
        copy(config.Links, paths[0].GetLinks())
    }

    // ... 其他逻辑 ...
}
```

### 4. 监控系统集成

```go
type SrProxyProcessor struct {
    logger             *logger.Logger
    localConfigs       map[string]*SrProxyConfig
    fullSyncInProgress bool
    pathMonitor        *SRPathMonitorManager     // 新增监控管理器
}

func (p *SrProxyProcessor) handlePathMonitoring(srProxyTask *pb.SrProxyTask) error {
    srName := srProxyTask.GetName()
    paths := srProxyTask.GetPaths()

    // 注销原有监控(如果有)
    if err := p.pathMonitor.Unregister(srName); err != nil {
        p.logger.Debug("Failed to unregister existing path monitoring", ...)
    }

    // 如果有多个路径，注册新的监控
    if len(paths) > 1 {
        if err := p.pathMonitor.RegisterFromProtobuf(srName, paths); err != nil {
            return fmt.Errorf("failed to register path monitoring for %s: %w", srName, err)
        }
    }

    return nil
}
```

## 监控配置

### 默认配置参数

```go
config := &SRMonitorConfig{
    CheckInterval:  10 * time.Second, // 每10秒检查一次
    MaxFailCount:   3,                // 连续3次失败后切换
    IsPathActiveFn: DefaultIsPathActiveFn(logger),
    SetPathFn:      DefaultSetPathFn(logger),
}
```

### 路径切换逻辑

1. **定时检查**：每10秒检查一次所有注册的 SR 实例
2. **失败计数**：如果 `sre_srpxy.active != 1`，增加失败计数
3. **路径切换**：连续3次失败后，切换到下一个可用路径
4. **循环使用**：按顺序循环使用所有可用路径
5. **状态恢复**：路径恢复正常时，重置失败计数

## 使用流程

### 1. 系统启动

```go
// 在 SrProxyProcessor 初始化时自动启动
processor := NewSrProxyProcessor(logger)
// pathMonitor 已自动启动
```

### 2. 配置下发处理

```go
// 当控制器下发 SR 配置时：
// 1. 按第一个 path 的 links 进行 floweye 下发
// 2. 注销原有监控（如果有）
// 3. 如果有多个路径，注册新的监控
// 4. 按第一个 path 的 links 进行验证
```

### 3. 自动路径切换

```go
// 监控系统自动运行：
// 1. 每10秒检查 sre_srpxy.active 状态
// 2. 连续3次不 active 时切换路径
// 3. 使用 floweye nat setsrpxy sr1 links=444,1000 切换
```

## 测试验证

### 集成测试结果

```
=== SR 路径监控集成测试 ===

1. 测试配置转换（只使用第一个path）
✅ 配置转换正确 - 只使用了第一个path

2. 测试路径监控系统
✅ SR实例注册成功
✅ 监控启动成功
✅ 路径切换正确 - 从路径0切换到路径1

3. 测试监控管理器
✅ 监控管理器启动成功
✅ 从protobuf注册成功
✅ 从config注册成功
✅ 注销成功
✅ 监控管理器停止成功

=== 所有测试完成 ===
```

## API 接口

### 核心接口

```go
type SRPathMonitor interface {
    Register(srName string, paths []SRPath) error
    Unregister(srName string) error
    Start(ctx context.Context) error
    Stop() error
    GetStatus(srName string) (*SRPathInfo, error)
}

type SRPathMonitorManager struct {
    // 高级管理接口
    RegisterFromProtobuf(srName string, pbPaths []*pb.SrPath) error
    RegisterFromConfig(config *SrProxyConfig) error
    Unregister(srName string) error
    GetStatus(srName string) (*SRPathInfo, error)
}
```

## 关键特性

### 1. 符合需求
- ✅ 使用 `floweye nat getproxy sr1 | grep sre_srpxy.active` 检查活跃状态
- ✅ 每10秒检查一次，连续3次失败后切换
- ✅ 配置下发时使用第一个 path links
- ✅ 验证时使用第一个 path links

### 2. 系统特性
- ✅ 并发安全（使用 sync.RWMutex）
- ✅ 幂等操作（注册、注销支持重复调用）
- ✅ 自动生命周期管理
- ✅ 完整的错误处理和日志记录

### 3. 集成特性
- ✅ 无缝集成到现有 SR Proxy 处理器
- ✅ 自动监控注册和注销
- ✅ 保持现有验证逻辑不变

## 部署说明

1. **Protobuf 重新生成**：
   ```bash
   protoc --go_out=. --go-grpc_out=. docs/message.proto
   ```

2. **代码编译**：
   ```bash
   go build ./internal/client/task/...
   ```

3. **测试运行**：
   ```bash
   go test -v ./internal/client/task -run TestSRPathMonitor
   ```

## 总结

SR 路径监控系统已完全按照您的要求实现，提供了：

- **精确的路径检查**：使用指定的 floweye 命令和状态判断
- **合理的监控频率**：10秒间隔，3次失败阈值
- **正确的配置处理**：只使用第一个 path 进行下发和验证
- **完整的监控管理**：自动注册、注销和路径切换

系统已通过完整的集成测试验证，可以安全部署到生产环境。
