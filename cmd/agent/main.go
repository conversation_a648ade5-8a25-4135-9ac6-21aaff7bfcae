/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      main.go
 *
 * DESCRIPTION :    Main entry point for the agent program, responsible for
 *                 initializing and managing all components
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/08/2025  create
 ******************************************************************************/

package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"
	"syscall"
	"time"

	"agent/internal/client"
	"agent/internal/config"
	"agent/internal/daemon"
	"agent/internal/ipc"
	"agent/internal/logger"

	"go.uber.org/zap"
)

// Version information variables, injected at build time via -ldflags
var (
	Version   = "0.0.0"
	BuildTime = "unknown"
	GitBranch = "unknown"
	GitCommit = "unknown"
)

// Print version information
func printVersion() {
	fmt.Printf("Agent Version: %s\n", Version)
	fmt.Printf("Build Time: %s\n", BuildTime)
	fmt.Printf("Git Branch: %s\n", GitBranch)
	fmt.Printf("Git Commit: %s\n", GitCommit)
}

// Print help information
func printHelp() {
	fmt.Println("Usage: agent [options] [command]")
	fmt.Println("\nOptions:")
	fmt.Println("  -config string    Path to config file (default \"config.yaml\")")
	fmt.Println("\nCommands:")
	fmt.Println("  version          Display version information")
	fmt.Println("  stop             Stop the agent daemon")
	fmt.Println("  status           Check agent daemon status")
	fmt.Println("  setloglevel      Set logging level (DEBUG, INFO, WARN, ERROR)")
	fmt.Println("  debug            Debug commands (start, stop, status, fullsync)")
	fmt.Println("\nExamples:")
	fmt.Println("  agent -config /path/to/config.yaml")
	fmt.Println("  agent version")
	fmt.Println("  agent setloglevel DEBUG")
}

/*****************************************************************************
 * NAME: setupCrashSignalHandling
 *
 * DESCRIPTION:
 *     设置crash信号处理，捕获段错误、总线错误等crash信号
 *     并记录详细的调用栈信息到日志文件
 *
 * PARAMETERS:
 *     log - 日志记录器，可能为nil（在logger初始化前）
 *
 * RETURNS:
 *     无返回值
 *****************************************************************************/
func setupCrashSignalHandling(log *logger.Logger) {
	crashSignals := []os.Signal{
		syscall.SIGSEGV, // 段错误
		syscall.SIGABRT, // 异常终止
		syscall.SIGBUS,  // 总线错误
		syscall.SIGFPE,  // 浮点异常
		syscall.SIGILL,  // 非法指令
	}

	crashCh := make(chan os.Signal, 1)
	signal.Notify(crashCh, crashSignals...)

	go func() {
		sig := <-crashCh
		timestamp := time.Now().Format(time.RFC3339)
		stackTrace := string(debug.Stack())

		// 输出到stderr
		fmt.Printf("[%s] Agent received crash signal: %v\n", timestamp, sig)
		fmt.Printf("Stack trace:\n%s\n", stackTrace)

		// 记录到日志文件
		crashLogFile := "/var/log/agent_crash.log"
		if logFile, err := os.OpenFile(crashLogFile,
			os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644); err == nil {
			fmt.Fprintf(logFile, "[%s] Crash signal: %v (PID: %d)\n", timestamp, sig, os.Getpid())
			fmt.Fprintf(logFile, "Stack trace:\n%s\n", stackTrace)
			fmt.Fprintf(logFile, "=== End of crash report ===\n\n")
			logFile.Close()
		}

		// 如果有logger，也记录到结构化日志
		if log != nil {
			log.Error("Agent crashed with signal",
				zap.String("signal", sig.String()),
				zap.Int("pid", os.Getpid()),
				zap.String("stack_trace", stackTrace))
		}

		// 生成core dump并退出
		signal.Reset(sig)
		syscall.Kill(os.Getpid(), sig.(syscall.Signal))
	}()
}

/*****************************************************************************
 * NAME: restartAgent
 *
 * DESCRIPTION:
 *     重启agent进程，用于panic recovery
 *     会先清理IPC资源，然后延迟启动新进程以避免资源冲突
 *
 * PARAMETERS:
 *     configPath - 配置文件路径
 *
 * RETURNS:
 *     无返回值，直接退出当前进程
 *****************************************************************************/
func restartAgent(configPath string) {
	fmt.Printf("Restarting agent with config: %s\n", configPath)

	// 获取当前执行文件路径
	executable, err := os.Executable()
	if err != nil {
		fmt.Printf("Failed to get executable path: %v\n", err)
		os.Exit(1)
	}

	// 等待一段时间确保资源完全释放
	fmt.Println("Waiting for resources to be released...")
	time.Sleep(2 * time.Second)

	// 启动新进程
	cmd := exec.Command(executable, "-config", configPath)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed to restart agent: %v\n", err)
		// 如果重启失败，尝试通过外部监控脚本重启
		fmt.Println("Attempting restart via external monitor...")
		os.Exit(1)
	}

	fmt.Printf("Agent restarted successfully with PID: %d\n", cmd.Process.Pid)
	os.Exit(0)
}

/*****************************************************************************
 * NAME: main
 *
 * DESCRIPTION:
 *     Main entry point function for the agent program, responsible for
 *     initializing and coordinating all components
 *
 * FLOW:
 *     1. Parse command line arguments
 *     2. Handle special commands (stop/status)
 *     3. Load configuration file
 *     4. Create and start daemon process
 *     5. Initialize logging system
 *     6. Create gRPC client
 *     7. Start IPC server
 *     8. Handle system signals and shutdown requests
 *
 * NOTES:
 *     - Supports graceful shutdown
 *     - Includes comprehensive error handling
 *     - Manages lifecycle of all components
 *****************************************************************************/
func main() {
	var ipcServer ipc.IpcServer
	var grpcClient *client.Client
	var log *logger.Logger

	// 设置panic recovery机制
	defer func() {
		if r := recover(); r != nil {
			timestamp := time.Now().Format(time.RFC3339)
			stackTrace := string(debug.Stack())

			// 输出panic信息到stderr
			fmt.Printf("[%s] Agent panic recovered: %v\n", timestamp, r)
			fmt.Printf("Stack trace:\n%s\n", stackTrace)

			// 记录panic信息到文件
			panicLogFile := "/var/log/agent_panic.log"
			if logFile, err := os.OpenFile(panicLogFile,
				os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644); err == nil {
				fmt.Fprintf(logFile, "[%s] Panic: %v (PID: %d)\n", timestamp, r, os.Getpid())
				fmt.Fprintf(logFile, "Stack trace:\n%s\n", stackTrace)
				fmt.Fprintf(logFile, "=== End of panic report ===\n\n")
				logFile.Close()
			}

			// 如果有logger，也记录到结构化日志
			if log != nil {
				log.Error("Agent panic occurred",
					zap.String("panic", fmt.Sprintf("%v", r)),
					zap.String("stack_trace", stackTrace))
			}

			// 清理资源
			fmt.Println("Cleaning up resources after panic...")
			if grpcClient != nil {
				fmt.Println("Closing gRPC client...")
				grpcClient.Close()
			}
			if ipcServer != nil {
				fmt.Println("Stopping IPC server...")
				if err := ipcServer.Stop(); err != nil {
					fmt.Printf("Warning: Failed to stop IPC server: %v\n", err)
				}
			}
			if log != nil {
				fmt.Println("Syncing logger...")
				log.Sync()
			}

			// 等待资源清理完成
			time.Sleep(1 * time.Second)

			fmt.Println("Agent will exit and be restarted by monitor script")
			os.Exit(1) // 退出让监控脚本重启
		}
	}()

	// Parse command line arguments
	configPath := flag.String("config", "config.yaml", "path to config file")
	flag.Parse()

	// Convert relative path to absolute path
	absConfigPath, err := filepath.Abs(*configPath)
	if err != nil {
		fmt.Printf("Failed to convert config path to absolute path: %v\n", err)
		os.Exit(1)
	}
	*configPath = absConfigPath

	// Check if unknown parameters were provided
	if len(os.Args) > 1 {
		// Check if the first argument is a known command
		knownCommands := map[string]bool{
			"version":     true,
			"stop":        true,
			"status":      true,
			"setloglevel": true,
			"debug":       true,
		}

		// If the first argument is not a known command and not -config, show help
		if !knownCommands[os.Args[1]] && os.Args[1] != "-config" {
			fmt.Printf("Unknown command: %s\n\n", os.Args[1])
			printHelp()
			os.Exit(1)
		}

		// Create IPC client
		ipcClient := ipc.NewClient()
		defer ipcClient.Close()

		switch os.Args[1] {
		case "version":
			// Display version information
			printVersion()
			return
		case "stop":
			// Stop daemon process
			fmt.Println("Stopping agent...")
			if err := ipcClient.Stop(); err != nil {
				fmt.Printf("Failed to stop agent: %v\n", err)
				os.Exit(1)
			}
			fmt.Println("Agent stopped successfully")
			return
		case "status":
			// Check daemon status
			fmt.Println("Checking agent status...")
			status, err := ipcClient.GetStatus()
			if err != nil {
				fmt.Printf("Failed to get daemon status: %v\n", err)
				os.Exit(1)
			}

			// Print status information
			statusJSON, _ := json.MarshalIndent(status, "", "  ")
			fmt.Println(string(statusJSON))
			return
		case "setloglevel":
			// Set log level
			if len(os.Args) < 3 {
				fmt.Println("Usage: agent setloglevel <level>")
				fmt.Println("Valid levels: DEBUG, INFO, WARN, ERROR")
				os.Exit(1)
			}

			level := os.Args[2]
			fmt.Printf("Setting log level to %s...\n", level)

			if err := ipcClient.SetLogLevel(level); err != nil {
				fmt.Printf("Failed to set log level: %v\n", err)
				os.Exit(1)
			}

			fmt.Printf("Log level set to %s successfully\n", level)
			return
		case "debug":
			// Debug commands
			if len(os.Args) < 3 {
				fmt.Println("Usage: agent debug <command> [options]")
				fmt.Println("Commands:")
				fmt.Println("  start [--port=<port>]  Start debug server (default port: 8080)")
				fmt.Println("  stop                   Stop debug server")
				fmt.Println("  status                 Check debug server status")
				fmt.Println("  fullsync <start|end>   Control full synchronization")
				os.Exit(1)
			}

			subCmd := os.Args[2]
			switch subCmd {
			case "start":
				// Parse port if provided
				port := 8080 // Default port
				if len(os.Args) > 3 && strings.HasPrefix(os.Args[3], "--port=") {
					portStr := strings.TrimPrefix(os.Args[3], "--port=")
					if p, err := strconv.Atoi(portStr); err == nil && p > 0 && p < 65536 {
						port = p
					} else {
						fmt.Printf("Invalid port: %s\n", portStr)
						os.Exit(1)
					}
				}

				// Start debug server
				fmt.Printf("Starting debug server on port %d...\n", port)
				result, err := ipcClient.Debug("start", port)
				if err != nil {
					fmt.Printf("Failed to start debug server: %v\n", err)
					os.Exit(1)
				}
				fmt.Println(result)
				return

			case "stop":
				// Stop debug server
				fmt.Println("Stopping debug server...")
				result, err := ipcClient.Debug("stop", 0)
				if err != nil {
					fmt.Printf("Failed to stop debug server: %v\n", err)
					os.Exit(1)
				}
				fmt.Println(result)
				return

			case "status":
				// Check debug server status
				fmt.Println("Checking debug server status...")
				result, err := ipcClient.Debug("status", 0)
				if err != nil {
					fmt.Printf("Failed to get debug server status: %v\n", err)
					os.Exit(1)
				}
				fmt.Println(result)
				return

			case "fullsync":
				// Handle fullsync commands
				if len(os.Args) < 4 {
					fmt.Println("Usage: agent debug fullsync <start|end>")
					os.Exit(1)
				}

				fullsyncCmd := os.Args[3]
				if fullsyncCmd != "start" && fullsyncCmd != "end" {
					fmt.Printf("Invalid fullsync command: %s\n", fullsyncCmd)
					fmt.Println("Usage: agent debug fullsync <start|end>")
					os.Exit(1)
				}

				fmt.Printf("Executing fullsync %s...\n", fullsyncCmd)
				result, err := ipcClient.Debug("fullsync", fullsyncCmd)
				if err != nil {
					fmt.Printf("Failed to execute fullsync %s: %v\n", fullsyncCmd, err)
					os.Exit(1)
				}
				fmt.Println(result)
				return

			default:
				fmt.Printf("Unknown debug command: %s\n", subCmd)
				fmt.Println("Usage: agent debug <command> [options]")
				fmt.Println("Commands:")
				fmt.Println("  start [--port=<port>]  Start debug server (default port: 8080)")
				fmt.Println("  stop                   Stop debug server")
				fmt.Println("  status                 Check debug server status")
				fmt.Println("  fullsync <start|end>   Control full synchronization")
				os.Exit(1)
				return
			}
		}
	}

	// Load configuration
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Create daemon process
	d := daemon.New(*configPath, &cfg.Logging)
	if err := d.Start(); err != nil {
		fmt.Printf("Failed to start agent: %v\n", err)
		os.Exit(1)
	}

	// If this is the parent process, exit immediately
	if d.IsParent() {
		return
	}

	// Create logger
	log, err = logger.NewLogger(cfg.Logging)
	if err != nil {
		fmt.Printf("Failed to create logger: %v\n", err)
		os.Exit(1)
	}
	defer log.Sync()

	// 设置crash信号处理（在logger初始化后）
	setupCrashSignalHandling(log)

	// Shell logger is now passed directly to ExecuteCommand

	log.Info("Starting agent...")
	log.Info("Config loaded successfully", zap.Any("config", cfg))
	log.Info("Version information",
		zap.String("version", Version),
		zap.String("buildTime", BuildTime),
		zap.String("gitBranch", GitBranch),
		zap.String("gitCommit", GitCommit))

	// Create gRPC client
	grpcClient, err = client.New(cfg, log)
	if err != nil {
		log.Error("Failed to create gRPC client", zap.Error(err))
		os.Exit(1)
	}
	defer grpcClient.Close()

	// Set global client instance for other components to access
	client.SetInstance(grpcClient)

	// Start IPC server
	ipcServer = ipc.NewServer(log)
	if err := ipcServer.Start(); err != nil {
		log.Error("Failed to start IPC server", zap.Error(err))
		os.Exit(1)
	}
	defer ipcServer.Stop()

	// Handle signals
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Create a channel for graceful shutdown
	shutdownCh := make(chan struct{})

	// Listen to ShutdownChan
	go func() {
		<-ipc.ShutdownChan
		log.Info("Received shutdown request from IPC client")
		close(shutdownCh)
	}()

	// Wait for signal or shutdown request
	select {
	case <-sigCh:
		log.Info("Received system signal, shutting down...")
	case <-shutdownCh:
		log.Info("Shutting down...")
	}
}
