/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      main.go
 *
 * DESCRIPTION :    Debug client for agent
 *
 * AUTHOR :         wei
 *
 * HISTORY :        04/09/2025  create
 ******************************************************************************/

package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"time"
)

func main() {
	// Parse command line arguments
	configFile := flag.String("config", "", "Path to the configuration file (JSON format)")
	serverAddr := flag.String("server", "http://127.0.0.1:8080", "Debug server address")
	flag.Parse()

	if *configFile == "" {
		fmt.Println("Error: config file is required")
		flag.Usage()
		os.Exit(1)
	}

	// Read configuration file
	configData, err := ioutil.ReadFile(*configFile)
	if err != nil {
		fmt.Printf("Error reading config file: %v\n", err)
		os.Exit(1)
	}

	// 直接使用原始JSON数据，不进行转换
	// 这样可以保留原始的JSON结构，包括字段名称和枚举值
	// 服务器端已经修改为可以处理各种格式的JSON

	// Send configuration to debug server
	url := *serverAddr + "/debug/config"
	fmt.Printf("Sending configuration to %s...\n", url)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Post(url, "application/json", bytes.NewBuffer(configData))
	if err != nil {
		fmt.Printf("Error sending request: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()

	// Read response
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response: %v\n", err)
		os.Exit(1)
	}

	// Format and print response
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, respBody, "", "  "); err != nil {
		fmt.Printf("Response: %s\n", respBody)
	} else {
		fmt.Printf("Response:\n%s\n", prettyJSON.String())
	}

	// Check response status
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("Error: server returned status %d\n", resp.StatusCode)
		os.Exit(1)
	}

	// Parse response content to check task execution results
	var taskResults []struct {
		TxID    string `json:"tx_id"`
		ErrCode int    `json:"err_code"`
		Desc    string `json:"desc"`
	}

	if err := json.Unmarshal(respBody, &taskResults); err != nil {
		fmt.Printf("Warning: Failed to parse response JSON: %v\n", err)
		// If we can't parse the response, assume success (backward compatibility)
		return
	}

	// Check if any task failed
	hasError := false
	for _, result := range taskResults {
		if result.ErrCode != 0 {
			hasError = true
			fmt.Printf("Task failed: %s (error code: %d) - %s\n", result.TxID, result.ErrCode, result.Desc)
		}
	}

	// Exit with error code if any task failed
	if hasError {
		os.Exit(1)
	}
}
