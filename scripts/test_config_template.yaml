# PA环境测试配置模板
# 此配置文件用于在PA系统上进行模块测试

client:
  customer-id: 12345    # 测试客户ID
  client-id: 999        # 测试客户端ID

comms:
  addrs:                # Orchestrator地址列表（测试环境可以为空）
    - "127.0.0.1:50051" # 本地测试地址
  
  # 连接配置
  timeout: 30s          # 连接超时
  retry-interval: 5s    # 重试间隔
  max-retries: 3        # 最大重试次数

logging:
  level: "DEBUG"        # 日志级别（测试时使用DEBUG）
  format: "console"     # 日志格式（控制台输出便于查看）
  outputs:
    - type: "stdout"    # 输出到标准输出
    - type: "file"      # 同时输出到文件
      file: "/tmp/agent_test.log"
      maxSize: 50       # 最大50MB
      maxAge: 7         # 保留7天
      maxBackups: 3     # 最多3个备份
      compress: true    # 压缩旧日志

# 调试配置
debug:
  enabled: true         # 启用调试模式
  listen-addr: "127.0.0.1:8080"  # 调试服务器监听地址
  ipc-socket: "/tmp/agent_debug.sock"  # IPC套接字路径

# 测试特定配置
test:
  # 测试模式标志
  mode: true
  
  # 测试超时配置
  timeouts:
    command: 30s        # 命令执行超时
    sync: 60s          # 同步操作超时
    
  # 测试环境配置
  environment:
    interfaces:         # 可用的测试接口
      - "eth0"
      - "eth1" 
      - "eth2"
    
    # 测试时使用的临时目录
    temp-dir: "/tmp/agent_test"
    
    # 是否在测试后清理配置
    cleanup: true
