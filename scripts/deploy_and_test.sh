#!/bin/bash

# 自动化测试部署和执行脚本
# 用于在真实PA环境中测试各个模块

set -e

# 启用详细执行过程显示
if [[ "${BASH_DEBUG:-}" == "true" ]]; then
    set -x
fi

# 配置变量
PA_HOST="*************"
PA_USER="root"
PA_DEPLOY_DIR="/root/agent"
BUILD_DIR="build/linux_arm64"
TEST_DIR="test"
PACKAGE_NAME="test_package.tar.gz"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}=== [$timestamp] $1 ===${NC}"
}

print_success() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}✓ [$timestamp] $1${NC}"
}

print_error() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}✗ [$timestamp] $1${NC}"
}

print_warning() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}⚠ [$timestamp] $1${NC}"
}

print_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}ℹ [$timestamp] $1${NC}"
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1
    print_error "脚本在第 $line_number 行失败，退出码: $exit_code"
    exit $exit_code
}

# 设置错误陷阱
trap 'handle_error $LINENO' ERR

# 检查SSH连接
check_ssh_connection() {
    print_info "检查SSH连接到 $PA_USER@$PA_HOST..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $PA_USER@$PA_HOST 'echo "SSH连接成功"' >/dev/null 2>&1; then
        print_success "SSH连接正常"
    else
        print_error "SSH连接失败，请检查："
        echo "  1. 网络连接是否正常"
        echo "  2. SSH密钥是否已配置"
        echo "  3. PA系统是否可访问"
        exit 1
    fi
}

# 检查必需文件
check_required_files() {
    print_info "检查必需文件..."
    
    local missing_files=()
    
    # 检查编译后的二进制文件
    if [ ! -f "$BUILD_DIR/agent" ]; then
        missing_files+=("$BUILD_DIR/agent")
    fi
    
    if [ ! -f "$BUILD_DIR/agent-debug-client" ]; then
        missing_files+=("$BUILD_DIR/agent-debug-client")
    fi
    
    # 检查配置文件
    if [ ! -f "config.yaml" ]; then
        missing_files+=("config.yaml")
    fi
    
    # 检查测试目录
    if [ ! -d "$TEST_DIR" ]; then
        missing_files+=("$TEST_DIR/")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "缺少必需文件："
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        print_info "请先运行 'make build-linux-arm64' 编译项目"
        exit 1
    fi
    
    print_success "所有必需文件检查通过"
}

# 创建测试包
create_test_package() {
    print_info "创建测试包..."

    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local package_dir="$temp_dir/agent"

    mkdir -p "$package_dir"

    # 复制文件到临时目录
    cp "$BUILD_DIR/agent" "$package_dir/"
    cp "$BUILD_DIR/agent-debug-client" "$package_dir/"
    cp "config.yaml" "$package_dir/"
    cp -r "$TEST_DIR" "$package_dir/"

    # 复制floweye_tool到测试目录
    if [ -f "tool/floweye_tool/floweye_tool" ]; then
        print_info "复制floweye_tool到测试包..."
        cp "tool/floweye_tool/floweye_tool" "$package_dir/test/"
        chmod +x "$package_dir/test/floweye_tool"
    else
        print_warning "floweye_tool不存在，某些测试可能失败"
    fi

    # 确保测试脚本有执行权限
    chmod +x "$package_dir/$TEST_DIR"/*.sh

    # 创建压缩包
    cd "$temp_dir"
    tar -czf "$PACKAGE_NAME" agent/

    # 移动到原始目录
    local original_dir="$OLDPWD"
    mv "$PACKAGE_NAME" "$original_dir/"
    cd "$original_dir"

    # 清理临时目录
    rm -rf "$temp_dir"

    print_success "测试包创建完成: $PACKAGE_NAME"
}

# 传输测试包到PA系统
transfer_package() {
    print_info "传输测试包到PA系统..."
    local start_time=$(date +%s)

    # 使用base64编码传输（因为PA系统没有scp）
    print_info "编码测试包..."
    if ! base64 -i "$PACKAGE_NAME" -o "$PACKAGE_NAME.b64"; then
        print_error "测试包编码失败"
        return 1
    fi
    local package_size=$(ls -lh "$PACKAGE_NAME" | awk '{print $5}')
    local encoded_size=$(ls -lh "$PACKAGE_NAME.b64" | awk '{print $5}')
    print_info "原始包大小: $package_size, 编码后大小: $encoded_size"

    print_info "传输编码后的文件..."
    if ! cat "$PACKAGE_NAME.b64" | ssh $PA_USER@$PA_HOST "cat > /tmp/$PACKAGE_NAME.b64"; then
        print_error "文件传输失败"
        return 1
    fi

    print_info "在PA系统上解码和部署..."
    if ! ssh $PA_USER@$PA_HOST << EOF
        set -e
        echo "开始解码和部署过程..."

        # 解码文件
        echo "解码文件..."
        base64 -d /tmp/$PACKAGE_NAME.b64 > /tmp/$PACKAGE_NAME
        echo "解码完成，文件大小: \$(ls -lh /tmp/$PACKAGE_NAME | awk '{print \$5}')"

        # 创建部署目录
        echo "创建部署目录: $PA_DEPLOY_DIR"
        mkdir -p $PA_DEPLOY_DIR

        # 备份现有文件（如果存在）
        if [ -d "$PA_DEPLOY_DIR" ]; then
            echo "备份现有文件..."
            rm -rf ${PA_DEPLOY_DIR}.backup
            cp -r $PA_DEPLOY_DIR ${PA_DEPLOY_DIR}.backup 2>/dev/null || true
        fi

        # 部署新文件
        echo "部署新文件到 $PA_DEPLOY_DIR..."
        cd $PA_DEPLOY_DIR
        tar -zxf /tmp/$PACKAGE_NAME --strip-components=1

        # 设置执行权限
        echo "设置执行权限..."
        chmod +x agent agent-debug-client
        chmod +x test/*.sh

        # 停止现有的agent进程
        echo "停止现有的agent进程..."
        ./agent stop 2>/dev/null || echo "agent进程未运行或停止失败"

        # 等待进程完全停止
        sleep 2

        # 启动新的agent进程
        echo "启动新的agent进程..."
        nohup ./agent > agent.log 2>&1 &

        # 等待agent启动
        sleep 3

        # 检查agent状态
        echo "检查agent状态..."
        ./agent status || echo "agent状态检查失败"

        # 清理临时文件
        echo "清理临时文件..."
        rm -f /tmp/$PACKAGE_NAME /tmp/$PACKAGE_NAME.b64

        echo "部署完成到 $PA_DEPLOY_DIR"
        echo "部署目录内容:"
        ls -la $PA_DEPLOY_DIR
        echo "测试目录内容:"
        ls -la $PA_DEPLOY_DIR/test/ | head -10
EOF
    then
        print_error "部署过程失败"
        return 1
    fi

    # 清理本地临时文件
    rm -f "$PACKAGE_NAME" "$PACKAGE_NAME.b64"

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    print_success "测试包传输和部署完成 (耗时: ${duration}秒)"
}

# 清空agent日志
clear_agent_log() {
    print_info "清空agent日志文件..."
    ssh $PA_USER@$PA_HOST << 'EOF'
        echo "清空agent日志文件..."

        # 清空agent日志
        echo "" > /var/log/agent.log 2>/dev/null || true
        echo "" > /tmp/agent_test.log 2>/dev/null || true

        echo "agent日志已清空"
        echo "当前日志文件状态:"
        ls -la /var/log/agent.log /tmp/agent_test.log 2>/dev/null || echo "日志文件不存在或无权限访问"

        # 记录清空时间
        echo "日志清空时间: $(date)" >> /var/log/agent.log 2>/dev/null || true
EOF
    print_success "agent日志清空完成"
}

# 执行测试
execute_test() {
    local module=$1
    local test_script=$2
    local start_time=$(date +%s)

    print_header "执行 $module 模块测试"

    # 清空日志
    clear_agent_log

    print_info "在PA系统上执行测试脚本: $test_script"

    # 执行测试并实时显示输出
    local temp_script="/tmp/run_test_$$.sh"

    # 创建临时执行脚本，使用动态变量替换
    cat > "$temp_script" << EOF
#!/bin/bash
set -e

# 启用详细执行过程显示
set -x

cd $PA_DEPLOY_DIR/test

echo "=== 开始执行 $module 模块测试 ==="
echo "测试开始时间: \$(date)"
echo "当前目录: \$(pwd)"
echo "可用文件:"
ls -la
echo ""

# 检查floweye命令是否可用
if ! command -v floweye &> /dev/null; then
    echo "⚠ 警告: floweye命令不可用，某些测试可能失败"
else
    echo "✓ floweye命令可用"
    echo "floweye版本信息:"
    floweye version 2>/dev/null || echo "无法获取floweye版本"
fi

echo ""
echo "=== 开始执行测试脚本 ==="

# 执行测试脚本
if [ -f "$test_script" ]; then
    echo "执行测试脚本: $test_script"
    echo "脚本内容预览:"
    head -20 "$test_script" | sed 's/^/  /'
    echo ""

    # 确保脚本有执行权限
    chmod +x "$test_script"

    # 执行测试并记录详细输出
    echo "开始执行测试脚本，时间: \$(date)"
    bash -x "$test_script" 2>&1
    test_exit_code=\$?

    echo ""
    echo "=== 测试执行完成 ==="
    echo "测试结束时间: \$(date)"
    echo "退出码: \$test_exit_code"

    # 显示测试结果文件（如果存在）
    echo ""
    echo "=== 测试生成的文件 ==="
    find . -name "*.log" -type f -exec echo "文件: {}" \; -exec ls -la {} \; 2>/dev/null || echo "未找到日志文件"

    # 显示agent日志的最后部分
    echo ""
    echo "=== Agent日志最后50行 ==="
    tail -50 /var/log/agent.log 2>/dev/null || echo "无法读取agent日志"

    exit \$test_exit_code
else
    echo "✗ 错误: 测试脚本 $test_script 不存在"
    echo "当前目录文件列表:"
    ls -la
    exit 1
fi
EOF

    # 传输并执行脚本（使用SSH而不是scp）
    local script_name="run_test_$$.sh"
    print_info "传输测试执行脚本..."
    cat "$temp_script" | ssh $PA_USER@$PA_HOST "cat > /tmp/$script_name"

    print_info "开始执行测试..."
    # 执行测试并保存输出到临时文件
    ssh $PA_USER@$PA_HOST "chmod +x /tmp/$script_name && /tmp/$script_name" | tee /tmp/test_output_$$.log
    local ssh_exit_code=${PIPESTATUS[0]}

    # 将测试输出传输到PA系统保存
    if [ -f "/tmp/test_output_$$.log" ]; then
        print_info "保存测试输出到PA系统..."
        cat "/tmp/test_output_$$.log" | ssh $PA_USER@$PA_HOST "cat > /tmp/test_execution_output.log"
        rm -f "/tmp/test_output_$$.log"
    fi

    # 清理临时文件
    rm -f "$temp_script"
    ssh $PA_USER@$PA_HOST "rm -f /tmp/$script_name" || true

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    if [ $ssh_exit_code -eq 0 ]; then
        print_success "$module 模块测试执行成功 (耗时: ${duration}秒)"
    else
        print_error "$module 模块测试执行失败 (退出码: $ssh_exit_code, 耗时: ${duration}秒)"

        print_info "收集详细错误信息..."
        ssh $PA_USER@$PA_HOST << 'EOF'
            echo "=== 详细错误信息收集 ==="
            echo "当前时间: $(date)"

            echo ""
            echo "=== 测试目录状态 ==="
            cd /root/agent/test 2>/dev/null || cd /root/agent/
            pwd
            ls -la

            echo ""
            echo "=== Agent进程状态 ==="
            ps aux | grep agent | grep -v grep || echo "未找到agent进程"

            echo ""
            echo "=== Agent日志 ==="
            if [ -f "/var/log/agent.log" ]; then
                echo "Agent日志文件大小: $(wc -l /var/log/agent.log)"
                echo "最后100行:"
                tail -100 /var/log/agent.log
            else
                echo "Agent日志文件不存在"
            fi

            echo ""
            echo "=== 系统日志相关 ==="
            dmesg | tail -20 | grep -i error || echo "系统日志无相关错误"

            echo ""
            echo "=== 网络接口状态 ==="
            if command -v floweye >/dev/null 2>&1; then
                echo "当前接口配置:"
                floweye if list 2>/dev/null || echo "无法获取接口列表"
            fi
EOF

        # 立即收集失败时的日志
        print_info "测试失败，立即收集日志进行分析..."
        ./scripts/collect_test_results.sh || true

        exit $ssh_exit_code
    fi
}

# 清理远程文件
cleanup_remote() {
    print_info "清理远程测试文件..."
    ssh $PA_USER@$PA_HOST << EOF
        if [ -d "$PA_DEPLOY_DIR" ]; then
            # 保留日志文件
            mkdir -p ${PA_DEPLOY_DIR}_logs
            cp $PA_DEPLOY_DIR/test/*.log ${PA_DEPLOY_DIR}_logs/ 2>/dev/null || true
            
            # 清理部署目录
            rm -rf $PA_DEPLOY_DIR
            
            echo "远程文件已清理"
            if [ -d "${PA_DEPLOY_DIR}_logs" ]; then
                echo "日志文件保存在: ${PA_DEPLOY_DIR}_logs/"
                ls -la ${PA_DEPLOY_DIR}_logs/
            fi
        fi
EOF
    print_success "远程清理完成"
}

# 显示使用帮助
show_usage() {
    echo "用法: $0 <module> [test_script]"
    echo ""
    echo "参数:"
    echo "  module      - 要测试的模块名称 (如: interface, wan, lan)"
    echo "  test_script - 测试脚本名称 (可选，默认为 {module}_quick_test.sh)"
    echo ""
    echo "示例:"
    echo "  $0 interface"
    echo "  $0 interface interface_comprehensive_test.sh"
    echo "  $0 wan wan_quick_test.sh"
    echo ""
    echo "环境变量:"
    echo "  PA_HOST     - PA系统IP地址 (默认: $PA_HOST)"
    echo "  PA_USER     - SSH用户名 (默认: $PA_USER)"
    echo "  PA_DEPLOY_DIR - 部署目录 (默认: $PA_DEPLOY_DIR)"
}

# 主函数
main() {
    local overall_start_time=$(date +%s)

    # 检查参数
    if [ $# -lt 1 ]; then
        print_error "缺少必需参数"
        show_usage
        exit 1
    fi

    local module=$1
    local test_script=${2:-"${module}_quick_test.sh"}

    print_header "自动化测试部署和执行系统"
    echo "模块: $module"
    echo "测试脚本: $test_script"
    echo "目标系统: $PA_USER@$PA_HOST"
    echo "部署目录: $PA_DEPLOY_DIR"
    echo "详细执行模式: ${BASH_DEBUG:-false}"
    echo ""

    # 执行步骤
    local step_start_time

    step_start_time=$(date +%s)
    check_ssh_connection
    print_info "SSH连接检查耗时: $(($(date +%s) - step_start_time))秒"

    step_start_time=$(date +%s)
    check_required_files
    print_info "文件检查耗时: $(($(date +%s) - step_start_time))秒"

    step_start_time=$(date +%s)
    create_test_package
    print_info "测试包创建耗时: $(($(date +%s) - step_start_time))秒"

    step_start_time=$(date +%s)
    transfer_package
    print_info "文件传输耗时: $(($(date +%s) - step_start_time))秒"

    step_start_time=$(date +%s)
    execute_test "$module" "$test_script"
    print_info "测试执行耗时: $(($(date +%s) - step_start_time))秒"

    local overall_end_time=$(date +%s)
    local total_duration=$((overall_end_time - overall_start_time))

    print_header "测试完成"
    print_success "所有步骤执行成功 (总耗时: ${total_duration}秒)"

    # 自动收集测试结果
    print_info "自动收集测试结果..."
    ./scripts/collect_test_results.sh || print_warning "测试结果收集失败，但测试本身成功"

    # 询问是否清理远程文件
    read -p "是否清理远程测试文件? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_remote
    else
        print_info "远程文件保留在: $PA_USER@$PA_HOST:$PA_DEPLOY_DIR"
        print_info "可以稍后运行 'make test-cleanup-remote' 清理"
    fi
}

# 执行主函数
main "$@"
