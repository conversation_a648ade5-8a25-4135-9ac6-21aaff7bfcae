#!/bin/bash

# 自动化测试系统演示脚本
# 展示完整的测试流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

print_step() {
    echo -e "${BLUE}步骤 $1: $2${NC}"
}

# 检查依赖
check_dependencies() {
    print_header "检查系统依赖"
    
    # 检查Go环境
    if command -v go >/dev/null 2>&1; then
        print_success "Go环境: $(go version)"
    else
        print_error "Go环境未安装"
        exit 1
    fi
    
    # 检查make命令
    if command -v make >/dev/null 2>&1; then
        print_success "Make工具可用"
    else
        print_error "Make工具未安装"
        exit 1
    fi
    
    # 检查SSH
    if command -v ssh >/dev/null 2>&1; then
        print_success "SSH客户端可用"
    else
        print_error "SSH客户端未安装"
        exit 1
    fi
    
    # 检查base64
    if command -v base64 >/dev/null 2>&1; then
        print_success "Base64工具可用"
    else
        print_error "Base64工具未安装"
        exit 1
    fi
}

# 显示系统信息
show_system_info() {
    print_header "自动化测试系统信息"
    
    echo "项目根目录: $(pwd)"
    echo "可用的测试目标:"
    make test-tools 2>/dev/null || echo "  请运行 'make test-tools' 查看详细信息"
    echo ""
    
    echo "测试脚本:"
    ls -la scripts/*.sh 2>/dev/null || echo "  未找到测试脚本"
    echo ""
    
    echo "测试用例文件:"
    ls -la test/test_interface_*.json 2>/dev/null || echo "  未找到Interface测试用例"
    echo ""
}

# 演示编译过程
demo_build() {
    print_step "1" "演示编译过程"
    
    print_info "清理之前的构建..."
    make clean >/dev/null 2>&1 || true
    
    print_info "编译Linux ARM64版本..."
    if make build-linux-arm64; then
        print_success "编译完成"
        
        print_info "检查编译结果:"
        ls -la build/linux_arm64/ 2>/dev/null || print_error "编译文件不存在"
    else
        print_error "编译失败"
        return 1
    fi
}

# 演示环境检查
demo_env_check() {
    print_step "2" "演示环境检查"
    
    print_info "检查PA测试环境连通性..."
    if make test-check-env; then
        print_success "环境检查通过"
    else
        print_error "环境检查失败"
        print_info "这是正常的，如果没有配置PA系统连接"
        return 0
    fi
}

# 演示测试用例
demo_test_cases() {
    print_step "3" "演示测试用例结构"
    
    print_info "Interface模块测试用例:"
    if [ -d "test" ]; then
        echo "快速测试脚本:"
        ls -la test/interface_quick_test.sh 2>/dev/null || echo "  未找到快速测试脚本"
        
        echo "综合测试脚本:"
        ls -la test/interface_comprehensive_test.sh 2>/dev/null || echo "  未找到综合测试脚本"
        
        echo "测试用例JSON文件:"
        ls test/test_interface_*.json 2>/dev/null | head -5 || echo "  未找到测试用例文件"
        
        if ls test/test_interface_*.json >/dev/null 2>&1; then
            local count=$(ls test/test_interface_*.json | wc -l)
            print_success "找到 $count 个Interface测试用例文件"
        fi
    else
        print_error "test目录不存在"
    fi
}

# 演示脚本功能
demo_scripts() {
    print_step "4" "演示测试脚本功能"
    
    print_info "主要测试脚本:"
    
    if [ -f "scripts/deploy_and_test.sh" ]; then
        print_success "部署和测试脚本: scripts/deploy_and_test.sh"
        echo "  功能: 自动化部署和执行测试"
        echo "  用法: ./scripts/deploy_and_test.sh interface"
    else
        print_error "部署和测试脚本不存在"
    fi
    
    if [ -f "scripts/collect_test_results.sh" ]; then
        print_success "结果收集脚本: scripts/collect_test_results.sh"
        echo "  功能: 收集PA系统测试结果"
        echo "  用法: ./scripts/collect_test_results.sh"
    else
        print_error "结果收集脚本不存在"
    fi
    
    if [ -f "scripts/test_config_template.yaml" ]; then
        print_success "测试配置模板: scripts/test_config_template.yaml"
        echo "  功能: PA环境测试配置模板"
    else
        print_error "测试配置模板不存在"
    fi
}

# 演示Makefile目标
demo_makefile_targets() {
    print_step "5" "演示Makefile测试目标"
    
    print_info "可用的测试目标:"
    echo ""
    echo "模块测试:"
    echo "  make test-interface              # Interface模块快速测试"
    echo "  make test-interface-comprehensive # Interface模块综合测试"
    echo "  make test-wan                    # WAN模块测试 (预留)"
    echo "  make test-all                    # 所有模块测试"
    echo ""
    echo "工具目标:"
    echo "  make test-check-env              # 检查测试环境"
    echo "  make test-collect-results        # 收集测试结果"
    echo "  make test-cleanup-remote         # 清理远程环境"
    echo "  make test-tools                  # 显示测试工具"
    echo ""
    
    print_info "环境变量配置:"
    echo "  PA_HOST=*************           # PA系统IP"
    echo "  PA_USER=root                    # SSH用户"
    echo "  PA_DEPLOY_DIR=/root/agent       # 部署目录"
}

# 演示完整流程（模拟）
demo_full_workflow() {
    print_step "6" "演示完整测试流程（模拟）"
    
    print_info "完整的自动化测试流程包括:"
    echo ""
    echo "1. 编译构建 (make build-linux-arm64)"
    echo "   ├── 编译agent二进制文件"
    echo "   └── 编译agent-debug-client"
    echo ""
    echo "2. 打包测试文件"
    echo "   ├── 创建临时目录"
    echo "   ├── 复制二进制文件"
    echo "   ├── 复制测试脚本和用例"
    echo "   └── 创建tar.gz压缩包"
    echo ""
    echo "3. 传输到PA系统"
    echo "   ├── Base64编码压缩包"
    echo "   ├── SSH传输编码文件"
    echo "   └── 在PA系统解码部署"
    echo ""
    echo "4. 执行测试"
    echo "   ├── 检查floweye命令"
    echo "   ├── 执行测试脚本"
    echo "   └── 生成测试日志"
    echo ""
    echo "5. 收集结果"
    echo "   ├── 下载测试日志"
    echo "   ├── 收集系统信息"
    echo "   ├── 生成测试摘要"
    echo "   └── 创建HTML报告"
    
    print_success "流程演示完成"
}

# 显示使用示例
show_usage_examples() {
    print_header "使用示例"
    
    print_info "基本使用:"
    echo "# 检查环境"
    echo "make test-check-env"
    echo ""
    echo "# 执行Interface模块测试"
    echo "make test-interface"
    echo ""
    echo "# 收集测试结果"
    echo "make test-collect-results"
    echo ""
    
    print_info "高级使用:"
    echo "# 使用不同PA系统"
    echo "PA_HOST=************* make test-interface"
    echo ""
    echo "# 执行综合测试"
    echo "make test-interface-comprehensive"
    echo ""
    echo "# 清理远程环境"
    echo "make test-cleanup-remote"
    echo ""
    
    print_info "手动使用脚本:"
    echo "# 直接使用部署脚本"
    echo "./scripts/deploy_and_test.sh interface"
    echo ""
    echo "# 使用自定义测试脚本"
    echo "./scripts/deploy_and_test.sh interface my_custom_test.sh"
    echo ""
    echo "# 收集结果"
    echo "./scripts/collect_test_results.sh"
}

# 主函数
main() {
    print_header "自动化测试系统演示"
    echo "本演示将展示完整的自动化测试系统功能"
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "Makefile" ] || [ ! -d "test" ]; then
        print_error "请在项目根目录运行此演示脚本"
        exit 1
    fi
    
    check_dependencies
    show_system_info
    demo_build
    demo_env_check
    demo_test_cases
    demo_scripts
    demo_makefile_targets
    demo_full_workflow
    show_usage_examples
    
    print_header "演示完成"
    print_success "自动化测试系统已准备就绪"
    echo ""
    print_info "下一步操作:"
    echo "1. 配置SSH密钥到PA系统: ssh-copy-id root@*************"
    echo "2. 检查测试环境: make test-check-env"
    echo "3. 执行Interface测试: make test-interface"
    echo "4. 查看详细文档: docs/AUTOMATED_TESTING.md"
}

# 执行主函数
main "$@"
