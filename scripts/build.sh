#!/bin/bash

# 获取版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "0.0.0")
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S')
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 编译参数
LDFLAGS="-X main.Version=$VERSION -X 'main.BuildTime=$BUILD_TIME' -X main.GitBranch=$GIT_BRANCH -X main.GitCommit=$GIT_COMMIT"

# 创建输出目录
mkdir -p build

# 编译
echo "Building agent..."
go build -ldflags "$LDFLAGS" -o build/agent ./cmd/agent

echo "Build completed: build/agent"
