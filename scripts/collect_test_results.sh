#!/bin/bash

# 测试结果收集脚本
# 用于从PA系统收集测试结果和日志

set -e

# 配置变量
PA_HOST="${PA_HOST:-*************}"
PA_USER="${PA_USER:-root}"
PA_DEPLOY_DIR="${PA_DEPLOY_DIR:-/root/agent}"
LOCAL_RESULTS_DIR="test_results"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# 创建本地结果目录
create_results_dir() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    LOCAL_RESULTS_DIR="test_results_$timestamp"
    
    mkdir -p "$LOCAL_RESULTS_DIR"
    print_success "创建结果目录: $LOCAL_RESULTS_DIR"
}

# 收集测试日志
collect_test_logs() {
    print_info "收集测试日志文件..."

    # 从PA系统下载日志文件和执行信息
    ssh $PA_USER@$PA_HOST << EOF > "$LOCAL_RESULTS_DIR/remote_files.txt"
        echo "=== PA系统上的测试文件 ==="
        if [ -d "$PA_DEPLOY_DIR" ]; then
            echo "部署目录内容:"
            ls -la $PA_DEPLOY_DIR/
            echo ""

            if [ -d "$PA_DEPLOY_DIR/test" ]; then
                echo "测试目录内容:"
                ls -la $PA_DEPLOY_DIR/test/
                echo ""

                echo "日志文件:"
                find $PA_DEPLOY_DIR/test -name "*.log" -exec ls -la {} \; 2>/dev/null || echo "未找到测试日志文件"
            fi
        fi

        if [ -d "${PA_DEPLOY_DIR}_logs" ]; then
            echo "备份日志目录内容:"
            ls -la ${PA_DEPLOY_DIR}_logs/
        fi

        echo ""
        echo "=== Agent日志文件状态 ==="
        if [ -f "/var/log/agent.log" ]; then
            echo "Agent日志文件: /var/log/agent.log"
            ls -la /var/log/agent.log
            echo "文件大小: \$(wc -l /var/log/agent.log | awk '{print \$1}') 行"
        else
            echo "Agent日志文件不存在: /var/log/agent.log"
        fi

        if [ -f "/tmp/agent_test.log" ]; then
            echo "测试日志文件: /tmp/agent_test.log"
            ls -la /tmp/agent_test.log
        else
            echo "测试日志文件不存在: /tmp/agent_test.log"
        fi
EOF

    # 下载agent日志文件
    print_info "下载agent日志文件..."
    if ssh $PA_USER@$PA_HOST "[ -f '/var/log/agent.log' ]" 2>/dev/null; then
        ssh $PA_USER@$PA_HOST "cat /var/log/agent.log" > "$LOCAL_RESULTS_DIR/agent.log"
        print_success "agent.log 下载完成"
    else
        print_info "agent.log 文件不存在或无权限访问"
    fi

    # 下载测试配置日志
    if ssh $PA_USER@$PA_HOST "[ -f '/tmp/agent_test.log' ]" 2>/dev/null; then
        ssh $PA_USER@$PA_HOST "cat /tmp/agent_test.log" > "$LOCAL_RESULTS_DIR/agent_test.log"
        print_success "agent_test.log 下载完成"
    else
        print_info "agent_test.log 文件不存在"
    fi

    # 下载测试目录中的日志文件
    local log_files_found=false

    # 尝试下载当前测试目录中的日志
    if ssh $PA_USER@$PA_HOST "[ -d '$PA_DEPLOY_DIR/test' ]" 2>/dev/null; then
        ssh $PA_USER@$PA_HOST "find $PA_DEPLOY_DIR/test -name '*.log' 2>/dev/null" | while read -r log_file; do
            if [ -n "$log_file" ]; then
                local filename=$(basename "$log_file")
                print_info "下载测试日志文件: $filename"
                ssh $PA_USER@$PA_HOST "cat '$log_file'" > "$LOCAL_RESULTS_DIR/test_$filename"
                log_files_found=true
            fi
        done
    fi

    # 尝试下载备份日志目录中的日志
    if ssh $PA_USER@$PA_HOST "[ -d '${PA_DEPLOY_DIR}_logs' ]" 2>/dev/null; then
        ssh $PA_USER@$PA_HOST "find ${PA_DEPLOY_DIR}_logs -name '*.log' 2>/dev/null" | while read -r log_file; do
            if [ -n "$log_file" ]; then
                local filename="backup_$(basename "$log_file")"
                print_info "下载备份日志文件: $filename"
                ssh $PA_USER@$PA_HOST "cat '$log_file'" > "$LOCAL_RESULTS_DIR/$filename"
                log_files_found=true
            fi
        done
    fi

    # 收集执行过程输出
    print_info "收集测试执行过程..."
    ssh $PA_USER@$PA_HOST << 'EOF' > "$LOCAL_RESULTS_DIR/test_execution_details.txt"
        echo "=== 测试执行详细信息 ==="
        echo "收集时间: $(date)"
        echo ""

        # 首先尝试收集测试执行输出
        if [ -f "/tmp/test_execution_output.log" ]; then
            echo "=== 测试执行输出 ==="
            cat /tmp/test_execution_output.log
            echo ""
        fi

        echo "=== 最近的floweye命令执行 ==="
        if [ -f "/var/log/agent.log" ]; then
            echo "从agent日志中提取floweye命令:"
            grep -i "floweye\|command\|execute" /var/log/agent.log | tail -20 || echo "未找到相关命令记录"
        fi

        echo ""
        echo "=== 当前接口状态 ==="
        if command -v floweye >/dev/null 2>&1; then
            echo "所有接口状态:"
            floweye if list 2>/dev/null || echo "无法获取接口列表"

            echo ""
            echo "eth2接口详细信息:"
            floweye if get eth2 2>/dev/null || echo "无法获取eth2接口信息"
        else
            echo "floweye命令不可用"
        fi

        echo ""
        echo "=== Agent进程信息 ==="
        ps aux | grep agent | grep -v grep || echo "未找到agent进程"

        echo ""
        echo "=== 最近的系统错误 ==="
        dmesg | tail -10 | grep -i error || echo "无系统错误"
EOF

    print_success "测试日志收集完成"
}

# 收集系统信息
collect_system_info() {
    print_info "收集PA系统信息..."
    
    ssh $PA_USER@$PA_HOST << 'EOF' > "$LOCAL_RESULTS_DIR/system_info.txt"
        echo "=== PA系统信息 ==="
        echo "主机名: $(hostname)"
        echo "系统时间: $(date)"
        echo "系统版本: $(uname -a)"
        echo ""
        
        echo "=== 网络接口信息 ==="
        if command -v floweye >/dev/null 2>&1; then
            echo "floweye接口列表:"
            floweye if list 2>/dev/null || echo "无法获取floweye接口信息"
        else
            echo "floweye命令不可用"
        fi
        echo ""
        
        echo "=== 系统接口信息 ==="
        ip link show 2>/dev/null || ifconfig -a 2>/dev/null || echo "无法获取系统接口信息"
        echo ""
        
        echo "=== 进程信息 ==="
        ps aux | grep -E "(agent|floweye)" | grep -v grep || echo "未找到相关进程"
        echo ""
        
        echo "=== 磁盘空间 ==="
        df -h 2>/dev/null || echo "无法获取磁盘信息"
        echo ""
        
        echo "=== 内存信息 ==="
        free -h 2>/dev/null || echo "无法获取内存信息"
EOF
    
    print_success "系统信息收集完成"
}

# 分析测试结果
analyze_test_results() {
    print_info "分析测试结果..."

    local summary_file="$LOCAL_RESULTS_DIR/test_summary.txt"

    {
        echo "=== 测试结果摘要 ==="
        echo "收集时间: $(date)"
        echo "PA系统: $PA_USER@$PA_HOST"
        echo ""

        # 分析日志文件
        local total_tests=0
        local passed_tests=0
        local failed_tests=0

        # 首先尝试从测试执行详情中提取统计信息
        if [ -f "$LOCAL_RESULTS_DIR/test_execution_details.txt" ]; then
            if grep -q "总测试数:" "$LOCAL_RESULTS_DIR/test_execution_details.txt"; then
                total_tests=$(grep "总测试数:" "$LOCAL_RESULTS_DIR/test_execution_details.txt" | tail -1 | sed 's/.*总测试数: *\([0-9]*\).*/\1/')
                passed_tests=$(grep "通过测试:" "$LOCAL_RESULTS_DIR/test_execution_details.txt" | tail -1 | sed 's/.*通过测试: *\([0-9]*\).*/\1/')
                failed_tests=$(grep "失败测试:" "$LOCAL_RESULTS_DIR/test_execution_details.txt" | tail -1 | sed 's/.*失败测试: *\([0-9]*\).*/\1/')
                echo "=== 从测试执行详情中提取统计信息 ==="
                echo "  总测试数: $total_tests"
                echo "  通过测试: $passed_tests"
                echo "  失败测试: $failed_tests"
                echo ""
            fi
        fi

        # 如果没有找到统计信息，尝试从测试执行详情中提取实际结果
        if [ $total_tests -eq 0 ]; then
            # 从test_execution_details.txt中查找测试脚本的实际输出
            if [ -f "$LOCAL_RESULTS_DIR/test_execution_details.txt" ]; then
                # 查找测试脚本中的TOTAL_TESTS和FAILED_TESTS变量
                local script_total=$(grep -o "TOTAL_TESTS=[0-9]*" "$LOCAL_RESULTS_DIR/test_execution_details.txt" | tail -1 | cut -d= -f2)
                local script_failed=$(grep -o "FAILED_TESTS=[0-9]*" "$LOCAL_RESULTS_DIR/test_execution_details.txt" | tail -1 | cut -d= -f2)

                # 查找测试失败的明确标记
                local explicit_failures=$(grep -c "print_error.*失败\|✗.*失败" "$LOCAL_RESULTS_DIR/test_execution_details.txt")

                if [ -n "$script_total" ] && [ "$script_total" -gt 0 ]; then
                    total_tests=$script_total
                    failed_tests=${script_failed:-$explicit_failures}
                    passed_tests=$((total_tests - failed_tests))
                    echo "=== 从测试脚本实际输出提取统计 ==="
                    echo "  总测试数: $total_tests"
                    echo "  通过测试: $passed_tests"
                    echo "  失败测试: $failed_tests"
                    echo ""
                elif [ $explicit_failures -gt 0 ]; then
                    # 如果找到明确的失败标记，但没有总数，则标记为失败
                    total_tests=$explicit_failures
                    failed_tests=$explicit_failures
                    passed_tests=0
                    echo "=== 检测到测试失败 ==="
                    echo "  检测到失败测试: $failed_tests"
                    echo "  状态: 测试执行失败"
                    echo ""
                fi
            fi

            # 如果仍然没有找到，才使用Agent日志推断（但标记为不准确）
            if [ $total_tests -eq 0 ] && [ -f "$LOCAL_RESULTS_DIR/agent.log" ]; then
                # 查找Agent任务处理的成功模式
                local task_success=$(grep -c '"desc": "success"' "$LOCAL_RESULTS_DIR/agent.log")
                local task_errors=$(grep -c '"err_code": [1-9]' "$LOCAL_RESULTS_DIR/agent.log")

                if [ $task_success -gt 0 ] || [ $task_errors -gt 0 ]; then
                    total_tests=$task_success
                    passed_tests=$task_success
                    failed_tests=$task_errors
                    echo "=== 从agent.log推断任务处理统计（仅供参考） ==="
                    echo "  推断成功任务: $task_success"
                    echo "  推断失败任务: $task_errors"
                    echo "  ⚠️ 注意: 这是基于Agent任务处理的推断，不代表测试脚本的实际验证结果"
                    echo "  ⚠️ 实际测试可能因为验证逻辑失败而与此统计不符"
                    echo ""
                fi
            fi
        fi

        for log_file in "$LOCAL_RESULTS_DIR"/*.log; do
            if [ -f "$log_file" ]; then
                local filename=$(basename "$log_file")
                echo "=== 分析日志文件: $filename ==="

                # 分析测试日志
                if [[ "$filename" == test_* ]]; then
                    # 提取测试统计信息
                    if grep -q "总测试数:" "$log_file"; then
                        local file_total=$(grep "总测试数:" "$log_file" | tail -1 | sed 's/.*总测试数: *\([0-9]*\).*/\1/')
                        local file_passed=$(grep "通过测试:" "$log_file" | tail -1 | sed 's/.*通过测试: *\([0-9]*\).*/\1/')
                        local file_failed=$(grep "失败测试:" "$log_file" | tail -1 | sed 's/.*失败测试: *\([0-9]*\).*/\1/')

                        echo "  总测试数: $file_total"
                        echo "  通过测试: $file_passed"
                        echo "  失败测试: $file_failed"

                        # 只有在之前没有找到统计信息时才累加
                        if [ $total_tests -eq 0 ]; then
                            total_tests=$((total_tests + file_total))
                            passed_tests=$((passed_tests + file_passed))
                            failed_tests=$((failed_tests + file_failed))
                        fi
                    fi

                    # 提取错误信息
                    if grep -q "✗" "$log_file"; then
                        echo "  错误信息:"
                        grep "✗" "$log_file" | head -5 | sed 's/^/    /'
                    fi
                fi

                # 分析agent日志
                if [[ "$filename" == "agent.log" ]]; then
                    echo "  Agent日志分析:"
                    local log_lines=$(wc -l < "$log_file")
                    echo "    总行数: $log_lines"

                    # 统计错误和警告
                    local error_count=$(grep -i "error" "$log_file" | wc -l)
                    local warning_count=$(grep -i "warning\|warn" "$log_file" | wc -l)
                    echo "    错误数量: $error_count"
                    echo "    警告数量: $warning_count"

                    # 提取最近的错误
                    if [ $error_count -gt 0 ]; then
                        echo "    最近的错误:"
                        grep -i "error" "$log_file" | tail -3 | sed 's/^/      /'
                    fi

                    # 分析floweye命令执行
                    local floweye_count=$(grep -i "floweye\|command.*if\|execute.*if" "$log_file" | wc -l)
                    echo "    floweye命令执行次数: $floweye_count"

                    # 查找特定的问题模式
                    if grep -q "name.*empty\|missing.*name\|invalid.*name" "$log_file"; then
                        echo "    发现name字段相关问题:"
                        grep -i "name.*empty\|missing.*name\|invalid.*name" "$log_file" | tail -2 | sed 's/^/      /'
                    fi
                fi

                echo ""
            fi
        done

        # 分析执行详情
        if [ -f "$LOCAL_RESULTS_DIR/test_execution_details.txt" ]; then
            echo "=== 执行过程分析 ==="
            echo "详细执行信息已收集到: test_execution_details.txt"

            # 提取关键信息
            if grep -q "floweye if get eth2" "$LOCAL_RESULTS_DIR/test_execution_details.txt"; then
                echo "最终接口状态:"
                grep -A 10 "floweye if get eth2" "$LOCAL_RESULTS_DIR/test_execution_details.txt" | head -5 | sed 's/^/  /'
            fi
            echo ""
        fi

        echo "=== 总体统计 ==="
        echo "总测试数: $total_tests"
        echo "通过测试: $passed_tests"
        echo "失败测试: $failed_tests"

        if [ $total_tests -gt 0 ]; then
            local success_rate=$((passed_tests * 100 / total_tests))
            echo "成功率: ${success_rate}%"
        fi

        if [ $failed_tests -eq 0 ]; then
            echo "状态: 全部通过 ✓"
        else
            echo "状态: 有失败测试 ✗"
            echo ""
            echo "=== 失败分析建议 ==="
            echo "1. 检查 agent.log 中的错误信息"
            echo "2. 查看 test_execution_details.txt 了解执行过程"
            echo "3. 验证floweye命令是否正确执行"
            echo "4. 检查配置验证逻辑是否符合预期"
        fi

    } > "$summary_file"

    print_success "测试结果分析完成"
}

# 生成测试报告
generate_report() {
    print_info "生成测试报告..."

    local report_file="$LOCAL_RESULTS_DIR/test_report.html"

    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Agent模块测试报告</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; }
        .log-content {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
        }
        .section { margin: 25px 0; }
        .section h2 {
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 5px;
        }
        .section h3 {
            color: #6c757d;
            margin-top: 20px;
        }
        .stats {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .collapsible {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            padding: 10px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 14px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .collapsible:hover { background-color: #0056b3; }
        .content {
            padding: 0;
            display: none;
            overflow: hidden;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-top: none;
        }
        .content.show { display: block; }
    </style>
    <script>
        function toggleContent(element) {
            var content = element.nextElementSibling;
            content.classList.toggle('show');
            element.textContent = content.classList.contains('show') ?
                element.textContent.replace('▶', '▼') :
                element.textContent.replace('▼', '▶');
        }
    </script>
</head>
<body>
    <div class="header">
        <h1>🧪 Agent模块测试报告</h1>
        <p><strong>生成时间:</strong> $(date)</p>
        <p><strong>PA系统:</strong> $PA_USER@$PA_HOST</p>
        <p><strong>测试类型:</strong> Interface模块自动化测试</p>
    </div>

    <div class="section">
        <h2>📊 测试摘要</h2>
        <div class="log-content">$(cat "$LOCAL_RESULTS_DIR/test_summary.txt" 2>/dev/null || echo "无摘要信息")</div>
    </div>
EOF

    # 添加执行过程详情
    if [ -f "$LOCAL_RESULTS_DIR/test_execution_details.txt" ]; then
        cat >> "$report_file" << EOF

    <div class="section">
        <h2>🔍 测试执行详情</h2>
        <button class="collapsible" onclick="toggleContent(this)">▶ 点击查看详细执行过程</button>
        <div class="content">
            <div class="log-content">$(cat "$LOCAL_RESULTS_DIR/test_execution_details.txt")</div>
        </div>
    </div>
EOF
    fi

    # 添加Agent日志分析
    if [ -f "$LOCAL_RESULTS_DIR/agent.log" ]; then
        local agent_log_size=$(wc -l < "$LOCAL_RESULTS_DIR/agent.log")
        local error_count=$(grep -i "error" "$LOCAL_RESULTS_DIR/agent.log" | wc -l)
        local warning_count=$(grep -i "warning\|warn" "$LOCAL_RESULTS_DIR/agent.log" | wc -l)

        cat >> "$report_file" << EOF

    <div class="section">
        <h2>📋 Agent日志分析</h2>
        <div class="stats">
            <strong>日志统计:</strong> 总行数: $agent_log_size | 错误: $error_count | 警告: $warning_count
        </div>

        <h3>🔴 错误信息</h3>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看错误详情</button>
        <div class="content">
            <div class="log-content">$(grep -i "error" "$LOCAL_RESULTS_DIR/agent.log" | tail -20 || echo "无错误信息")</div>
        </div>

        <h3>⚠️ 警告信息</h3>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看警告详情</button>
        <div class="content">
            <div class="log-content">$(grep -i "warning\|warn" "$LOCAL_RESULTS_DIR/agent.log" | tail -10 || echo "无警告信息")</div>
        </div>

        <h3>🔧 Floweye命令执行</h3>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看命令执行记录</button>
        <div class="content">
            <div class="log-content">$(grep -i "floweye\|command.*if\|execute.*if" "$LOCAL_RESULTS_DIR/agent.log" | tail -15 || echo "无命令执行记录")</div>
        </div>

        <h3>📄 完整Agent日志</h3>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看完整日志 (最后200行)</button>
        <div class="content">
            <div class="log-content">$(tail -200 "$LOCAL_RESULTS_DIR/agent.log")</div>
        </div>
    </div>
EOF
    fi

    cat >> "$report_file" << EOF

    <div class="section">
        <h2>💻 系统信息</h2>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看系统详情</button>
        <div class="content">
            <div class="log-content">$(cat "$LOCAL_RESULTS_DIR/system_info.txt" 2>/dev/null || echo "无系统信息")</div>
        </div>
    </div>

    <div class="section">
        <h2>📁 远程文件状态</h2>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看文件列表</button>
        <div class="content">
            <div class="log-content">$(cat "$LOCAL_RESULTS_DIR/remote_files.txt" 2>/dev/null || echo "无文件信息")</div>
        </div>
    </div>
EOF

    # 添加其他日志文件内容
    for log_file in "$LOCAL_RESULTS_DIR"/*.log; do
        if [ -f "$log_file" ]; then
            local filename=$(basename "$log_file")
            # 跳过已经处理的agent.log
            if [[ "$filename" != "agent.log" ]]; then
                cat >> "$report_file" << EOF

    <div class="section">
        <h2>📝 $(echo "$filename" | sed 's/_/ /g' | sed 's/\.log//')</h2>
        <button class="collapsible" onclick="toggleContent(this)">▶ 查看 $filename</button>
        <div class="content">
            <div class="log-content">$(cat "$log_file")</div>
        </div>
    </div>
EOF
            fi
        fi
    done

    cat >> "$report_file" << EOF

    <div class="section">
        <h2>🔧 问题诊断建议</h2>
        <div class="stats">
            <h3>如果测试失败，请检查以下项目：</h3>
            <ul>
                <li><strong>Agent日志错误:</strong> 查看上方Agent日志中的错误信息</li>
                <li><strong>Floweye命令:</strong> 确认floweye命令是否正确执行</li>
                <li><strong>配置验证:</strong> 检查配置验证逻辑是否符合预期</li>
                <li><strong>网络接口:</strong> 确认eth0、eth1、eth2接口状态</li>
                <li><strong>权限问题:</strong> 检查agent是否有足够权限执行floweye命令</li>
            </ul>

            <h3>常见问题解决方案：</h3>
            <ul>
                <li><strong>name字段验证问题:</strong> 检查protobuf验证逻辑和agent处理流程</li>
                <li><strong>接口配置失败:</strong> 确认floweye命令语法和参数正确性</li>
                <li><strong>权限不足:</strong> 确保agent以适当权限运行</li>
            </ul>
        </div>
    </div>

    <footer style="margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 4px; text-align: center; color: #6c757d;">
        <p>报告生成时间: $(date) | 自动化测试系统 v1.0</p>
    </footer>
</body>
</html>
EOF

    print_success "测试报告生成完成: $report_file"
}

# 显示结果摘要
show_summary() {
    print_header "测试结果收集完成"
    
    echo "结果目录: $LOCAL_RESULTS_DIR"
    echo "包含文件:"
    ls -la "$LOCAL_RESULTS_DIR/"
    echo ""
    
    if [ -f "$LOCAL_RESULTS_DIR/test_summary.txt" ]; then
        echo "测试摘要:"
        cat "$LOCAL_RESULTS_DIR/test_summary.txt"
    fi
    
    echo ""
    print_info "可以通过以下方式查看详细结果:"
    echo "  - 查看摘要: cat $LOCAL_RESULTS_DIR/test_summary.txt"
    echo "  - 查看报告: open $LOCAL_RESULTS_DIR/test_report.html"
    echo "  - 查看日志: ls $LOCAL_RESULTS_DIR/*.log"
}

# 主函数
main() {
    print_header "收集PA系统测试结果"
    
    create_results_dir
    collect_test_logs
    collect_system_info
    analyze_test_results
    generate_report
    show_summary
}

# 执行主函数
main "$@"
