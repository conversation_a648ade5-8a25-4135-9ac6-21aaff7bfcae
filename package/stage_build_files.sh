#!/bin/bash

#
# Prepare Application Package Files (stage_build_files.sh)
#
# This script is the core of the application packaging process. It processes
# source code and resource files into the final, deployment-ready format
# and places them in the `build/` directory.
#
# It performs the following four main steps:
#
# 1. Prepare Build Environment:
#    - Clean and create the `build/` directory structure.
#    - Read the application version from the command-line arguments.
#
# 2. Process and Copy Files:
#    - Copy binary files and static assets (`bin/`, `app.png`, `web/html/UniSASE.svg`).
#    - Update `app.inf` with the version number and convert its encoding to GB2312.
#    - Convert `appenv.conf` and other shell scripts to GB2312.
#    - Convert web assets (`.html`, `.css`, `.js`, CGI scripts) to GB2312.
#    - Process `config/` files, stripping any non-ASCII characters.
#
# 3. Set File Permissions:
#    - Add execute permissions (a+x) for binary files, CGI scripts, and shell scripts.
#    - Set standard read/write permissions (644) for configuration files, web assets,
#      and other non-executable files like `app.inf`, `appenv.conf`, and `app.png`.
#
# 4. (Optional) Verify Encoding:
#    - If `chardetect` is installed, verify that all text files have been correctly
#      converted to their target encodings (GB2312 or ASCII).
#
#

echo "--- Starting to prepare package files ---"

# --- Base Path Settings ---
BASE_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
BUILD_DIR="${BASE_DIR}/build"
WEB_DIR="${BASE_DIR}/web"
CONFIG_DIR="${BASE_DIR}/config"
BUILD_BIN_DIR="${BUILD_DIR}/bin"
BUILD_WEB_DIR="${BUILD_DIR}/web"
BUILD_CONFIG_DIR="${BUILD_DIR}/config"
BUILD_WEB_CGI_DIR="${BUILD_WEB_DIR}/cgi"
BUILD_WEB_HTML_DIR="${BUILD_WEB_DIR}/html"

# --- File List Definitions ---
FILES_TO_CONVERT=(
    "appctrl"
    "monitor.sh"
    "preinstall"
    "afterinstall"
    "preuninstall"
    "afteruninstall"
    "install_floweye.sh"
    "uninstall_floweye.sh"
)
BINARY_FILES_TO_COPY=(
    "app.png"
    "bin"
)
WEB_FILES_TO_COPY=(
    "html/UniSASE.svg"
)
EXECUTABLE_SCRIPTS=(
    "appctrl"
    "monitor.sh"
    "preinstall"
    "afterinstall"
    "preuninstall"
    "afteruninstall"
)

# --- Environment Check ---
if ! command -v iconv >/dev/null 2>&1; then
    echo "Error: 'iconv' command is missing. Cannot perform file encoding conversion."
    exit 1
fi

# --- Get VERSION from the first argument ---
APP_VERSION=$1
if [ -z "${APP_VERSION}" ]; then
    echo "Error: Version argument is missing. Please provide the version as the first argument."
    exit 1
fi
echo "--> Application version set to: ${APP_VERSION}"

# --- 1. Create Target Directories ---
echo "--> Preparing build output directory..."
mkdir -p "${BUILD_DIR}" "${BUILD_WEB_DIR}" "${BUILD_CONFIG_DIR}" "${BUILD_WEB_CGI_DIR}" "${BUILD_WEB_HTML_DIR}"

# --- 2. Update app.inf version and convert encoding ---
echo "--> Processing and converting app.inf..."
APP_INF_SRC="${BASE_DIR}/app.inf"
APP_INF_DEST="${BUILD_DIR}/app.inf"
if [ -f "${APP_INF_SRC}" ]; then
    # Replace app_version in a temporary file, preserving the quotes
    sed "s/^app_version=\".*\"/app_version=\"${APP_VERSION}\"/" "${APP_INF_SRC}" > "${APP_INF_SRC}.tmp"
    # Convert encoding from the temporary file to the final destination
    iconv -f UTF-8 -t GB2312//IGNORE "${APP_INF_SRC}.tmp" > "${APP_INF_DEST}"

    # Remove the temporary file
    rm "${APP_INF_SRC}.tmp"
    echo "  Updated app_version to ${APP_VERSION} and converted app.inf to GB2312."
else
    echo "  Warning: Source file not found, skipping: ${APP_INF_SRC}"
fi

# --- 2.1 Convert appenv.conf encoding ---
echo "--> Processing and converting appenv.conf..."
APPENV_CONF_SRC="${BASE_DIR}/appenv.conf"
APPENV_CONF_DEST="${BUILD_DIR}/appenv.conf"
if [ -f "${APPENV_CONF_SRC}" ]; then
    iconv -f UTF-8 -t GB2312//IGNORE "${APPENV_CONF_SRC}" > "${APPENV_CONF_DEST}"
    echo "  Converted appenv.conf to GB2312."
else
    echo "  Warning: Source file not found, skipping: ${APPENV_CONF_SRC}"
fi


# --- 2.2 Copy Binary Files and Directories ---
echo "--> Copying binary files..."
for item in "${BINARY_FILES_TO_COPY[@]}"; do
    src_path="${BASE_DIR}/${item}"
    if [ -e "${src_path}" ]; then
        echo "  Copying: ${item} -> build/"
        cp -a "${src_path}" "${BUILD_DIR}/"
    else
        echo "  Warning: Source file or directory not found, skipping: ${src_path}"
    fi
done

# Copy web files that don't need encoding conversion
echo "--> Copying static web files..."
for item in "${WEB_FILES_TO_COPY[@]}"; do
    src_path="${WEB_DIR}/${item}"
    dest_path="${BUILD_WEB_DIR}/${item}"
    if [ -f "${src_path}" ]; then
        echo "  Copying: web/${item} -> build/web/${item}"
        mkdir -p "$(dirname "${dest_path}")"
        cp "${src_path}" "${dest_path}"
    else
        echo "  Warning: Source file not found, skipping: ${src_path}"
    fi
done

# --- 2.3 Convert Text File Encoding ---
echo "--> Converting text file encoding..."
# Convert files in the root directory
for file in "${FILES_TO_CONVERT[@]}"; do
    src_path="${BASE_DIR}/${file}"
    dest_path="${BUILD_DIR}/${file}"
    if [ -f "${src_path}" ]; then
        echo "  Converting: ${file} -> build/${file}"
        iconv -f UTF-8 -t GB2312//IGNORE "${src_path}" > "${dest_path}"
    else
        echo "  Warning: Source file not found, skipping: ${src_path}"
    fi
done

# Convert files in web/html/ and web/cgi/ directories
if [ -d "${WEB_DIR}/html" ]; then
    find "${WEB_DIR}/html" -type f \( -name "*.html" -o -name "*.css" -o -name "*.js" \) | while read -r src_path; do
        relative_path="${src_path#${WEB_DIR}/}"
        dest_path="${BUILD_WEB_DIR}/${relative_path}"
        mkdir -p "$(dirname "${dest_path}")"
        echo "  Converting: web/${relative_path} -> build/web/${relative_path}"
        iconv -f UTF-8 -t GB2312//IGNORE "${src_path}" > "${dest_path}"
    done
fi

if [ -d "${WEB_DIR}/cgi" ]; then
    find "${WEB_DIR}/cgi" -type f | while read -r src_path; do
        relative_path="${src_path#${WEB_DIR}/}"
        dest_path="${BUILD_WEB_DIR}/${relative_path}"
        mkdir -p "$(dirname "${dest_path}")"
        echo "  Converting: web/${relative_path} -> build/web/${relative_path}"
        iconv -f UTF-8 -t GB2312//IGNORE "${src_path}" > "${dest_path}"
    done
fi

# Process files in config/ directory, stripping non-ASCII characters
if [ -d "${CONFIG_DIR}" ]; then
    find "${CONFIG_DIR}" -type f | while read -r src_path; do
        relative_path="${src_path#${CONFIG_DIR}/}"
        dest_path="${BUILD_CONFIG_DIR}/${relative_path}"
        mkdir -p "$(dirname "${dest_path}")"
        echo "  Processing: config/${relative_path} -> build/config/${relative_path} (stripping non-ASCII)"
        iconv -f UTF-8 -t ASCII//IGNORE "${src_path}" > "${dest_path}"
    done
fi

# --- 3. Set File Permissions ---
echo "--> Setting file permissions..."
# Set executable permissions (a+x)
echo "  Setting executable permissions (a+x)..."
if [ -d "${BUILD_BIN_DIR}" ]; then
    echo "    - All files under build/bin/"
    chmod -R a+x "${BUILD_BIN_DIR}"
fi
if [ -d "${BUILD_WEB_CGI_DIR}" ]; then
    echo "    - All files under build/web/cgi/"
    chmod -R a+x "${BUILD_WEB_CGI_DIR}"
fi
for script in "${EXECUTABLE_SCRIPTS[@]}"; do
    if [ -f "${BUILD_DIR}/${script}" ]; then
        echo "    - build/${script}"
        chmod a+x "${BUILD_DIR}/${script}"
    fi
done

# Set to 644 permissions
echo "  Setting standard read/write permissions (644)..."
if [ -d "${BUILD_CONFIG_DIR}" ]; then
    echo "    - All files under build/config/"
    chmod -R 644 "${BUILD_CONFIG_DIR}"
fi
if [ -d "${BUILD_WEB_HTML_DIR}" ]; then
    echo "    - All files under build/web/html/"
    chmod -R 644 "${BUILD_WEB_HTML_DIR}"
fi

# Set 644 permissions for specific files in the build root
if [ -f "${BUILD_DIR}/app.inf" ]; then
    echo "    - build/app.inf"
    chmod 644 "${BUILD_DIR}/app.inf"
fi
if [ -f "${BUILD_DIR}/appenv.conf" ]; then
    echo "    - build/appenv.conf"
    chmod 644 "${BUILD_DIR}/appenv.conf"
fi
if [ -f "${BUILD_DIR}/app.png" ]; then
    echo "    - build/app.png"
    chmod 644 "${BUILD_DIR}/app.png"
fi

echo ""
echo "All file processing complete!"
echo "Packaged files are ready: ${BUILD_DIR}"
echo ""

# --- 4. Encoding Verification ---
echo "---> Starting file encoding verification..."
if ! command -v chardetect >/dev/null 2>&1; then
    echo "Warning: 'chardetect' command not found, skipping encoding verification."
else
    echo "Using 'chardetect' to verify the encoding of converted text files..."

    # Verify files in the root directory
    GB2312_FILES_TO_VERIFY=("app.inf" "appenv.conf" "${FILES_TO_CONVERT[@]}")
    for file in "${GB2312_FILES_TO_VERIFY[@]}"; do
        file_path="${BUILD_DIR}/${file}"
        if [ -f "${file_path}" ]; then
            detected_encoding=$(chardetect "${file_path}" | awk '{print $2}' | tr '[:lower:]' '[:upper:]')
            if [[ "${detected_encoding}" == "GB2312" || "${detected_encoding}" == "GB18030" || "${detected_encoding}" == "ASCII" ]]; then
                echo "  [SUCCESS] $(basename "${file_path}"): ${detected_encoding}"
            else
                echo "  [FAILURE] $(basename "${file_path}"): Detected ${detected_encoding} (should be GB2312/ASCII)"
            fi
        fi
    done

    # Verify files in web/html/ and web/cgi/ directories
    if [ -d "${BUILD_WEB_HTML_DIR}" ]; then
        find "${BUILD_WEB_HTML_DIR}" -type f \( -name "*.html" -o -name "*.css" -o -name "*.js" \) | while read -r file_path; do
            relative_path="${file_path#${BUILD_DIR}/}"
            detected_encoding=$(chardetect "${file_path}" | awk '{print $2}' | tr '[:lower:]' '[:upper:]')
            if [[ "${detected_encoding}" == "GB2312" || "${detected_encoding}" == "GB18030" || "${detected_encoding}" == "ASCII" ]]; then
                echo "  [SUCCESS] ${relative_path}: ${detected_encoding}"
            else
                echo "  [FAILURE] ${relative_path}: Detected ${detected_encoding} (should be GB2312/ASCII)"
            fi
        done
    fi
    if [ -d "${BUILD_WEB_CGI_DIR}" ]; then
        find "${BUILD_WEB_CGI_DIR}" -type f | while read -r file_path; do
            relative_path="${file_path#${BUILD_DIR}/}"
            detected_encoding=$(chardetect "${file_path}" | awk '{print $2}' | tr '[:lower:]' '[:upper:]')
            if [[ "${detected_encoding}" == "GB2312" || "${detected_encoding}" == "GB18030" || "${detected_encoding}" == "ASCII" ]]; then
                echo "  [SUCCESS] ${relative_path}: ${detected_encoding}"
            else
                echo "  [FAILURE] ${relative_path}: Detected ${detected_encoding} (should be GB2312/ASCII)"
            fi
        done
    fi

    # Verify files in config/ directory
    if [ -d "${BUILD_CONFIG_DIR}" ]; then
        find "${BUILD_CONFIG_DIR}" -type f | while read -r file_path; do
            relative_path="${file_path#${BUILD_DIR}/}"
            detected_encoding=$(chardetect "${file_path}" | awk '{print $2}' | tr '[:lower:]' '[:upper:]')
            if [[ "${detected_encoding}" == "ASCII" ]]; then
                echo "  [SUCCESS] ${relative_path}: ${detected_encoding}"
            else
                echo "  [FAILURE] ${relative_path}: Detected ${detected_encoding} (should be ASCII)"
            fi
        done
    fi
fi
echo "--- All tasks completed ---"
