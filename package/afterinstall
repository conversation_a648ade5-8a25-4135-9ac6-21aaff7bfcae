#!/bin/bash

# UniSASE Agent Post-installation Script
# Executed after installation to perform configuration initialization

echo "Executing UniSASE Agent post-installation configuration..."

# Environment Variables
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
. "${SCRIPT_DIR}/appenv.conf"

# Create runtime directory
echo "Creating runtime directory..."
mkdir -p "${RAM_ROOT_DIR}"
chmod 755 "${RAM_ROOT_DIR}"

# Set binary file permissions
echo "Setting file permissions..."
if [ -f "${AGENT_BIN}" ]; then
    chmod +x "${AGENT_BIN}"
fi

# Prepare config file
# If old config file exists, just keep it, and we don't need to copy the default config.
if [ ! -f "${AGENT_CONF_FILE}" ]; then
    echo "Creating default config file..."
    mkdir -p "${APP_CONF_DIR}"
    cp "${APP_ROOT_DIR}/config/config.yaml" "${AGENT_CONF_FILE}"
fi

# Set config file permissions
if [ -f "${AGENT_CONF_FILE}" ]; then
    chmod 644 "${AGENT_CONF_FILE}"
fi

# Create log directory
if [ ! -d "${APP_LOG_DIR}" ]; then
    mkdir -p "${APP_LOG_DIR}"
fi

if [ ! -d "${AGENT_DEFAULT_LOG_DIR}" ]; then
    mkdir -p "${AGENT_DEFAULT_LOG_DIR}"
fi

# Set Web interface permissions
if [ -d "${RAM_ROOT_DIR}/web" ]; then
    find "${RAM_ROOT_DIR}/web" -type f -name "*.html" -exec chmod 644 {} \;
    find "${RAM_ROOT_DIR}/web" -type f -name "*.css" -exec chmod 644 {} \;
    find "${RAM_ROOT_DIR}/web" -type f -name "*.js" -exec chmod 644 {} \;
    find "${RAM_ROOT_DIR}/web" -type d -exec chmod 755 {} \;
    # Set CGI script permissions
    find "${RAM_ROOT_DIR}/web/cgi" -type f -exec chmod +x {} \;
fi

# Copy Web files to ramdisk webroot
if [ -d "${RAM_ROOT_DIR}/web" ]; then
    echo "Copying Web files to ramdisk..."
    # Create target directories
    mkdir -p "${WEBROOT}/html/App/${APP_NAME}"
    mkdir -p "${WEBROOT}/cgi-bin/App/${APP_NAME}"

    # Copy HTML files
    if [ -d "${RAM_ROOT_DIR}/web/html" ]; then
        cp -a "${RAM_ROOT_DIR}/web/html"/* "${WEBROOT}/html/App/${APP_NAME}/"
    fi

    # Copy CGI scripts
    if [ -d "${RAM_ROOT_DIR}/web/cgi" ]; then
        cp -a "${RAM_ROOT_DIR}/web/cgi"/* "${WEBROOT}/cgi-bin/App/${APP_NAME}/"
        # Ensure CGI scripts have execute permissions
        chmod +x "${WEBROOT}/cgi-bin/App/${APP_NAME}"/*
    fi
fi

# Set monitor script permissions
if [ -f "${MONITOR_SCRIPT}" ]; then
    chmod +x "${MONITOR_SCRIPT}"
fi

# Run the floweye installation script
if [ -f "${APP_ROOT_DIR}/install_floweye.sh" ]; then
    echo "Running install_floweye.sh..."
    "${APP_ROOT_DIR}"/install_floweye.sh
fi

# Remove unnecessary files in ramdisk directory
sync

# Remove agent-debug-client from ramdisk
rm -f "${RAM_ROOT_DIR}/bin/agent-debug-client"

# Remove web files from ramdisk
rm -rf "${RAM_ROOT_DIR}/web"

# Remove configuration file from ramdisk
rm -rf "${RAM_ROOT_DIR}/config"

# Remove install & uninstall floweye scripts from ramdisk
rm -f "${RAM_ROOT_DIR}/install_floweye.sh"
rm -f "${RAM_ROOT_DIR}/uninstall_floweye.sh"

echo "Post-installation configuration completed."
exit 0