#!/bin/bash

# UniSASE Agent Post-uninstallation Script
# Executed after uninstallation to perform post-cleanup operations

echo "Executing UniSASE Agent post-uninstallation cleanup..."

# Environment Variables
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
. "${SCRIPT_DIR}/appenv.conf"

# Run the floweye uninstallation script
if [ -f "${APP_ROOT_DIR}/uninstall_floweye.sh" ]; then
    echo "Running uninstall_floweye.sh..."
    "${APP_ROOT_DIR}"/uninstall_floweye.sh
fi

# Clean up Web files from ramdisk
echo "Cleaning up Web files..."
if [ -d "${WEBROOT}/html/App/${APP_NAME}" ]; then
  rm -rf "${RAMDISK}/admin/html/App/${APP_NAME}"
fi

if [ -d "${WEBROOT}/cgi-bin/App/${APP_NAME}" ]; then
  rm -rf "${RAMDISK}/admin/cgi-bin/App/${APP_NAME}"
fi

# Clean up ramdisk files
echo "Cleaning up runtime files..."
if [ -d "${RAM_ROOT_DIR}" ]; then
    rm -rf "${RAM_ROOT_DIR}"
fi

# Clean up configuration files
echo "Cleaning up configuration files..."
if [ -d "${APP_CONF_DIR}" ]; then
    rm -rf "${APP_CONF_DIR}"
fi

# Clean up log files (optional, user may want to keep them)
# rm -f "${AGENT_DEFAULT_LOG_DIR}"
rm -rf "${APP_LOG_DIR}"


echo "Post-uninstallation cleanup completed."
echo "UniSASE Agent has been completely uninstalled."
exit 0