#!/bin/bash

# UniSASE Agent Application Control Script
# Supports start, stop, enable, disable, status commands

# Environment Variables
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
. "${SCRIPT_DIR}/appenv.conf"

# Create necessary directories
mkdir -p "${RAM_ROOT_DIR}"
mkdir -p "${APP_LOG_DIR}"

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "${APPCTRL_LOG_FILE}"
}

# Log an error message and also print it to stderr
log_error() {
    log_message "ERROR: $1"
    echo "Error: $1" >&2
}

# Setup core dump configuration
setup_core_dump() {
    log_message "Setting up core dump configuration..."

    # Set core dump file size to unlimited
    ulimit -c unlimited

    # Create core dump directories
    mkdir -p /var/core
    mkdir -p /var/log

    # Set core dump filename format (including process name and PID)
    if [ -w /proc/sys/kernel/core_pattern ]; then
        echo "/var/core/core.%e.%p.%t" > /proc/sys/kernel/core_pattern 2>/dev/null || true
    fi

    # Set directory permissions
    chmod 755 /var/core
    chmod 755 /var/log

    log_message "Core dump configuration complete: ulimit -c $(ulimit -c)"
    log_message "Core dump files will be saved to: /var/core/"
    log_message "Crash/Panic logs will be saved to: /var/log/agent_crash.log and /var/log/agent_panic.log"
}

# Check if agent binary exists
check_binary() {
    if [ ! -f "${AGENT_BIN}" ]; then
        log_error "agent binary not found: ${AGENT_BIN}"
        return 1
    fi
    if [ ! -x "${AGENT_BIN}" ]; then
        log_error "agent binary is not executable: ${AGENT_BIN}"
        return 1
    fi
    return 0
}

# Check if config file exists
check_config() {
    if [ ! -f "${AGENT_CONF_FILE}" ]; then
        log_error "config file not found: ${AGENT_CONF_FILE}"
        return 1
    fi
    return 0
}

# Get agent process ID
get_agent_pid() {
    # Find running agent process, matching config file path
    pgrep -f "${AGENT_BIN}.*${AGENT_CONF_FILE}" 2>/dev/null | head -1
}

# Check if agent process is running
is_agent_running() {
    local pid=$(get_agent_pid)
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Get monitor script process ID
get_monitor_pid() {
    if [ -f "${MONITOR_PID_FILE}" ]; then
        local pid=$(cat "${MONITOR_PID_FILE}")
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            echo "$pid"
        else
            rm -f "${MONITOR_PID_FILE}"
            echo ""
        fi
    else
        echo ""
    fi
}

# Check if monitor script is running
is_monitor_running() {
    local pid=$(get_monitor_pid)
    [ -n "$pid" ]
}

# Start monitor script
start_monitor() {
    if is_monitor_running; then
        log_message "Monitor script is already running."
        return 0
    fi

    if [ ! -f "${MONITOR_SCRIPT}" ]; then
        log_message "Warning: monitor script not found: ${MONITOR_SCRIPT}"
        return 1
    fi

    # Start monitor script
    nohup "${MONITOR_SCRIPT}" > "${APPCTRL_LOG_FILE}" 2>&1 &
    local monitor_pid=$!
    echo $monitor_pid > "${MONITOR_PID_FILE}"
    log_message "Monitor script started (PID: $monitor_pid)."
}

# Stop monitor script
stop_monitor() {
    local monitor_pid=$(get_monitor_pid)
    if [ -n "$monitor_pid" ]; then
        kill -TERM "$monitor_pid" 2>/dev/null
        rm -f "${MONITOR_PID_FILE}"
        log_message "Monitor script stopped."
    fi
}

# Start unisase agent application
unisase_agent_start() {
    log_message "Starting UniSASE Agent..."

    # Setup core dump configuration
    setup_core_dump

    # Check binary and config file
    if ! check_binary || ! check_config; then
        return 1
    fi

    # Check if already running
    if is_agent_running; then
        local pid=$(get_agent_pid)
        log_message "UniSASE Agent is already running (PID: $pid)."
        return 0
    fi

    # Start agent
    cd "${APP_ROOT_DIR}"
    nohup "${AGENT_BIN}" -config "${AGENT_CONF_FILE}" >> "${APPCTRL_LOG_FILE}" 2>&1 &

    # Wait a moment to check if started successfully
    sleep 2
    if is_agent_running; then
        local pid=$(get_agent_pid)
        log_message "UniSASE Agent started successfully (PID: $pid)."

        # Start monitor script
        start_monitor
        return 0
    else
        log_error "Failed to start UniSASE Agent."
        return 1
    fi
}

# Stop unisase agent application
unisase_agent_stop() {
    log_message "Stopping UniSASE Agent..."

    # Stop monitor script first
    stop_monitor

    local pid=$(get_agent_pid)
    if [ -z "$pid" ]; then
        log_message "UniSASE Agent is not running."
        return 0
    fi

    # Send TERM signal
    if kill -TERM "$pid" 2>/dev/null; then
        # Wait for process to terminate
        local count=0
        while [ $count -lt 10 ] && kill -0 "$pid" 2>/dev/null; do
            sleep 1
            count=$((count + 1))
        done

        # If still not terminated, force kill
        if kill -0 "$pid" 2>/dev/null; then
            log_message "Force stopping UniSASE Agent..."
            kill -KILL "$pid" 2>/dev/null
        fi

        log_message "UniSASE Agent stopped."
        return 0
    else
        log_error "Failed to stop UniSASE Agent."
        return 1
    fi
}

# Restart unisase agent application
unisase_agent_restart() {
    unisase_agent_stop
    sleep 1
    unisase_agent_start
}

# Enable unisase agent application
unisase_agent_enable() {
    unisase_agent_start
}

# Disable unisase agent application
unisase_agent_disable() {
    unisase_agent_stop
}

# Get unisase agent application status
unisase_agent_status() {
    local agent_running=false
    local monitor_running=false

    if is_agent_running; then
        local pid=$(get_agent_pid)
        log_message "Status: UniSASE Agent is running (PID: $pid)."
        agent_running=true
    else
        log_message "Status: UniSASE Agent is not running."
    fi

    if is_monitor_running; then
        local monitor_pid=$(get_monitor_pid)
        log_message "Status: Monitor script is running (PID: $monitor_pid)."
        monitor_running=true
    else
        log_message "Status: Monitor script is not running."
    fi

    if $agent_running; then
        echo "enable"
        return 0
    else
        echo "disable"
        return 1
    fi
}

main() {
    case "$1" in
        start)
            unisase_agent_start
            ;;
        stop)
            unisase_agent_stop
            ;;
        restart)
            unisase_agent_restart
            ;;
        enable)
            unisase_agent_enable
            ;;
        disable)
            unisase_agent_disable
            ;;
        status)
            unisase_agent_status
            ;;
        *)
            echo "unknown action!"
            ;;
    esac
}

# Usage: ./appctrl {start|stop|restart|enable|disable|status}"
#           start   - Start unisase agent application"
#           stop    - Stop unisase agent application"
#           restart - Restart unisase agent application"
#           enable  - Enable unisase agent application"
#           disable - isable unisase agent application"
#           status  - Get unisase agent application status"
main "$@"