# UniSASE Agent 打包说明

## 概述

本文档说明如何将 UniSASE Agent 打包成第三方系统可安装的应用格式（.apk）。

## 打包要求

### 1. 目标格式
- 最终生成 `.apk` 格式的压缩包（实际为 tar.gz 格式，使用 .apk 扩展名）
- 符合第三方系统的安装规范

### 2. 目录结构
```
unisase_agent-1.0.0.apk
├── app.inf                 # 应用信息文件（必需）
├── appctrl                 # 应用控制脚本（必需）
├── preinstall             # 预安装脚本（可选）
├── afterinstall           # 后安装脚本（可选）
├── preuninstall           # 预卸载脚本（可选）
├── afteruninstall         # 后卸载脚本（可选）
├── bin/                   # 二进制文件目录
│   ├── agent              # 主程序
│   └── agent-debug-client # 调试客户端
├── config/                # 配置文件目录
│   └── config.yaml        # 配置文件
├── html/                  # Web界面文件
│   ├── index.html         # 配置管理界面
│   ├── style.css          # 样式文件
│   └── script.js          # JavaScript脚本
└── cgi-bin/               # CGI脚本目录
    └── config.cgi         # 配置管理CGI脚本
```

## 使用方法

### 1. 编译和打包
```bash
# 编译并打包
make package

# 仅打包（需要先编译）
make package-prepare package-copy-files package-set-permissions package-create-archive

# 验证打包结果
make package-verify

# 清理打包文件
make package-clean
```

### 2. 环境变量
打包过程中使用以下环境变量：
- `PGPATH=/usr/panabit` - 主安装目录
- `RAMDISK=/usr/ramdisk` - 运行时目录

### 3. 生成的文件
- `unisase_agent-1.0.0.apk` - 最终的安装包

## Web配置界面

### 功能特性
1. **配置参数管理**
   - 客户端配置（客户ID、客户端ID）
   - 通信配置（服务器地址列表）
   - 日志配置（级别、格式、文件路径等）

2. **用户界面**
   - 响应式设计，支持移动设备
   - 实时参数验证
   - 友好的错误提示
   - 配置加载/保存/重置功能

3. **数据验证**
   - 客户ID和客户端ID必须为正整数
   - 服务器地址格式验证（host:port）
   - 日志配置参数范围验证
   - YAML格式验证

### 访问方式
安装后可通过以下方式访问Web配置界面：
- 直接访问：`/usr/ramdisk/admin/html/App/unisase_agent/index.html`
- CGI接口：`/usr/ramdisk/admin/cgi-bin/App/unisase_agent/config.cgi`

## 应用控制脚本

### 支持的命令
```bash
# 启动应用
appctrl start

# 停止应用
appctrl stop

# 重启应用
appctrl restart

# 启用开机自启动
appctrl enable

# 禁用开机自启动
appctrl disable

# 查询运行状态
appctrl status
```

### 脚本功能
- 自动检查二进制文件和配置文件
- 进程管理（启动、停止、状态检查）
- PID文件管理
- 日志文件管理
- 权限检查

## 安装流程

### 1. 预安装检查
- 检查操作系统和架构兼容性
- 验证目录权限
- 检查磁盘空间

### 2. 文件复制
- 复制到 `${PGPATH}/app/${app_name}/` 目录
- 复制到 `${RAMDISK}/app/${app_name}/` 目录
- 复制Web文件到相应目录

### 3. 后安装配置
- 设置文件权限
- 创建运行时目录
- 初始化配置

### 4. 启动应用
- 调用 `appctrl start` 启动应用

## 卸载流程

### 1. 停止应用
- 调用 `appctrl stop` 停止应用

### 2. 预卸载清理
- 备份配置文件
- 清理运行时资源

### 3. 删除文件
- 删除应用目录
- 删除Web相关目录

### 4. 后卸载清理
- 清理临时文件
- 清理运行时目录

## 测试验证

### 1. 功能测试
使用 `package/test_web_interface.html` 进行Web界面功能测试：
- 配置加载测试
- 配置保存测试
- 数据验证测试
- 地址管理测试

### 2. 安装测试
在目标系统上测试完整的安装/卸载流程：
```bash
# 解压测试
tar -tzf unisase_agent-1.0.0.apk

# 权限检查
tar -xzf unisase_agent-1.0.0.apk
ls -la appctrl preinstall afterinstall

# 脚本测试
./preinstall
./appctrl status
```

## 注意事项

1. **权限要求**
   - 所有脚本文件必须具有可执行权限
   - 配置文件权限设置为644
   - 二进制文件权限设置为755

2. **依赖要求**
   - Python3（用于YAML/JSON转换）
   - 标准Linux工具（tar, chmod, mkdir等）

3. **兼容性**
   - 支持Linux ARM64架构
   - 兼容标准的第三方系统安装规范

4. **安全考虑**
   - 配置文件备份机制
   - 权限最小化原则
   - 错误处理和日志记录

## 故障排除

### 常见问题
1. **打包失败**
   - 检查编译是否成功
   - 确认所有必需文件存在
   - 验证文件权限

2. **Web界面无法访问**
   - 检查CGI脚本权限
   - 确认Python3可用性
   - 验证配置文件路径

3. **应用启动失败**
   - 检查配置文件格式
   - 确认二进制文件权限
   - 查看日志文件

### 调试方法
```bash
# 检查打包内容
tar -tzf unisase_agent-1.0.0.apk

# 测试脚本
./appctrl status
./preinstall

# 检查权限
ls -la bin/ config/ html/

# 验证配置
python3 -c "import yaml; yaml.safe_load(open('config/config.yaml'))"
```
