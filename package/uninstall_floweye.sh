#!/bin/bash

# Set path variables
FLOWEYE_DIR="/usr/ramdisk/bin"
FLOWEYE_ORIG="$FLOWEYE_DIR/floweye"
FLOWEYE_REAL="$FLOWEYE_DIR/floweye.real"

# Restore the original floweye
if [ -f "$FLOWEYE_REAL" ]; then
  echo "Restoring original floweye"
  mv "$FLOWEYE_REAL" "$FLOWEYE_ORIG"
else
  echo "Error: Backup file $FLOWEYE_REAL not found, cannot restore original floweye."
  exit 1
fi

echo "Uninstallation complete: All changes have been reverted."
