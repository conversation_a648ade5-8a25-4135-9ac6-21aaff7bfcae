<!DOCTYPE html>
<html>
<head>
    <title>UniSASE Agent 配置管理</title>
    <link rel="stylesheet" href="../../../html/assert/css/layui.css">
    <style>
        /* 全局样式调整 */
        html {
            overflow-y: scroll; /* 强制滚动条始终可见，防止页面跳动 */
        }
        body {
            background-color: #f2f2f2;
            padding-bottom: 80px;
        }
        .page-container {
            padding: 20px;
        }

        /* 品牌化页头 */
        .page-header {
            background-color: #fff;
            padding: 15px 25px;
            margin: -20px -20px 25px -20px;
            box-shadow: 0 4px 10px -5px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e6e6e6;
        }
        .logo-placeholder {
            height: 40px;
            margin-right: 20px;
        }
        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #009688;
            letter-spacing: 0.5px;
        }

        /* 主题色 */
        .layui-btn-normal,
        .layui-tab-brief>.layui-tab-title .layui-this {
            background-color: #009688;
            color: #fff;
        }
        .layui-tab-brief>.layui-tab-title .layui-this:after {
            border-bottom-color: #009688;
        }
        .layui-form-switch em {
            background-color: #009688 !important;
        }
        .layui-input:focus, .layui-textarea:focus {
            border-color: #009688 !important;
        }
        .layui-btn-primary {
            border-color: #009688;
            color: #009688;
        }
        .layui-btn-primary:hover {
            background-color: #f0fdf9;
        }
        .layui-btn-blue {
            background-color: #1E9FFF;
            color: #fff;
        }
        .layui-btn-blue:hover { opacity: 0.9; color: #fff; }

        /* 自定义 Layer 皮肤 */
        .layer-skin-danger .layui-layer-title { background-color: #FF5722; color: #fff; border: none; }
        .layer-skin-danger .layui-layer-btn .layui-layer-btn0 { border-color: #FF5722; background-color: #FF5722; color: #fff; }
        .layer-skin-blue .layui-layer-title { background-color: #1E9FFF; color: #fff; border: none; }
        .layer-skin-blue .layui-layer-btn .layui-layer-btn0 { border-color: #1E9FFF; background-color: #1E9FFF; color: #fff; }
        .layer-skin-green .layui-layer-title { background-color: #009688; color: #fff; border: none; }
        .layer-skin-green .layui-layer-btn .layui-layer-btn0 { border-color: #009688; background-color: #009688; color: #fff; }
        .layer-skin-yellow .layui-layer-title { background-color: #FFB800; color: #fff; border: none; }
        .layer-skin-yellow .layui-layer-btn .layui-layer-btn0 { border-color: #FFB800; background-color: #FFB800; color: #fff; }

        /* 卡片美化 */
        .layui-card {
            border-radius: 4px;
            transition: all 0.3s ease;
            margin-bottom: 25px;
            border: 1px solid #e6e6e6;
            box-shadow: none;
        }
        .layui-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transform: translateY(-2px);
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            background-color: #fafafa;
            border-radius: 4px 4px 0 0;
            padding: 12px 20px;
            border-bottom: 1px solid #e6e6e6;
        }
        .layui-card-header .layui-icon {
            margin-right: 8px;
            color: #009688;
            font-weight: normal;
        }

        .config-card .layui-form-label { width: 100px; }
        .config-card .layui-input-block { margin-left: 130px; }
        .modal-padding { padding: 20px 25px; }

        /* 系统状态微件样式 */
        .status-block {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            display: flex;
            align-items: center;
            transition: all .3s ease;
            min-height: 90px;
        }
        .status-block:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.06);
            transform: translateY(-3px);
        }
        .status-block .status-icon {
            font-size: 38px;
            margin-right: 20px;
            transition: color .3s ease;
            line-height: 1;
        }
        .status-block .status-content {
            width: 100%;
        }
        .status-block .status-content .status-value {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            line-height: 1.5;
            margin-bottom: 4px;
            transition: color .3s ease;
        }
        .status-block .status-content .status-label {
            color: #999;
            font-size: 13px;
        }

        /* 服务器状态表格 */
        .server-status-table {
            width: 100%;
            font-size: 14px;
            margin: 0;
        }
        .server-status-table th {
            text-align: left;
            font-weight: 500;
            color: #666;
            padding: 4px 0;
        }
        .server-status-table td {
            padding: 6px 0;
            border-top: 1px solid #f2f2f2;
            transition: color .3s ease;
        }

        /* 状态化颜色系统 */
        .status-ok .status-icon, .status-ok .status-value { color: #009688 !important; }
        .status-ok .layui-badge { background-color: #009688 !important; }
        .status-warning .status-icon,
        .status-warning .status-value { color: #FFB800 !important; }
        .status-error .status-icon,
        .status-error .status-value { color: #FF5722 !important; }
        .layui-badge.layui-bg-red { background-color: #FF5722 !important; }

        /* 新增：主机可达状态的深灰色样式 */
        .layui-badge.layui-bg-gray { background-color: #666 !important; color: #fff !important; }

        /* TLS配置区域 */
        #tls-config-section {
            display: none;
            margin-top: 5px;
        }

        /* 兼容性样式 */
        .status-good { color: #009688; font-weight: bold; }
        .status-bad { color: #FF5722; font-weight: bold; }
        .status-warning { color: #FFB800; font-weight: bold; }
    </style>
</head>
<body>

<div class="page-container">
    <div class="page-header">
        <img src="../../../html/App/unisase_agent/UniSASE.svg" alt="Logo" class="logo-placeholder">
        <h1 class="main-title">UniSASE Agent 配置管理</h1>
    </div>

    <div class="layui-tab layui-tab-brief" lay-filter="main-tab">
        <ul class="layui-tab-title">
            <li class="layui-this" lay-id="config-tab">客户端配置</li>
            <li lay-id="status-tab">系统状态</li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <form class="layui-form" action="" id="config-form" lay-filter="config-form">
                    <!-- 卡片1: 客户端基本信息 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-user"></i>客户端基本信息配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="customer_id" class="layui-input" data-tips="客户的唯一标识符，用于系统识别">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户端ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="client_id" class="layui-input" data-tips="客户端的唯一标识符，范围 1-65535">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片2: 服务器通信配置 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-website"></i>服务器通信配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">服务器地址</label>
                                <div class="layui-input-block" id="server-list-container">
                                    <!-- 服务器地址列表将在这里动态生成 -->
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="add-server-btn"><i class="layui-icon layui-icon-add-1"></i> 添加地址</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片3: TLS 安全配置 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-auz"></i>TLS 加密设置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item" pane>
                                <label class="layui-form-label">启用TLS</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="tls_enabled" lay-skin="switch" lay-text="开启|关闭" lay-filter="tls-switch" data-tips="是否启用 TLS 加密保护通信安全">
                                </div>
                            </div>
                            <div id="tls-config-section">
                                <hr>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">证书目录</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="tls_cert_dir" class="layui-input" data-tips="证书文件存放目录">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">证书文件</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="tls_cert_file" class="layui-input" data-tips="服务器证书文件名">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">私钥文件</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="tls_key_file" class="layui-input" data-tips="服务器私钥文件名">
                                    </div>
                                </div>
                                <div class="layui-form-item" pane>
                                    <label class="layui-form-label">其他选项</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="tls_skip_verify" title="跳过服务器证书验证" lay-skin="primary" data-tips="适用于自签名证书，生产环境建议关闭">
                                        <input type="checkbox" name="tls_auto_generate" title="自动生成自签名证书" lay-skin="primary" data-tips="如果证书文件不存在，自动生成自签名证书">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片4: 日志配置 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-log"></i>日志配置</div>
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">日志级别</label>
                                        <div class="layui-input-block">
                                            <select name="log_level" data-tips="选择日志输出的详细程度">
                                                <option value="DEBUG">DEBUG</option>
                                                <option value="INFO">INFO</option>
                                                <option value="WARN">WARN</option>
                                                <option value="ERROR">ERROR</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">日志格式</label>
                                        <div class="layui-input-block">
                                            <select name="log_format" data-tips="选择日志输出格式">
                                                <option value="json">JSON</option>
                                                <option value="text">TEXT</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="layui-form-item">
                                <label class="layui-form-label">日志文件</label>
                                <div class="layui-input-block">
                                    <input type="text" name="log_file" class="layui-input" data-tips="日志文件的完整路径">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最大文件大小</label>
                                <div class="layui-input-block">
                                    <div class="layui-input-group">
                                        <input type="number" name="log_max_size" class="layui-input" data-tips="单个日志文件的最大大小">
                                        <span class="layui-input-group-addon">MB</span>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">保留天数</label>
                                <div class="layui-input-block">
                                    <div class="layui-input-group">
                                        <input type="number" name="log_max_age" class="layui-input" data-tips="日志文件保留的最大天数">
                                        <span class="layui-input-group-addon">天</span>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">备份文件数</label>
                                <div class="layui-input-block">
                                    <input type="number" name="log_max_backups" class="layui-input" data-tips="保留的备份日志文件数量">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">压缩旧文件</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="log_compress" lay-skin="switch" lay-text="启用|禁用" data-tips="是否压缩旧的日志文件以节省空间">
                                </div>
                            </div>
                            <hr>
                            <div class="layui-form-item">
                                <label class="layui-form-label">控制台输出</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="log_console_stderr" lay-skin="switch" lay-text="开启|关闭" data-tips="是否将日志输出到控制台的标准错误流">
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="layui-tab-item">
                <!-- 系统状态监控 -->
                <div id="status-widget-container">
                    <!-- 第一行：Agent运行状态微件、日志文件大小微件 -->
                    <div class="layui-row layui-col-space15" style="margin-bottom: 15px;">
                        <div class="layui-col-md8" data-widget="agent-status">
                            <div class="status-block">
                                <i class="layui-icon status-icon layui-icon-engine"></i>
                                <div class="status-content">
                                    <div class="status-value">加载中...</div>
                                    <div class="status-label">Agent运行状态</div>
                                    <div class="status-actions" style="margin-top: 8px; display: none;">
                                        <!-- 操作按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4" data-widget="log-size">
                            <div class="status-block">
                                <i class="layui-icon status-icon layui-icon-log"></i>
                                <div class="status-content">
                                    <div class="status-value">加载中...</div>
                                    <div class="status-label">日志文件大小</div>
                                    <div class="status-actions" style="margin-top: 8px; display: none;">
                                        <!-- 操作按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第二行：配置文件状态微件、最后修改时间微件、系统版本微件 -->
                    <div class="layui-row layui-col-space15" style="margin-bottom: 15px;">
                        <div class="layui-col-md4" data-widget="config-file">
                            <div class="status-block">
                                <i class="layui-icon status-icon layui-icon-file"></i>
                                <div class="status-content">
                                    <div class="status-value">加载中...</div>
                                    <div class="status-label">配置文件状态</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4" data-widget="last-modified">
                            <div class="status-block">
                                <i class="layui-icon status-icon layui-icon-date"></i>
                                <div class="status-content">
                                    <div class="status-value">加载中...</div>
                                    <div class="status-label">最后修改时间</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4" data-widget="version">
                            <div class="status-block">
                                <i class="layui-icon status-icon layui-icon-release"></i>
                                <div class="status-content">
                                    <div class="status-value">加载中...</div>
                                    <div class="status-label">系统版本</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第三行：服务器连接状态微件（占满整行） -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md8" data-widget="server-connection">
                            <div class="status-block">
                                <i class="layui-icon status-icon layui-icon-website"></i>
                                <div class="status-content">
                                    <div class="status-value">加载中...</div>
                                    <div class="status-label">服务器连接状态</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局固定操作按钮栏 -->
    <div class="layui-footer" style="position: fixed; bottom: 0; left:0; right:0; padding: 10px 20px; text-align: right; box-shadow: 0 -2px 5px rgba(0,0,0,.05); background-color:#fff;">
        <button type="button" class="layui-btn layui-btn-primary" id="load-config-btn">加载配置</button>
        <button class="layui-btn layui-btn-normal" id="save-config-btn">保存配置</button>
        <button type="button" id="reset-btn" class="layui-btn layui-btn-danger">重置</button>
        <button type="button" id="backup-btn" class="layui-btn layui-btn-blue">配置备份与恢复</button>
    </div>
</div>

<!-- 用于备份管理弹窗的HTML内容模板 -->
<div id="backup-modal-content" style="display: none;">
    <div class="modal-padding">
        <p class="layui-text">共 <span class="layui-badge layui-bg-green" id="backup-count">0</span> 个备份文件，系统自动保留最新的10个备份。</p>
        <hr>
        <table class="layui-table" lay-skin="line" id="backup-table">
            <colgroup>
                <col>
                <col width="180">
                <col width="100">
                <col width="150">
            </colgroup>
            <thead>
            <tr>
                <th>备份文件</th>
                <th>创建时间</th>
                <th>文件大小</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody id="backup-table-body">
                <!-- 备份列表将在这里动态生成 -->
            </tbody>
        </table>
    </div>
</div>

<script src="../../../html/assert/layui.all.js"></script>
<script>
    layui.use(['form', 'layer', 'element'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
        var element = layui.element;

        // API端点
        var api_main = '../../../cgi-bin/App/unisase_agent/ajax_agent_main';
        var api_status = '../../../cgi-bin/App/unisase_agent/ajax_agent_status';

        // TLS开关交互
        form.on('switch(tls-switch)', function(data){
            if(data.elem.checked) {
                $('#tls-config-section').slideDown(200);
            } else {
                $('#tls-config-section').slideUp(200);
            }
        });

        // 服务器地址动态增删
        var serverRowTemplate = function(value) {
            return `
            <div class="layui-input-group" style="margin-top: 10px;">
                <input type="text" value="${value || ''}" class="layui-input server-addr-input" placeholder="主机名:端口号" data-tips="服务器地址格式：主机名:端口号，支持域名和IP地址">
                <span class="layui-input-group-btn">
                    <button type="button" class="layui-btn layui-btn-danger delete-server-btn">删除</button>
                </span>
            </div>`;
        };

        $('#add-server-btn').on('click', function() {
            var newRow = $(serverRowTemplate());
            if ($('#server-list-container').children().length === 0) {
                newRow.css('margin-top', '0');
            }
            $('#server-list-container').append(newRow);
            transferTips(); // 重新绑定提示
        });

        $(document).on('click', '.delete-server-btn', function() {
            var target = this;
            layer.confirm('确定要删除此服务器地址吗？', {
                title:'删除确认',
                icon: 2,
                skin: 'layer-skin-danger'
            }, function(index){
                $(target).closest('.layui-input-group').remove();
                layer.close(index);
            });
        });

        // 悬浮提示
        function transferTips() {
            $('select[data-tips]').each(function(){
                $(this).next('.layui-form-select').attr('data-tips', $(this).attr('data-tips'));
            });
            $('input[type=checkbox][data-tips]').each(function(){
                $(this).next('.layui-form-checkbox, .layui-form-switch').attr('data-tips', $(this).attr('data-tips'));
            });
        }
        transferTips();

        $(document).on('mouseenter', '[data-tips]', function(){
            layer.tips($(this).attr('data-tips'), this, {
                tips: [1, '#009688'],
                time: 0
            });
        }).on('mouseleave', '[data-tips]', function(){
            layer.closeAll('tips');
        });

        // 加载配置 - 添加参数控制提示显示
        window.loadConfig = function(showSuccessMsg) {
            // 默认显示成功提示，除非明确传入 false
            if (showSuccessMsg === undefined) showSuccessMsg = true;

            layer.load(1);

            $.ajax({
                url: api_main,
                type: 'POST',
                data: {action: 'load_config'},
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');

                    if (res.code == "0") {
                        var data = res.data;

                        // 填充表单数据 - 修复布尔值字段映射
                        form.val('config-form', {
                            customer_id: data.customer_id,
                            client_id: data.client_id,
                            tls_enabled: data.tls_enabled === true || data.tls_enabled === 'true',
                            tls_cert_dir: data.tls_cert_dir,
                            tls_cert_file: data.tls_cert_file,
                            tls_key_file: data.tls_key_file,
                            tls_skip_verify: data.tls_skip_verify === true || data.tls_skip_verify === 'true',
                            tls_auto_generate: data.tls_auto_generate === true || data.tls_auto_generate === 'true',
                            log_level: data.log_level,
                            log_format: data.log_format,
                            log_file: data.log_file,
                            log_max_size: data.log_max_size,
                            log_max_age: data.log_max_age,
                            log_max_backups: data.log_max_backups,
                            log_compress: data.log_compress === true || data.log_compress === 'true',
                            log_console_stderr: data.log_console_stderr === true || data.log_console_stderr === 'true'
                        });

                        // 处理服务器地址列表
                        $('#server-list-container').empty();
                        if (data.server_addrs) {
                            var addrs = data.server_addrs.split(',');
                            addrs.forEach(function(addr, index) {
                                if (addr.trim()) {
                                    var row = $(serverRowTemplate(addr.trim()));
                                    if (index === 0) row.css('margin-top', '0');
                                    $('#server-list-container').append(row);
                                }
                            });
                        }

                        // 根据TLS启用状态显示/隐藏TLS配置区域 - 修复布尔值判断
                        var tlsEnabled = data.tls_enabled === true || data.tls_enabled === 'true';
                        if (tlsEnabled) {
                            $('#tls-config-section').show();
                        } else {
                            $('#tls-config-section').hide();
                        }

                        // 重新渲染表单
                        form.render();
                        transferTips();

                        // 智能显示恢复状态信息
                        if (data.recovery_status && data.recovery_status !== '') {
                            // 自动恢复或创建默认配置时，始终显示提示
                            layer.msg('配置加载成功：' + data.recovery_status, {icon: 1, time: 3000});
                        } else if (showSuccessMsg) {
                            // 只有手动加载时才显示普通成功提示
                            layer.msg('配置加载成功', {icon: 1});
                        }
                    } else {
                        layer.msg('配置加载失败：' + res.msg);
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('网络错误，请稍后重试');
                }
            });
        };

        // 保存配置
        window.saveConfig = function() {
            // 获取表单数据
            var formData = form.val('config-form');

            // 收集服务器地址
            var serverAddrs = [];
            $('.server-addr-input').each(function() {
                var addr = $(this).val().trim();
                if (addr) {
                    serverAddrs.push(addr);
                }
            });

            // 验证必填项
            if (!formData.customer_id || !formData.client_id) {
                layer.msg('客户ID和客户端ID不能为空', {icon: 2});
                return;
            }

            // 验证客户ID和客户端ID必须是数字
            if (isNaN(formData.customer_id) || isNaN(formData.client_id)) {
                layer.msg('客户ID和客户端ID必须是数字', {icon: 2});
                return;
            }

            // 验证客户ID和客户端ID不能同时为0
            if (parseInt(formData.customer_id) === 0 && parseInt(formData.client_id) === 0) {
                layer.msg('客户ID和客户端ID不能同时为0', {icon: 2});
                return;
            }

            if (serverAddrs.length === 0) {
                layer.msg('至少需要一个服务器地址', {icon: 2});
                return;
            }

            // 构建请求数据
            var requestData = {
                action: 'save_config',
                customer_id: formData.customer_id,
                client_id: formData.client_id,
                server_addrs: serverAddrs.join(','),
                tls_enabled: formData.tls_enabled ? 'true' : 'false',
                tls_cert_dir: formData.tls_cert_dir || './certs',
                tls_cert_file: formData.tls_cert_file || 'server.crt',
                tls_key_file: formData.tls_key_file || 'server.key',
                tls_skip_verify: formData.tls_skip_verify ? 'true' : 'false',
                tls_auto_generate: formData.tls_auto_generate ? 'true' : 'false',
                log_level: formData.log_level || 'DEBUG',
                log_format: formData.log_format || 'json',
                log_file: formData.log_file || '/var/log/unisase_agent/agent.log',
                log_max_size: formData.log_max_size || '128',
                log_max_age: formData.log_max_age || '30',
                log_max_backups: formData.log_max_backups || '10',
                log_compress: formData.log_compress ? 'true' : 'false',
                log_console_stderr: formData.log_console_stderr ? 'true' : 'false'
            };

            layer.load(1);

            $.ajax({
                url: api_main,
                type: 'POST',
                data: requestData,
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');

                    if (res.code == "0") {
                        layer.msg('配置保存成功', {icon: 1});
                    } else {
                        layer.msg('配置保存失败：' + res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
        };

        // 重置配置
        window.resetConfig = function() {
            layer.confirm('您确定要重置所有配置吗？', {
                title: '重置确认',
                icon: 0,
                btn: ['确认重置', '取消'],
                btnAlign: 'c',
                skin: 'layer-skin-danger'
            }, function(index) {
                layer.load(1);

                $.ajax({
                    url: api_main,
                    type: 'POST',
                    data: {action: 'reset_config'},
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        layer.close(index);

                        if (res.code == "0") {
                            layer.msg('配置重置成功', {icon: 1});
                            loadConfig(false); // 重新加载配置，不显示加载成功提示
                        } else {
                            layer.msg('配置重置失败：' + res.msg);
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('网络错误，请稍后重试');
                    }
                });
            });
        };

        // 从指定备份恢复配置
        window.recoverFromBackup = function(filename) {
            layer.confirm('确定要从备份 "' + filename + '" 恢复配置吗？<br><span style="color:red;">当前配置将被覆盖！</span>', {
                icon: 3,
                title: '确认恢复',
                skin: 'layer-skin-blue'
            }, function(index) {
                layer.load(1);

                $.ajax({
                    url: api_main,
                    type: 'POST',
                    data: {
                        action: 'recover_config',
                        backup_filename: filename
                    },
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        layer.close(index);

                        if (res.code == "0") {
                            // 先关闭所有类型为'page'或'iframe'的弹窗
                            layer.closeAll('page');

                            layer.msg('配置恢复成功：' + res.msg, {icon: 1, time: 2000});
                            loadConfig(false); // 重新加载配置，不显示加载成功提示
                        } else {
                            layer.msg('配置恢复失败：' + res.msg);
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('网络错误，请稍后重试');
                    }
                });
            });
        };

        // 删除指定备份文件
        window.deleteBackup = function(filename) {
            layer.confirm('确定要删除备份文件 "' + filename + '" 吗？<br><span style="color:red;">此操作不可恢复！</span>', {
                icon: 2,
                title: '确认删除',
                skin: 'layer-skin-danger'
            }, function(index) {
                layer.load(1);

                $.ajax({
                    url: api_main,
                    type: 'POST',
                    data: {
                        action: 'delete_backup',
                        backup_filename: filename
                    },
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        layer.close(index);

                        if (res.code == "0") {
                            layer.msg('备份文件删除成功', {icon: 1});
                            // 直接刷新当前弹窗的备份列表，不关闭弹窗
                            refreshBackupList();
                        } else {
                            layer.msg('删除失败：' + res.msg);
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('网络错误，请稍后重试');
                    }
                });
            });
        };

        // 刷新备份列表（在当前弹窗内）
        function refreshBackupList() {
            $.ajax({
                url: api_main,
                type: 'POST',
                data: {action: 'list_backups'},
                dataType: 'json',
                success: function(res) {
                    if (res.code == "0") {
                        var data = res.data;

                        // 直接使用ID选择器，因为现在ID是唯一的
                        var backupCountEl = $('#backup-count');
                        var tbodyEl = $('#backup-table-body');

                        // 更新备份数量
                        backupCountEl.text(data.total);

                        // 清空并重新填充备份列表
                        tbodyEl.empty();

                        if (data.backups && data.backups.length > 0) {
                            data.backups.forEach(function(backup) {
                                var sizeText = backup.size > 0 ? Math.round(backup.size / 1024) + ' KB' : '0 KB';
                                var row = `
                            <tr>
                                <td>${backup.filename}</td>
                                <td>${backup.timestamp}</td>
                                <td>${sizeText}</td>
                                <td>
                                    <button type="button" class="layui-btn layui-btn-xs layui-btn-blue" onclick="recoverFromBackup('${backup.filename}')">恢复</button>
                                    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteBackup('${backup.filename}')">删除</button>
                                </td>
                            </tr>
                        `;
                                tbodyEl.append(row);
                            });
                        } else {
                            tbodyEl.append('<tr><td colspan="4" style="text-align: center; color: #999;">暂无备份文件</td></tr>');
                        }
                    } else {
                        layer.msg('刷新备份列表失败：' + res.msg);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请稍后重试');
                }
            });
        }

        // 显示备份管理
        window.showBackups = function() {
            layer.load(1);

            $.ajax({
                url: api_main,
                type: 'POST',
                data: {action: 'list_backups'},
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');

                    if (res.code == "0") {
                        var data = res.data;

                        // 更新备份数量
                        $('#backup-count').text(data.total);

                        // 清空并重新填充备份列表
                        var tbody = $('#backup-table-body');
                        tbody.empty();

                        if (data.backups && data.backups.length > 0) {
                            data.backups.forEach(function(backup) {
                                var sizeText = backup.size > 0 ? Math.round(backup.size / 1024) + ' KB' : '0 KB';
                                var row = `
                                    <tr>
                                        <td>${backup.filename}</td>
                                        <td>${backup.timestamp}</td>
                                        <td>${sizeText}</td>
                                        <td>
                                            <button type="button" class="layui-btn layui-btn-xs layui-btn-blue" onclick="recoverFromBackup('${backup.filename}')">恢复</button>
                                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteBackup('${backup.filename}')">删除</button>
                                        </td>
                                    </tr>
                                `;
                                tbody.append(row);
                            });
                        } else {
                            tbody.append('<tr><td colspan="4" style="text-align: center; color: #999;">暂无备份文件</td></tr>');
                        }

                        // 显示备份管理弹窗
                        layer.open({
                            type: 1,
                            title: '配置备份管理',
                            area: ['750px', 'auto'],
                            content: $('#backup-modal-content'),
                            shade: 0.3,
                            shadeClose: true,
                            end: function() {
                                // 弹窗关闭时，将内容元素移回body并隐藏，防止布局问题
                                $('#backup-modal-content').hide();
                            }
                        });
                    } else {
                        layer.msg('获取备份列表失败：' + res.msg);
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('网络错误，请稍后重试');
                }
            });
        };

        // 加载系统状态
        function loadStatus() {
            $.ajax({
                url: api_status,
                type: 'POST',
                data: {action: 'get_status'},
                dataType: 'json',
                success: function(res) {
                    if (res.code == "0") {
                        var data = res.data;

                        // 更新配置文件状态
                        var configWidget = $('[data-widget="config-file"]');
                        if (data.config_status === '正常') {
                            configWidget.find('.status-block').removeClass('status-warning status-error').addClass('status-ok');
                            configWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-success');
                            configWidget.find('.status-value').text('正常');
                        } else {
                            configWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                            configWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-error');
                            configWidget.find('.status-value').text(data.config_status);
                        }

                        // 更新最后修改时间 - 优化"未知"状态显示
                        var mtimeWidget = $('[data-widget="last-modified"]');
                        if (data.config_mtime === '未知') {
                            mtimeWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                            mtimeWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-error');
                            mtimeWidget.find('.status-value').text(data.config_mtime);
                        } else {
                            mtimeWidget.find('.status-block').removeClass('status-warning status-error').addClass('status-ok');
                            mtimeWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-date');
                            mtimeWidget.find('.status-value').text(data.config_mtime);
                        }

                        // 更新日志文件大小 - 简化不存在时的显示
                        var logWidget = $('[data-widget="log-size"]');
                        var logActions = logWidget.find('.status-actions');

                        if (data.log_size && data.log_size.indexOf('不存在') !== -1) {
                            logWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                            logWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-error');
                            logWidget.find('.status-value').text('日志文件不存在');

                            // 隐藏下载日志按钮
                            logActions.hide();

                        } else {
                            logWidget.find('.status-block').removeClass('status-warning status-error').addClass('status-ok');
                            logWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-log');
                            logWidget.find('.status-value').text(data.log_size);

                            // 显示下载日志按钮
                            logActions.html(`
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadLog()">
                                    <i class="layui-icon layui-icon-download-circle"></i> 下载日志
                                </button>
                            `).show();
                        }

                        // 更新服务器连接状态
                        var serverWidget = $('[data-widget="server-connection"]');
                        var connectionsHtml = '';
                        if (Array.isArray(data.server_connections)) {
                            if (data.server_connections.length === 0) {
                                if (data.config_status === '网络错误' || data.config_status === '配置文件不存在') {
                                    connectionsHtml = '<span class="status-bad">配置文件不存在，无法检测连接状态</span>';
                                    serverWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                                    serverWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-unlink');
                                } else {
                                    connectionsHtml = '<span class="status-warning">未配置服务器地址</span>';
                                    serverWidget.find('.status-block').removeClass('status-ok status-error').addClass('status-warning');
                                    serverWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-website');
                                }
                            } else {
                                var tableHtml = '<table class="server-status-table"><thead><tr><th>服务器地址</th><th>端口</th><th style="text-align: right;">状态</th></tr></thead><tbody>';
                                var connectedCount = 0;
                                var reachableCount = 0;
                                var failedCount = 0;
                                var totalCount = data.server_connections.length;

                                data.server_connections.forEach(function(conn) {
                                    var badge = '';
                                    var rowStyle = '';

                                    // 根据后端返回的状态文本进行分类
                                    if (conn.status === '已连接') {
                                        badge = '<span class="layui-badge layui-bg-green">已连接</span>';
                                        rowStyle = 'style="color: #009688;"';
                                        connectedCount++;
                                    } else if (conn.status === '主机可达') {
                                        badge = '<span class="layui-badge layui-bg-gray">主机可达</span>';
                                        rowStyle = 'style="color: #666;"';
                                        reachableCount++;
                                    } else if (conn.status === '未连接' || conn.status === '主机不可达') {
                                        badge = '<span class="layui-badge layui-bg-red">' + conn.status + '</span>';
                                        rowStyle = 'style="color: #FF5722;"';
                                        failedCount++;
                                    } else {
                                        badge = '<span class="layui-badge layui-bg-red">未知状态: ' + conn.status + '</span>';
                                        rowStyle = 'style="color: #FF5722;"';
                                        failedCount++;
                                    }

                                    var parts = conn.addr.split(':');
                                    var host = parts[0] || '';
                                    var port = parts[1] || '';
                                    tableHtml += `<tr><td ${rowStyle}>${host}</td><td ${rowStyle}>${port}</td><td style="text-align: right;">${badge}</td></tr>`;
                                });
                                tableHtml += '</tbody></table>';
                                connectionsHtml = tableHtml;

                                // 重新定义整体状态逻辑
                                if (failedCount === 0) {
                                    // 所有服务器均为"已连接"或"主机可达" → 正常状态
                                    serverWidget.find('.status-block').removeClass('status-warning status-error').addClass('status-ok');
                                    serverWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-website');
                                } else if (connectedCount === 0 && reachableCount === 0) {
                                    // 所有服务器均为"未连接"或"不可达" → 失败状态
                                    serverWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                                    serverWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-unlink');
                                } else {
                                    // 存在混合状态（既有成功又有失败） → 警告状态
                                    serverWidget.find('.status-block').removeClass('status-ok status-error').addClass('status-warning');
                                    serverWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-tips');
                                }
                            }
                        } else {
                            connectionsHtml = '<span class="status-bad">连接状态检测失败</span>';
                            serverWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                            serverWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-unlink');
                        }
                        serverWidget.find('.status-value').html(connectionsHtml);

                        // 更新系统版本 - 处理"未知"状态
                        var versionWidget = $('[data-widget="version"]');
                        if (data.system_version === '未知') {
                            versionWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                            versionWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-error');
                            versionWidget.find('.status-value').text(data.system_version);
                        } else {
                            versionWidget.find('.status-block').removeClass('status-warning status-error').addClass('status-ok');
                            versionWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-release');
                            versionWidget.find('.status-value').text(data.system_version);
                        }

                        // 更新Agent运行状态 - 新增功能
                        var agentWidget = $('[data-widget="agent-status"]');
                        var agentActions = agentWidget.find('.status-actions');

                        if (data.agent_status === '运行中') {
                            agentWidget.find('.status-block').removeClass('status-warning status-error').addClass('status-ok');
                            agentWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-engine');
                            agentWidget.find('.status-value').text(data.agent_status);

                            // 显示重启和停止按钮
                            agentActions.html(`
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-warm" onclick="agentRestart()">
                                    <i class="layui-icon layui-icon-refresh"></i> 重启
                                </button>
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="agentStop()">
                                    <i class="layui-icon layui-icon-pause"></i> 停止
                                </button>
                            `).show();
                        } else if (data.agent_status === '未运行') {
                            agentWidget.find('.status-block').removeClass('status-ok status-error').addClass('status-warning');
                            agentWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-pause');
                            agentWidget.find('.status-value').text(data.agent_status);

                            // 显示启动按钮
                            agentActions.html(`
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="agentStart()">
                                    <i class="layui-icon layui-icon-play"></i> 启动
                                </button>
                            `).show();
                        } else {
                            // 不存在或其他错误状态
                            agentWidget.find('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                            agentWidget.find('.status-icon').removeClass().addClass('layui-icon status-icon layui-icon-error');
                            agentWidget.find('.status-value').text(data.agent_status);

                            // 不显示操作按钮
                            agentActions.hide();
                        }
                    } else {
                        // 状态加载失败时显示错误信息
                        $('.status-value').text('加载失败');
                        $('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                    }
                },
                error: function() {
                    // 网络错误时显示错误状态
                    $('.status-value').text('网络错误');
                    $('.status-block').removeClass('status-ok status-warning').addClass('status-error');
                }
            });
        }

        // Agent控制函数
        window.agentStart = function() {
            layer.confirm('确定要启动 UniSASE Agent 吗？', {
                icon: 3,
                title: '启动确认',
                skin: 'layer-skin-green'
            }, function(index) {
                layer.load(1);

                $.ajax({
                    url: api_main,
                    type: 'POST',
                    data: {action: 'agent_start'},
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        layer.close(index);

                        if (res.code == "0") {
                            layer.msg('Agent启动成功', {icon: 1});
                            // 刷新状态
                            setTimeout(function() {
                                loadStatus();
                            }, 1000);
                        } else {
                            layer.msg('Agent启动失败：' + res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });
            });
        };

        window.agentStop = function() {
            layer.confirm('确定要停止 UniSASE Agent 吗？<br><span style="color:red;">停止后服务将不可用！</span>', {
                icon: 0,
                title: '停止确认',
                skin: 'layer-skin-danger'
            }, function(index) {
                layer.load(1);

                $.ajax({
                    url: api_main,
                    type: 'POST',
                    data: {action: 'agent_stop'},
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        layer.close(index);

                        if (res.code == "0") {
                            layer.msg('Agent停止成功', {icon: 1});
                            // 刷新状态
                            setTimeout(function() {
                                loadStatus();
                            }, 1000);
                        } else {
                            layer.msg('Agent停止失败：' + res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });
            });
        };

        window.agentRestart = function() {
            layer.confirm('确定要重启 UniSASE Agent 吗？<br>重启过程中服务会短暂中断。', {
                icon: 3,
                title: '重启确认',
                skin: 'layer-skin-yellow'
            }, function(index) {
                layer.load(1);

                $.ajax({
                    url: api_main,
                    type: 'POST',
                    data: {action: 'agent_restart'},
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        layer.close(index);

                        if (res.code == "0") {
                            layer.msg('Agent重启成功', {icon: 1});
                            // 刷新状态
                            setTimeout(function() {
                                loadStatus();
                            }, 2000);  // 重启需要更长时间
                        } else {
                            layer.msg('Agent重启失败：' + res.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });
            });
        };

        // 日志文件下载功能
        window.downloadLog = function() {
            layer.msg('正在准备下载...', {icon: 16, time: 0, shade: 0.3}); // 显示加载中提示

            var downloadUrl = api_main + '?action=download_log';

            fetch(downloadUrl)
                .then(response => {
                    // 检查响应头，判断是文件流还是JSON错误
                    const contentType = response.headers.get('Content-Type');
                    if (contentType && contentType.includes('application/octet-stream')) {
                        // 是文件流，正常下载
                        return response.blob().then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            const link = document.createElement('a');
                            link.href = url;

                            // 从Content-Disposition头获取文件名（更健壮）
                            const disposition = response.headers.get('Content-Disposition');
                            let filename = 'agent.log'; // 默认文件名
                            if (disposition && disposition.indexOf('attachment') !== -1) {
                                const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                                const matches = filenameRegex.exec(disposition);
                                if (matches != null && matches[1]) {
                                    filename = matches[1].replace(/['"]/g, '');
                                }
                            }

                            link.setAttribute('download', filename);
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            window.URL.revokeObjectURL(url); // 释放URL对象
                            layer.closeAll(); // 关闭加载提示
                            layer.msg('下载已开始', {icon: 1});
                        });
                    } else {
                        // 可能是JSON错误或其他问题
                        // 1. 获取原始的字节数据
                        return response.arrayBuffer().then(buffer => {
                            // 2. 使用TextDecoder和正确的编码(gb2312)来解码
                            const decoder = new TextDecoder('gb2312');
                            const decodedText = decoder.decode(buffer);

                            // 3. 解析手动解码后的字符串为JSON对象
                            const jsonData = JSON.parse(decodedText);

                            // 4. 抛出一个错误，这样可以被下面的 .catch() 捕获
                            throw new Error(jsonData.msg || '未知错误');
                        });
                    }
                })
                .catch(error => {
                    layer.closeAll(); // 关闭加载提示
                    // 显示后端返回的或网络请求的错误信息
                    layer.msg('下载失败: ' + error.message, {icon: 2, time: 3000});
                });
        };

        // 事件绑定
        $('#load-config-btn').on('click', loadConfig);
        $('#save-config-btn').on('click', saveConfig);
        $('#reset-btn').on('click', resetConfig);
        $('#backup-btn').on('click', showBackups);

        // 标签页切换事件
        element.on('tab(main-tab)', function(data){
            if (data.index === 1) { // 系统状态页面
                loadStatus();
            }
        });

        // 页面加载完成后自动加载配置 - 不显示成功提示
        loadConfig(false);
    });
</script>
</body>
</html>
