<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>UniSASE Agent 配置管理 - 巅峰版</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/layui/2.9.11/css/layui.css">
    <style>
        /* 全局样式调整 */
        html {
            overflow-y: scroll;
        }
        body {
            background-color: #f2f2f2;
            padding-bottom: 80px;
        }
        .page-container {
            padding: 20px;
        }

        /* 品牌化页头 */
        .page-header {
            background-color: #fff;
            padding: 15px 25px;
            margin: -20px -20px 25px -20px;
            box-shadow: 0 4px 10px -5px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e6e6e6;
        }
        .logo-placeholder {
            height: 40px;
            margin-right: 20px;
        }
        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #009688;
            letter-spacing: 0.5px;
        }

        /* 主题色 */
        .layui-btn-normal,
        .layui-tab-brief>.layui-tab-title .layui-this {
            background-color: #009688;
            color: #fff;
        }
        .layui-tab-brief>.layui-tab-title .layui-this:after {
            border-bottom-color: #009688;
        }
        .layui-form-switch em {
            background-color: #009688 !important;
        }
        .layui-input:focus, .layui-textarea:focus {
            border-color: #009688 !important;
        }
        .layui-btn-primary {
            border-color: #009688;
            color: #009688;
        }
        .layui-btn-primary:hover {
            background-color: #f0fdf9;
        }
        .layui-btn-blue {
            background-color: #1E9FFF;
            color: #fff;
        }
        .layui-btn-blue:hover { opacity: 0.9; color: #fff; }

        /* 为不同颜色的幽灵按钮新增辅助类 */
        .layui-btn-primary-warm {
            border-color: #FFB800;
            color: #FFB800;
        }
        .layui-btn-primary-warm:hover {
            background-color: #fff8e6;
        }
        .layui-btn-primary-danger {
            border-color: #FF5722;
            color: #FF5722;
        }
        .layui-btn-primary-danger:hover {
            background-color: #fff5f2;
        }

        /* 自定义 Layer 皮肤 */
        .layer-skin-danger .layui-layer-title { background-color: #FF5722; color: #fff; border: none; }
        .layer-skin-danger .layui-layer-btn .layui-layer-btn0 { border-color: #FF5722; background-color: #FF5722; color: #fff; }
        .layer-skin-blue .layui-layer-title { background-color: #1E9FFF; color: #fff; border: none; }
        .layer-skin-blue .layui-layer-btn .layui-layer-btn0 { border-color: #1E9FFF; background-color: #1E9FFF; color: #fff; }

        /* 卡片美化 */
        .layui-card {
            border-radius: 4px;
            transition: all 0.3s ease;
            margin-bottom: 25px;
            border: 1px solid #e6e6e6;
            box-shadow: none;
        }
        .layui-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transform: translateY(-2px);
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            background-color: #fafafa;
            border-radius: 4px 4px 0 0;
            padding: 12px 20px;
            border-bottom: 1px solid #e6e6e6;
        }
        .layui-card-header .layui-icon {
            margin-right: 8px;
            color: #009688;
            font-weight: normal;
        }

        .config-card .layui-form-label { width: 100px; }
        .config-card .layui-input-block { margin-left: 130px; }
        .modal-padding { padding: 20px 25px; }

        /* 系统状态微件样式 */
        .status-block {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            display: flex;
            align-items: center;
            transition: all .3s ease;
            min-height: 110px;
        }
        .status-block:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.06);
            transform: translateY(-3px);
        }
        .status-block .status-icon {
            font-size: 38px;
            margin-right: 20px;
            transition: color .3s ease;
            line-height: 1;
        }
        .status-block .status-content {
            width: 100%;
        }
        .status-block .status-content .status-value {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            line-height: 1.5;
            margin-bottom: 4px;
            transition: color .3s ease;
        }
        .status-block .status-content .status-label {
            color: #999;
            font-size: 13px;
        }
        .server-status-table {
            width: 100%;
            font-size: 14px;
            margin: 0;
        }
        .server-status-table th {
            text-align: left;
            font-weight: 500;
            color: #666;
            padding: 4px 0;
        }
        .server-status-table td {
            padding: 6px 0;
            border-top: 1px solid #f2f2f2;
            transition: color .3s ease;
        }

        /* 状态化颜色系统 */
        .status-ok .status-icon, .status-ok .status-value { color: #009688 !important; }
        .status-ok .layui-badge { background-color: #009688 !important; }
        .status-warning .status-icon,
        .status-warning .status-value { color: #FFB800 !important; }
        .status-error .status-icon,
        .status-error .status-value { color: #FF5722 !important; }
        .layui-badge.layui-bg-red { background-color: #FF5722 !important; }

        /* Agent 和 日志 操作按钮样式 */
        .status-actions {
            margin-top: 10px;
        }
        .status-actions .layui-btn + .layui-btn {
            margin-left: 8px;
        }
    </style>
</head>
<body>

<div class="page-container">
    <div class="page-header">
        <img src="UniSASE.svg" alt="Logo" class="logo-placeholder">
        <h1 class="main-title">UniSASE Agent 配置管理</h1>
    </div>

    <div class="layui-tab layui-tab-brief" lay-filter="main-tab">
        <ul class="layui-tab-title">
            <li class="layui-this" lay-id="config-tab">客户端配置</li>
            <li lay-id="status-tab">系统状态</li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <form class="layui-form" action="" id="config-form">
                    <!-- 卡片1: 客户端基本信息 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-user"></i>客户端基本信息配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户ID</label>
                                <div class="layui-input-block">
                                    <input type="text" value="12345" class="layui-input" data-tips="客户的唯一标识符，用于系统识别">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户端ID</label>
                                <div class="layui-input-block">
                                    <input type="text" value="223" class="layui-input" data-tips="客户端的唯一标识符，范围 1-65535">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片2: 服务器通信配置 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-website"></i>服务器通信配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">服务器地址</label>
                                <div class="layui-input-block" id="server-list-container">
                                    <div class="layui-input-group">
                                        <input type="text" value="comm.com:12345" class="layui-input" placeholder="主机名:端口号" data-tips="服务器地址格式：主机名:端口号，支持域名和IP地址">
                                        <span class="layui-input-group-btn">
                            <button type="button" class="layui-btn layui-btn-danger delete-server-btn">删除</button>
                          </span>
                                    </div>
                                    <div class="layui-input-group" style="margin-top: 10px;">
                                        <input type="text" value="127.0.0.1:50051" class="layui-input" placeholder="主机名:端口号" data-tips="服务器地址格式：主机名:端口号，支持域名和IP地址">
                                        <span class="layui-input-group-btn">
                            <button type="button" class="layui-btn layui-btn-danger delete-server-btn">删除</button>
                           </span>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="add-server-btn"><i class="layui-icon layui-icon-add-1"></i> 添加地址</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片3: TLS 安全配置 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-auz"></i>TLS 加密设置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item" pane>
                                <label class="layui-form-label">启用TLS</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="tls_enabled" lay-skin="switch" lay-text="开启|关闭" lay-filter="tls-switch" data-tips="是否启用 TLS 加密保护通信安全">
                                </div>
                            </div>
                            <div id="tls-options-block" style="display: none;">
                                <hr>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">证书目录</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="cert_path" value="./certs" class="layui-input" data-tips="证书文件存放目录">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">证书文件</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="cert_file" value="server.crt" class="layui-input" data-tips="服务器证书文件名">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">私钥文件</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="key_file" value="server.key" class="layui-input" data-tips="服务器私钥文件名">
                                    </div>
                                </div>
                                <div class="layui-form-item" pane>
                                    <label class="layui-form-label">其他选项</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="skip_verify" title="跳过服务器证书验证" lay-skin="primary" data-tips="适用于自签名证书，生产环境建议关闭">
                                        <input type="checkbox" name="auto_generate" title="自动生成自签名证书" lay-skin="primary" data-tips="如果证书文件不存在，自动生成自签名证书">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片4: 日志配置 -->
                    <div class="layui-card config-card">
                        <div class="layui-card-header"><i class="layui-icon layui-icon-log"></i>日志配置</div>
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">日志级别</label>
                                        <div class="layui-input-block">
                                            <select name="log_level" data-tips="选择日志输出的详细程度">
                                                <option value="DEBUG" selected>DEBUG</option>
                                                <option value="INFO">INFO</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">日志格式</label>
                                        <div class="layui-input-block">
                                            <select name="log_format" data-tips="选择日志输出格式">
                                                <option value="JSON" selected>JSON</option>
                                                <option value="TEXT">TEXT</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="layui-form-item">
                                <label class="layui-form-label">日志文件</label>
                                <div class="layui-input-block">
                                    <input type="text" name="log_file" value="/var/log/unisase_agent/agent.log" class="layui-input" data-tips="日志文件的完整路径">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最大文件大小</label>
                                <div class="layui-input-block">
                                    <div class="layui-input-group">
                                        <input type="number" name="log_max_size" value="128" class="layui-input" data-tips="单个日志文件的最大大小">
                                        <span class="layui-input-group-addon">MB</span>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">保留天数</label>
                                <div class="layui-input-block">
                                    <div class="layui-input-group">
                                        <input type="number" name="log_max_age" value="30" class="layui-input" data-tips="日志文件保留的最大天数">
                                        <span class="layui-input-group-addon">天</span>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">备份文件数</label>
                                <div class="layui-input-block">
                                    <input type="number" name="log_backups" value="10" class="layui-input" data-tips="保留的备份日志文件数量">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">压缩旧文件</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="log_compress" lay-skin="switch" lay-text="启用|禁用" checked data-tips="是否压缩旧的日志文件以节省空间">
                                </div>
                            </div>
                            <hr>
                            <div class="layui-form-item">
                                <label class="layui-form-label">控制台输出</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="log_console" lay-skin="switch" lay-text="开启|关闭" data-tips="是否将日志输出到控制台的标准错误流">
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="layui-tab-item">
                <div style="margin-bottom: 20px;">
                    <button class="layui-btn layui-btn-sm" id="demo-toggle-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 切换演示状态
                    </button>
                </div>

                <div id="status-widget-container">
                    <!-- 第一行 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md8" data-widget="agent-status">
                            <div class="status-block">
                                <i class="layui-icon status-icon"></i>
                                <div class="status-content">
                                    <div class="status-value"></div>
                                    <div class="status-label">Agent 运行状态</div>
                                    <div class="status-actions"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4" data-widget="log-size">
                            <div class="status-block">
                                <i class="layui-icon status-icon"></i>
                                <div class="status-content">
                                    <div class="status-value"></div>
                                    <div class="status-label">日志文件大小</div>
                                    <div class="status-actions"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第二行 -->
                    <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                        <div class="layui-col-md4" data-widget="config-file">
                            <div class="status-block">
                                <i class="layui-icon status-icon"></i>
                                <div class="status-content">
                                    <div class="status-value"></div>
                                    <div class="status-label">配置文件状态</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4" data-widget="last-modified">
                            <div class="status-block">
                                <i class="layui-icon status-icon"></i>
                                <div class="status-content">
                                    <div class="status-value"></div>
                                    <div class="status-label">最后修改时间</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4" data-widget="version">
                            <div class="status-block">
                                <i class="layui-icon status-icon"></i>
                                <div class="status-content">
                                    <div class="status-value"></div>
                                    <div class="status-label">系统版本</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 第三行 -->
                    <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                        <div class="layui-col-md8" data-widget="server-connection">
                            <div class="status-block">
                                <i class="layui-icon status-icon"></i>
                                <div class="status-content">
                                    <div class="status-value"></div>
                                    <div class="status-label">服务器连接状态</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局固定操作按钮栏 -->
    <div class="layui-footer" style="position: fixed; bottom: 0; left:0; right:0; padding: 10px 20px; text-align: right; box-shadow: 0 -2px 5px rgba(0,0,0,.05); background-color:#fff;">
        <button type="button" class="layui-btn layui-btn-primary">加载配置</button>
        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="config-form-submit">保存配置</button>
        <button type="button" id="reset-btn" class="layui-btn layui-btn-danger">重置</button>
        <button type="button" id="backup-btn" class="layui-btn layui-btn-blue">配置备份与恢复</button>
    </div>
</div>

<!-- 用于备份管理弹窗的HTML内容模板 -->
<div id="backup-modal-content" style="display: none;">
    <div class="modal-padding">
        <p class="layui-text">共 <span class="layui-badge layui-bg-green">2</span> 个备份文件，系统自动保留最新的10个备份。</p>
        <hr>
        <table class="layui-table" lay-skin="line">
            <colgroup>
                <col>
                <col width="180">
                <col width="100">
                <col width="150">
            </colgroup>
            <thead>
            <tr>
                <th>备份文件</th>
                <th>创建时间</th>
                <th>文件大小</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>config_20250730_095629.yaml</td>
                <td>2025-07-30 09:56:29</td>
                <td>0 KB</td>
                <td>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-blue restore-btn">恢复</button>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-btn">删除</button>
                </td>
            </tr>
            <tr>
                <td>config_20250730_223937.yaml</td>
                <td>2025-07-30 22:39:37</td>
                <td>1 KB</td>
                <td>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-blue restore-btn">恢复</button>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-btn">删除</button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/layui/2.9.11/layui.js"></script>
<script>
    layui.use(['form', 'layer', 'element'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
        var element = layui.element;

        // 初始化时，根据开关状态决定是否显示TLS选项
        if(!$('input[name=tls_enabled]').is(':checked')) {
            $('#tls-options-block').hide();
        }

        // TLS开关交互
        form.on('switch(tls-switch)', function(data){
            $('#tls-options-block').slideToggle(200);
        });

        // 重置按钮的二次确认交互
        $('#reset-btn').on('click', function() {
            layer.confirm('您确定要重置所有配置吗？', {
                title: '重置确认',
                icon: 0,
                btn: ['确认重置', '取消'],
                btnAlign: 'c',
                skin: 'layer-skin-danger'
            }, function(index) {
                $('#config-form')[0].reset();
                form.render();
                if(!$('input[name=tls_enabled]').is(':checked')) {
                    $('#tls-options-block').hide();
                }
                layer.close(index);
                layer.msg('配置已重置', {icon: 1, time: 1500});
            });
        });

        // 配置备份与恢复弹窗
        $('#backup-btn').on('click', function() {
            layer.open({
                type: 1,
                title: '配置备份管理',
                area: ['750px', 'auto'],
                content: $('#backup-modal-content').html(),
                shade: 0.3,
                shadeClose: true
            });
        });

        // 使用事件委托为弹窗内的动态按钮绑定事件
        $(document).on('click', '.restore-btn', function(){
            layer.confirm('确定要恢复此配置吗？<br>当前未保存的修改将会丢失。', {
                title: '恢复确认',
                icon: 3,
                skin: 'layer-skin-blue'
            }, function(index){
                layer.msg('恢复成功 (模拟)', {icon: 1});
                layer.closeAll();
            });
        });

        $(document).on('click', '.delete-btn', function(){
            var row = $(this).closest('tr');
            layer.confirm('确定要永久删除此备份文件吗？', {
                title: '删除确认',
                icon: 2,
                skin: 'layer-skin-danger'
            }, function(index){
                row.remove();
                layer.msg('删除成功 (模拟)', {icon: 1});
                layer.close(index);
            });
        });

        // 悬浮提示
        function transferTips() {
            $('select[data-tips]').each(function(){
                $(this).next('.layui-form-select').attr('data-tips', $(this).attr('data-tips'));
            });
            $('input[type=checkbox][data-tips]').each(function(){
                $(this).next('.layui-form-checkbox, .layui-form-switch').attr('data-tips', $(this).attr('data-tips'));
            });
        }
        transferTips();

        $(document).on('mouseenter', '[data-tips]', function(){
            layer.tips($(this).attr('data-tips'), this, {
                tips: [1, '#009688'],
                time: 0
            });
        }).on('mouseleave', '[data-tips]', function(){
            layer.closeAll('tips');
        });

        // 表单提交
        form.on('submit(config-form-submit)', function(data){
            layer.msg("配置已保存（模拟）", {icon: 1, time: 1500});
            return false;
        });

        // 服务器地址动态增删
        var serverRowTemplate = `
    <div class="layui-input-group" style="margin-top: 10px;">
        <input type="text" value="" class="layui-input" placeholder="主机名:端口号" data-tips="服务器地址格式：主机名:端口号，支持域名和IP地址">
        <span class="layui-input-group-btn">
            <button type="button" class="layui-btn layui-btn-danger delete-server-btn">删除</button>
        </span>
    </div>`;

        $('#add-server-btn').on('click', function() {
            var newRow = $(serverRowTemplate);
            if ($('#server-list-container').children().length === 0) {
                newRow.css('margin-top', '0');
            }
            $('#server-list-container').append(newRow);
        });

        $(document).on('click', '.delete-server-btn', function() {
            var target = this;
            layer.confirm('确定要删除此服务器地址吗？', {
                title:'删除确认',
                icon: 2,
                skin: 'layer-skin-danger'
            }, function(index){
                $(target).closest('.layui-input-group').remove();
                layer.close(index);
            });
        });

        // --- 系统状态演示 ---

        const agentStatusMap = {
            running: {
                text: '正在运行',
                statusClass: 'status-ok',
                iconClass: 'layui-icon-engine',
                buttons: `
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-primary-warm layui-btn-xs restart-agent-btn"><i class="layui-icon layui-icon-refresh-1"></i> 重启</button>
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-primary-danger layui-btn-xs stop-agent-btn"><i class="layui-icon layui-icon-pause"></i> 停止</button>
                `
            },
            stopped: {
                text: '未运行',
                statusClass: 'status-warning',
                iconClass: 'layui-icon-pause',
                buttons: `
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-xs start-agent-btn"><i class="layui-icon layui-icon-play"></i> 启动</button>
                `
            },
            not_found: {
                text: '不存在',
                statusClass: 'status-error',
                iconClass: 'layui-icon-error',
                buttons: ''
            }
        };

        const states = {
            allNormal: {
                name: "全部正常", next: "mixedStatus", buttonText: "查看Agent警告状态",
                data: {
                    'agent-status': { state: 'running' },
                    'log-size': { value: '56 MB', statusClass: 'status-ok', iconClass: 'layui-icon-log' },
                    'config-file': { value: '正常', statusClass: 'status-ok', iconClass: 'layui-icon-success' },
                    'last-modified': { value: '2025-07-30 22:39:37', statusClass: 'status-ok', iconClass: 'layui-icon-date' },
                    'version': { value: 'UniSASE Agent v1.0', statusClass: 'status-ok', iconClass: 'layui-icon-release' },
                    'server-connection': { servers: [ { host: 'comm.com', port: '12345', status: 'ok'}, { host: '127.0.0.1', port: '50051', status: 'ok'} ]}
                }
            },
            mixedStatus: {
                name: "Agent警告", next: "allError", buttonText: "查看Agent错误状态",
                data: {
                    'agent-status': { state: 'stopped' },
                    'log-size': { value: '128 MB', statusClass: 'status-ok', iconClass: 'layui-icon-log' },
                    'config-file': { value: '正常', statusClass: 'status-ok', iconClass: 'layui-icon-success' },
                    'last-modified': { value: '2025-07-30 22:39:37', statusClass: 'status-ok', iconClass: 'layui-icon-date' },
                    'version': { value: 'UniSASE Agent v1.0', statusClass: 'status-ok', iconClass: 'layui-icon-release' },
                    'server-connection': { servers: [ { host: 'comm.com', port: '12345', status: 'ok'}, { host: 'service.backup', port: '8080', status: 'error'}, { host: '127.0.0.1', port: '50051', status: 'ok'} ]}
                }
            },
            allError: {
                name: "Agent错误", next: "noConfig", buttonText: "查看未配置状态",
                data: {
                    'agent-status': { state: 'not_found' },
                    'log-size': { value: '56 MB', statusClass: 'status-ok', iconClass: 'layui-icon-log' },
                    'config-file': { value: '正常', statusClass: 'status-ok', iconClass: 'layui-icon-success' },
                    'last-modified': { value: '2025-07-30 22:39:37', statusClass: 'status-ok', iconClass: 'layui-icon-date' },
                    'version': { value: 'UniSASE Agent v1.0', statusClass: 'status-ok', iconClass: 'layui-icon-release' },
                    'server-connection': { servers: [ { host: 'comm.com', port: '12345', status: 'error'}, { host: '127.0.0.1', port: '50051', status: 'error'} ]}
                }
            },
            noConfig: {
                name: "未配置", next: "allNormal", buttonText: "查看全部正常状态",
                data: {
                    'agent-status': { state: 'not_found' },
                    'log-size': { value: '日志文件不存在', statusClass: 'status-error', iconClass: 'layui-icon-error' },
                    'config-file': { value: '配置文件不存在', statusClass: 'status-error', iconClass: 'layui-icon-error' },
                    'last-modified': { value: '未知', statusClass: 'status-warning', iconClass: 'layui-icon-question' },
                    'version': { value: 'UniSASE Agent v1.0', statusClass: 'status-ok', iconClass: 'layui-icon-release' },
                    'server-connection': { servers: [] }
                }
            }
        };

        let currentStateKey = 'allNormal';

        function renderStatusWidgets(stateData) {
            for (const widgetKey in stateData) {
                const widgetData = stateData[widgetKey];
                const $widget = $(`[data-widget="${widgetKey}"]`);
                const $statusBlock = $widget.find('.status-block');
                const $statusIcon = $widget.find('.status-icon');
                const $statusValue = $widget.find('.status-value');
                const $actions = $widget.find('.status-actions');

                if (widgetKey === 'agent-status') {
                    const agentInfo = agentStatusMap[widgetData.state];
                    $statusBlock.removeClass('status-ok status-warning status-error').addClass(agentInfo.statusClass);
                    $statusIcon.removeClass().addClass('layui-icon status-icon ' + agentInfo.iconClass);
                    $statusValue.html(agentInfo.text);
                    $actions.html(agentInfo.buttons);
                } else if (widgetKey === 'server-connection') {
                    let worstStatus = 'ok';
                    const totalServers = widgetData.servers.length;
                    const errorServers = widgetData.servers.filter(s => s.status === 'error').length;

                    if (totalServers === 0) { worstStatus = 'warning'; }
                    else if (errorServers === totalServers) { worstStatus = 'error'; }
                    else if (errorServers > 0) { worstStatus = 'warning'; }

                    let statusClass = 'status-ok';
                    let iconClass = 'layui-icon-website';

                    if (worstStatus === 'warning') { statusClass = 'status-warning'; iconClass = 'layui-icon-tips';}
                    else if(worstStatus === 'error') { statusClass = 'status-error'; iconClass = 'layui-icon-unlink'; }

                    let tableHtml = '<table class="server-status-table"><thead><tr><th>服务器地址</th><th>端口</th><th style="text-align: right;">状态</th></tr></thead><tbody>';

                    if (totalServers === 0) {
                        tableHtml += '<tr><td colspan="3" style="text-align: center; color: #FFB800;">未配置服务器地址</td></tr>';
                    } else {
                        widgetData.servers.forEach(server => {
                            const badge = server.status === 'ok' ? '<span class="layui-badge layui-bg-green">可达</span>' : '<span class="layui-badge layui-bg-red">失败</span>';
                            const rowStyle = server.status === 'ok' ? 'style="color: #009688;"' : 'style="color: #FF5722;"';
                            tableHtml += `<tr><td ${rowStyle}>${server.host}</td><td ${rowStyle}>${server.port}</td><td style="text-align: right;">${badge}</td></tr>`;
                        });
                    }
                    tableHtml += '</tbody></table>';

                    $statusBlock.removeClass('status-ok status-warning status-error').addClass(statusClass);
                    $statusIcon.removeClass().addClass('layui-icon status-icon ' + iconClass);
                    $statusValue.html(tableHtml);
                } else {
                    $statusBlock.removeClass('status-ok status-warning status-error').addClass(widgetData.statusClass);
                    $statusIcon.removeClass().addClass('layui-icon status-icon ' + widgetData.iconClass);
                    $statusValue.html(widgetData.value);

                    if (widgetKey === 'log-size' && $actions) {
                        if (widgetData.statusClass !== 'status-error') {
                            $actions.html('<button type="button" class="layui-btn layui-btn-primary layui-btn-xs download-log-btn"><i class="layui-icon layui-icon-download-circle"></i> 下载日志</button>');
                        } else {
                            $actions.html('');
                        }
                    }
                }
            }
        }

        // 初始化
        renderStatusWidgets(states[currentStateKey].data);
        $('#demo-toggle-btn').html('<i class="layui-icon layui-icon-refresh"></i> ' + states[currentStateKey].buttonText);

        // 切换按钮事件
        $('#demo-toggle-btn').on('click', function() {
            currentStateKey = states[currentStateKey].next;
            const currentState = states[currentStateKey];
            renderStatusWidgets(currentState.data);
            $(this).html('<i class="layui-icon layui-icon-refresh"></i> ' + currentState.buttonText);
        });

        // Agent 操作按钮事件委托
        $(document).on('click', '.start-agent-btn', function(){
            var loading = layer.load(1, {shade: [0.2, '#000']});
            setTimeout(function() {
                layer.close(loading);
                currentStateKey = 'allNormal';
                renderStatusWidgets(states[currentStateKey].data);
                $('#demo-toggle-btn').html('<i class="layui-icon layui-icon-refresh"></i> ' + states[currentStateKey].buttonText);
                layer.msg('Agent 启动成功 (模拟)', {icon: 1});
            }, 1000);
        });

        $(document).on('click', '.stop-agent-btn', function(){
            layer.confirm('您确定要停止 Agent 服务吗?', {icon: 3, title:'停止确认'}, function(index){
                layer.close(index);
                var loading = layer.load(1, {shade: [0.2, '#000']});
                setTimeout(function() {
                    layer.close(loading);
                    currentStateKey = 'mixedStatus';
                    renderStatusWidgets(states[currentStateKey].data);
                    $('#demo-toggle-btn').html('<i class="layui-icon layui-icon-refresh"></i> ' + states[currentStateKey].buttonText);
                    layer.msg('Agent 已停止 (模拟)', {icon: 1});
                }, 1000);
            });
        });

        $(document).on('click', '.restart-agent-btn', function(){
            layer.confirm('您确定要重启 Agent 服务吗?', {icon: 3, title:'重启确认'}, function(index){
                layer.close(index);
                var loading = layer.load(1, {shade: [0.2, '#000']});
                setTimeout(function() {
                    layer.close(loading);
                    layer.msg('Agent 重启成功 (模拟)', {icon: 1});
                }, 1500);
            });
        });

        // 日志下载按钮事件委托
        $(document).on('click', '.download-log-btn', function(){
            layer.msg('开始下载日志 (模拟)', { icon: 1, time: 1500 });
        });
    });
</script>
</body>
</html>