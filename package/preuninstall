#!/bin/bash

# UniSASE Agent Pre-uninstallation Script
# Executed during the uninstallation process to clean up resources

echo "Executing UniSASE Agent pre-uninstallation cleanup..."

# Environment Variables
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
. "${SCRIPT_DIR}/appenv.conf"

# Stop the service
echo "Stopping UniSASE Agent service..."
if [ -f "${RAM_APP_CTRL}" ]; then
    "${RAM_APP_CTRL}" stop
fi

echo "Pre-uninstallation cleanup completed."
exit 0
