#!/bin/bash

# UniSASE Agent Pre-installation Script
# Executed during the installation process to check environment dependencies

# Environment Variables
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
. "${SCRIPT_DIR}/appenv.conf"

echo "Executing UniSASE Agent pre-installation checks..."

# Check operating system
if [ ! -f /etc/os-release ]; then
    echo "Error: Cannot determine operating system version."
    exit 1
fi

# Check architecture
ARCH=$(uname -m)
case $ARCH in
    x86_64|amd64)
        echo "Detected AMD64 architecture."
        ;;
    aarch64|arm64)
        echo "Detected ARM64 architecture."
        ;;
    *)
        echo "Error: Unsupported architecture: $ARCH"
        exit 1
        ;;
esac

# Check disk space
REQUIRED_SPACE=50  # 50MB
AVAILABLE_SPACE=$(df "$PGPATH" | awk 'NR==2 {print int($4/1024)}')
if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
    echo "Error: Insufficient disk space. Requires at least ${REQUIRED_SPACE}MB, but only ${AVAILABLE_SPACE}MB is available."
    exit 1
fi

echo "Pre-installation checks completed."
exit 0
