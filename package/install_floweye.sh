#!/bin/bash

# Set path variables
FLOWEYE_DIR="/usr/ramdisk/bin"
FLOWEYE_ORIG="$FLOWEYE_DIR/floweye"
FLOWEYE_REAL="$FLOWEYE_DIR/floweye.real"
LOG_FILE="/var/log/unisase_agent/floweye.log"

# Check if the original floweye exists
if [ ! -f "$FLOWEYE_ORIG" ]; then
  echo "Error: $FLOWEYE_ORIG not found."
  exit 1
fi

# Backup the original floweye
if [ -f "$FLOWEYE_REAL" ]; then
  echo "$FLOWEYE_REAL already exists, skipping backup."
else
  echo "Backing up original floweye to floweye.real"
  mv "$FLOWEYE_ORIG" "$FLOWEYE_REAL"
fi

# Write the wrapper script
echo "Creating wrapper script: $FLOWEYE_ORIG"
cat > "$FLOWEYE_ORIG" << 'EOF'
#!/bin/bash

LOG_FILE="/var/log/unisase_agent/floweye.log"
REAL_BINARY="/usr/ramdisk/bin/floweye.real"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Log the execution
echo "[$TIMESTAMP] Executing: floweye $*" >> "$LOG_FILE"

# Execute the original floweye
exec "$REAL_BINARY" "$@"
EOF

# Add execute permission
echo "Setting execute permission."
chmod +x "$FLOWEYE_ORIG"

# Create log file and set permissions
if [ ! -f "$LOG_FILE" ]; then
  echo "Creating log file $LOG_FILE"
  touch "$LOG_FILE"
fi

echo "Setting log permissions."
chmod 644 "$LOG_FILE"

echo "Installation complete: Calling floweye will now log to $LOG_FILE"
