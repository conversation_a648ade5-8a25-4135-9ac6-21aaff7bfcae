#!/bin/bash

# UniSASE Agent Monitoring Script
# Checks the agent status every 30 seconds and restarts it if it's down

# Environment Variables
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
. "${SCRIPT_DIR}/appenv.conf"

# Check interval (seconds)
CHECK_INTERVAL=30

# Log function
log_message() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" >> "$MONITOR_LOG_FILE"
}

# Get agent process ID
get_agent_pid() {
    pgrep -f "${AGENT_BIN}.*${AGENT_CONF_FILE}" 2>/dev/null | head -1
}

# Check if agent is running
is_agent_running() {
    local pid=$(get_agent_pid)
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Setup core dump configuration
setup_core_dump() {
    log_message "Setting up core dump configuration..."

    # Set core dump file size to unlimited
    ulimit -c unlimited

    # Create core dump directories
    mkdir -p /var/core
    mkdir -p /var/log

    # Set core dump filename format (including process name and PID)
    if [ -w /proc/sys/kernel/core_pattern ]; then
        echo "/var/core/core.%e.%p.%t" > /proc/sys/kernel/core_pattern 2>/dev/null || true
    fi

    # Set directory permissions
    chmod 755 /var/core
    chmod 755 /var/log

    log_message "Core dump configuration complete: ulimit -c $(ulimit -c)"
}

# Start agent
start_agent() {
    log_message "Agent process stopped, attempting to restart..."

    # Setup core dump configuration
    setup_core_dump

    # Check if binary file exists
    if [ ! -f "${AGENT_BIN}" ]; then
        log_message "Error: agent binary not found: ${AGENT_BIN}"
        return 1
    fi

    # Check if config file exists
    if [ ! -f "${AGENT_CONF_FILE}" ]; then
        log_message "Error: config file not found: ${AGENT_CONF_FILE}"
        return 1
    fi

    # Start agent
    cd "${RAM_ROOT_DIR}"
    nohup "${AGENT_BIN}" -config "${AGENT_CONF_FILE}" >> "${MONITOR_LOG_FILE}" 2>&1 &
    
    # Wait a moment to check if started successfully
    sleep 3
    if is_agent_running; then
        local pid=$(get_agent_pid)
        log_message "Agent restarted successfully (PID: $pid)"
        return 0
    else
        log_message "Agent restart failed."
        return 1
    fi
}

# Signal handler function
cleanup() {
    log_message "Monitor script received stop signal, exiting..."
    exit 0
}

# Register signal handlers
trap cleanup TERM INT

# Main monitoring loop
main() {
    log_message "UniSASE Agent monitoring script started (PID: $)"
    log_message "Check interval: ${CHECK_INTERVAL} seconds"
    log_message "Monitoring target: ${AGENT_BIN}"
    log_message "Config file: ${AGENT_CONF_FILE}"
    
    local restart_count=0
    local last_restart_time=0
    local max_restart_per_hour=10  # Max restarts per hour
    
    while true; do
        if is_agent_running; then
            local pid=$(get_agent_pid)
            log_message "Agent is running normally (PID: $pid)"
        else
            # Check restart frequency limit
            local current_time=$(date +%s)
            local time_diff=$((current_time - last_restart_time))
            
            if [ $time_diff -lt 3600 ]; then
                # Within one hour
                if [ $restart_count -ge $max_restart_per_hour ]; then
                    log_message "Warning: Too many restarts within an hour ($restart_count), pausing auto-restart."
                    sleep $CHECK_INTERVAL
                    continue
                fi
            else
                # More than an hour, reset counter
                restart_count=0
            fi
            
            # Attempt to restart agent
            if start_agent; then
                restart_count=$((restart_count + 1))
                last_restart_time=$current_time
                log_message "Restart count: $restart_count"
            else
                log_message "Restart failed, will retry in ${CHECK_INTERVAL} seconds."
            fi
        fi
        
        # Wait for the next check
        sleep $CHECK_INTERVAL
    done
}

# Check if another monitor script is already running
check_existing_monitor() {
    local existing_pids=$(pgrep -f "monitor.sh" | grep -v $)
    if [ -n "$existing_pids" ]; then
        log_message "Warning: Other monitor script processes detected: $existing_pids"
        log_message "Current monitor script PID: $"
    fi
}

# Pre-start checks
startup_check() {
    # Create necessary directories
    mkdir -p "${RAM_ROOT_DIR}"
    mkdir -p "${APP_LOG_DIR}"
    
    # Check if another monitor script is running
    check_existing_monitor
    
    # Check if agent is already running
    if is_agent_running; then
        local pid=$(get_agent_pid)
        log_message "Agent already running on startup (PID: $pid)"
    else
        log_message "Agent not running on startup, will start on first check."
    fi
}

# Execute startup checks and main loop
startup_check
main
