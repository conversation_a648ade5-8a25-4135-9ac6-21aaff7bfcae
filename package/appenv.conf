# Environment Variables
. /etc/PG.conf

# SYS ENV
PGPATH=${PGPATH:-/usr/panabit}
PGETC=${PGETC:-/usr/panaetc}
RAMDISK=${RAMDISK:-/usr/ramdisk}
DATAPATH=${DATAPATH:-/usr/panalog}
WEBROOT="${RAMDISK}/admin"

# APP ENV
APP_NAME="unisase_agent"
APP_ROOT_DIR="${PGPATH}/app/${APP_NAME}"
RAM_ROOT_DIR="${RAMDISK}/app/${APP_NAME}"
RAM_APP_CTRL="${RAM_ROOT_DIR}/appctrl"

# APP CONF
APP_CONF_DIR="${PGETC}/App/${APP_NAME}"
AGENT_CONF_FILE="${APP_CONF_DIR}/config.yaml"

# APP BIN & SCRIPT
AGENT_BIN="${RAM_ROOT_DIR}/bin/agent"
MONITOR_SCRIPT="${RAM_ROOT_DIR}/monitor.sh"
MONITOR_PID_FILE="${RAM_ROOT_DIR}/monitor.pid"

# APP LOG
APP_LOG_DIR="${DATAPATH}/App/${APP_NAME}"
APPCTRL_LOG_FILE="${APP_LOG_DIR}/appctrl.log"
MONITOR_LOG_FILE="${APP_LOG_DIR}/monitor.log"
AGENT_DEFAULT_LOG_DIR="/var/log/${APP_NAME}"
AGENT_DEFAULT_LOG_FILE="${AGENT_DEFAULT_LOG_DIR}/agent.log"