# UniSASE Agent 打包说明

## 1. 必需文件
压缩包中必须包含以下文件，且这些文件必须位于压缩包的根目录或指定路径下：

## (1) app.inf 文件
- 路径：必须位于压缩包的根目录下。
- 内容格式：  
  必须包含以下键值对：
- 
```
app_version="1.0.0"
app_name="unisase_agent"        # 应用的名称（必须唯一）
app_id="unisase_agent"          # 应用的唯一标识符
app_cname="UniSASE Agent"       # 应用的显示名称
app_desc="UniSASE Agent"        # 应用描述
app_desc_en="UniSASE Agent"     # 英文描述
```

### (2) appctrl 脚本
- 路径：必须位于压缩包的根目录下。
- 权限：必须具有可执行权限（`chmod +x appctrl`）。
- 功能：  
  该脚本用于控制应用的生命周期，必须支持以下命令：

```
    start     # 启动应用
    stop      # 停止应用
    enable    # 启用应用
    disable   # 禁用应用
    status    # 查询状态
```

- 脚本的返回值：
    - 返回 0 表示成功。
    - 返回非 0 表示失败，同时可以输出错误信息（errmsg）。

### (3) preinstall 脚本（可选）
- 路径：位于压缩包的根目录下。
- 权限：必须具有可执行权限（`chmod +x preinstall`）。
- 功能：
    - 在安装过程中执行，用于检查环境依赖或执行预安装操作。
    - 如果返回非 0，安装过程会终止，并返回错误信息。

### (4) afterinstall 脚本（可选）
- 路径：位于压缩包的根目录下。
- 权限：必须具有可执行权限（`chmod +x afterinstall`）。
- 功能：
    - 在安装完成后执行，用于执行一些后置操作（如配置初始化）。
    - 如果返回非 0，安装过程会终止，并返回错误信息。

### (5) preuninstall 脚本（可选）
- 路径：位于压缩包的根目录下。
- 权限：必须具有可执行权限（`chmod +x preuninstall`）。
- 功能：
    - 在卸载过程中执行，用于清理资源或执行卸载前的操作。
    - 如果返回非 0，卸载过程会终止，并返回错误信息。

### (6) afteruninstall 脚本（可选）
- 路径：位于压缩包的根目录下。
- 权限：必须具有可执行权限（`chmod +x afteruninstall`）。
- 功能：
    - 在卸载完成后执行，用于执行一些后置清理操作。
 
## 2. 目录结构
压缩包中可以包含以下目录，用于存放应用的相关文件：

### (1) bin/ 目录
- 用于存放应用的可执行文件或二进制文件。
### (2) config/ 目录
- 用于存放应用的配置文件。
### (3) html/ 目录
- 用于存放 Web 界面相关的文件（如 HTML、CSS、JavaScript 文件）。
- 这些文件会被复制到 `/usr/ramdisk/admin/html/App/${CGI_app}/` 目录下。
### (4) cgi-bin/ 目录
- 用于存放 CGI 脚本文件。
- 这些文件会被复制到 `/usr/ramdisk/admin/cgi-bin/App/${CGI_app}/` 目录下。
 
## 3. 示例压缩包结构
以下是一个符合要求的压缩包示例：

```
myapp.tar.gz
├── app.inf
├── appctrl
├── preinstall
├── afterinstall
├── preuninstall
├── afteruninstall
├── bin/
│   └── myapp_binary
├── config/
│   └── myapp_config.conf
├── html/
│   └── index.html
└── cgi-bin/
    └── myapp_cgi
```
 
## 4. 安装流程
PA 内部，安装流程如下：
1. 解压压缩包：将压缩包解压到临时目录 `${app_installroot}`。
2. 检查文件：
   - 检查是否存在 `app.inf` 和 `appctrl` 文件。
   - 检查 `appctrl` 是否具有可执行权限。
3. 执行 `preinstall` 脚本（如果存在）。
4. 复制文件：
   - 将文件复制到 `${PGPATH}/app/${app_name}/` 目录。
   - 将文件复制到 `${RAMDISK}/app/${app_name}/` 目录。
5. 执行 `afterinstall` 脚本（如果存在）。
6. 启动应用：调用 `appctrl start` 启动应用。
 
## 5. 卸载流程
PA 内部，卸载流程如下：
1. 停止应用：调用 `appctrl stop` 停止应用。
2. 执行 `preuninstall` 脚本（如果存在）。
3. 删除文件：
   - 删除 `${PGPATH}/app/${CGI_app}/` 目录。
   - 删除 `${RAMDISK}/app/${CGI_app}/` 目录。
   - 删除 Web 相关目录（如 `/usr/ramdisk/admin/html/App/${CGI_app}/`）。 
4. 执行 `afteruninstall` 脚本（如果存在）。
 
## 6. 注意事项
- 确保所有脚本（如 `appctrl`、`preinstall` 等）具有可执行权限。
- 确保 `app.inf` 文件中的 `app_name` 和 `app_id` 唯一，避免冲突。
- 压缩包中的文件路径不要过长，避免解压时出现问题。
- 确保 `appctrl` 脚本的实现正确，支持 start、stop、enable、disable 等命令。
通过满足以上要求，压缩包可以被脚本正确处理，并支持安装、卸载、启用、禁用等操作。

