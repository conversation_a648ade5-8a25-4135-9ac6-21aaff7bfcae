#!/bin/bash

# Domain Group模块综合测试脚本
# 测试所有domain group模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="domain_group_test_results.log"
echo "Domain Group模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 标准化域名格式以匹配floweye的存储格式
normalize_domain_for_floweye() {
    local domain=$1

    # floweye将*.domain.com转换为.domain.com
    if [[ "$domain" == \** ]]; then
        echo "${domain:1}"  # 移除*，保留.domain.com
    else
        # @domain.com, ^domain.com, domain.com保持不变
        echo "$domain"
    fi
}

# 验证域名群组配置
verify_domain_group() {
    local group_name=$1
    local expected_domains=("${@:2}")

    echo "验证域名群组 $group_name 配置..." >> $LOG_FILE

    # 获取域名群组ID
    local group_id=$(get_domain_group_id "$group_name")
    if [ "$group_id" = "-1" ]; then
        print_error "域名群组 $group_name 不存在"
        return 1
    fi

    # 获取域名群组成员
    local output=$(floweye dns dumpgrp $group_id 2>/dev/null)
    if [ $? -ne 0 ]; then
        print_error "无法获取域名群组 $group_name 成员"
        return 1
    fi

    # 验证域名成员数量
    local actual_count=$(echo "$output" | grep -v "^$" | wc -l)
    local expected_count=${#expected_domains[@]}

    if [ "$actual_count" -eq "$expected_count" ]; then
        print_success "域名群组 $group_name 成员数量验证通过 ($actual_count)"
    else
        print_error "域名群组 $group_name 成员数量验证失败，期望: $expected_count，实际: $actual_count"
        echo "期望域名: ${expected_domains[*]}" >> $LOG_FILE
        echo "实际成员: $output" >> $LOG_FILE
        return 1
    fi

    # 验证每个域名成员（使用标准化格式）
    for domain in "${expected_domains[@]}"; do
        local normalized_domain=$(normalize_domain_for_floweye "$domain")
        if echo "$output" | grep -q "^$normalized_domain$"; then
            print_success "域名群组 $group_name 包含域名: $domain (标准化为: $normalized_domain)"
        else
            print_error "域名群组 $group_name 缺少域名: $domain (标准化为: $normalized_domain)"
            echo "期望域名: ${expected_domains[*]}" >> $LOG_FILE
            echo "实际成员: $output" >> $LOG_FILE
            return 1
        fi
    done

    return 0
}

# 验证域名群组不存在
verify_domain_group_not_exists() {
    local group_name=$1

    echo "验证域名群组 $group_name 不存在..." >> $LOG_FILE

    local group_id=$(get_domain_group_id "$group_name")
    if [ "$group_id" = "-1" ]; then
        print_success "域名群组 $group_name 确实不存在"
        return 0
    else
        print_error "域名群组 $group_name 仍然存在 (ID: $group_id)"
        return 1
    fi
}

# 执行测试并验证结果的包装函数
run_test_with_verification() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}
    shift 3
    local verification_func=$1
    shift
    local verification_args=("$@")

    # 执行测试
    run_test "$test_file" "$test_name" "$expected_success"
    local test_result=$?

    # 如果测试成功且有验证函数，则执行验证
    if [ $test_result -eq 0 ] && [ -n "$verification_func" ]; then
        if ! $verification_func "${verification_args[@]}"; then
            print_error "验证失败: $test_name"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            PASSED_TESTS=$((PASSED_TESTS - 1))  # 减去之前在run_test中增加的计数
            return 1
        fi
    fi

    return $test_result
}

# 获取域名群组ID
get_domain_group_id() {
    local group_name=$1

    local output=$(floweye dns listgrp 2>/dev/null)
    if [ $? -ne 0 ]; then
        echo "-1"
        return
    fi

    # 解析输出，格式: usr <id> <name> <count>
    local group_id=$(echo "$output" | grep "usr .* $group_name " | awk '{print $2}')
    if [ -n "$group_id" ]; then
        echo "$group_id"
    else
        echo "-1"
    fi
}

# 清理域名群组
cleanup_domain_group() {
    local group_name=$1
    echo "清理域名群组 $group_name..." >> $LOG_FILE

    local group_id=$(get_domain_group_id "$group_name")
    if [ "$group_id" != "-1" ]; then
        floweye dns rmvgrp $group_id >> $LOG_FILE 2>&1
        print_info "已清理域名群组: $group_name (ID: $group_id)"
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有测试域名群组
cleanup_all_test_domain_groups() {
    echo "清理所有测试域名群组..." >> $LOG_FILE

    # 获取所有域名群组列表
    local output=$(floweye dns listgrp 2>/dev/null)
    if [ $? -eq 0 ]; then
        # 查找以test-开头的域名群组
        echo "$output" | grep "usr .* test-" | while read line; do
            local group_id=$(echo "$line" | awk '{print $2}')
            local group_name=$(echo "$line" | awk '{print $3}')
            echo "删除测试域名群组: $group_name (ID: $group_id)" >> $LOG_FILE
            floweye dns rmvgrp $group_id >> $LOG_FILE 2>&1
        done
    fi
}

# 主测试流程
main() {
    print_header "Domain Group模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 获取初始域名群组状态
    print_header "获取初始域名群组状态"
    echo "初始域名群组状态:" >> $LOG_FILE
    floweye dns listgrp >> $LOG_FILE 2>&1

    # 清理所有测试域名群组
    cleanup_all_test_domain_groups

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础域名群组
    run_test_with_verification "test_domain_group_basic_new.json" "创建基础域名群组" "true" \
        "verify_domain_group" "test-domain-basic" "example.com" "test.com"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test_with_verification "test_domain_group_idempotent_new.json" "幂等性测试-重复创建" "true" \
        "verify_domain_group" "test-domain-basic" "example.com" "test.com"

    # 1.3 修改域名群组成员
    run_test_with_verification "test_domain_group_modify_members.json" "修改域名群组成员" "true" \
        "verify_domain_group" "test-domain-basic" "modified.com" "updated.com" "new.com"

    # 1.4 删除域名群组
    run_test_with_verification "test_domain_group_delete.json" "删除域名群组" "true" \
        "verify_domain_group_not_exists" "test-domain-basic"

    # 1.5 删除不存在域名群组的幂等性测试
    run_test_with_verification "test_domain_group_delete_idempotent.json" "删除域名群组幂等性测试" "true" \
        "verify_domain_group_not_exists" "test-domain-basic"

    # 阶段2: 文件内容测试
    print_header "阶段2: 文件内容测试"

    # 2.1 使用文件内容创建域名群组
    run_test_with_verification "test_domain_group_file_content_new.json" "文件内容创建域名群组" "true" \
        "verify_domain_group" "test-domain-file" "*.example.com" "@test.com" "^exact.com"

    # 2.2 修改文件内容
    run_test_with_verification "test_domain_group_file_content_modify.json" "修改文件内容" "true" \
        "verify_domain_group" "test-domain-file" "*.modified.com" "@updated.com"

    # 2.3 删除文件内容域名群组
    run_test_with_verification "test_domain_group_file_content_delete.json" "删除文件内容域名群组" "true" \
        "verify_domain_group_not_exists" "test-domain-file"

    # 阶段3: 空域名群组测试
    print_header "阶段3: 空域名群组测试"

    # 3.1 创建空域名群组
    run_test_with_verification "test_domain_group_empty_new.json" "创建空域名群组" "true" \
        "verify_domain_group" "test-domain-empty"

    # 3.2 为空域名群组添加成员
    run_test_with_verification "test_domain_group_empty_add_members.json" "为空域名群组添加成员" "true" \
        "verify_domain_group" "test-domain-empty" "added.com" "member.com"

    # 3.3 清空域名群组成员
    run_test_with_verification "test_domain_group_empty_clear.json" "清空域名群组成员" "true" \
        "verify_domain_group" "test-domain-empty"

    # 3.4 删除空域名群组
    run_test_with_verification "test_domain_group_empty_delete.json" "删除空域名群组" "true" \
        "verify_domain_group_not_exists" "test-domain-empty"

    # 阶段4: 全量同步测试
    print_header "阶段4: 全量同步测试"

    # 4.1 设置初始配置（增量模式）
    run_test_with_verification "test_domain_group_full_sync_setup.json" "全量同步初始配置" "true" \
        "verify_domain_group" "test-domain-sync1" "sync1.com" "test1.com"
    if ! verify_domain_group "test-domain-sync2" "sync2.com" "test2.com"; then
        print_error "验证失败: test-domain-sync2"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    if ! verify_domain_group "test-domain-sync3" "sync3.com" "test3.com"; then
        print_error "验证失败: test-domain-sync3"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 4.2 启动全量同步
    start_full_sync || exit 1

    # 4.3 发送全量同步配置（只保留部分域名群组）
    run_test_with_verification "test_domain_group_full_sync_cleanup.json" "全量同步清理配置" "true" \
        "verify_domain_group" "test-domain-sync1" "updated1.com" "new1.com"
    if ! verify_domain_group "test-domain-sync3" "updated3.com" "new3.com"; then
        print_error "验证失败: test-domain-sync3"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 4.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 4.5 验证清理结果：test-domain-sync2应该被删除
    sleep 2  # 等待清理完成
    if ! verify_domain_group "test-domain-sync1" "updated1.com" "new1.com"; then
        print_error "验证失败: test-domain-sync1 清理后"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    if ! verify_domain_group_not_exists "test-domain-sync2"; then
        print_error "验证失败: test-domain-sync2 应该被删除"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    if ! verify_domain_group "test-domain-sync3" "updated3.com" "new3.com"; then
        print_error "验证失败: test-domain-sync3 清理后"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 5.1 无效参数测试（这些应该失败）
    run_test "test_domain_group_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_domain_group_error_empty_config.json" "空配置错误测试" "false"

    # 阶段6: 完整配置测试
    print_header "阶段6: 完整配置测试"

    # 6.1 创建包含所有字段的完整配置
    run_test_with_verification "test_domain_group_complete_config.json" "创建完整配置" "true" \
        "verify_domain_group" "test-domain-complete" "*.wildcard.com" "@prefix.com" "^exact.com" "normal.com"

    # 6.2 修改完整配置
    run_test_with_verification "test_domain_group_complete_modify.json" "修改完整配置" "true" \
        "verify_domain_group" "test-domain-complete" "*.modified.com" "@updated.com" "^new.com"

    # 阶段7: 默认值验证测试
    print_header "阶段7: 默认值验证测试"

    # 7.1 创建包含文件内容的配置
    run_test_with_verification "test_domain_group_default_file.json" "创建文件内容配置" "true" \
        "verify_domain_group" "test-domain-default" "file1.com" "file2.com" "file3.com"

    # 7.2 修改为基础配置，验证默认值恢复
    run_test_with_verification "test_domain_group_default_basic.json" "修改为基础配置验证默认值" "true" \
        "verify_domain_group" "test-domain-default" "basic1.com" "basic2.com"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_test_domain_groups

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Domain Group模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
