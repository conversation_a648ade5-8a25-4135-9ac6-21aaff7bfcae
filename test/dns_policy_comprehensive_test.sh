#!/bin/bash
set -e
# DNS Policy模块综合测试脚本
# 测试所有DNS Policy模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="dns_policy_test_results.log"
echo "DNS Policy模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"
        return 1
    fi

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 检查是否有解析错误
    if echo "$output" | grep -q "Failed to parse"; then
        has_task_failed=true
    fi

    # 检查是否有语法错误
    if echo "$output" | grep -q "syntax error"; then
        has_task_failed=true
    fi

    # 检查连接错误
    if echo "$output" | grep -q "connection refused"; then
        has_task_failed=true
    fi

    # 检查其他网络错误
    if echo "$output" | grep -q "Error sending request"; then
        has_task_failed=true
    fi

    # 检查HTTP错误
    if echo "$output" | grep -q "dial tcp.*connect:"; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            echo "TOTAL_TESTS=$TOTAL_TESTS"
            echo "PASSED_TESTS=$PASSED_TESTS"
            echo "FAILED_TESTS=$FAILED_TESTS"
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            echo "TOTAL_TESTS=$TOTAL_TESTS"
            echo "PASSED_TESTS=$PASSED_TESTS"
            echo "FAILED_TESTS=$FAILED_TESTS"
            return 1
        fi
    fi
}

# 验证DNS策略配置
verify_dns_policy() {
    local cookie=$1
    local expected_action=$2
    local expected_disable=$3

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "验证DNS策略 cookie=$cookie 配置..." >> $LOG_FILE

    # 使用cookie查询策略详细配置
    local output=$(floweye dnspolicy get cookie=$cookie 2>/dev/null)
    local get_exit_code=$?

    echo "DNS策略详细配置输出: $output" >> $LOG_FILE

    # 检查策略是否存在
    if [ $get_exit_code -eq 0 ] && [ -n "$output" ] && echo "$output" | grep -q "cookie=$cookie"; then
        print_success "DNS策略 cookie=$cookie 存在"

        # 验证action（如果提供了期望值）
        if [ -n "$expected_action" ]; then
            if echo "$output" | grep -q "action=$expected_action"; then
                print_success "DNS策略 cookie=$cookie 的action正确: $expected_action"
            else
                print_error "DNS策略 cookie=$cookie 的action不正确，期望: $expected_action"
                echo "实际配置: $output" >> $LOG_FILE
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
        fi

        # 验证disable状态（如果提供了期望值）
        if [ -n "$expected_disable" ]; then
            if echo "$output" | grep -q "disable=$expected_disable"; then
                print_success "DNS策略 cookie=$cookie 的disable状态正确: $expected_disable"
            else
                print_error "DNS策略 cookie=$cookie 的disable状态不正确，期望: $expected_disable"
                echo "实际配置: $output" >> $LOG_FILE
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
        fi

        echo "DNS策略详细信息: $output" >> $LOG_FILE
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "DNS策略 cookie=$cookie 不存在"
        echo "获取策略配置失败，退出码: $get_exit_code" >> $LOG_FILE
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 验证DNS策略不存在
verify_dns_policy_not_exists() {
    local cookie=$1

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "验证DNS策略 cookie=$cookie 不存在..." >> $LOG_FILE

    # 尝试通过cookie获取策略详细配置
    local output=$(floweye dnspolicy get cookie=$cookie 2>/dev/null)
    local get_exit_code=$?

    echo "DNS策略查询结果，退出码: $get_exit_code" >> $LOG_FILE
    echo "DNS策略查询输出: $output" >> $LOG_FILE

    # 检查策略是否不存在
    if [ $get_exit_code -ne 0 ] || [ -z "$output" ] || ! echo "$output" | grep -q "cookie=$cookie"; then
        print_success "DNS策略 cookie=$cookie 确实不存在"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "DNS策略 cookie=$cookie 仍然存在"
        echo "策略详细信息: $output" >> $LOG_FILE
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 验证DNS策略详细配置
verify_dns_policy_detailed() {
    local cookie=$1
    local expected_action=$2
    local expected_disable=$3
    local expected_schtime=$4
    local expected_pool=$5
    local expected_usrtype=$6
    local expected_inif=$7
    local expected_bridge=$8
    local expected_vlan=$9
    local expected_atype=${10}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "验证DNS策略 cookie=$cookie 详细配置..." >> $LOG_FILE

    # 使用cookie查询策略详细配置
    local output=$(floweye dnspolicy get cookie=$cookie 2>/dev/null)
    local get_exit_code=$?

    echo "DNS策略详细配置输出: $output" >> $LOG_FILE

    # 检查策略是否存在
    if [ $get_exit_code -ne 0 ] || [ -z "$output" ] || ! echo "$output" | grep -q "cookie=$cookie"; then
        print_error "DNS策略 cookie=$cookie 不存在"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    local field_errors=0

    # 验证action
    if [ -n "$expected_action" ]; then
        if echo "$output" | grep -q "action=$expected_action"; then
            print_success "DNS策略 cookie=$cookie 的action正确: $expected_action"
        else
            print_error "DNS策略 cookie=$cookie 的action不正确，期望: $expected_action"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证disable状态
    if [ -n "$expected_disable" ]; then
        if echo "$output" | grep -q "disable=$expected_disable"; then
            print_success "DNS策略 cookie=$cookie 的disable状态正确: $expected_disable"
        else
            print_error "DNS策略 cookie=$cookie 的disable状态不正确，期望: $expected_disable"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证schtime（策略时段）
    if [ -n "$expected_schtime" ]; then
        if echo "$output" | grep -q "schtime=$expected_schtime"; then
            print_success "DNS策略 cookie=$cookie 的schtime正确: $expected_schtime"
        else
            print_error "DNS策略 cookie=$cookie 的schtime不正确，期望: $expected_schtime"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证pool（用户组）
    if [ -n "$expected_pool" ]; then
        if echo "$output" | grep -q "pool=$expected_pool"; then
            print_success "DNS策略 cookie=$cookie 的pool正确: $expected_pool"
        else
            print_error "DNS策略 cookie=$cookie 的pool不正确，期望: $expected_pool"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证usrtype（用户类型）
    if [ -n "$expected_usrtype" ]; then
        if echo "$output" | grep -q "usrtype=$expected_usrtype"; then
            print_success "DNS策略 cookie=$cookie 的usrtype正确: $expected_usrtype"
        else
            print_error "DNS策略 cookie=$cookie 的usrtype不正确，期望: $expected_usrtype"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证inif（源接口）
    if [ -n "$expected_inif" ]; then
        if echo "$output" | grep -q "inif=$expected_inif"; then
            print_success "DNS策略 cookie=$cookie 的inif正确: $expected_inif"
        else
            print_error "DNS策略 cookie=$cookie 的inif不正确，期望: $expected_inif"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证bridge（网桥接口）
    if [ -n "$expected_bridge" ]; then
        if echo "$output" | grep -q "bridge=$expected_bridge"; then
            print_success "DNS策略 cookie=$cookie 的bridge正确: $expected_bridge"
        else
            print_error "DNS策略 cookie=$cookie 的bridge不正确，期望: $expected_bridge"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证vlan
    if [ -n "$expected_vlan" ]; then
        if echo "$output" | grep -q "vlan=$expected_vlan"; then
            print_success "DNS策略 cookie=$cookie 的vlan正确: $expected_vlan"
        else
            print_error "DNS策略 cookie=$cookie 的vlan不正确，期望: $expected_vlan"
            field_errors=$((field_errors + 1))
        fi
    fi

    # 验证atype（查询类型）
    if [ -n "$expected_atype" ]; then
        if echo "$output" | grep -q "atype=$expected_atype"; then
            print_success "DNS策略 cookie=$cookie 的atype正确: $expected_atype"
        else
            print_error "DNS策略 cookie=$cookie 的atype不正确，期望: $expected_atype"
            field_errors=$((field_errors + 1))
        fi
    fi

    if [ $field_errors -eq 0 ]; then
        print_success "DNS策略 cookie=$cookie 详细配置验证通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "DNS策略 cookie=$cookie 详细配置验证失败，共 $field_errors 个字段不匹配"
        echo "实际配置: $output" >> $LOG_FILE
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 验证DNS策略可选字段默认值
verify_dns_policy_optional_defaults() {
    local cookie=$1

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "验证DNS策略 cookie=$cookie 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye dnspolicy get cookie=$cookie 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$output" ]; then
        print_error "无法获取DNS策略 cookie=$cookie 配置"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    local field_errors=0

    # 验证schtime默认值为0
    if echo "$output" | grep -q "schtime=0"; then
        print_success "DNS策略 cookie=$cookie schtime默认值验证通过 (0)"
    else
        print_error "DNS策略 cookie=$cookie schtime默认值验证失败，期望: 0"
        field_errors=$((field_errors + 1))
    fi

    # 验证pool默认值为0
    if echo "$output" | grep -q "pool=0"; then
        print_success "DNS策略 cookie=$cookie pool默认值验证通过 (0)"
    else
        print_error "DNS策略 cookie=$cookie pool默认值验证失败，期望: 0"
        field_errors=$((field_errors + 1))
    fi

    # 验证usrtype默认值为any
    if echo "$output" | grep -q "usrtype=any"; then
        print_success "DNS策略 cookie=$cookie usrtype默认值验证通过 (any)"
    else
        print_error "DNS策略 cookie=$cookie usrtype默认值验证失败，期望: any"
        field_errors=$((field_errors + 1))
    fi

    # 验证inif默认值为any
    if echo "$output" | grep -q "inif=any"; then
        print_success "DNS策略 cookie=$cookie inif默认值验证通过 (any)"
    else
        print_error "DNS策略 cookie=$cookie inif默认值验证失败，期望: any"
        field_errors=$((field_errors + 1))
    fi

    # 验证bridge默认值为0
    if echo "$output" | grep -q "bridge=0"; then
        print_success "DNS策略 cookie=$cookie bridge默认值验证通过 (0)"
    else
        print_error "DNS策略 cookie=$cookie bridge默认值验证失败，期望: 0"
        field_errors=$((field_errors + 1))
    fi

    # 验证vlan默认值为0
    if echo "$output" | grep -q "vlan=0"; then
        print_success "DNS策略 cookie=$cookie vlan默认值验证通过 (0)"
    else
        print_error "DNS策略 cookie=$cookie vlan默认值验证失败，期望: 0"
        field_errors=$((field_errors + 1))
    fi

    # 验证atype默认值为any
    if echo "$output" | grep -q "atype=any"; then
        print_success "DNS策略 cookie=$cookie atype默认值验证通过 (any)"
    else
        print_error "DNS策略 cookie=$cookie atype默认值验证失败，期望: any"
        field_errors=$((field_errors + 1))
    fi

    if [ $field_errors -eq 0 ]; then
        print_success "DNS策略 cookie=$cookie 可选字段默认值验证通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "DNS策略 cookie=$cookie 可选字段默认值验证失败，共 $field_errors 个字段不匹配"
        echo "实际配置: $output" >> $LOG_FILE
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 验证DNS策略排序
verify_dns_policy_order() {
    local expected_order=("$@")

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo "验证DNS策略排序..." >> $LOG_FILE

    # 获取策略列表
    local output=$(floweye dnspolicy list json=1 2>/dev/null)
    if [ $? -ne 0 ]; then
        print_error "无法获取DNS策略列表"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    echo "DNS策略列表输出: $output" >> $LOG_FILE

    # 解析JSON输出获取策略ID，然后通过详细查询获取cookie顺序
    local actual_order=()
    local policy_ids=()

    # 使用全局匹配提取所有polno值
    while [[ $output =~ \"polno\":([0-9]+) ]]; do
        policy_ids+=(${BASH_REMATCH[1]})
        # 移除已匹配的部分，继续匹配下一个
        output=${output#*"\"polno\":${BASH_REMATCH[1]}"}
    done

    # 按策略ID顺序获取每个策略的cookie
    for policy_id in "${policy_ids[@]}"; do
        local policy_detail=$(floweye dnspolicy get id=$policy_id 2>/dev/null)
        if [[ $policy_detail =~ cookie=([0-9]+) ]]; then
            actual_order+=(${BASH_REMATCH[1]})
        fi
    done

    echo "期望顺序: ${expected_order[*]}" >> $LOG_FILE
    echo "实际顺序: ${actual_order[*]}" >> $LOG_FILE

    # 过滤实际顺序，只保留期望的策略
    local filtered_order=()
    for cookie in "${actual_order[@]}"; do
        for expected_cookie in "${expected_order[@]}"; do
            if [ "$cookie" = "$expected_cookie" ]; then
                filtered_order+=("$cookie")
                break
            fi
        done
    done

    echo "过滤后顺序: ${filtered_order[*]}" >> $LOG_FILE

    # 比较期望顺序和过滤后的实际顺序
    if [ ${#filtered_order[@]} -ne ${#expected_order[@]} ]; then
        print_error "DNS策略数量不匹配，期望: ${#expected_order[@]}，实际: ${#filtered_order[@]}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    for i in "${!expected_order[@]}"; do
        if [ "${filtered_order[i]}" != "${expected_order[i]}" ]; then
            print_error "DNS策略排序不匹配，位置$i期望: ${expected_order[i]}，实际: ${filtered_order[i]}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    done

    print_success "DNS策略排序验证通过: ${actual_order[*]}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
    return 0
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理现有DNS策略配置
cleanup_dns_policies() {
    echo "清理现有DNS策略配置..." >> $LOG_FILE

    # 获取所有DNS策略列表
    local policy_list=$(floweye dnspolicy list json=1 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$policy_list" ] && [ "$policy_list" != "null" ]; then
        # 解析JSON并提取策略ID
        local policy_ids=$(echo "$policy_list" | grep -o '"polno":[0-9]*' | cut -d':' -f2)

        if [ -n "$policy_ids" ]; then
            echo "发现现有DNS策略: $policy_ids" >> $LOG_FILE
            for policy_id in $policy_ids; do
                echo "删除DNS策略: $policy_id" >> $LOG_FILE
                floweye dnspolicy remove id=$policy_id > /dev/null 2>&1
            done
            print_success "清理了现有DNS策略配置"
        fi
    fi
}

# 启动debug服务器
start_debug_server() {
    print_info "启动debug服务器..."
    echo "启动debug服务器..." >> $LOG_FILE

    # 检查debug服务器是否已经运行
    local status_output=$(../agent debug status 2>&1)
    if echo "$status_output" | grep -q "Debug server is running"; then
        print_info "Debug服务器已经在运行"
        return 0
    fi

    # 启动debug服务器
    local start_output=$(../agent debug start 2>&1)
    local exit_code=$?
    echo "启动debug服务器输出: $start_output" >> $LOG_FILE

    if [ $exit_code -eq 0 ]; then
        # 等待服务器启动
        sleep 2

        # 验证服务器是否启动成功
        local verify_output=$(../agent debug status 2>&1)
        if echo "$verify_output" | grep -q "Debug server is running"; then
            print_success "Debug服务器启动成功"
            return 0
        else
            print_error "Debug服务器启动失败: $verify_output"
            return 1
        fi
    else
        print_error "Debug服务器启动失败: $start_output"
        return 1
    fi
}

# 检查debug服务器状态
check_debug_server() {
    print_info "检查debug服务器状态..."
    echo "检查debug服务器状态..." >> $LOG_FILE

    # 检查端口是否被监听
    if ! netstat -ln 2>/dev/null | grep -q ":8080.*LISTEN" && ! ss -ln 2>/dev/null | grep -q ":8080.*LISTEN"; then
        print_error "Debug服务器无法启动且端口8080未被监听"
        echo "Debug服务器无法启动且端口8080未被监听" >> $LOG_FILE
        return 1
    fi

    print_success "Debug服务器状态正常"
    return 0
}

# 设置测试依赖
setup_test_dependencies() {
    echo "设置测试依赖..." >> $LOG_FILE

    # 启动debug服务器
    if ! start_debug_server; then
        print_error "无法启动debug服务器，测试终止"
        exit 1
    fi

    # 检查debug服务器状态
    if ! check_debug_server; then
        print_error "Debug服务器状态异常，测试终止"
        exit 1
    fi

    # 创建测试依赖对象
    run_test "test_dns_policy_dependencies_setup.json" "创建测试依赖对象"

    print_success "测试依赖设置完成"
}

# 主测试函数
main() {
    print_header "DNS Policy模块综合测试"

    # 设置测试依赖
    setup_test_dependencies

    # 清理现有配置
    cleanup_dns_policies

    # 1. 基础CRUD操作测试
    print_header "1. 基础CRUD操作测试"

    # 1.1 创建基础DNS策略
    run_test "test_dns_policy_basic_new.json" "创建基础DNS策略"
    verify_dns_policy "10001" "pass" "0"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_dns_policy_idempotent.json" "幂等性测试-重复创建"
    verify_dns_policy "10001" "pass" "0"

    # 1.3 字段修改测试
    run_test "test_dns_policy_modify_action.json" "修改action字段"
    verify_dns_policy "10001" "deny" "0"

    run_test "test_dns_policy_modify_disable.json" "修改disable字段"
    verify_dns_policy "10001" "deny" "1"

    # 1.4 启用/禁用测试
    run_test "test_dns_policy_enable.json" "启用DNS策略"
    verify_dns_policy "10001" "deny" "0"

    run_test "test_dns_policy_disable.json" "禁用DNS策略"
    verify_dns_policy "10001" "deny" "1"

    # 1.5 删除配置测试
    run_test "test_dns_policy_delete.json" "删除DNS策略"
    verify_dns_policy_not_exists "10001"

    # 1.6 删除不存在配置的幂等性测试
    run_test "test_dns_policy_delete_idempotent.json" "删除策略幂等性测试"
    verify_dns_policy_not_exists "10001"

    # 2. 完整配置和默认值测试
    print_header "2. 完整配置和默认值测试"

    # 2.1 创建包含所有字段的完整配置
    run_test "test_dns_policy_complete_config.json" "创建完整配置DNS策略"
    verify_dns_policy "10002" "pass" "0"

    # 2.2 修改为默认值配置，验证默认值恢复
    run_test "test_dns_policy_default_values.json" "验证默认值恢复"
    verify_dns_policy "10002" "pass" "0"

    # 2.3 可选字段默认值恢复测试
    print_header "2.3 可选字段默认值恢复测试"

    # 创建包含所有可选字段的完整配置
    run_test "test_dns_policy_optional_fields_complete.json" "创建包含所有可选字段的完整配置"

    # 验证详细配置
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if verify_dns_policy_detailed "10007" "pass" "0" "1" "2" "ippxy" "eth0" "1" "100-200" "any"; then
        print_success "DNS策略详细配置验证通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "DNS策略详细配置验证失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 修改配置，移除所有可选字段，验证默认值恢复
    run_test "test_dns_policy_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_dns_policy_optional_defaults "10007"

    # 2.4 动作类型完整性测试
    print_header "2.4 动作类型完整性测试"

    # 测试reply动作
    run_test "test_dns_policy_action_reply.json" "测试reply动作类型"
    verify_dns_policy "10008" "reply" "0"

    # 测试limit动作
    run_test "test_dns_policy_action_limit.json" "测试limit动作类型"
    verify_dns_policy "10009" "limit" "0"

    # 测试ippxy动作
    run_test "test_dns_policy_action_ippxy.json" "测试ippxy动作类型"
    verify_dns_policy "10010" "ippxy" "0"

    # 测试zeroreply动作
    run_test "test_dns_policy_action_zeroreply.json" "测试zeroreply动作类型"
    verify_dns_policy "10011" "zeroreply" "0"

    # 清理动作类型测试策略
    run_test "test_dns_policy_action_reply_delete.json" "清理reply动作策略"
    verify_dns_policy_not_exists "10008"

    run_test "test_dns_policy_action_limit_delete.json" "清理limit动作策略"
    verify_dns_policy_not_exists "10009"

    run_test "test_dns_policy_action_ippxy_delete.json" "清理ippxy动作策略"
    verify_dns_policy_not_exists "10010"

    run_test "test_dns_policy_action_zeroreply_delete.json" "清理zeroreply动作策略"
    verify_dns_policy_not_exists "10011"

    # 3. 策略排序功能测试
    print_header "3. 策略排序功能测试"

    # 3.1 设置五种策略类型
    run_test "test_dns_policy_five_types_setup.json" "设置五种策略类型"

    # 3.2 验证初始排序
    verify_dns_policy_order "10101" "10102" "10103" "10104" "10105"

    # 3.3 前向移动测试
    run_test "test_dns_policy_five_types_forward_move.json" "前向移动策略"
    verify_dns_policy_order "10101" "10104" "10102" "10103" "10105"

    # 3.4 后向移动测试
    run_test "test_dns_policy_five_types_backward_move.json" "后向移动策略"
    verify_dns_policy_order "10101" "10102" "10103" "10104" "10105"

    # 3.5 移动到首位测试
    run_test "test_dns_policy_five_types_move_to_first.json" "移动策略到首位"
    verify_dns_policy_order "10103" "10101" "10102" "10104" "10105"

    # 3.6 移动到末尾测试
    run_test "test_dns_policy_five_types_move_to_last.json" "移动策略到末尾"
    verify_dns_policy_order "10101" "10102" "10104" "10105" "10103"

    # 3.7 中间插入测试
    run_test "test_dns_policy_five_types_insert_middle.json" "中间插入新策略"
    verify_dns_policy_order "10101" "10102" "10106" "10104" "10105" "10103"

    # 3.8 删除中间策略测试
    run_test "test_dns_policy_five_types_delete_middle.json" "删除中间策略"
    verify_dns_policy_order "10101" "10102" "10104" "10105" "10103"

    # 4. 全量同步测试
    print_header "4. 全量同步测试"

    # 4.1 设置初始配置（增量模式）
    run_test "test_dns_policy_full_sync_setup.json" "全量同步初始配置"
    verify_dns_policy "10201" "pass" "0"
    verify_dns_policy "10202" "deny" "0"
    verify_dns_policy "10203" "pass" "0"

    # 4.2 启动全量同步
    start_full_sync || exit 1

    # 4.3 发送全量同步配置（只包含部分策略）
    run_test "test_dns_policy_full_sync_cleanup.json" "全量同步清理配置"
    verify_dns_policy "10201" "pass" "0"

    # 4.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 4.5 验证清理结果：未在全量同步中的策略应被删除
    sleep 2  # 等待清理完成
    verify_dns_policy_not_exists "10201"  # 全量同步结束后，不在配置中的策略应被删除
    verify_dns_policy_not_exists "10202"
    verify_dns_policy_not_exists "10203"

    # 5. 错误处理测试
    print_header "5. 错误处理测试"

    # 5.1 缺少必需字段测试
    run_test "test_dns_policy_error_no_cookie.json" "缺少cookie字段错误测试" "false"
    run_test "test_dns_policy_error_no_action.json" "缺少action字段错误测试" "false"

    # 5.2 无效参数测试
    run_test "test_dns_policy_error_invalid_action.json" "无效action错误测试" "false"
    run_test "test_dns_policy_error_invalid_domain_group.json" "无效域名群组错误测试" "false"

    # 清理测试配置
    cleanup_dns_policies

    # 输出测试结果
    print_header "测试结果汇总"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"

    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE

    # 输出统计信息供结果收集脚本使用
    echo "TOTAL_TESTS=$TOTAL_TESTS"
    echo "PASSED_TESTS=$PASSED_TESTS"
    echo "FAILED_TESTS=$FAILED_TESTS"

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        echo "DNS Policy模块测试完成 - $(date)" >> $LOG_FILE
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败"
        echo "DNS Policy模块测试完成 - $(date)" >> $LOG_FILE
        exit 1
    fi
}

# 检查是否在正确的目录
if [ ! -f "../agent-debug-client" ]; then
    echo "错误: 请在test目录下运行此脚本，且确保agent-debug-client在上级目录"
    exit 1
fi

# 运行主测试函数
main
