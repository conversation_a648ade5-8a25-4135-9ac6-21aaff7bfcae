[{"tx_id": "test-iwan-proxy-optional-complete-001", "device_tasks": [{"task_type": "TASK_IWAN_PROXY", "task_action": "NEW_CONFIG", "iwan_proxy_task": {"name": "iwan3", "ifname": "wan1", "mtu": 1420, "svr_addr": "*************", "svr_port": 8000, "username": "testuser", "password": "testpass", "encrypt": true, "link": 999, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "*******"}, "max_delay": 500}, "dns_pxy": true}}]}]