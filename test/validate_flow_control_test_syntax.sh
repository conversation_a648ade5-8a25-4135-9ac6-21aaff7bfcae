#!/bin/bash
# Flow Control Policy测试用例语法验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
TOTAL_FILES=0
VALID_FILES=0
INVALID_FILES=0

echo -e "${GREEN}=== Flow Control Policy测试用例语法验证 ===${NC}"

# 验证JSON文件语法
validate_json_file() {
    local file=$1
    TOTAL_FILES=$((TOTAL_FILES + 1))
    
    echo -n "验证 $file ... "
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}文件不存在${NC}"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
    
    # 使用python验证JSON语法
    if python3 -m json.tool "$file" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 有效${NC}"
        VALID_FILES=$((VALID_FILES + 1))
        return 0
    else
        echo -e "${RED}✗ 无效JSON语法${NC}"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
}

# 验证所有Flow Control Policy相关的测试文件
echo "验证基础测试文件..."
validate_json_file "test_flow_control_policy_basic_new.json"
validate_json_file "test_flow_control_policy_complete_config.json"
validate_json_file "test_flow_control_policy_group_basic_new.json"

echo ""
echo "验证策略移动测试文件..."
validate_json_file "test_flow_control_policy_move_to_first.json"
validate_json_file "test_flow_control_policy_move_to_last.json"
validate_json_file "test_flow_control_policy_order_setup.json"
validate_json_file "test_flow_control_policy_order_forward.json"
validate_json_file "test_flow_control_policy_order_backward.json"

echo ""
echo "验证5种策略类型测试文件..."
validate_json_file "test_flow_control_policy_five_types_setup.json"
validate_json_file "test_flow_control_policy_five_types_forward_move.json"
validate_json_file "test_flow_control_policy_five_types_backward_move.json"
validate_json_file "test_flow_control_policy_five_types_move_to_first.json"
validate_json_file "test_flow_control_policy_five_types_move_to_last.json"
validate_json_file "test_flow_control_policy_five_types_delete_middle.json"
validate_json_file "test_flow_control_policy_five_types_insert_middle.json"

echo ""
echo "验证策略类型测试文件..."
validate_json_file "test_flow_control_policy_channel_complete.json"
validate_json_file "test_flow_control_policy_channel_default.json"
validate_json_file "test_flow_control_policy_deny.json"
validate_json_file "test_flow_control_policy_mixed_types_setup.json"
validate_json_file "test_flow_control_policy_mixed_types_ordering.json"

echo ""
echo "验证支持文件..."
validate_json_file "test_flow_control_policy_group_movement_new.json"
validate_json_file "test_flow_control_policy_group_mixed_new.json"
validate_json_file "test_flow_control_dependencies_setup.json"

echo ""
echo "验证删除和插入测试文件..."
validate_json_file "test_flow_control_policy_delete_id_adjustment.json"
validate_json_file "test_flow_control_policy_insert_id_adjustment.json"

echo ""
echo -e "${GREEN}=== 验证结果统计 ===${NC}"
echo "总文件数: $TOTAL_FILES"
echo -e "有效文件: ${GREEN}$VALID_FILES${NC}"
echo -e "无效文件: ${RED}$INVALID_FILES${NC}"

if [ $INVALID_FILES -eq 0 ]; then
    echo -e "${GREEN}✓ 所有测试文件语法验证通过！${NC}"
    exit 0
else
    echo -e "${RED}✗ 有 $INVALID_FILES 个文件语法验证失败${NC}"
    exit 1
fi
