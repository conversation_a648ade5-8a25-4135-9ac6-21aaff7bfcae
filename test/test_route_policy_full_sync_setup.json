[{"tx_id": "route-policy-full-sync-setup-001", "device_tasks": [{"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10031, "desc": "full_sync_policy1", "previous": 0, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_ROUTE", "route_config": {"proxy": "wan"}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10032, "desc": "full_sync_policy2", "previous": 10031, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_NAT", "route_config": {"proxy": "wan"}, "nat_config": {"nat_ip": {"ip_ranges": [{"start_ip": {"ip_string": "**************"}, "end_ip": {"ip_string": "**************"}}]}}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10033, "desc": "full_sync_policy3", "previous": 10032, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_DNAT", "route_config": {"proxy": "wan"}, "nat_config": {"new_dst_ip": {"ip_string": "**************"}}}}]}]