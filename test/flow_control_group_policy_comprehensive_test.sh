#!/bin/bash

# 设置严格模式：遇到错误立即退出
set -e

# Flow Control Group Policy模块综合测试脚本
# 测试所有flow control group policy模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="flow_control_group_policy_test_results.log"
echo "Flow Control Group Policy模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # 临时禁用 set -e，以便捕获命令的返回值
    set +e
    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    set -e

    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证策略组配置
verify_policy_group() {
    local group_name=$1
    local expected_disable=$2
    local expected_stop=$3

    echo "验证策略组 $group_name 配置..." >> $LOG_FILE
    local output=$(floweye policygroup2 list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取策略组列表"
        return 1
    fi

    # 查找指定策略组
    local group_line=$(echo "$output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_error "策略组 $group_name 不存在"
        echo "当前策略组列表:" >> $LOG_FILE
        echo "$output" >> $LOG_FILE
        return 1
    fi

    print_success "策略组 $group_name 存在"
    echo "策略组信息: $group_line" >> $LOG_FILE

    # 验证disable状态（第5列）
    local actual_disable=$(echo "$group_line" | awk '{print $5}')
    if [ "$actual_disable" = "$expected_disable" ]; then
        print_success "策略组 $group_name disable=$expected_disable 验证通过"
    else
        print_error "策略组 $group_name disable 验证失败，期望: $expected_disable，实际: $actual_disable"
        return 1
    fi

    # 验证stop状态（第6列）
    local actual_stop=$(echo "$group_line" | awk '{print $6}')
    if [ "$actual_stop" = "$expected_stop" ]; then
        print_success "策略组 $group_name stop=$expected_stop 验证通过"
    else
        print_error "策略组 $group_name stop 验证失败，期望: $expected_stop，实际: $actual_stop"
        return 1
    fi

    return 0
}

# 验证策略组不存在
verify_policy_group_not_exists() {
    local group_name=$1

    echo "验证策略组 $group_name 不存在..." >> $LOG_FILE
    local output=$(floweye policygroup2 list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取策略组列表"
        return 1
    fi

    # 查找指定策略组
    local group_line=$(echo "$output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_success "策略组 $group_name 确实不存在"
        return 0
    else
        print_error "策略组 $group_name 仍然存在"
        echo "策略组信息: $group_line" >> $LOG_FILE
        return 1
    fi
}

# 验证策略组排序
verify_policy_group_order() {
    local expected_order=("$@")

    echo "验证策略组排序..." >> $LOG_FILE
    local output=$(floweye policygroup2 list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取策略组列表"
        return 1
    fi

    # 解析策略组顺序（按ID顺序）
    local actual_order=()
    while IFS= read -r line; do
        if [[ $line =~ ^[0-9]+ ]]; then
            local group_name=$(echo "$line" | awk '{print $2}')
            # 只关注测试相关的策略组
            if [[ $group_name =~ ^(test_|flow_control_test) ]]; then
                actual_order+=("$group_name")
            fi
        fi
    done <<< "$output"

    echo "期望顺序: ${expected_order[*]}" >> $LOG_FILE
    echo "实际顺序: ${actual_order[*]}" >> $LOG_FILE

    # 比较期望顺序和实际顺序
    for i in "${!expected_order[@]}"; do
        if [ "${actual_order[i]}" != "${expected_order[i]}" ]; then
            print_error "策略组排序不匹配，位置$i期望: ${expected_order[i]}，实际: ${actual_order[i]}"
            return 1
        fi
    done

    print_success "策略组排序验证通过: ${actual_order[*]}"
    return 0
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有测试策略组
cleanup_all_flow_control_groups() {
    echo "清理所有测试策略组..." >> $LOG_FILE

    # 获取所有测试相关的策略组
    local groups=$(floweye policygroup2 list 2>/dev/null | grep -E "(test_|flow_control_test)" | awk '{print $1}')

    for group_id in $groups; do
        echo "删除策略组 ID: $group_id" >> $LOG_FILE
        floweye policygroup2 remove id=$group_id >> $LOG_FILE 2>&1
    done

    print_success "清理完成"
}

# 验证函数包装器 - 处理验证失败并更新计数器
run_verification() {
    local verification_name=$1
    shift  # 移除第一个参数，剩下的传给验证函数

    echo "执行验证: $verification_name" >> $LOG_FILE

    # 临时禁用 set -e，以便捕获验证函数的返回值
    set +e
    "$@"  # 执行验证函数
    local result=$?
    set -e

    if [ $result -ne 0 ]; then
        print_error "验证失败: $verification_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        return 1
    else
        print_success "验证通过: $verification_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        return 0
    fi
}

# 测试执行包装器 - 处理测试失败但不退出脚本
run_test_safe() {
    # 临时禁用 set -e，以便捕获测试函数的返回值
    set +e
    run_test "$@"
    local result=$?
    set -e

    # run_test 函数已经处理了计数器更新，这里只需要返回结果
    return $result
}

# 主测试流程
main() {
    print_header "Flow Control Group Policy模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    print_info "检查floweye命令可用性..."
    if command -v floweye &> /dev/null; then
        print_success "floweye命令在PATH中找到"
    elif [ -x "/usr/bin/floweye" ]; then
        print_success "floweye命令在/usr/bin中找到"
        export PATH="/usr/bin:$PATH"
    elif [ -x "/usr/local/bin/floweye" ]; then
        print_success "floweye命令在/usr/local/bin中找到"
        export PATH="/usr/local/bin:$PATH"
    else
        # 尝试直接执行floweye命令测试
        if floweye help &> /dev/null; then
            print_success "floweye命令可以直接执行"
        else
            print_warning "floweye命令检查失败，但继续执行测试（可能通过agent调用）"
        fi
    fi

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_flow_control_groups

    # 获取初始状态
    print_header "获取初始状态"
    echo "初始策略组状态:" >> $LOG_FILE
    floweye policygroup2 list >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建策略组
    run_test_safe "test_flow_control_group_policy_basic_new.json" "创建基础策略组"
    run_verification "策略组test_flow_control_group1配置验证" verify_policy_group "test_flow_control_group1" "0" "0"

    # 1.2 幂等性测试 - 重复创建相同策略组
    run_test_safe "test_flow_control_group_policy_idempotent.json" "策略组幂等性测试"
    run_verification "策略组test_flow_control_group1幂等性验证" verify_policy_group "test_flow_control_group1" "0" "0"

    # 1.3 修改策略组
    run_test_safe "test_flow_control_group_policy_modify.json" "修改策略组配置"
    run_verification "策略组test_flow_control_group1修改验证" verify_policy_group "test_flow_control_group1" "1" "1"

    # 1.4 启用策略组
    run_test_safe "test_flow_control_group_policy_enable.json" "启用策略组"
    run_verification "策略组test_flow_control_group1启用验证" verify_policy_group "test_flow_control_group1" "0" "1"

    # 1.5 删除策略组
    run_test_safe "test_flow_control_group_policy_delete.json" "删除策略组"
    run_verification "策略组test_flow_control_group1删除验证" verify_policy_group_not_exists "test_flow_control_group1"

    # 1.6 删除不存在策略组的幂等性测试
    run_test_safe "test_flow_control_group_policy_delete_idempotent.json" "策略组删除幂等性测试"
    run_verification "策略组test_flow_control_group1删除幂等性验证" verify_policy_group_not_exists "test_flow_control_group1"

    # 阶段2: 排序功能测试
    print_header "阶段2: 排序功能测试"

    # 2.1 创建多个策略组用于排序测试
    run_test_safe "test_flow_control_group_policy_ordering_setup.json" "创建多个策略组用于排序测试"
    run_verification "策略组test_flow_control_group1创建验证" verify_policy_group "test_flow_control_group1" "0" "0"
    run_verification "策略组test_flow_control_group2创建验证" verify_policy_group "test_flow_control_group2" "0" "0"
    run_verification "策略组test_flow_control_group3创建验证" verify_policy_group "test_flow_control_group3" "0" "0"

    # 2.2 验证初始排序
    run_verification "初始排序验证" verify_policy_group_order "test_flow_control_group1" "test_flow_control_group2" "test_flow_control_group3"

    # 2.3 测试移动到首位（previous="null"）
    run_test_safe "test_flow_control_group_policy_move_to_first.json" "策略组移动到首位"
    run_verification "移动到首位后排序验证" verify_policy_group_order "test_flow_control_group3" "test_flow_control_group1" "test_flow_control_group2"

    # 2.4 测试移动到指定位置
    run_test_safe "test_flow_control_group_policy_move_to_position.json" "策略组移动到指定位置"
    run_verification "移动到指定位置后排序验证" verify_policy_group_order "test_flow_control_group3" "test_flow_control_group1" "test_flow_control_group2"

    # 2.5 测试追加模式（previous="append"）- 保持当前位置不变
    run_test_safe "test_flow_control_group_policy_move_to_last.json" "策略组追加模式测试"
    run_verification "追加模式后排序验证" verify_policy_group_order "test_flow_control_group3" "test_flow_control_group1" "test_flow_control_group2"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 设置初始配置（增量模式）
    run_test_safe "test_flow_control_group_policy_full_sync_setup.json" "全量同步初始配置"
    run_verification "全量同步前策略组1创建验证" verify_policy_group "test_flow_control_group1" "0" "0"
    run_verification "全量同步前策略组2创建验证" verify_policy_group "test_flow_control_group2" "0" "0"

    # 3.2 启动全量同步
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只包含需要保留的配置）
    run_test_safe "test_flow_control_group_policy_full_sync_config.json" "全量同步配置"
    run_verification "全量同步期间策略组1验证" verify_policy_group "test_flow_control_group1" "0" "0"

    # 3.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 3.5 验证清理结果：未在全量同步中的配置应被删除
    sleep 2  # 等待清理完成
    run_verification "全量同步后策略组1保留验证" verify_policy_group "test_flow_control_group1" "0" "0"
    run_verification "全量同步后策略组2清理验证" verify_policy_group_not_exists "test_flow_control_group2"

    # 阶段4: 边界条件和错误处理测试
    print_header "阶段4: 边界条件和错误处理测试"

    # 4.1 策略组错误测试
    run_test_safe "test_flow_control_group_policy_error_no_name.json" "策略组缺少名称错误测试" "false"
    run_test_safe "test_flow_control_group_policy_error_invalid_time.json" "策略组无效时间错误测试" "false"

    # 阶段5: 可选字段默认值恢复测试
    print_header "阶段5: 可选字段默认值恢复测试"

    # 5.1 创建包含所有字段的完整策略组配置
    run_test_safe "test_flow_control_group_policy_complete_config.json" "完整策略组配置测试"
    run_verification "完整策略组配置验证" verify_policy_group "test_complete_group" "0" "0"

    # 5.2 移除可选字段，验证默认值恢复
    run_test_safe "test_flow_control_group_policy_default_values.json" "策略组默认值验证测试"
    run_verification "策略组默认值恢复验证" verify_policy_group "test_complete_group" "0" "0"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_flow_control_groups

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Flow Control Group Policy模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
