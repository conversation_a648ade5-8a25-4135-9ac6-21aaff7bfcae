[{"tx_id": "route-policy-complete-config-001", "device_tasks": [{"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10002, "desc": "complete_route_policy", "previous": 0, "disable": false, "sch_time": 1, "zone": "CUST_TIER_T2", "src": [{"ip_addr": {"ip_string": "***********", "prefix_len": 24}}, {"ip_range": {"start_ip": {"ip_string": "********"}, "end_ip": {"ip_string": "********00"}}}], "src_port": {"port_list": [80, 443], "port_ranges": [{"start": 8000, "end": 8999}]}, "usr_type": "USER_TYPE_IPPXY", "pool": 100, "dst": [{"ip_addr": {"ip_string": "*******", "prefix_len": 32}}], "dst_port": {"port_list": [53, 80, 443]}, "proto": {"app_name": "any", "app_protocol": "tcp"}, "app": "http", "in_if": "any", "wan_bw": 1000, "wan_bw_out": 2000, "vlan": {"start": 100, "end": 200}, "ttl": {"start": 64, "end": 128}, "dscp": {"start": 0, "end": 63}, "action": "ROUTE_ACTION_DNAT", "route_config": {"proxy": "wan", "next_hop": {"ip_string": "***********"}}, "nat_config": {"new_dst_ip": {"ip_string": "**************"}, "nat_ip": {"ip_ranges": [{"start_ip": {"ip_string": "**************"}, "end_ip": {"ip_string": "**************"}}]}, "full_cone_nat": true, "no_snat": false}}}]}]