[{"tx_id": "flow-control-policy-basic-new-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 12345, "desc": "flow_control_test_policy1", "group_name": "flow_control_test_group1", "disable": false, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": false, "ip_rate": 1000, "tos": 0}, "in_ip": [{"ip": {"ipv4": 3232235776, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}], "in_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}]}, "out_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}]}, "app": {"app_name": "any", "app_protocol": "tcp"}, "interface": {"bridge": "any", "dir": "FLOW_DIRECTION_BOTH", "ifname": "any", "inif": "any", "vlan": {"start": 0, "end": 0}}}}}]}]