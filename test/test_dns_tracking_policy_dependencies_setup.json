[{"tx_id": "dns-tracking-policy-dependencies-setup-001", "device_tasks": [{"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth0", "mode": "INTERFACE_MODE_MONITOR", "zone": "INTERFACE_ZONE_OUTSIDE", "mix_mode": false}}, {"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth1", "mode": "INTERFACE_MODE_MONITOR", "zone": "INTERFACE_ZONE_OUTSIDE", "mix_mode": false}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan", "ifname": "eth0", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan1", "ifname": "eth0", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan2", "ifname": "eth0", "mtu": 1500, "dhcp": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}]}]