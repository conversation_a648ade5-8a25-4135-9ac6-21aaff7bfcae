[{"tx_id": "route-policy-ordering-setup-001", "device_tasks": [{"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10011, "desc": "policy_first", "previous": 0, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_ROUTE", "route_config": {"proxy": "wan"}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10012, "desc": "policy_second", "previous": 10011, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_NAT", "route_config": {"proxy": "wan"}, "nat_config": {"nat_ip": {"ip_ranges": [{"start_ip": {"ip_string": "**************"}, "end_ip": {"ip_string": "**************"}}]}}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10013, "desc": "policy_third", "previous": 10012, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_DNAT", "route_config": {"proxy": "wan"}, "nat_config": {"new_dst_ip": {"ip_string": "**************"}}}}]}]