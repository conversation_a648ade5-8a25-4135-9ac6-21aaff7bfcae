[{"tx_id": "flow-control-dependencies-setup-001", "device_tasks": [{"task_type": "TASK_TRAFFIC_CHANNEL", "task_action": "NEW_CONFIG", "traffic_channel_task": {"name": "testchannel1", "rate": 5000, "quota": 0, "priority": 3}}, {"task_type": "TASK_TRAFFIC_CHANNEL", "task_action": "NEW_CONFIG", "traffic_channel_task": {"name": "testchannel2", "rate": 8000, "quota": 0, "priority": 4}}, {"task_type": "TASK_TRAFFIC_CHANNEL", "task_action": "NEW_CONFIG", "traffic_channel_task": {"name": "testchannel3", "rate": 10000, "quota": 0, "priority": 5}}, {"task_type": "TASK_TRAFFIC_STAT", "task_action": "NEW_CONFIG", "traffic_stat_task": {"name": "test_stat_complete", "track_ip": true}}, {"task_type": "TASK_TRAFFIC_STAT", "task_action": "NEW_CONFIG", "traffic_stat_task": {"name": "test_stat_channel", "track_ip": true}}, {"task_type": "TASK_IP_GROUP", "task_action": "NEW_CONFIG", "ip_group_task": {"name": "test_ip_group", "members": [{"ip_addr": {"ip": {"ip_string": "*************/24"}}, "info": "test-network"}]}}, {"task_type": "TASK_USER_GROUP", "task_action": "NEW_CONFIG", "user_group_task": {"id": 100, "pid": 1, "name": "test_group_flow_control"}}, {"task_type": "TASK_USER", "task_action": "NEW_CONFIG", "user_task": {"name": "test_user", "pool_id": 100, "password": "test123456", "start": {"year": 2025, "month": 1, "day": 1}, "expire": {"year": 2026, "month": 1, "day": 1}, "enable": true, "restriction": {"max_online": 1, "bind_ip": {"ip_string": "0.0.0.0"}, "bind_mac": [], "out_vlan": 0}}}]}]