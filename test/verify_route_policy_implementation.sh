#!/bin/bash

# Route Policy 模块测试实施验证脚本
# 验证所有组件是否正确实施并可以执行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查函数
check_item() {
    local description=$1
    local check_command=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_success "$description"
        return 0
    else
        print_error "$description"
        return 1
    fi
}

# 主验证函数
main() {
    print_header "Route Policy 模块测试实施验证"
    
    # 1. 检查主测试脚本
    print_header "1. 主测试脚本检查"
    check_item "主测试脚本存在" "[ -f 'route_policy_comprehensive_test.sh' ]"
    check_item "主测试脚本可执行" "[ -x 'route_policy_comprehensive_test.sh' ]"
    check_item "主测试脚本语法正确" "bash -n 'route_policy_comprehensive_test.sh'"
    
    # 2. 检查测试配置文件
    print_header "2. 测试配置文件检查"
    local test_files=(test_route_policy_*.json)
    local file_count=${#test_files[@]}
    
    if [ -f "${test_files[0]}" ]; then
        check_item "测试配置文件存在 ($file_count 个)" "[ $file_count -gt 30 ]"
        check_item "所有JSON文件格式正确" "./validate_route_policy_tests.sh"
    else
        print_error "未找到测试配置文件"
        TOTAL_CHECKS=$((TOTAL_CHECKS + 2))
        FAILED_CHECKS=$((FAILED_CHECKS + 2))
    fi
    
    # 3. 检查Makefile集成
    print_header "3. Makefile集成检查"
    check_item "test-route-policy目标存在" "grep -q 'test-route-policy:' ../Makefile"
    check_item "test-route-policy-comprehensive别名存在" "grep -q 'test-route-policy-comprehensive:' ../Makefile"
    check_item "test-all包含route-policy" "grep -A25 'test-all:' ../Makefile | grep -q 'test-route-policy'"
    check_item "帮助信息包含route-policy" "make -C .. help | grep -q 'test-route-policy'"
    
    # 4. 检查验证工具
    print_header "4. 验证工具检查"
    check_item "验证脚本存在" "[ -f 'validate_route_policy_tests.sh' ]"
    check_item "验证脚本可执行" "[ -x 'validate_route_policy_tests.sh' ]"
    check_item "验证脚本语法正确" "bash -n 'validate_route_policy_tests.sh'"
    
    # 5. 检查文档
    print_header "5. 文档检查"
    check_item "实施报告存在" "[ -f 'ROUTE_POLICY_TEST_IMPLEMENTATION_REPORT.md' ]"
    check_item "最终总结存在" "[ -f 'ROUTE_POLICY_TEST_FINAL_SUMMARY.md' ]"
    
    # 6. 检查测试文件分类
    print_header "6. 测试文件分类检查"
    check_item "基础CRUD测试文件" "ls test_route_policy_basic_*.json test_route_policy_*_idempotent.json test_route_policy_modify_*.json test_route_policy_delete*.json test_route_policy_disable.json test_route_policy_enable.json >/dev/null 2>&1"
    check_item "完整配置测试文件" "ls test_route_policy_complete_*.json >/dev/null 2>&1"
    check_item "默认值验证测试文件" "ls test_route_policy_optional_fields_*.json >/dev/null 2>&1"
    check_item "排序测试文件" "ls test_route_policy_*ordering*.json test_route_policy_move_*.json test_route_policy_insert_*.json >/dev/null 2>&1"
    check_item "五种策略类型测试文件" "ls test_route_policy_five_types_*.json >/dev/null 2>&1"
    check_item "LMP测试文件" "ls test_route_policy_lmp_*.json >/dev/null 2>&1"
    check_item "全量同步测试文件" "ls test_route_policy_full_sync_*.json >/dev/null 2>&1"
    check_item "错误处理测试文件" "ls test_route_policy_error_*.json >/dev/null 2>&1"
    
    # 7. 检查关键测试场景
    print_header "7. 关键测试场景检查"
    check_item "包含ROUTE动作测试" "grep -q 'ROUTE_ACTION_ROUTE' test_route_policy_*.json"
    check_item "包含NAT动作测试" "grep -q 'ROUTE_ACTION_NAT' test_route_policy_*.json"
    check_item "包含DNAT动作测试" "grep -q 'ROUTE_ACTION_DNAT' test_route_policy_*.json"
    check_item "包含PROXY动作测试" "grep -q 'ROUTE_ACTION_PROXY' test_route_policy_*.json"
    check_item "包含LPM_TIER_T3测试" "grep -q 'LPM_TIER_T3' test_route_policy_*.json"
    check_item "包含CUST_TIER_T2测试" "grep -q 'CUST_TIER_T2' test_route_policy_*.json"
    check_item "包含全量同步标记" "grep -q 'full_sync.*true' test_route_policy_*.json"
    
    # 8. 检查Makefile语法
    print_header "8. Makefile语法检查"
    check_item "Makefile语法正确" "make -C .. -n test-route-policy >/dev/null 2>&1"
    
    # 统计结果
    print_header "验证结果统计"
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过检查: $PASSED_CHECKS"
    echo "失败检查: $FAILED_CHECKS"
    echo "成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "所有检查通过！Route Policy模块测试已完全实施并可以执行"
        echo ""
        print_info "现在可以执行以下命令来运行测试："
        echo "  make test-route-policy"
        echo "  make test-route-policy-comprehensive"
        echo "  make test-all"
        echo ""
        exit 0
    else
        print_error "有 $FAILED_CHECKS 个检查失败，请修复后重试"
        exit 1
    fi
}

# 执行主函数
main "$@"
