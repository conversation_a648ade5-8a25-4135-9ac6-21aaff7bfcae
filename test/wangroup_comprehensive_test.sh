#!/bin/bash

# WAN Group模块综合测试脚本
# 测试WAN Group配置的完整生命周期，包括CRUD操作、全量同步、边界条件和模块特定功能

# 注意：不使用 set -e，因为我们需要处理预期的失败情况

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/wangroup_test_results.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING] $1" >> $LOG_FILE
}

# 验证专用的成功和失败函数（不影响测试统计）
verify_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $1" >> $LOG_FILE
}

verify_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >> $LOG_FILE
}

# 初始化日志文件
init_log() {
    echo "WAN Group模块综合测试开始 - $(date)" > $LOG_FILE
    echo "========================================" >> $LOG_FILE
}

# 运行测试用例
run_test() {
    local test_file=$1
    local test_description=$2
    local should_succeed=${3:-"true"}
    
    ((TOTAL_TESTS++))
    
    print_info "执行测试: $test_description"
    echo "测试文件: $test_file" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        return 1
    fi
    
    # 执行测试 - 使用正确的agent debug client命令格式
    local output=$(../agent-debug-client -config="$test_file" 2>&1)
    local exit_code=$?
    
    echo "测试输出: $output" >> $LOG_FILE
    echo "退出代码: $exit_code" >> $LOG_FILE
    
    if [ "$should_succeed" = "true" ]; then
        # 成功测试：exit_code为0且没有err_code
        if [ $exit_code -eq 0 ] && ! echo "$output" | grep -q "err_code"; then
            print_success "$test_description - 通过"
            ((PASSED_TESTS++))
            return 0
        else
            print_error "$test_description - 失败: $output"
            ((FAILED_TESTS++))
            return 1
        fi
    else
        # 失败测试：应该有err_code或exit_code非0
        if echo "$output" | grep -q "err_code" || [ $exit_code -ne 0 ]; then
            print_success "$test_description - 正确失败"
            ((PASSED_TESTS++))
            return 0
        else
            print_error "$test_description - 应该失败但成功了: $output"
            ((FAILED_TESTS++))
            return 1
        fi
    fi
}

# 验证WAN Group配置
verify_wangroup() {
    local group_id=$1
    local expected_name=$2
    local expected_type=$3
    local expected_members=$4

    print_info "验证WAN Group配置 ID=$group_id"
    echo "验证WAN Group配置 ID=$group_id" >> $LOG_FILE

    # 这是验证操作，不计入测试统计

    # 检查floweye命令是否可用
    if ! command -v floweye >/dev/null 2>&1; then
        verify_error "floweye命令不可用，请确保在PA环境中运行"
        return 1
    fi

    # 获取WAN Group列表
    local output=$(floweye wangroup list 2>/dev/null)
    if [ $? -ne 0 ]; then
        verify_error "无法获取WAN Group列表"
        return 1
    fi
    
    # 查找指定ID的群组
    local group_line=$(echo "$output" | grep "^$group_id ")
    if [ -z "$group_line" ]; then
        verify_error "WAN Group ID $group_id 不存在"
        echo "当前WAN Group列表: $output" >> $LOG_FILE
        return 1
    fi
    
    # 解析群组信息
    local fields=($group_line)
    local actual_name=${fields[1]}
    local member_count=${fields[2]}
    local actual_type=${fields[3]}
    
    # 验证名称
    if [ "$actual_name" != "$expected_name" ]; then
        verify_error "WAN Group名称不匹配，期望: $expected_name，实际: $actual_name"
        return 1
    fi

    # 验证类型
    if [ "$actual_type" != "$expected_type" ]; then
        verify_error "WAN Group类型不匹配，期望: $expected_type，实际: $actual_type"
        return 1
    fi
    
    # 验证成员数量
    if [ -n "$expected_members" ]; then
        local expected_count=$(echo "$expected_members" | tr ',' '\n' | wc -l)
        if [ "$member_count" != "$expected_count" ]; then
            verify_error "WAN Group成员数量不匹配，期望: $expected_count，实际: $member_count"
            echo "完整群组信息: $group_line" >> $LOG_FILE
            return 1
        fi

        # 验证成员列表（从第7个字段开始是成员列表）
        if [ ${#fields[@]} -gt 6 ]; then
            local actual_members="${fields[@]:6}"
            for expected_member in $(echo "$expected_members" | tr ',' ' '); do
                if [[ ! " $actual_members " =~ " $expected_member " ]]; then
                    verify_error "WAN Group缺少成员: $expected_member"
                    echo "期望成员: $expected_members" >> $LOG_FILE
                    echo "实际成员: $actual_members" >> $LOG_FILE
                    return 1
                fi
            done
        else
            if [ "$expected_count" -gt 0 ]; then
                verify_error "WAN Group没有成员信息，但期望有 $expected_count 个成员"
                return 1
            fi
        fi
    fi
    
    verify_success "WAN Group ID=$group_id 验证通过"
    return 0
}

# 验证WAN Group不存在
verify_wangroup_not_exists() {
    local group_id=$1

    print_info "验证WAN Group ID=$group_id 不存在"
    echo "验证WAN Group ID=$group_id 不存在" >> $LOG_FILE

    # 检查floweye命令是否可用
    if ! command -v floweye >/dev/null 2>&1; then
        verify_error "floweye命令不可用，请确保在PA环境中运行"
        return 1
    fi

    local output=$(floweye wangroup list 2>/dev/null)
    if [ $? -ne 0 ]; then
        verify_error "无法获取WAN Group列表"
        return 1
    fi

    local group_line=$(echo "$output" | grep "^$group_id ")
    if [ -n "$group_line" ]; then
        verify_error "WAN Group ID $group_id 仍然存在: $group_line"
        return 1
    fi

    verify_success "WAN Group ID=$group_id 确认不存在"
    return 0
}

# 清理所有测试相关的WAN Group配置
cleanup_test_wangroups() {
    print_info "清理测试相关的WAN Group配置..."
    echo "清理测试相关的WAN Group配置..." >> $LOG_FILE

    # 检查floweye命令是否可用
    if ! command -v floweye >/dev/null 2>&1; then
        print_warning "floweye命令不可用，跳过清理"
        return 0
    fi

    # 获取所有WAN Group列表
    local output=$(floweye wangroup list 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$output" ]; then
        # 删除测试相关的群组（ID 10-50范围）
        echo "$output" | while read line; do
            if [ -n "$line" ]; then
                local id=$(echo "$line" | awk '{print $1}')
                if [[ "$id" =~ ^[0-9]+$ ]] && [ "$id" -ge 10 ] && [ "$id" -le 50 ]; then
                    echo "删除测试WAN Group ID: $id" >> $LOG_FILE
                    floweye wangroup remove id=$id > /dev/null 2>&1 || true
                fi
            fi
        done
    fi

    # 注意：不清理WAN配置，因为WAN Group依赖它们
    # floweye nat rmvproxy wan-test-1 > /dev/null 2>&1 || true
    # floweye nat rmvproxy wan-test-2 > /dev/null 2>&1 || true

    print_info "测试环境清理完成"
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_info "全量同步启动成功"
        return 0
    else
        print_warning "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_info "全量同步结束成功"
        return 0
    else
        print_warning "全量同步结束失败: $response"
        return 1
    fi
}

# 启动debug服务器
start_debug_server() {
    print_info "启动debug服务器..."
    echo "启动debug服务器..." >> $LOG_FILE

    # 检查debug服务器是否已经运行
    local status_output=$(../agent debug status 2>&1)
    if echo "$status_output" | grep -q "Debug server is running"; then
        print_info "Debug服务器已经在运行"
        return 0
    fi

    # 启动debug服务器
    local start_output=$(../agent debug start 2>&1)
    local exit_code=$?
    echo "启动debug服务器输出: $start_output" >> $LOG_FILE

    if [ $exit_code -eq 0 ]; then
        # 等待服务器启动
        sleep 2

        # 验证服务器是否启动成功
        local verify_output=$(../agent debug status 2>&1)
        if echo "$verify_output" | grep -q "Debug server is running"; then
            print_info "Debug服务器启动成功"
            return 0
        else
            print_warning "Debug服务器启动失败: $verify_output"
            return 1
        fi
    else
        print_warning "Debug服务器启动失败: $start_output"
        return 1
    fi
}

# 停止debug服务器
stop_debug_server() {
    print_info "停止debug服务器..."
    echo "停止debug服务器..." >> $LOG_FILE

    local stop_output=$(../agent debug stop 2>&1)
    local exit_code=$?
    echo "停止debug服务器输出: $stop_output" >> $LOG_FILE

    if [ $exit_code -eq 0 ]; then
        print_info "Debug服务器停止成功"
    else
        print_warning "Debug服务器停止失败: $stop_output"
    fi
}

# 检查测试环境
check_test_environment() {
    print_info "检查测试环境..."

    # 检查floweye命令是否可用
    if ! command -v floweye >/dev/null 2>&1; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        print_error "测试无法继续执行"
        exit 1
    fi

    # 检查agent debug client是否可用
    if ! command -v ../agent-debug-client >/dev/null 2>&1; then
        print_error "agent-debug-client不可用，测试无法继续"
        exit 1
    fi

    # 检查agent debug命令是否可用
    if ! command -v ../agent >/dev/null 2>&1; then
        print_error "agent命令不可用，测试无法继续"
        exit 1
    fi

    print_info "测试环境检查通过"
}

# 主测试函数
main() {
    init_log

    print_info "开始WAN Group模块综合测试"
    print_info "测试日志文件: $LOG_FILE"

    # 检查测试环境
    check_test_environment

    # 启动debug服务器
    start_debug_server || exit 1

    # 清理测试环境
    cleanup_test_wangroups
    
    print_info "=== 第一阶段：依赖关系设置 ==="
    
    # 设置WAN依赖（接口和多个WAN配置）
    run_test "test_wangroup_multi_wan_dependencies.json" "设置WAN Group依赖的接口和多个WAN配置"
    
    # 等待配置生效
    sleep 2
    
    print_info "=== 第二阶段：基础CRUD操作测试 ==="
    
    # 1. 基础新增测试
    run_test "test_wangroup_basic_new.json" "创建基础WAN Group配置"
    verify_wangroup "10" "test-group-basic" "srcdst" "wan-test-1"
    
    # 2. 幂等性测试
    run_test "test_wangroup_idempotent.json" "WAN Group配置幂等性测试"
    verify_wangroup "10" "test-group-basic" "srcdst" "wan-test-1"
    
    # 3. 修改类型测试
    run_test "test_wangroup_modify_type.json" "修改WAN Group类型"
    verify_wangroup "10" "test-group-basic" "spdp" "wan-test-1"
    
    # 4. 修改成员测试
    run_test "test_wangroup_modify_members.json" "修改WAN Group成员列表"
    verify_wangroup "10" "test-group-basic" "spdp" "wan-test-1,wan-test-2"
    
    # 5. 删除测试
    run_test "test_wangroup_delete.json" "删除WAN Group配置"
    verify_wangroup_not_exists "10"
    
    # 6. 删除幂等性测试
    run_test "test_wangroup_delete_idempotent.json" "WAN Group删除幂等性测试"
    verify_wangroup_not_exists "10"
    
    print_info "=== 第三阶段：全量同步测试 ==="
    
    # 1. 设置初始配置（增量模式）
    run_test "test_wangroup_full_sync_setup.json" "全量同步初始配置设置"
    verify_wangroup "11" "group-cleanup-1" "srcdst" "wan-test-1"
    verify_wangroup "12" "group-cleanup-2" "src" "wan-test-2"
    verify_wangroup "13" "group-keep" "dst" "wan-test-1,wan-test-2"
    
    # 2. 启动全量同步
    start_full_sync || exit 1
    
    # 3. 发送全量同步配置（只保留group-keep和wan-test-1，但修改类型和成员）
    run_test "test_wangroup_full_sync_cleanup_fixed.json" "全量同步配置"
    verify_wangroup "13" "group-keep" "failover" "wan-test-1"
    
    # 4. 结束全量同步，触发清理逻辑
    end_full_sync || exit 1
    
    # 5. 验证清理结果：group-cleanup-1和group-cleanup-2应该被删除
    sleep 3  # 等待清理完成
    verify_wangroup_not_exists "11"
    verify_wangroup_not_exists "12"
    # 注意：全量同步后，只有在全量同步配置中的WAN Group会保留
    verify_wangroup "13" "group-keep" "failover" "wan-test-1"
    
    print_info "=== 第四阶段：边界条件和错误处理测试 ==="
    
    # 1. 缺少必需字段测试
    run_test "test_wangroup_error_no_name.json" "缺少name字段错误测试" "false"
    
    # 2. 无效ID测试
    run_test "test_wangroup_error_invalid_id.json" "无效ID错误测试" "false"
    
    # 3. 名称冲突测试
    run_test "test_wangroup_error_name_conflict.json" "名称冲突错误测试" "false"
    
    print_info "=== 第五阶段：模块特定功能测试 ==="

    # 重新创建WAN依赖（因为全量同步可能清理了某些WAN配置）
    run_test "test_wangroup_multi_wan_dependencies.json" "重新设置WAN依赖"

    # 1. 不同群组类型测试
    run_test "test_wangroup_types.json" "不同WAN Group类型测试"
    verify_wangroup "40" "type-srcdst" "srcdst" "wan-test-1"
    verify_wangroup "41" "type-spdp" "spdp" "wan-test-2"
    verify_wangroup "42" "type-failover" "failover" "wan-test-1,wan-test-2"
    
    print_info "=== 测试完成，清理环境 ==="
    
    # 最终清理
    cleanup_test_wangroups

    # 停止debug服务器
    stop_debug_server
    
    # 输出测试结果统计
    echo "" >> $LOG_FILE
    echo "========================================" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过: $PASSED_TESTS" >> $LOG_FILE
    echo "失败: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "========================================" >> $LOG_FILE
    
    print_info "WAN Group模块综合测试完成"
    print_info "总测试数: $TOTAL_TESTS，通过: $PASSED_TESTS，失败: $FAILED_TESTS"
    print_info "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    print_info "详细日志请查看: $LOG_FILE"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}[SUCCESS]${NC} 所有测试通过！"
        exit 0
    else
        echo -e "${RED}[ERROR]${NC} 有 $FAILED_TESTS 个测试失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
