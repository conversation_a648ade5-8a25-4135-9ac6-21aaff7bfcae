[{"tx_id": "route-policy-dependencies-setup-001", "device_tasks": [{"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth0", "zone": "outside"}}, {"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth1", "zone": "outside"}}, {"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth2", "zone": "outside"}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan", "ifname": "eth0", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan2", "ifname": "eth1", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan3", "ifname": "eth2", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan4", "ifname": "eth0", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "***********00"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wan5", "ifname": "eth1", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***********"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}, {"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth0", "zone": "inside"}}, {"task_type": "TASK_LAN", "task_action": "NEW_CONFIG", "lan_task": {"name": "lan", "ifname": "eth0", "addr": {"ip_string": "************"}, "mask": {"ip_string": "*************"}, "mtu": 1500}}, {"task_type": "TASK_IP_GROUP", "task_action": "NEW_CONFIG", "ip_group_task": {"name": "test_ip_group", "members": [{"ip_addr": {"ip": {"ip_string": "*************/24"}}, "info": "test-network"}, {"ip_addr": {"ip_range": {"start_ip": {"ip_string": "********"}, "end_ip": {"ip_string": "**********"}}}, "info": "test-range"}]}}, {"task_type": "TASK_USER_GROUP", "task_action": "NEW_CONFIG", "user_group_task": {"id": 100, "pid": 1, "name": "test_user_group"}}, {"task_type": "TASK_EFFECTIVE_TIME", "task_action": "NEW_CONFIG", "effective_time_task": {"id": 1, "name": "test_time_1", "time_range": {"start_day": 1, "end_day": 5, "start_time": {"hour": 8, "min": 0, "sec": 0}, "end_time": {"hour": 18, "min": 0, "sec": 0}}}}, {"task_type": "TASK_EFFECTIVE_TIME", "task_action": "NEW_CONFIG", "effective_time_task": {"id": 2, "name": "test_time_2", "time_range": {"start_day": 1, "end_day": 7, "start_time": {"hour": 9, "min": 0, "sec": 0}, "end_time": {"hour": 17, "min": 0, "sec": 0}}}}, {"task_type": "TASK_EFFECTIVE_TIME", "task_action": "NEW_CONFIG", "effective_time_task": {"id": 3, "name": "test_time_3", "time_range": {"start_day": 6, "end_day": 7, "start_time": {"hour": 10, "min": 0, "sec": 0}, "end_time": {"hour": 22, "min": 0, "sec": 0}}}}]}]