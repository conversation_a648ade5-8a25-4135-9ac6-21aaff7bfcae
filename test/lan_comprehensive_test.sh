#!/bin/bash

# LAN模块综合测试脚本
# 测试所有LAN模块的核心功能和边界条件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="lan_test_results.log"
echo "LAN模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    if ../agent-debug-client --config=$test_file >> $LOG_FILE 2>&1; then
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证LAN配置
verify_lan() {
    local lan_name=$1
    local expected_ifname=$2
    local expected_mtu=$3
    local expected_addr=$4
    local expected_mask=$5
    
    echo "验证LAN $lan_name 配置..." >> $LOG_FILE
    local output=$(floweye nat getproxy $lan_name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取LAN $lan_name 配置"
        return 1
    fi
    
    # 验证ifname
    if echo "$output" | grep -q "ifname=$expected_ifname"; then
        print_success "LAN $lan_name ifname=$expected_ifname 验证通过"
    else
        print_error "LAN $lan_name ifname 验证失败，期望: $expected_ifname"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证mtu
    if echo "$output" | grep -q "mtu=$expected_mtu"; then
        print_success "LAN $lan_name mtu=$expected_mtu 验证通过"
    else
        print_error "LAN $lan_name mtu 验证失败，期望: $expected_mtu"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证addr
    if echo "$output" | grep -q "addr=$expected_addr"; then
        print_success "LAN $lan_name addr=$expected_addr 验证通过"
    else
        print_error "LAN $lan_name addr 验证失败，期望: $expected_addr"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证netmask
    if echo "$output" | grep -q "netmask=$expected_mask"; then
        print_success "LAN $lan_name netmask=$expected_mask 验证通过"
    else
        print_error "LAN $lan_name netmask 验证失败，期望: $expected_mask"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 验证LAN不存在
verify_lan_not_exists() {
    local lan_name=$1
    
    echo "验证LAN $lan_name 不存在..." >> $LOG_FILE
    local output=$(floweye nat getproxy $lan_name 2>&1)
    
    if echo "$output" | grep -q "NEXIST" || [ $? -ne 0 ]; then
        print_success "LAN $lan_name 确实不存在"
        return 0
    else
        print_error "LAN $lan_name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证LAN配置包括可选字段
verify_lan_optional() {
    local lan_name=$1
    local expected_ifname=$2
    local expected_mtu=$3
    local expected_addr=$4
    local expected_mask=$5
    local expected_clone_mac=$6

    echo "验证LAN $lan_name 配置（包括可选字段）..." >> $LOG_FILE

    local output=$(floweye nat getproxy $lan_name)
    echo "LAN配置输出: $output" >> $LOG_FILE

    # 验证基础字段
    if ! echo "$output" | grep -q "ifname=$expected_ifname"; then
        print_error "LAN ifname 验证失败，期望: $expected_ifname"
        echo "✗ LAN ifname 验证失败，期望: $expected_ifname" >> $LOG_FILE
        return 1
    fi
    print_success "LAN $lan_name ifname=$expected_ifname 验证通过"
    echo "✓ LAN $lan_name ifname=$expected_ifname 验证通过" >> $LOG_FILE

    if ! echo "$output" | grep -q "mtu=$expected_mtu"; then
        print_error "LAN MTU 验证失败，期望: $expected_mtu"
        echo "✗ LAN MTU 验证失败，期望: $expected_mtu" >> $LOG_FILE
        return 1
    fi
    print_success "LAN $lan_name mtu=$expected_mtu 验证通过"
    echo "✓ LAN $lan_name mtu=$expected_mtu 验证通过" >> $LOG_FILE

    if ! echo "$output" | grep -q "addr=$expected_addr"; then
        print_error "LAN IP地址 验证失败，期望: $expected_addr"
        echo "✗ LAN IP地址 验证失败，期望: $expected_addr" >> $LOG_FILE
        return 1
    fi
    print_success "LAN $lan_name addr=$expected_addr 验证通过"
    echo "✓ LAN $lan_name addr=$expected_addr 验证通过" >> $LOG_FILE

    if ! echo "$output" | grep -q "netmask=$expected_mask"; then
        print_error "LAN 子网掩码 验证失败，期望: $expected_mask"
        echo "✗ LAN 子网掩码 验证失败，期望: $expected_mask" >> $LOG_FILE
        return 1
    fi
    print_success "LAN $lan_name netmask=$expected_mask 验证通过"
    echo "✓ LAN $lan_name netmask=$expected_mask 验证通过" >> $LOG_FILE

    # 验证可选字段 - clone MAC
    if ! echo "$output" | grep -q "clonemac=$expected_clone_mac"; then
        print_error "LAN clone MAC 验证失败，期望: $expected_clone_mac"
        echo "✗ LAN clone MAC 验证失败，期望: $expected_clone_mac" >> $LOG_FILE
        return 1
    fi
    print_success "LAN $lan_name clonemac=$expected_clone_mac 验证通过"
    echo "✓ LAN $lan_name clonemac=$expected_clone_mac 验证通过" >> $LOG_FILE

    return 0
}

# 设置interface依赖项（LAN需要interface zone设置为inside）
setup_interface_for_lan() {
    local interface=$1

    print_info "设置接口 $interface 为接内模式..."
    echo "设置接口 $interface 为接内模式..." >> $LOG_FILE

    local output=$(floweye if set name=$interface mode=0 zone=inside mixmode=0 2>&1)
    if [ $? -eq 0 ]; then
        print_success "接口 $interface 设置为接内模式成功"
        return 0
    else
        print_error "接口 $interface 设置为接内模式失败: $output"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 清理函数
cleanup_lan() {
    local lan_name=$1
    echo "清理LAN $lan_name 配置..." >> $LOG_FILE
    floweye nat rmvproxy $lan_name >> $LOG_FILE 2>&1 || true
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有现有的LAN配置
cleanup_all_lan_configs() {
    echo "清理所有现有LAN配置..." >> $LOG_FILE

    # 获取所有LAN配置列表
    local lan_list=$(floweye nat listproxy type=lan json=1 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$lan_list" ] && [ "$lan_list" != "null" ]; then
        # 解析JSON并提取LAN名称
        local lan_names=$(echo "$lan_list" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

        if [ -n "$lan_names" ]; then
            echo "发现现有LAN配置: $lan_names" >> $LOG_FILE
            for lan_name in $lan_names; do
                echo "删除LAN配置: $lan_name" >> $LOG_FILE
                floweye nat rmvproxy "$lan_name" > /dev/null 2>&1
            done
            print_success "清理了现有LAN配置"
        fi
    fi
}

print_info() {
    echo -e "${BLUE}$1${NC}"
    echo "$1" >> $LOG_FILE
}

# 主测试流程
main() {
    print_header "LAN模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 设置interface依赖项
    print_header "设置interface依赖项"
    setup_interface_for_lan "eth2"

    # 清理现有配置
    print_header "清理现有配置"
    cleanup_all_lan_configs

    # 获取初始LAN状态
    print_header "获取初始LAN状态"
    echo "初始LAN状态:" >> $LOG_FILE
    floweye nat listproxy type=lan >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建LAN配置
    run_test "test_lan_new.json" "创建LAN配置"
    verify_lan "testlan" "eth2" "1500" "*************" "*************"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_lan_idempotent.json" "幂等性测试-重复创建"
    verify_lan "testlan" "eth2" "1500" "*************" "*************"

    # 1.3 字段修改测试
    run_test "test_lan_modify_mtu.json" "修改MTU字段"
    verify_lan "testlan" "eth2" "1400" "*************" "*************"

    run_test "test_lan_modify_ip.json" "修改IP地址"
    verify_lan "testlan" "eth2" "1400" "*************" "*************"

    run_test "test_lan_modify_mask.json" "修改子网掩码"
    verify_lan "testlan" "eth2" "1400" "*************" "*************"

    # 1.6 可选字段默认值恢复测试
    run_test "test_lan_optional_fields_complete.json" "创建包含所有可选字段的LAN配置"
    verify_lan_optional "testlanoptional" "eth1" "1400" "*************" "*************" "aa-bb-cc-dd-ee-ff"

    run_test "test_lan_optional_fields_default.json" "修改配置移除可选字段验证默认值恢复"
    verify_lan_optional "testlanoptional" "eth1" "1400" "*************" "*************" "00-00-00-00-00-00"

    # 1.4 删除配置测试
    run_test "test_lan_delete.json" "删除LAN配置"
    verify_lan_not_exists "testlan"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_lan_delete_idempotent.json" "删除配置幂等性测试"
    verify_lan_not_exists "testlan"

    # 阶段2: 全量同步测试
    print_header "阶段2: 全量同步测试"

    # 2.1 设置初始配置（增量模式）
    run_test "test_lan_full_sync_setup.json" "全量同步初始配置"
    verify_lan "lan-setup1" "eth2" "1500" "192.168.110.1" "*************"
    verify_lan "lan-setup2" "eth2" "1500" "192.168.111.1" "*************"

    # 2.2 启动全量同步
    start_full_sync || exit 1

    # 2.3 发送全量同步配置（只保留lan-setup1）
    run_test "test_lan_full_sync_cleanup.json" "全量同步清理配置"
    verify_lan "lan-setup1" "eth2" "1400" "192.168.110.1" "*************"

    # 2.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 2.5 验证清理结果：lan-setup2应该被删除
    sleep 2  # 等待清理完成
    verify_lan "lan-setup1" "eth2" "1400" "192.168.110.1" "*************"
    verify_lan_not_exists "lan-setup2"

    # 阶段3: 边界条件和错误处理测试
    print_header "阶段3: 边界条件和错误处理测试"

    # 3.1 无效参数测试（这些应该失败）
    run_test "test_lan_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_lan_error_no_ifname.json" "缺少ifname字段错误测试" "false"
    run_test "test_lan_error_invalid_mtu.json" "无效MTU错误测试" "false"
    run_test "test_lan_error_invalid_ip.json" "无效IP地址错误测试" "false"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_lan "testlan"
    cleanup_lan "testlanoptional"
    cleanup_lan "lan-setup1"
    cleanup_lan "lan-setup2"

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "LAN模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
