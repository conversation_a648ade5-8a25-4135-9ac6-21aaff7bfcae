[{"tx_id": "test-iwan-proxy-complete-modify-001", "device_tasks": [{"task_type": "TASK_IWAN_PROXY", "task_action": "EDIT_CONFIG", "iwan_proxy_task": {"name": "iwan2", "ifname": "wan1", "mtu": 1300, "svr_addr": "server2.example.com", "svr_port": 8080, "username": "admin2", "password": "admin456", "encrypt": false, "link": 456, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "*******"}, "max_delay": 200}, "dns_pxy": false}}]}]