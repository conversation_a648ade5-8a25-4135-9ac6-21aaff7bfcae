#!/bin/bash

# Effective Time模块综合测试脚本
# 测试所有effective time模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="effective_time_test_results.log"
echo "Effective Time模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证effective time配置
verify_effective_time() {
    local id=$1
    local expected_name=$2
    local expected_start_wday=$3
    local expected_end_wday=$4
    local expected_start_time=$5
    local expected_end_time=$6

    echo "验证effective time ID $id 配置..." >> $LOG_FILE
    local output=$(floweye rtptime get id=$id 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取effective time ID $id 配置"
        return 1
    fi

    # 验证name
    if echo "$output" | grep -q "name=$expected_name"; then
        print_success "Effective time $id name=$expected_name 验证通过"
    else
        print_error "Effective time $id name 验证失败，期望: $expected_name"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证start weekday
    if echo "$output" | grep -q "startwday=$expected_start_wday"; then
        print_success "Effective time $id startwday=$expected_start_wday 验证通过"
    else
        print_error "Effective time $id startwday 验证失败，期望: $expected_start_wday"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证end weekday
    if echo "$output" | grep -q "endwday=$expected_end_wday"; then
        print_success "Effective time $id endwday=$expected_end_wday 验证通过"
    else
        print_error "Effective time $id endwday 验证失败，期望: $expected_end_wday"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}

# 验证effective time不存在
verify_effective_time_not_exists() {
    local id=$1

    echo "验证effective time ID $id 不存在..." >> $LOG_FILE
    local output=$(floweye rtptime get id=$id 2>&1)

    if [ $? -ne 0 ] || echo "$output" | grep -q "not found\|NEXIST"; then
        print_success "Effective time $id 确认不存在"
        return 0
    else
        print_error "Effective time $id 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 清理effective time配置
cleanup_effective_time() {
    local id=$1
    echo "清理effective time ID $id 配置..." >> $LOG_FILE
    floweye rtptime remove id=$id >> $LOG_FILE 2>&1
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有测试effective time配置
cleanup_all_test_configs() {
    echo "清理所有测试effective time配置..." >> $LOG_FILE

    # 清理测试ID范围 100-110
    for id in {100..110}; do
        cleanup_effective_time $id
    done
}

# 主测试流程
main() {
    print_header "Effective Time模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_test_configs

    # 获取初始effective time状态
    print_header "获取初始effective time状态"
    echo "初始effective time状态:" >> $LOG_FILE
    floweye rtptime list >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础effective time配置
    run_test "test_effective_time_basic_new.json" "创建基础effective time配置"
    verify_effective_time "100" "test-effective-time" "1" "5" "08:00:00" "18:00:00"

    # 1.2 幂等性测试 - 重复新增相同配置
    run_test "test_effective_time_idempotent_new.json" "幂等性测试-重复新增"
    verify_effective_time "100" "test-effective-time" "1" "5" "08:00:00" "18:00:00"

    # 1.3 字段修改测试
    run_test "test_effective_time_modify_name.json" "修改name字段"
    verify_effective_time "100" "modified-time" "1" "5" "08:00:00" "18:00:00"

    run_test "test_effective_time_modify_time_range.json" "修改时间范围"
    verify_effective_time "100" "modified-time" "2" "6" "09:00:00" "17:00:00"

    run_test "test_effective_time_modify_weekdays.json" "修改星期范围"
    verify_effective_time "100" "modified-time" "1" "7" "09:00:00" "17:00:00"

    # 1.4 删除配置测试
    run_test "test_effective_time_delete.json" "删除配置测试"
    verify_effective_time_not_exists "100"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_effective_time_delete_idempotent.json" "删除配置幂等性测试"
    verify_effective_time_not_exists "100"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建包含所有字段的完整配置
    run_test "test_effective_time_complete_config.json" "创建完整配置"
    verify_effective_time "101" "complete-config" "1" "7" "00:00:00" "23:59:59"

    # 2.2 修改完整配置
    run_test "test_effective_time_complete_modify.json" "修改完整配置"
    verify_effective_time "101" "complete-modified" "2" "5" "08:30:00" "17:30:00"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 设置初始配置（增量模式）
    run_test "test_effective_time_full_sync_setup.json" "全量同步初始配置"
    verify_effective_time "102" "sync-test-1" "1" "5" "08:00:00" "18:00:00"
    verify_effective_time "103" "sync-test-2" "6" "7" "10:00:00" "22:00:00"
    verify_effective_time "104" "sync-test-3" "1" "7" "00:00:00" "23:59:59"

    # 3.2 启动全量同步
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只包含需要保留的配置）
    run_test "test_effective_time_full_sync_cleanup.json" "全量同步清理配置"
    verify_effective_time "102" "sync-test-1-updated" "2" "6" "09:00:00" "17:00:00"

    # 3.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 3.5 验证清理结果：未在全量同步中的配置应被删除
    sleep 2  # 等待清理完成
    verify_effective_time "102" "sync-test-1-updated" "2" "6" "09:00:00" "17:00:00"
    verify_effective_time_not_exists "103"
    verify_effective_time_not_exists "104"

    # 阶段4: 边界条件和错误处理测试
    print_header "阶段4: 边界条件和错误处理测试"

    # 4.1 无效参数测试（这些应该失败）
    run_test "test_effective_time_error_no_id.json" "缺少ID字段错误测试" "false"
    run_test "test_effective_time_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_effective_time_error_no_time_range.json" "缺少time_range字段错误测试" "false"
    run_test "test_effective_time_error_invalid_weekday.json" "无效星期几错误测试" "false"
    run_test "test_effective_time_error_invalid_time.json" "无效时间格式错误测试" "false"

    # 4.2 边界值测试
    run_test "test_effective_time_boundary_min_id.json" "最小ID边界测试"
    verify_effective_time "1" "min-id-test" "1" "1" "00:00:00" "00:00:01"

    run_test "test_effective_time_boundary_max_id.json" "最大ID边界测试"
    verify_effective_time "128" "max-id-test" "7" "7" "23:59:58" "23:59:59"

    # 阶段5: 默认值验证测试
    print_header "阶段5: 默认值验证测试"

    # 5.1 创建包含所有字段的完整配置
    run_test "test_effective_time_optional_fields_complete.json" "创建包含所有字段的完整配置"
    verify_effective_time "105" "optional-complete" "1" "5" "08:00:00" "18:00:00"

    # 5.2 修改配置，移除可选字段，验证默认值恢复
    run_test "test_effective_time_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_effective_time "105" "optional-default" "1" "7" "00:00:00" "23:59:59"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_test_configs
    cleanup_effective_time "1"
    cleanup_effective_time "128"

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Effective Time模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
