#!/bin/bash

# iWAN Service测试设置验证脚本
# 验证所有测试文件和配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

check_file() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        print_success "$description 存在"
        return 0
    else
        print_error "$description 不存在: $file"
        return 1
    fi
}

check_json_file() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        if python3 -m json.tool "$file" > /dev/null 2>&1; then
            print_success "$description JSON格式正确"
            return 0
        else
            print_error "$description JSON格式错误: $file"
            return 1
        fi
    else
        print_error "$description 不存在: $file"
        return 1
    fi
}

check_executable() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -x "$file" ]; then
        print_success "$description 可执行"
        return 0
    else
        print_error "$description 不可执行: $file"
        return 1
    fi
}

main() {
    print_header "iWAN Service测试设置验证"
    
    # 检查主测试脚本
    print_header "检查主测试脚本"
    check_file "iwan_service_comprehensive_test.sh" "主测试脚本"
    check_executable "iwan_service_comprehensive_test.sh" "主测试脚本"
    
    # 检查基础CRUD测试用例
    print_header "检查基础CRUD测试用例"
    check_json_file "test_iwan_service_basic_new.json" "基础配置创建"
    check_json_file "test_iwan_service_idempotent.json" "幂等性测试"
    check_json_file "test_iwan_service_modify_addr.json" "地址修改测试"
    check_json_file "test_iwan_service_modify_mtu.json" "MTU修改测试"
    check_json_file "test_iwan_service_modify_pool.json" "用户组修改测试"
    check_json_file "test_iwan_service_delete.json" "删除测试"
    check_json_file "test_iwan_service_delete_idempotent.json" "删除幂等性测试"
    
    # 检查完整配置测试用例
    print_header "检查完整配置测试用例"
    check_json_file "test_iwan_service_complete_config.json" "完整配置创建"
    check_json_file "test_iwan_service_complete_modify.json" "完整配置修改"
    
    # 检查全量同步测试用例
    print_header "检查全量同步测试用例"
    check_json_file "test_iwan_service_full_sync_setup.json" "全量同步初始配置"
    check_json_file "test_iwan_service_full_sync_cleanup.json" "全量同步清理配置"
    
    # 检查可选字段测试用例
    print_header "检查可选字段测试用例"
    check_json_file "test_iwan_service_optional_fields_complete.json" "完整字段配置"
    check_json_file "test_iwan_service_optional_fields_default.json" "默认值恢复配置"
    
    # 检查错误处理测试用例
    print_header "检查错误处理测试用例"
    check_json_file "test_iwan_service_error_no_name.json" "缺少name字段错误"
    check_json_file "test_iwan_service_error_no_addr.json" "缺少addr字段错误"
    check_json_file "test_iwan_service_error_no_mtu.json" "缺少mtu字段错误"
    check_json_file "test_iwan_service_error_no_pool.json" "缺少pool字段错误"
    check_json_file "test_iwan_service_error_invalid_mtu.json" "无效MTU错误"
    check_json_file "test_iwan_service_error_invalid_pool.json" "无效用户组错误"
    
    # 检查文档文件
    print_header "检查文档文件"
    check_file "IWAN_SERVICE_TEST_README.md" "测试文档"
    check_file "IWAN_SERVICE_TEST_SUMMARY.md" "测试总结模板"
    
    # 检查Makefile目标
    print_header "检查Makefile目标"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if grep -q "test-iwan-service:" ../Makefile; then
        print_success "Makefile包含test-iwan-service目标"
    else
        print_error "Makefile缺少test-iwan-service目标"
    fi
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if grep -q "test-iwan-service-comprehensive:" ../Makefile; then
        print_success "Makefile包含test-iwan-service-comprehensive目标"
    else
        print_error "Makefile缺少test-iwan-service-comprehensive目标"
    fi
    
    # 检查测试脚本语法
    print_header "检查脚本语法"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if bash -n iwan_service_comprehensive_test.sh; then
        print_success "测试脚本语法正确"
    else
        print_error "测试脚本语法错误"
    fi
    
    # 统计结果
    print_header "验证结果统计"
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过检查: $PASSED_CHECKS"
    echo "失败检查: $FAILED_CHECKS"
    echo "成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "所有检查通过！iWAN Service测试设置完整且正确。"
        echo ""
        echo "可以执行以下命令进行测试："
        echo "  make test-iwan-service"
        echo "  VERBOSE=true make test-iwan-service"
        exit 0
    else
        print_error "有 $FAILED_CHECKS 个检查失败，请修复后重新验证。"
        exit 1
    fi
}

# 执行主函数
main "$@"
