#!/bin/bash

# DHCP模块综合测试脚本
# 测试所有DHCP模块的核心功能和边界条件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="dhcp_test_results.log"
echo "DHCP模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}$1${NC}"
    echo "$1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    if ../agent-debug-client --config=$test_file >> $LOG_FILE 2>&1; then
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证DHCP配置
verify_dhcp() {
    local lan_name=$1
    local expected_enable=$2
    local expected_pool=$3
    local expected_lease_ttl=$4
    local expected_dns0=${5:-""}
    local expected_dns1=${6:-""}
    
    echo "验证DHCP $lan_name 配置..." >> $LOG_FILE
    local output=$(floweye nat getproxy $lan_name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取LAN $lan_name 配置"
        return 1
    fi
    
    # 验证dhcp_enable
    if echo "$output" | grep -q "dhcp_enable=$expected_enable"; then
        print_success "DHCP $lan_name dhcp_enable=$expected_enable 验证通过"
    else
        print_error "DHCP $lan_name dhcp_enable 验证失败，期望: $expected_enable"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 如果DHCP启用，验证其他参数
    if [ "$expected_enable" = "1" ]; then
        # 验证dhcp_pool
        if [ -n "$expected_pool" ] && echo "$output" | grep -q "dhcp_pool=$expected_pool"; then
            print_success "DHCP $lan_name dhcp_pool=$expected_pool 验证通过"
        elif [ -n "$expected_pool" ]; then
            print_error "DHCP $lan_name dhcp_pool 验证失败，期望: $expected_pool"
            echo "$output" >> $LOG_FILE
            return 1
        fi
        
        # 验证leasettl
        if [ -n "$expected_lease_ttl" ] && echo "$output" | grep -q "leasettl=$expected_lease_ttl"; then
            print_success "DHCP $lan_name leasettl=$expected_lease_ttl 验证通过"
        elif [ -n "$expected_lease_ttl" ]; then
            print_error "DHCP $lan_name leasettl 验证失败，期望: $expected_lease_ttl"
            echo "$output" >> $LOG_FILE
            return 1
        fi
        
        # 验证dns0（如果指定）
        if [ -n "$expected_dns0" ]; then
            if echo "$output" | grep -q "dns0=$expected_dns0"; then
                print_success "DHCP $lan_name dns0=$expected_dns0 验证通过"
            else
                print_error "DHCP $lan_name dns0 验证失败，期望: $expected_dns0"
                echo "$output" >> $LOG_FILE
                return 1
            fi
        fi
        
        # 验证dns1（如果指定）
        if [ -n "$expected_dns1" ]; then
            if echo "$output" | grep -q "dns1=$expected_dns1"; then
                print_success "DHCP $lan_name dns1=$expected_dns1 验证通过"
            else
                print_error "DHCP $lan_name dns1 验证失败，期望: $expected_dns1"
                echo "$output" >> $LOG_FILE
                return 1
            fi
        fi
    fi
    
    return 0
}

# 验证DHCP配置包括可选字段
verify_dhcp_optional() {
    local lan_name=$1
    local expected_enable=$2
    local expected_pool=$3
    local expected_lease=$4
    local expected_dns0=$5
    local expected_dns1=$6
    local expected_gateway=$7
    local expected_mask=$8
    local expected_domain=$9

    echo "验证DHCP $lan_name 配置（包括可选字段）..." >> $LOG_FILE

    local output=$(floweye nat getproxy $lan_name)
    echo "DHCP配置输出: $output" >> $LOG_FILE

    # 验证DHCP启用状态
    if ! echo "$output" | grep -q "dhcp_enable=$expected_enable"; then
        print_error "DHCP $lan_name dhcp_enable 验证失败，期望: $expected_enable"
        echo "✗ DHCP $lan_name dhcp_enable 验证失败，期望: $expected_enable" >> $LOG_FILE
        return 1
    fi
    print_success "DHCP $lan_name dhcp_enable=$expected_enable 验证通过"
    echo "✓ DHCP $lan_name dhcp_enable=$expected_enable 验证通过" >> $LOG_FILE

    # 如果DHCP未启用，不需要验证其他字段
    if [ "$expected_enable" = "0" ]; then
        return 0
    fi

    # 验证DHCP地址池
    if [ -n "$expected_pool" ] && ! echo "$output" | grep -q "dhcp_pool=$expected_pool"; then
        print_error "DHCP $lan_name dhcp_pool 验证失败，期望: $expected_pool"
        echo "✗ DHCP $lan_name dhcp_pool 验证失败，期望: $expected_pool" >> $LOG_FILE
        return 1
    fi
    print_success "DHCP $lan_name dhcp_pool=$expected_pool 验证通过"
    echo "✓ DHCP $lan_name dhcp_pool=$expected_pool 验证通过" >> $LOG_FILE

    # 验证租约时间
    if [ -n "$expected_lease" ] && ! echo "$output" | grep -q "leasettl=$expected_lease"; then
        print_error "DHCP $lan_name leasettl 验证失败，期望: $expected_lease"
        echo "✗ DHCP $lan_name leasettl 验证失败，期望: $expected_lease" >> $LOG_FILE
        return 1
    fi
    print_success "DHCP $lan_name leasettl=$expected_lease 验证通过"
    echo "✓ DHCP $lan_name leasettl=$expected_lease 验证通过" >> $LOG_FILE

    # 验证DNS0（可选字段）
    if [ -n "$expected_dns0" ]; then
        if ! echo "$output" | grep -q "dns0=$expected_dns0"; then
            print_error "DHCP $lan_name dns0 验证失败，期望: $expected_dns0"
            echo "✗ DHCP $lan_name dns0 验证失败，期望: $expected_dns0" >> $LOG_FILE
            return 1
        fi
        print_success "DHCP $lan_name dns0=$expected_dns0 验证通过"
        echo "✓ DHCP $lan_name dns0=$expected_dns0 验证通过" >> $LOG_FILE
    else
        # 验证DNS0为空或默认值
        print_success "DHCP $lan_name dns0 为空（默认值）验证通过"
        echo "✓ DHCP $lan_name dns0 为空（默认值）验证通过" >> $LOG_FILE
    fi

    # 验证DNS1（可选字段）
    if [ -n "$expected_dns1" ]; then
        if ! echo "$output" | grep -q "dns1=$expected_dns1"; then
            print_error "DHCP $lan_name dns1 验证失败，期望: $expected_dns1"
            echo "✗ DHCP $lan_name dns1 验证失败，期望: $expected_dns1" >> $LOG_FILE
            return 1
        fi
        print_success "DHCP $lan_name dns1=$expected_dns1 验证通过"
        echo "✓ DHCP $lan_name dns1=$expected_dns1 验证通过" >> $LOG_FILE
    else
        # 验证DNS1为空或默认值
        print_success "DHCP $lan_name dns1 为空（默认值）验证通过"
        echo "✓ DHCP $lan_name dns1 为空（默认值）验证通过" >> $LOG_FILE
    fi

    # 验证DHCP网关
    if [ -n "$expected_gateway" ] && ! echo "$output" | grep -q "dhcp_gateway=$expected_gateway"; then
        print_error "DHCP $lan_name dhcp_gateway 验证失败，期望: $expected_gateway"
        echo "✗ DHCP $lan_name dhcp_gateway 验证失败，期望: $expected_gateway" >> $LOG_FILE
        return 1
    fi
    print_success "DHCP $lan_name dhcp_gateway=$expected_gateway 验证通过"
    echo "✓ DHCP $lan_name dhcp_gateway=$expected_gateway 验证通过" >> $LOG_FILE

    # 验证DHCP掩码
    if [ -n "$expected_mask" ] && ! echo "$output" | grep -q "dhcp_mask=$expected_mask"; then
        print_error "DHCP $lan_name dhcp_mask 验证失败，期望: $expected_mask"
        echo "✗ DHCP $lan_name dhcp_mask 验证失败，期望: $expected_mask" >> $LOG_FILE
        return 1
    fi
    print_success "DHCP $lan_name dhcp_mask=$expected_mask 验证通过"
    echo "✓ DHCP $lan_name dhcp_mask=$expected_mask 验证通过" >> $LOG_FILE

    # 验证DHCP域名（可选字段）
    if [ -n "$expected_domain" ]; then
        if ! echo "$output" | grep -q "dhcp_domain=$expected_domain"; then
            print_error "DHCP $lan_name dhcp_domain 验证失败，期望: $expected_domain"
            echo "✗ DHCP $lan_name dhcp_domain 验证失败，期望: $expected_domain" >> $LOG_FILE
            return 1
        fi
        print_success "DHCP $lan_name dhcp_domain=$expected_domain 验证通过"
        echo "✓ DHCP $lan_name dhcp_domain=$expected_domain 验证通过" >> $LOG_FILE
    else
        # 验证域名为空或默认值
        print_success "DHCP $lan_name dhcp_domain 为空（默认值）验证通过"
        echo "✓ DHCP $lan_name dhcp_domain 为空（默认值）验证通过" >> $LOG_FILE
    fi

    return 0
}

# 创建LAN配置（DHCP的前置条件）
create_lan_for_dhcp() {
    local lan_name=$1
    local ifname=$2
    local addr=$3
    local mask=$4

    print_info "创建LAN $lan_name 作为DHCP前置条件..."
    echo "创建LAN $lan_name 作为DHCP前置条件..." >> $LOG_FILE

    # 检查接口状态
    echo "检查接口 $ifname 状态..." >> $LOG_FILE
    floweye if list >> $LOG_FILE 2>&1

    # 检查是否已存在同名LAN
    echo "检查是否已存在LAN $lan_name..." >> $LOG_FILE
    local existing=$(floweye nat getproxy $lan_name 2>&1)
    if [ $? -eq 0 ]; then
        print_info "LAN $lan_name 已存在，先删除..."
        floweye nat rmvproxy $lan_name >> $LOG_FILE 2>&1
        sleep 1
    fi

    local output=$(floweye nat addrtif name=$lan_name ifname=$ifname mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 vlan=0 clonemac=00-00-00-00-00-00 standby=NULL addr=$addr mask=$mask 2>&1)
    local exit_code=$?
    echo "floweye addrtif 命令输出: $output" >> $LOG_FILE
    echo "floweye addrtif 退出码: $exit_code" >> $LOG_FILE

    # 检查退出码和输出内容
    if [ $exit_code -eq 0 ] && [ "$output" != "INV_NAME" ] && [ "$output" != "NEXIST" ] && [ "$output" != "EXIST" ]; then
        print_success "LAN $lan_name 创建成功"
        # 验证创建结果
        echo "验证LAN $lan_name 创建结果..." >> $LOG_FILE
        local verify_output=$(floweye nat getproxy $lan_name 2>&1)
        echo "验证输出: $verify_output" >> $LOG_FILE
        if echo "$verify_output" | grep -q "name=$lan_name"; then
            print_success "LAN $lan_name 验证成功"
            return 0
        else
            print_error "LAN $lan_name 创建后验证失败: $verify_output"
            return 1
        fi
    else
        print_error "LAN $lan_name 创建失败: $output (退出码: $exit_code)"
        echo "LAN创建失败详细信息: $output" >> $LOG_FILE
        echo "当前LAN列表:" >> $LOG_FILE
        floweye nat listproxy type=lan >> $LOG_FILE 2>&1
        return 1
    fi
}

# 设置interface依赖项（LAN需要interface zone设置为inside）
setup_interface_for_lan() {
    local interface=$1

    print_info "设置接口 $interface 为接内模式..."
    echo "设置接口 $interface 为接内模式..." >> $LOG_FILE

    local output=$(floweye if set name=$interface mode=0 zone=inside mixmode=0 2>&1)
    if [ $? -eq 0 ]; then
        print_success "接口 $interface 设置为接内模式成功"
        return 0
    else
        print_error "接口 $interface 设置为接内模式失败: $output"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 清理函数
cleanup_lan() {
    local lan_name=$1
    echo "清理LAN $lan_name 配置..." >> $LOG_FILE
    floweye nat rmvproxy $lan_name >> $LOG_FILE 2>&1 || true
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有现有的LAN配置
cleanup_all_lan_configs() {
    echo "清理所有现有LAN配置..." >> $LOG_FILE

    # 获取所有LAN配置列表
    local lan_list=$(floweye nat listproxy type=lan json=1 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$lan_list" ] && [ "$lan_list" != "null" ]; then
        # 解析JSON并提取LAN名称
        local lan_names=$(echo "$lan_list" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

        if [ -n "$lan_names" ]; then
            echo "发现现有LAN配置: $lan_names" >> $LOG_FILE
            for lan_name in $lan_names; do
                echo "删除LAN配置: $lan_name" >> $LOG_FILE
                floweye nat rmvproxy "$lan_name" > /dev/null 2>&1
            done
            print_success "清理了现有LAN配置"
        fi
    fi
}

# 主测试流程
main() {
    print_header "DHCP模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 设置interface依赖项
    print_header "设置interface依赖项"
    setup_interface_for_lan "eth2"

    # 清理现有配置
    print_header "清理现有配置"
    cleanup_all_lan_configs

    # 获取初始LAN状态
    print_header "获取初始LAN状态"
    echo "初始LAN状态:" >> $LOG_FILE
    floweye nat listproxy type=lan >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建LAN作为DHCP前置条件
    if ! create_lan_for_dhcp "testdhcplan" "eth2" "192.168.200.1" "255.255.255.0"; then
        print_error "无法创建LAN testdhcplan，测试无法继续"
        exit 1
    fi

    # 1.2 启用DHCP配置
    run_test "test_dhcp_enable.json" "启用DHCP配置"
    verify_dhcp "testdhcplan" "1" "192.168.200.100-192.168.200.200" "86400" "8.8.8.8" "8.8.4.4"

    # 1.3 幂等性测试 - 重复启用相同配置
    run_test "test_dhcp_idempotent.json" "幂等性测试-重复启用"
    verify_dhcp "testdhcplan" "1" "192.168.200.100-192.168.200.200" "86400" "8.8.8.8" "8.8.4.4"

    # 1.4 字段修改测试
    run_test "test_dhcp_modify_pool.json" "修改DHCP地址池"
    verify_dhcp "testdhcplan" "1" "192.168.200.50-192.168.200.150" "86400" "8.8.8.8" "8.8.4.4"

    run_test "test_dhcp_modify_lease.json" "修改租约时间"
    verify_dhcp "testdhcplan" "1" "192.168.200.50-192.168.200.150" "3600" "8.8.8.8" "8.8.4.4"

    run_test "test_dhcp_modify_dns.json" "修改DNS服务器"
    verify_dhcp "testdhcplan" "1" "192.168.200.50-192.168.200.150" "3600" "114.114.114.114" "223.5.5.5"

    # 1.6 可选字段默认值恢复测试
    setup_interface_for_lan "eth0"
    if ! create_lan_for_dhcp "testdhcpopt" "eth0" "192.168.220.1" "255.255.255.0"; then
        print_error "无法创建LAN testdhcpopt，跳过可选字段测试"
        FAILED_TESTS=$((FAILED_TESTS + 2))  # 跳过2个测试
        TOTAL_TESTS=$((TOTAL_TESTS + 2))
    else
        run_test "test_dhcp_optional_fields_complete.json" "创建包含所有可选字段的DHCP配置"
        verify_dhcp_optional "testdhcpopt" "1" "192.168.220.100-192.168.220.200" "7200" "8.8.8.8" "8.8.4.4" "192.168.220.1" "255.255.255.0" "test.local"

        run_test "test_dhcp_optional_fields_default.json" "修改配置移除可选字段验证默认值恢复"
        verify_dhcp_optional "testdhcpopt" "1" "192.168.220.100-192.168.220.200" "7200" "" "" "192.168.220.1" "255.255.255.0" ""
    fi

    # 1.5 禁用DHCP测试
    run_test "test_dhcp_disable.json" "禁用DHCP配置"
    verify_dhcp "testdhcplan" "0"

    # 1.6 删除DHCP配置测试（实际是禁用）
    run_test "test_dhcp_delete.json" "删除DHCP配置"
    verify_dhcp "testdhcplan" "0"

    # 1.7 删除不存在配置的幂等性测试
    run_test "test_dhcp_delete_idempotent.json" "删除配置幂等性测试"
    verify_dhcp "testdhcplan" "0"

    # 阶段2: 全量同步测试
    print_header "阶段2: 全量同步测试"

    # 2.1 设置初始配置（增量模式）
    setup_interface_for_lan "eth1"
    if ! create_lan_for_dhcp "dhcp-setup1" "eth1" "192.168.210.1" "255.255.255.0"; then
        print_error "无法创建LAN dhcp-setup1，跳过全量同步测试"
        FAILED_TESTS=$((FAILED_TESTS + 5))  # 跳过全量同步相关测试
        TOTAL_TESTS=$((TOTAL_TESTS + 5))
        print_header "阶段3: 边界条件和错误处理测试"
    else
        setup_interface_for_lan "eth0"
        if ! create_lan_for_dhcp "dhcp-setup2" "eth0" "192.168.211.1" "255.255.255.0"; then
            print_error "无法创建LAN dhcp-setup2，跳过全量同步测试"
            cleanup_lan "dhcp-setup1"
            FAILED_TESTS=$((FAILED_TESTS + 5))  # 跳过全量同步相关测试
            TOTAL_TESTS=$((TOTAL_TESTS + 5))
            print_header "阶段3: 边界条件和错误处理测试"
        else
            # 继续全量同步测试
            run_test "test_dhcp_full_sync_setup.json" "全量同步初始配置"
            verify_dhcp "dhcp-setup1" "1" "192.168.210.100-192.168.210.200" "86400"
            verify_dhcp "dhcp-setup2" "1" "192.168.211.100-192.168.211.200" "86400"

            # 2.2 启动全量同步
            if start_full_sync; then
                # 2.3 发送全量同步配置（只保留dhcp-setup1）
                run_test "test_dhcp_full_sync_cleanup.json" "全量同步清理配置"
                verify_dhcp "dhcp-setup1" "1" "192.168.210.50-192.168.210.150" "3600"

                # 2.4 结束全量同步，触发清理逻辑
                if end_full_sync; then
                    # 2.5 验证清理结果：dhcp-setup2的LAN应该被删除
                    sleep 2  # 等待清理完成
                    verify_dhcp "dhcp-setup1" "1" "192.168.210.50-192.168.210.150" "3600"

                    # 验证dhcp-setup2的LAN被删除（因为没有在全量同步中配置）
                    echo "验证LAN dhcp-setup2 不存在..." >> $LOG_FILE
                    local output=$(floweye nat getproxy dhcp-setup2 2>&1)
                    if echo "$output" | grep -q "NEXIST" || [ $? -ne 0 ]; then
                        print_success "LAN dhcp-setup2 确实被删除了（符合全量同步清理逻辑）"
                    else
                        print_error "LAN dhcp-setup2 仍然存在，全量同步清理失败"
                        echo "$output" >> $LOG_FILE
                    fi
                else
                    print_error "全量同步结束失败"
                fi
            else
                print_error "全量同步启动失败"
            fi
        fi
    fi

    # 阶段3: 边界条件和错误处理测试
    print_header "阶段3: 边界条件和错误处理测试"

    # 3.1 无效参数测试（这些应该失败）
    run_test "test_dhcp_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_dhcp_error_nonexistent_lan.json" "不存在LAN错误测试" "false"
    run_test "test_dhcp_error_invalid_pool.json" "无效地址池错误测试" "false"
    run_test "test_dhcp_error_invalid_lease.json" "无效租约时间错误测试" "false"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_lan "testdhcplan"
    cleanup_lan "testdhcpopt"
    cleanup_lan "dhcp-setup1"
    cleanup_lan "dhcp-setup2"

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "DHCP模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
