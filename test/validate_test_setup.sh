#!/bin/bash

# LAN和DHCP模块测试设置验证脚本
# 验证测试环境和文件的完整性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

check_file() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        print_success "$description 存在"
        return 0
    else
        print_error "$description 不存在: $file"
        return 1
    fi
}

check_executable() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -x "$file" ]; then
        print_success "$description 可执行"
        return 0
    else
        print_error "$description 不可执行: $file"
        return 1
    fi
}

check_json_syntax() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if python3 -m json.tool "$file" > /dev/null 2>&1; then
        print_success "$description JSON格式正确"
        return 0
    else
        print_error "$description JSON格式错误: $file"
        return 1
    fi
}

check_bash_syntax() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if bash -n "$file" 2>/dev/null; then
        print_success "$description Bash语法正确"
        return 0
    else
        print_error "$description Bash语法错误: $file"
        return 1
    fi
}

main() {
    print_header "LAN和DHCP模块测试设置验证"
    
    # 验证测试脚本
    print_header "验证测试脚本"
    check_file "lan_comprehensive_test.sh" "LAN综合测试脚本"
    check_file "dhcp_comprehensive_test.sh" "DHCP综合测试脚本"
    check_executable "lan_comprehensive_test.sh" "LAN综合测试脚本"
    check_executable "dhcp_comprehensive_test.sh" "DHCP综合测试脚本"
    check_bash_syntax "lan_comprehensive_test.sh" "LAN综合测试脚本"
    check_bash_syntax "dhcp_comprehensive_test.sh" "DHCP综合测试脚本"
    
    # 验证LAN测试用例
    print_header "验证LAN测试用例"
    local lan_test_files=(
        "test_lan_new.json"
        "test_lan_idempotent.json"
        "test_lan_modify_mtu.json"
        "test_lan_modify_ip.json"
        "test_lan_modify_mask.json"
        "test_lan_delete.json"
        "test_lan_delete_idempotent.json"
        "test_lan_full_sync_setup.json"
        "test_lan_full_sync_cleanup.json"
        "test_lan_error_no_name.json"
        "test_lan_error_no_ifname.json"
        "test_lan_error_invalid_mtu.json"
        "test_lan_error_invalid_ip.json"
    )
    
    for file in "${lan_test_files[@]}"; do
        check_file "$file" "LAN测试用例: $file"
        if [ -f "$file" ]; then
            check_json_syntax "$file" "LAN测试用例: $file"
        fi
    done
    
    # 验证DHCP测试用例
    print_header "验证DHCP测试用例"
    local dhcp_test_files=(
        "test_dhcp_enable.json"
        "test_dhcp_idempotent.json"
        "test_dhcp_modify_pool.json"
        "test_dhcp_modify_lease.json"
        "test_dhcp_modify_dns.json"
        "test_dhcp_disable.json"
        "test_dhcp_delete.json"
        "test_dhcp_delete_idempotent.json"
        "test_dhcp_full_sync_setup.json"
        "test_dhcp_full_sync_cleanup.json"
        "test_dhcp_error_no_name.json"
        "test_dhcp_error_nonexistent_lan.json"
        "test_dhcp_error_invalid_pool.json"
        "test_dhcp_error_invalid_lease.json"
    )
    
    for file in "${dhcp_test_files[@]}"; do
        check_file "$file" "DHCP测试用例: $file"
        if [ -f "$file" ]; then
            check_json_syntax "$file" "DHCP测试用例: $file"
        fi
    done
    
    # 验证文档
    print_header "验证文档"
    check_file "LAN_DHCP_TEST_README.md" "测试文档"
    
    # 验证Makefile目标
    print_header "验证Makefile目标"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if grep -q "test-lan:" ../Makefile && grep -q "test-dhcp:" ../Makefile; then
        print_success "Makefile包含LAN和DHCP测试目标"
    else
        print_error "Makefile缺少LAN或DHCP测试目标"
    fi
    
    # 统计结果
    print_header "验证结果统计"
    echo "总验证项: $TOTAL_CHECKS"
    echo "通过验证: $PASSED_CHECKS"
    echo "失败验证: $FAILED_CHECKS"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "${GREEN}所有验证通过！测试设置完整。${NC}"
        echo ""
        echo "可以执行以下命令进行测试："
        echo "  make test-lan              # 执行LAN模块测试"
        echo "  make test-dhcp             # 执行DHCP模块测试"
        echo "  make test-all              # 执行所有模块测试"
        echo "  VERBOSE=true make test-lan # 详细模式执行LAN测试"
        exit 0
    else
        echo -e "${RED}有 $FAILED_CHECKS 个验证失败，请检查并修复。${NC}"
        exit 1
    fi
}

# 执行主函数
main "$@"
