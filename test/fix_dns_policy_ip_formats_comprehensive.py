#!/usr/bin/env python3
"""
全面修复DNS policy测试用例中的IP地址格式问题
"""

import json
import os
import glob
import re
import socket
import struct

def ip_to_int(ip_str):
    """将IP地址字符串转换为整数"""
    try:
        return struct.unpack('!I', socket.inet_aton(ip_str))[0]
    except:
        return None

def fix_ip_address_format(obj):
    """递归修复对象中的IP地址格式"""
    if isinstance(obj, dict):
        # 情况1: 错误的CIDR格式 {"ip": {"ipv4": value, "prefix_length": value}}
        if "ip" in obj and isinstance(obj["ip"], dict):
            ip_content = obj["ip"]
            if "ipv4" in ip_content and "prefix_length" in ip_content:
                # 处理字符串格式的IP地址
                ipv4_value = ip_content["ipv4"]
                if isinstance(ipv4_value, str):
                    # 如果是CIDR格式字符串，提取IP和前缀长度
                    if "/" in ipv4_value:
                        ip_str, prefix_str = ipv4_value.split("/")
                        ip_int = ip_to_int(ip_str)
                        if ip_int is not None:
                            return {"ip": {"v4_cidr": {"ip": ip_int, "prefix_length": int(prefix_str)}}}
                    else:
                        # 单纯的IP地址字符串
                        ip_int = ip_to_int(ipv4_value)
                        if ip_int is not None:
                            return {"ip": {"ipv4": ip_int}}
                else:
                    # 数字格式，但结构错误
                    return {"ip": {"v4_cidr": {"ip": ipv4_value, "prefix_length": ip_content["prefix_length"]}}}
        
        # 情况2: 裸露的IP地址格式需要包装
        if "ipv4" in obj and isinstance(obj["ipv4"], str):
            # 字符串格式的IP地址
            ip_int = ip_to_int(obj["ipv4"])
            if ip_int is not None:
                return {"ip": {"ipv4": ip_int}}
        
        # 情况3: IP范围格式修复
        if "ip_range" in obj:
            ip_range = obj["ip_range"]
            result = {"ip_range": {}}
            for key in ["start_ip", "end_ip"]:
                if key in ip_range:
                    ip_value = ip_range[key]
                    if isinstance(ip_value, dict):
                        if "ipv4" in ip_value:
                            ipv4_val = ip_value["ipv4"]
                            if isinstance(ipv4_val, str):
                                ip_int = ip_to_int(ipv4_val)
                                if ip_int is not None:
                                    result["ip_range"][key] = {"ip": {"ipv4": ip_int}}
                                else:
                                    result["ip_range"][key] = {"ip": {"ipv4": ipv4_val}}
                            else:
                                result["ip_range"][key] = {"ip": {"ipv4": ipv4_val}}
                        else:
                            result["ip_range"][key] = fix_ip_address_format(ip_value)
                    else:
                        result["ip_range"][key] = ip_value
            return result
        
        # 递归处理所有字段
        result = {}
        for key, value in obj.items():
            result[key] = fix_ip_address_format(value)
        return result
    elif isinstance(obj, list):
        return [fix_ip_address_format(item) for item in obj]
    else:
        return obj

def fix_file(filepath):
    """修复单个JSON文件"""
    print(f"检查文件: {filepath}")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 修复IP地址格式
        original_data = json.dumps(data, sort_keys=True)
        fixed_data = fix_ip_address_format(data)
        fixed_json = json.dumps(fixed_data, sort_keys=True)
        
        # 检查是否有变化
        if original_data != fixed_json:
            # 写回文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(fixed_data, f, indent=2, ensure_ascii=False)
            print(f"✓ 修复完成: {filepath}")
            return True
        else:
            print(f"- 无需修复: {filepath}")
            return False
        
    except Exception as e:
        print(f"✗ 修复失败: {filepath} - {e}")
        return False

def main():
    """主函数"""
    # 获取所有DNS policy测试文件
    pattern = "test_dns_policy_*.json"
    files = glob.glob(pattern)
    
    if not files:
        print("未找到DNS policy测试文件")
        return
    
    print(f"找到 {len(files)} 个DNS policy测试文件")
    
    fixed_count = 0
    for filepath in sorted(files):
        if fix_file(filepath):
            fixed_count += 1
    
    print(f"\n修复完成: {fixed_count}/{len(files)} 个文件被修复")

if __name__ == "__main__":
    main()
