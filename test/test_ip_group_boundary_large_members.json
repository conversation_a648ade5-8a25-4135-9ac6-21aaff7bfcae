[{"tx_id": "ip-group-boundary-large-001", "device_tasks": [{"task_type": "TASK_IP_GROUP", "task_action": "NEW_CONFIG", "ip_group_task": {"name": "test-ip-group-boundary", "members": [{"ip_addr": {"ip": {"ip_string": "***********"}}, "info": "server-1"}, {"ip_addr": {"ip": {"ip_string": "***********"}}, "info": "server-2"}, {"ip_addr": {"ip": {"ip_string": "***********"}}, "info": "server-3"}, {"ip_addr": {"ip": {"ip_string": "***********"}}, "info": "server-4"}, {"ip_addr": {"ip": {"ip_string": "***********"}}, "info": "server-5"}, {"ip_addr": {"ip": {"ip_string": "***********/24"}}, "info": "network-1"}, {"ip_addr": {"ip": {"ip_string": "***********/24"}}, "info": "network-2"}, {"ip_addr": {"ip_range": {"start_ip": {"ip_string": "********"}, "end_ip": {"ip_string": "********00"}}}, "info": "range-1"}, {"ip_addr": {"ip_range": {"start_ip": {"ip_string": "********"}, "end_ip": {"ip_string": "*********"}}}, "info": "range-2"}, {"ip_addr": {"ip": {"ip_string": "**********"}}, "info": "server-6"}]}}]}]