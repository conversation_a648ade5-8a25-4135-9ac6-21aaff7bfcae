[{"tx_id": "flow-control-policy-mixed-types-setup-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 50001, "desc": "mixed_test_permit_policy", "group_name": "flow_control_mixed_group", "disable": false, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": true, "ip_rate": 1000, "tos": 0}, "in_ip": [{"ip": {"ipv4": 3232235776, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 50002, "desc": "mixed_test_channel_policy", "group_name": "flow_control_mixed_group", "disable": false, "previous": 50001, "action": "FLOW_CONTROL_ACTION_CHANNEL", "action_channel": {"next": true, "channel": "testchannel3", "pri": 5, "ip_rate": 3000}, "in_ip": [{"ip": {"ipv4": 3232236032, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 50003, "desc": "mixed_test_deny_policy", "group_name": "flow_control_mixed_group", "disable": false, "previous": 50002, "action": "FLOW_CONTROL_ACTION_DENY", "action_deny": {"next": false}, "in_ip": [{"ip": {"ipv4": 3232236288, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}]}]