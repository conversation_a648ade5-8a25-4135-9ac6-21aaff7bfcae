#!/bin/bash

# Traffic Channel模块综合测试脚本
# 测试所有traffic_channel模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="traffic_channel_test_results.log"
echo "Traffic Channel模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证流量通道配置（带重试机制）
verify_traffic_channel() {
    local channel_name=$1
    local expected_rate=$2
    local expected_quota=${3:-""}
    local max_retries=3
    local retry_delay=1

    echo "验证流量通道 $channel_name 配置..." >> $LOG_FILE

    for ((i=1; i<=max_retries; i++)); do
        local output=$(floweye policy getbwo name=$channel_name 2>/dev/null)

        if [ $? -ne 0 ]; then
            if [ $i -eq $max_retries ]; then
                print_error "无法获取流量通道 $channel_name 配置"
                return 1
            fi
            echo "重试 $i/$max_retries: 获取配置失败，等待 ${retry_delay}s..." >> $LOG_FILE
            sleep $retry_delay
            continue
        fi

        # 验证rate
        local rate_match=false
        if echo "$output" | grep -q "rate=$expected_rate"; then
            rate_match=true
        fi

        # 验证quota（如果指定）
        local quota_match=true
        if [ -n "$expected_quota" ]; then
            if ! echo "$output" | grep -q "quota=$expected_quota"; then
                quota_match=false
            fi
        fi

        # 如果所有验证都通过，返回成功
        if [ "$rate_match" = "true" ] && [ "$quota_match" = "true" ]; then
            print_success "流量通道 $channel_name rate=$expected_rate 验证通过"
            if [ -n "$expected_quota" ]; then
                print_success "流量通道 $channel_name quota=$expected_quota 验证通过"
            fi
            return 0
        fi

        # 如果是最后一次重试，输出错误
        if [ $i -eq $max_retries ]; then
            if [ "$rate_match" = "false" ]; then
                print_error "流量通道 $channel_name rate 验证失败，期望: $expected_rate"
            fi
            if [ "$quota_match" = "false" ]; then
                print_error "流量通道 $channel_name quota 验证失败，期望: $expected_quota"
            fi
            echo "$output" >> $LOG_FILE
            return 1
        fi

        echo "重试 $i/$max_retries: 配置不匹配，等待 ${retry_delay}s..." >> $LOG_FILE
        sleep $retry_delay
    done

    return 1
}

# 验证流量通道优先级配置
verify_traffic_channel_priority() {
    local channel_name=$1
    local expected_pri=$2
    local expected_maxrate=$3
    local expected_gbw=$4
    local expected_desc=${5:-""}
    
    echo "验证流量通道 $channel_name 优先级 $expected_pri 配置..." >> $LOG_FILE
    local output=$(floweye policy gethtb $channel_name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取流量通道 $channel_name 优先级配置"
        return 1
    fi
    
    # 解析优先级配置，查找指定优先级的行
    local priority_line=$(echo "$output" | awk -v pri="$expected_pri" '$1 == pri {print}')
    
    if [ -z "$priority_line" ]; then
        print_error "未找到优先级 $expected_pri 的配置"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证maxrate和gbw
    local actual_gbw=$(echo "$priority_line" | awk '{print $2}')
    local actual_maxrate=$(echo "$priority_line" | awk '{print $3}')
    
    if [ "$actual_gbw" = "$expected_gbw" ]; then
        print_success "流量通道 $channel_name 优先级 $expected_pri gbw=$expected_gbw 验证通过"
    else
        print_error "流量通道 $channel_name 优先级 $expected_pri gbw 验证失败，期望: $expected_gbw，实际: $actual_gbw"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    if [ "$actual_maxrate" = "$expected_maxrate" ]; then
        print_success "流量通道 $channel_name 优先级 $expected_pri maxrate=$expected_maxrate 验证通过"
    else
        print_error "流量通道 $channel_name 优先级 $expected_pri maxrate 验证失败，期望: $expected_maxrate，实际: $actual_maxrate"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 验证流量通道不存在
verify_traffic_channel_not_exists() {
    local channel_name=$1
    
    echo "验证流量通道 $channel_name 不存在..." >> $LOG_FILE
    local output=$(floweye policy getbwo name=$channel_name 2>&1)
    
    if echo "$output" | grep -q "NEXIST" || [ $? -ne 0 ]; then
        print_success "流量通道 $channel_name 确实不存在"
        return 0
    else
        print_error "流量通道 $channel_name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证流量通道可选字段默认值
verify_traffic_channel_optional_defaults() {
    local channel_name=$1
    
    echo "验证流量通道 $channel_name 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye policy getbwo name=$channel_name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取流量通道 $channel_name 配置"
        return 1
    fi
    
    # 验证quota默认值为0
    if echo "$output" | grep -q "quota=0"; then
        print_success "流量通道 $channel_name quota默认值验证通过 (0)"
    else
        print_error "流量通道 $channel_name quota默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 清理流量通道配置
cleanup_traffic_channel() {
    local channel_name=$1
    echo "清理流量通道 $channel_name 配置..." >> $LOG_FILE
    floweye policy rmvbwo name=$channel_name >> $LOG_FILE 2>&1 || true
}

# 清理所有测试流量通道
cleanup_all_test_channels() {
    echo "清理所有测试流量通道..." >> $LOG_FILE
    
    # 获取所有流量通道列表
    local channel_list=$(floweye policy listbwo 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$channel_list" ]; then
        # 查找以test开头的通道名称并删除
        echo "$channel_list" | grep -E "test.*channel" | while read line; do
            local channel_name=$(echo "$line" | awk '{print $2}')
            if [ -n "$channel_name" ]; then
                echo "删除测试流量通道: $channel_name" >> $LOG_FILE
                floweye policy rmvbwo name=$channel_name >> $LOG_FILE 2>&1 || true
            fi
        done
        print_success "清理了测试流量通道"
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "Traffic Channel模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 清理现有测试流量通道
    print_header "清理现有测试流量通道"
    cleanup_all_test_channels

    # 获取初始流量通道状态
    print_header "获取初始流量通道状态"
    echo "初始流量通道状态:" >> $LOG_FILE
    floweye policy listbwo >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础流量通道
    run_test "test_traffic_channel_basic_new.json" "创建基础流量通道"
    verify_traffic_channel "testchannel1" "10000" "0"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_traffic_channel_idempotent_new.json" "幂等性测试-重复创建"
    verify_traffic_channel "testchannel1" "10000" "0"

    # 1.3 字段修改测试
    run_test "test_traffic_channel_modify_rate.json" "修改rate字段"
    verify_traffic_channel "testchannel1" "20000" "0"

    run_test "test_traffic_channel_modify_quota.json" "修改quota字段"
    verify_traffic_channel "testchannel1" "20000" "5000"

    # 1.4 删除配置测试
    run_test "test_traffic_channel_delete.json" "删除配置测试"
    verify_traffic_channel_not_exists "testchannel1"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_traffic_channel_delete_idempotent.json" "删除配置幂等性测试"
    verify_traffic_channel_not_exists "testchannel1"

    # 阶段2: 优先级配置测试
    print_header "阶段2: 优先级配置测试"

    # 2.1 创建带优先级的流量通道
    run_test "test_traffic_channel_priority_new.json" "创建带优先级的流量通道"
    verify_traffic_channel "testchannel2" "50000" "0"
    verify_traffic_channel_priority "testchannel2" "1" "30000" "10000"
    verify_traffic_channel_priority "testchannel2" "2" "20000" "5000"

    # 2.2 修改优先级配置
    run_test "test_traffic_channel_priority_modify.json" "修改优先级配置"
    verify_traffic_channel "testchannel2" "60000" "0"
    verify_traffic_channel_priority "testchannel2" "1" "40000" "15000"
    verify_traffic_channel_priority "testchannel2" "3" "15000" "5000"

    # 2.3 删除带优先级的流量通道
    run_test "test_traffic_channel_priority_delete.json" "删除带优先级的流量通道"
    verify_traffic_channel_not_exists "testchannel2"

    # 阶段3: 完整配置测试
    print_header "阶段3: 完整配置测试"

    # 3.1 创建包含所有字段的完整配置
    run_test "test_traffic_channel_complete_config.json" "创建完整配置"
    verify_traffic_channel "testchannel3" "100000" "10000"
    verify_traffic_channel_priority "testchannel3" "1" "80000" "20000"
    verify_traffic_channel_priority "testchannel3" "2" "60000" "15000"
    verify_traffic_channel_priority "testchannel3" "3" "40000" "10000"

    # 3.2 修改完整配置
    run_test "test_traffic_channel_complete_modify.json" "修改完整配置"
    verify_traffic_channel "testchannel3" "150000" "15000"
    verify_traffic_channel_priority "testchannel3" "1" "120000" "40000"
    verify_traffic_channel_priority "testchannel3" "2" "100000" "30000"

    # 阶段4: 可选字段默认值恢复测试
    print_header "阶段4: 可选字段默认值恢复测试"

    # 4.1 修改配置，移除可选字段，验证默认值恢复
    run_test "test_traffic_channel_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_traffic_channel "testchannel3" "200000" "0"
    verify_traffic_channel_optional_defaults "testchannel3"

    # 清理测试通道
    cleanup_traffic_channel "testchannel3"

    # 阶段5: 全量同步测试
    print_header "阶段5: 全量同步测试"

    # 5.1 设置初始配置（增量模式）
    run_test "test_traffic_channel_full_sync_setup.json" "全量同步初始配置"
    verify_traffic_channel "testchannel4" "30000" "2000"
    verify_traffic_channel "testchannel5" "40000" "3000"

    # 5.2 启动全量同步
    start_full_sync || exit 1

    # 5.3 发送全量同步配置（只包含testchannel4）
    run_test "test_traffic_channel_full_sync_cleanup.json" "全量同步清理配置"
    verify_traffic_channel "testchannel4" "35000" "2500"

    # 5.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 5.5 验证清理结果：testchannel5应该被删除
    sleep 2  # 等待清理完成
    verify_traffic_channel "testchannel4" "35000" "2500"
    verify_traffic_channel_not_exists "testchannel5"

    # 清理剩余测试通道
    cleanup_traffic_channel "testchannel4"

    # 阶段6: 边界条件和错误处理测试
    print_header "阶段6: 边界条件和错误处理测试"

    # 6.1 无效参数测试（这些应该失败）
    run_test "test_traffic_channel_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_traffic_channel_error_no_rate.json" "缺少rate字段错误测试" "false"
    run_test "test_traffic_channel_error_invalid_rate.json" "无效rate值错误测试" "false"
    run_test "test_traffic_channel_error_invalid_priority.json" "无效优先级错误测试" "false"
    run_test "test_traffic_channel_error_gbw_exceeds_rate.json" "保证带宽超过通道带宽错误测试" "false"

    # 6.2 边界值测试
    run_test "test_traffic_channel_boundary_min_rate.json" "最小rate值边界测试"
    verify_traffic_channel "testchannelmin" "1" "0"
    cleanup_traffic_channel "testchannelmin"

    run_test "test_traffic_channel_boundary_max_rate.json" "最大rate值边界测试"
    verify_traffic_channel "testchannelmax" "16000000" "0"
    cleanup_traffic_channel "testchannelmax"

    # 最终清理
    print_header "清理测试环境"
    cleanup_all_test_channels

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Traffic Channel模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
