/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      route_policy_integration_test.go
 *
 * DESCRIPTION :    Integration tests for route policy module
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/16/2025  create
 ******************************************************************************/

package test

import (
	"agent/internal/client/task"
	"agent/internal/logger"
	pb "agent/internal/pb"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRoutePolicyIntegration tests the complete route policy workflow
func TestRoutePolicyIntegration(t *testing.T) {
	// Skip integration tests in CI environment
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	log := logger.NewLogger()
	processor := task.NewRoutePolicyProcessor(log)

	// Test data
	testCookie := uint32(99999)
	testDesc := "Integration Test Route Policy"

	// Create test route policy task
	routePolicyTask := &pb.RoutePolicyTask{
		Cookie:  testCookie,
		Desc:    testDesc,
		Disable: false,
		Schtime: 0,
		Action:  pb.RoutePolicyAction_ROUTE_ACTION_ROUTE,
		RouteConfig: &pb.RouteActionConfig{
			Proxy: "wan1",
			NextHop: &pb.IpAddress{
				Ip: "***********",
			},
		},
	}

	deviceTask := &pb.DeviceTask{
		TaskType:        pb.TaskType_TASK_ROUTE_POLICY,
		TaskAction:      pb.TaskAction_NEW_CONFIG,
		RoutePolicyTask: routePolicyTask,
	}

	ctx := context.Background()

	t.Run("Create Route Policy", func(t *testing.T) {
		result, err := processor.ProcessTask(ctx, deviceTask)
		
		// Note: This test may fail in environments without floweye
		// In such cases, we just log the error and continue
		if err != nil {
			t.Logf("Create route policy failed (expected in test environment): %v", err)
			t.Logf("Result: %s", result)
		} else {
			assert.Contains(t, result, "created")
			t.Logf("Create route policy succeeded: %s", result)
		}
	})

	t.Run("Update Route Policy", func(t *testing.T) {
		// Update the description
		routePolicyTask.Desc = testDesc + " Updated"
		deviceTask.TaskAction = pb.TaskAction_EDIT_CONFIG

		result, err := processor.ProcessTask(ctx, deviceTask)
		
		if err != nil {
			t.Logf("Update route policy failed (expected in test environment): %v", err)
			t.Logf("Result: %s", result)
		} else {
			assert.Contains(t, result, "updated")
			t.Logf("Update route policy succeeded: %s", result)
		}
	})

	t.Run("Delete Route Policy", func(t *testing.T) {
		deviceTask.TaskAction = pb.TaskAction_DELETE_CONFIG

		result, err := processor.ProcessTask(ctx, deviceTask)
		
		if err != nil {
			t.Logf("Delete route policy failed (expected in test environment): %v", err)
			t.Logf("Result: %s", result)
		} else {
			assert.Contains(t, result, "deleted")
			t.Logf("Delete route policy succeeded: %s", result)
		}
	})
}

// TestRoutePolicyFullSync tests the full synchronization workflow
func TestRoutePolicyFullSync(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	log := logger.NewLogger()
	processor := task.NewRoutePolicyProcessor(log)

	t.Run("Start Full Sync", func(t *testing.T) {
		err := processor.StartFullSync()
		
		// May fail in test environment without floweye
		if err != nil {
			t.Logf("Start full sync failed (expected in test environment): %v", err)
		} else {
			t.Log("Start full sync succeeded")
		}
	})

	t.Run("End Full Sync", func(t *testing.T) {
		processor.EndFullSync()
		t.Log("End full sync completed")
	})
}

// TestRoutePolicyVerification tests the verification functionality
func TestRoutePolicyVerification(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	log := logger.NewLogger()
	processor := task.NewRoutePolicyProcessor(log)

	testCookie := uint32(99999)

	t.Run("Verify Non-existent Policy", func(t *testing.T) {
		exists, err := processor.VerifyConfig(testCookie)
		
		// Should not error, but policy should not exist
		assert.NoError(t, err)
		assert.False(t, exists)
		t.Logf("Verification of non-existent policy: exists=%v", exists)
	})
}

// TestRoutePolicyErrorHandling tests error handling scenarios
func TestRoutePolicyErrorHandling(t *testing.T) {
	log := logger.NewLogger()
	processor := task.NewRoutePolicyProcessor(log)

	ctx := context.Background()

	t.Run("Nil Route Policy Task", func(t *testing.T) {
		deviceTask := &pb.DeviceTask{
			TaskType:   pb.TaskType_TASK_ROUTE_POLICY,
			TaskAction: pb.TaskAction_NEW_CONFIG,
			// RoutePolicyTask is nil
		}

		result, err := processor.ProcessTask(ctx, deviceTask)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "nil")
		t.Logf("Nil task error: %v, result: %s", err, result)
	})

	t.Run("Unknown Task Action", func(t *testing.T) {
		routePolicyTask := &pb.RoutePolicyTask{
			Cookie: 12345,
			Desc:   "Test Policy",
		}

		deviceTask := &pb.DeviceTask{
			TaskType:        pb.TaskType_TASK_ROUTE_POLICY,
			TaskAction:      pb.TaskAction_UNKNOWN_ACTION,
			RoutePolicyTask: routePolicyTask,
		}

		result, err := processor.ProcessTask(ctx, deviceTask)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unknown")
		t.Logf("Unknown action error: %v, result: %s", err, result)
	})
}

// TestRoutePolicyPerformance tests performance characteristics
func TestRoutePolicyPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	log := logger.NewLogger()
	processor := task.NewRoutePolicyProcessor(log)

	t.Run("Processor Creation Performance", func(t *testing.T) {
		start := time.Now()
		
		// Create multiple processors to test initialization performance
		for i := 0; i < 10; i++ {
			_ = task.NewRoutePolicyProcessor(log)
		}
		
		duration := time.Since(start)
		t.Logf("Created 10 processors in %v", duration)
		
		// Should complete within reasonable time
		assert.Less(t, duration, 5*time.Second)
	})

	t.Run("Task Processing Performance", func(t *testing.T) {
		routePolicyTask := &pb.RoutePolicyTask{
			Cookie:  12345,
			Desc:    "Performance Test Policy",
			Disable: false,
			Action:  pb.RoutePolicyAction_ROUTE_ACTION_ROUTE,
			RouteConfig: &pb.RouteActionConfig{
				Proxy: "wan1",
			},
		}

		deviceTask := &pb.DeviceTask{
			TaskType:        pb.TaskType_TASK_ROUTE_POLICY,
			TaskAction:      pb.TaskAction_NEW_CONFIG,
			RoutePolicyTask: routePolicyTask,
		}

		ctx := context.Background()
		start := time.Now()

		// Process multiple tasks to test performance
		for i := 0; i < 5; i++ {
			routePolicyTask.Cookie = uint32(12345 + i)
			_, err := processor.ProcessTask(ctx, deviceTask)
			// Errors are expected in test environment
			if err != nil {
				t.Logf("Task %d processing failed (expected): %v", i, err)
			}
		}

		duration := time.Since(start)
		t.Logf("Processed 5 tasks in %v", duration)
		
		// Should complete within reasonable time
		assert.Less(t, duration, 10*time.Second)
	})
}

// BenchmarkRoutePolicyProcessor benchmarks the processor performance
func BenchmarkRoutePolicyProcessor(b *testing.B) {
	log := logger.NewLogger()
	processor := task.NewRoutePolicyProcessor(log)

	routePolicyTask := &pb.RoutePolicyTask{
		Cookie:  12345,
		Desc:    "Benchmark Test Policy",
		Disable: false,
		Action:  pb.RoutePolicyAction_ROUTE_ACTION_ROUTE,
		RouteConfig: &pb.RouteActionConfig{
			Proxy: "wan1",
		},
	}

	deviceTask := &pb.DeviceTask{
		TaskType:        pb.TaskType_TASK_ROUTE_POLICY,
		TaskAction:      pb.TaskAction_NEW_CONFIG,
		RoutePolicyTask: routePolicyTask,
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		routePolicyTask.Cookie = uint32(12345 + i)
		_, _ = processor.ProcessTask(ctx, deviceTask)
	}
}
