#!/bin/bash

# iWAN Mapping模块综合测试脚本
# 测试所有iWAN Mapping模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="iwan_mapping_test_results.log"
echo "iWAN Mapping模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证iWAN Mapping配置
verify_iwan_mapping() {
    local proxy=$1
    local port=$2
    local expected_server=$3

    echo "验证iWAN Mapping配置 proxy=$proxy port=$port..." >> $LOG_FILE
    local output=$(floweye iwanmap list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取iWAN Mapping配置列表"
        return 1
    fi

    # 查找指定的映射
    local found=false
    while IFS= read -r line; do
        if [ -z "$line" ]; then
            continue
        fi

        # 解析行：<proxy_name> <proxy_id> <port> <server_name> <server_id> <unknown>
        local fields=($line)
        if [ ${#fields[@]} -ge 4 ]; then
            local line_proxy=${fields[0]}
            local line_port=${fields[2]}
            local line_server=${fields[3]}

            if [ "$line_proxy" = "$proxy" ] && [ "$line_port" = "$port" ]; then
                found=true
                if [ "$line_server" = "$expected_server" ]; then
                    print_success "iWAN Mapping验证通过: $proxy:$port -> $expected_server"
                    return 0
                else
                    print_error "iWAN Mapping服务器不匹配，期望: $expected_server，实际: $line_server"
                    echo "$output" >> $LOG_FILE
                    return 1
                fi
            fi
        fi
    done <<< "$output"

    if [ "$found" = "false" ]; then
        if [ "$expected_server" = "NULL" ] || [ "$expected_server" = "" ]; then
            print_success "iWAN Mapping已删除验证通过: $proxy:$port"
            return 0
        else
            print_error "未找到iWAN Mapping: $proxy:$port"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
}

# 验证iWAN Mapping不存在
verify_iwan_mapping_not_exists() {
    local proxy=$1
    local port=$2

    echo "验证iWAN Mapping不存在 proxy=$proxy port=$port..." >> $LOG_FILE
    local output=$(floweye iwanmap list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取iWAN Mapping配置列表"
        return 1
    fi

    # 查找指定的映射
    while IFS= read -r line; do
        if [ -z "$line" ]; then
            continue
        fi

        # 解析行：<proxy_name> <proxy_id> <port> <server_name> <server_id> <unknown>
        local fields=($line)
        if [ ${#fields[@]} -ge 3 ]; then
            local line_proxy=${fields[0]}
            local line_port=${fields[2]}

            if [ "$line_proxy" = "$proxy" ] && [ "$line_port" = "$port" ]; then
                print_error "iWAN Mapping仍然存在: $proxy:$port"
                echo "$output" >> $LOG_FILE
                return 1
            fi
        fi
    done <<< "$output"

    print_success "iWAN Mapping不存在验证通过: $proxy:$port"
    return 0
}

# 清理所有测试相关的iWAN Mapping配置
cleanup_test_iwan_mappings() {
    echo "清理测试相关的iWAN Mapping配置..." >> $LOG_FILE
    
    # 获取所有iWAN Mapping配置
    local output=$(floweye iwanmap list 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$output" ]; then
        while IFS= read -r line; do
            if [ -z "$line" ]; then
                continue
            fi

            # 解析行：<proxy_name> <proxy_id> <port> <server_name> <server_id> <unknown>
            local fields=($line)
            if [ ${#fields[@]} -ge 4 ]; then
                local proxy=${fields[0]}
                local port=${fields[2]}
                local server=${fields[3]}

                # 清理测试相关的映射（包含test关键字的）
                if echo "$proxy" | grep -q "test" || echo "$server" | grep -q "test"; then
                    echo "清理测试映射: $proxy:$port -> $server" >> $LOG_FILE
                    floweye iwanmap set proxy=$proxy port=$port server=NULL > /dev/null 2>&1
                fi
            fi
        done <<< "$output"
    fi
}

# 设置测试依赖（通过agent debug工具）
setup_test_dependencies() {
    print_info "设置测试依赖..."

    # 通过agent debug工具设置完整的依赖链
    echo "通过agent debug工具设置依赖..." >> $LOG_FILE
    local output=$(../agent-debug-client --config=test_iwan_mapping_dependencies_setup.json 2>&1)
    local exit_code=$?
    echo "依赖设置命令输出: $output" >> $LOG_FILE
    echo "依赖设置退出码: $exit_code" >> $LOG_FILE

    # 检查是否有任务失败
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    if [ "$has_task_failed" = "false" ]; then
        # 验证依赖创建成功
        echo "验证测试依赖..." >> $LOG_FILE
        sleep 2  # 等待配置生效

        local wan_proxy_count=$(floweye nat listproxy type=wan | wc -l)
        local iwan_service_count=$(floweye nat listproxy type=iwansvc | wc -l)
        echo "WAN proxy数量: $wan_proxy_count, iWAN service数量: $iwan_service_count" >> $LOG_FILE

        if [ "$wan_proxy_count" -gt 0 ] && [ "$iwan_service_count" -gt 0 ]; then
            print_success "测试依赖设置完成"
        else
            print_warning "依赖数量验证失败，但继续测试"
            echo "WAN proxy列表:" >> $LOG_FILE
            floweye nat listproxy type=wan >> $LOG_FILE 2>&1
            echo "iWAN service列表:" >> $LOG_FILE
            floweye nat listproxy type=iwansvc >> $LOG_FILE 2>&1
        fi
    else
        print_error "测试依赖设置失败"
        echo "错误输出: $output" >> $LOG_FILE
        exit 1
    fi
}

# 清理测试依赖
cleanup_test_dependencies() {
    print_info "清理测试依赖..."

    # 通过agent debug工具清理依赖
    echo "通过agent debug工具清理依赖..." >> $LOG_FILE
    ../agent-debug-client --config=test_iwan_mapping_dependencies_cleanup.json > /dev/null 2>&1

    # 手动清理（备用方法）
    echo "手动清理测试依赖..." >> $LOG_FILE
    floweye nat rmvproxy iwan-svc-test1 > /dev/null 2>&1
    floweye nat rmvproxy iwan-svc-test2 > /dev/null 2>&1
    floweye nat rmvproxy wan-test-mapping > /dev/null 2>&1
    floweye if set name=eth2 zone=inside > /dev/null 2>&1

    print_success "测试依赖清理完成"
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "iWAN Mapping模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 设置测试依赖
    setup_test_dependencies

    # 清理现有测试配置
    cleanup_test_iwan_mappings

    # 获取初始状态
    print_header "获取初始iWAN Mapping状态"
    echo "初始iWAN Mapping状态:" >> $LOG_FILE
    floweye iwanmap list >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建iWAN Mapping
    run_test "test_iwan_mapping_basic_new.json" "创建基础iWAN Mapping"
    verify_iwan_mapping "wantestmapping" "8080" "iwan-svc-test1"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_iwan_mapping_idempotent.json" "幂等性测试-重复创建"
    verify_iwan_mapping "wantestmapping" "8080" "iwan-svc-test1"

    # 1.3 修改iWAN Mapping服务器
    run_test "test_iwan_mapping_modify_server.json" "修改iWAN服务器"
    verify_iwan_mapping "wantestmapping" "8080" "iwan-svc-test2"

    # 1.4 删除iWAN Mapping
    run_test "test_iwan_mapping_delete.json" "删除iWAN Mapping"
    verify_iwan_mapping_not_exists "wantestmapping" "8080"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_iwan_mapping_delete_idempotent.json" "删除配置幂等性测试"
    verify_iwan_mapping_not_exists "wantestmapping" "8080"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建完整配置
    run_test "test_iwan_mapping_complete_config.json" "创建完整iWAN Mapping配置"
    verify_iwan_mapping "wantestmapping" "8081" "iwan-svc-test1"
    verify_iwan_mapping "wantestmapping" "8082" "iwan-svc-test2"

    # 2.2 修改完整配置
    run_test "test_iwan_mapping_complete_modify.json" "修改完整iWAN Mapping配置"
    verify_iwan_mapping "wantestmapping" "8081" "iwan-svc-test2"
    verify_iwan_mapping "wantestmapping" "8082" "iwan-svc-test1"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 设置初始配置（增量模式）
    run_test "test_iwan_mapping_full_sync_setup.json" "全量同步初始配置"
    verify_iwan_mapping "wantestmapping" "8083" "iwan-svc-test1"
    verify_iwan_mapping "wantestmapping" "8084" "iwan-svc-test2"
    verify_iwan_mapping "wantestmapping" "8085" "iwan-svc-test1"

    # 3.2 启动全量同步
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只保留部分配置）
    run_test "test_iwan_mapping_full_sync_cleanup.json" "全量同步清理配置"
    verify_iwan_mapping "wantestmapping" "8083" "iwan-svc-test2"

    # 3.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 3.5 验证清理结果：未在全量同步中的配置应被删除
    sleep 2  # 等待清理完成
    verify_iwan_mapping "wantestmapping" "8083" "iwan-svc-test2"
    verify_iwan_mapping_not_exists "wantestmapping" "8084"
    verify_iwan_mapping_not_exists "wantestmapping" "8085"

    # 阶段4: 边界条件和错误处理测试
    print_header "阶段4: 边界条件和错误处理测试"

    # 4.1 无效参数测试（这些应该失败）
    run_test "test_iwan_mapping_error_no_proxy.json" "缺少proxy字段错误测试" "false"
    run_test "test_iwan_mapping_error_no_port.json" "缺少port字段错误测试" "false"
    run_test "test_iwan_mapping_error_invalid_port.json" "无效port字段错误测试" "false"
    run_test "test_iwan_mapping_error_nonexistent_proxy.json" "不存在proxy错误测试" "false"
    run_test "test_iwan_mapping_error_nonexistent_server.json" "不存在server错误测试" "false"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_test_iwan_mappings
    cleanup_test_dependencies

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "iWAN Mapping模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
