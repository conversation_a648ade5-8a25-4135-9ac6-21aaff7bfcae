#!/bin/bash

# iWAN Proxy模块综合测试脚本
# 测试所有iWAN Proxy模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="iwan_proxy_test_results.log"
echo "iWAN Proxy模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证iWAN Proxy配置
verify_iwan_proxy() {
    local name=$1
    local expected_ifname=$2
    local expected_mtu=$3
    local expected_svraddr=$4
    local expected_svrport=$5
    local expected_username=$6
    
    echo "验证iWAN Proxy $name 配置..." >> $LOG_FILE
    local output=$(floweye nat getproxy $name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取iWAN Proxy $name 配置"
        return 1
    fi
    
    # 验证type为iwan
    if echo "$output" | grep -q "type=iwan"; then
        print_success "iWAN Proxy $name type=iwan 验证通过"
    else
        print_error "iWAN Proxy $name type 验证失败，期望: iwan"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证ifname
    if echo "$output" | grep -q "ifname=$expected_ifname"; then
        print_success "iWAN Proxy $name ifname=$expected_ifname 验证通过"
    else
        print_error "iWAN Proxy $name ifname 验证失败，期望: $expected_ifname"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证MTU (对于iWAN proxy，应该检查cfgmtu字段)
    if echo "$output" | grep -q "cfgmtu=$expected_mtu"; then
        print_success "iWAN Proxy $name cfgmtu=$expected_mtu 验证通过"
    else
        print_error "iWAN Proxy $name cfgmtu 验证失败，期望: $expected_mtu"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证服务器地址
    if echo "$output" | grep -q "svraddr=$expected_svraddr"; then
        print_success "iWAN Proxy $name svraddr=$expected_svraddr 验证通过"
    else
        print_error "iWAN Proxy $name svraddr 验证失败，期望: $expected_svraddr"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证服务器端口
    if echo "$output" | grep -q "svrport=$expected_svrport"; then
        print_success "iWAN Proxy $name svrport=$expected_svrport 验证通过"
    else
        print_error "iWAN Proxy $name svrport 验证失败，期望: $expected_svrport"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证用户名
    if echo "$output" | grep -q "username=$expected_username"; then
        print_success "iWAN Proxy $name username=$expected_username 验证通过"
    else
        print_error "iWAN Proxy $name username 验证失败，期望: $expected_username"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 验证iWAN Proxy不存在
verify_iwan_proxy_not_exists() {
    local name=$1
    
    echo "验证iWAN Proxy $name 不存在..." >> $LOG_FILE
    local output=$(floweye nat getproxy $name 2>&1)
    
    if echo "$output" | grep -q "NEXIST"; then
        print_success "iWAN Proxy $name 不存在验证通过"
        return 0
    else
        print_error "iWAN Proxy $name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证iWAN Proxy可选字段默认值
verify_iwan_proxy_optional_defaults() {
    local name=$1
    
    echo "验证iWAN Proxy $name 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye nat getproxy $name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取iWAN Proxy $name 配置"
        return 1
    fi
    
    # 验证encrypt默认值为0
    if echo "$output" | grep -q "cfgencrypt=0"; then
        print_success "iWAN Proxy $name encrypt默认值验证通过 (0)"
    else
        print_error "iWAN Proxy $name encrypt默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证link默认值为0
    if echo "$output" | grep -q "link=0" || ! echo "$output" | grep -q "link="; then
        print_success "iWAN Proxy $name link默认值验证通过 (0)"
    else
        print_error "iWAN Proxy $name link默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证pingip默认值为0.0.0.0
    if echo "$output" | grep -q "pingip=0.0.0.0"; then
        print_success "iWAN Proxy $name pingip默认值验证通过 (0.0.0.0)"
    else
        print_error "iWAN Proxy $name pingip默认值验证失败，期望: 0.0.0.0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证pingip2默认值为0.0.0.0
    if echo "$output" | grep -q "pingip2=0.0.0.0"; then
        print_success "iWAN Proxy $name pingip2默认值验证通过 (0.0.0.0)"
    else
        print_error "iWAN Proxy $name pingip2默认值验证失败，期望: 0.0.0.0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证maxdelay默认值为0
    if echo "$output" | grep -q "maxdelay=0"; then
        print_success "iWAN Proxy $name maxdelay默认值验证通过 (0)"
    else
        print_error "iWAN Proxy $name maxdelay默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证dnspxy默认值为0
    if echo "$output" | grep -q "dnspxy=0"; then
        print_success "iWAN Proxy $name dnspxy默认值验证通过 (0)"
    else
        print_error "iWAN Proxy $name dnspxy默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 设置WAN接口为外部模式（iWAN Proxy的前置条件）
setup_wan_interface() {
    local interface=$1
    
    print_info "设置接口 $interface 为外部模式..."
    echo "设置接口 $interface 为外部模式..." >> $LOG_FILE
    
    local output=$(floweye if set name=$interface mode=0 zone=outside mixmode=0 2>&1)
    if [ $? -eq 0 ]; then
        print_success "接口 $interface 设置为外部模式成功"
        return 0
    else
        print_error "接口 $interface 设置为外部模式失败: $output"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 创建测试用的WAN配置
setup_test_wan() {
    local wan_name=$1
    local interface=$2

    print_info "创建测试WAN配置 $wan_name..."
    echo "创建测试WAN配置 $wan_name..." >> $LOG_FILE

    # 先设置接口为外部模式
    setup_wan_interface $interface

    # 创建静态WAN配置
    local output=$(floweye nat addproxy name=$wan_name ifname=$interface mtu=1500 ping_disable=0 pingip=0.0.0.0 pingip2=0.0.0.0 maxdelay=0 addr=************** gateway=************* dns=******* vlan=0 vlan1=0 clonemac=00-00-00-00-00-00 natip=0.0.0.0 gwpxy=0 dnspxy=0 2>&1)
    if [ $? -eq 0 ]; then
        print_success "测试WAN配置 $wan_name 创建成功"
        return 0
    else
        print_error "测试WAN配置 $wan_name 创建失败: $output"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 清理测试用的WAN配置
cleanup_test_wan() {
    local wan_name=$1

    echo "清理测试WAN配置 $wan_name..." >> $LOG_FILE
    floweye nat rmvproxy $wan_name >> $LOG_FILE 2>&1
}

# 清理所有现有的iWAN Proxy配置
cleanup_all_iwan_proxy_configs() {
    echo "清理所有现有iWAN Proxy配置..." >> $LOG_FILE

    # 获取所有iWAN Proxy配置列表
    local proxy_list=$(floweye nat listproxy json=1 type=wan 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$proxy_list" ] && [ "$proxy_list" != "null" ]; then
        # 解析JSON并提取iWAN Proxy名称（type=iwan）
        local iwan_names=$(echo "$proxy_list" | grep -o '"name":"[^"]*"[^}]*"type":"iwan"' | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

        if [ -n "$iwan_names" ]; then
            echo "发现现有iWAN Proxy配置: $iwan_names" >> $LOG_FILE
            for iwan_name in $iwan_names; do
                echo "删除iWAN Proxy配置: $iwan_name" >> $LOG_FILE
                floweye nat rmvproxy "$iwan_name" > /dev/null 2>&1
            done
            print_success "清理了现有iWAN Proxy配置"
        fi
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "iWAN Proxy模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    else
        print_success "floweye命令可用"
    fi

    # 清理现有配置
    print_header "清理现有配置"
    cleanup_all_iwan_proxy_configs

    # 设置测试环境
    print_header "设置测试环境"
    setup_test_wan "wan1" "eth2" || exit 1
    setup_test_wan "wan2" "eth1" || exit 1

    # 获取初始状态
    print_header "获取初始iWAN Proxy状态"
    echo "初始iWAN Proxy状态:" >> $LOG_FILE
    floweye nat listproxy type=wan >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础iWAN Proxy配置
    run_test "test_iwan_proxy_basic_new.json" "创建基础iWAN Proxy配置"
    verify_iwan_proxy "iwan1" "wan1" "1420" "192.168.1.100" "8000" "testuser"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_iwan_proxy_idempotent.json" "幂等性测试-重复创建"
    verify_iwan_proxy "iwan1" "wan1" "1420" "192.168.1.100" "8000" "testuser"

    # 1.3 字段修改测试
    run_test "test_iwan_proxy_modify_mtu.json" "修改MTU字段"
    verify_iwan_proxy "iwan1" "wan1" "1300" "192.168.1.100" "8000" "testuser"

    run_test "test_iwan_proxy_modify_server.json" "修改服务器地址"
    verify_iwan_proxy "iwan1" "wan1" "1300" "192.168.1.200" "8080" "testuser"

    run_test "test_iwan_proxy_modify_credentials.json" "修改用户凭据"
    verify_iwan_proxy "iwan1" "wan1" "1300" "192.168.1.200" "8080" "newuser"

    # 1.4 删除配置测试
    run_test "test_iwan_proxy_delete.json" "删除iWAN Proxy配置"
    verify_iwan_proxy_not_exists "iwan1"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_iwan_proxy_delete_idempotent.json" "删除配置幂等性测试"
    verify_iwan_proxy_not_exists "iwan1"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建包含所有字段的完整配置
    run_test "test_iwan_proxy_complete_config.json" "创建完整配置"
    verify_iwan_proxy "iwan2" "wan1" "1420" "server.example.com" "8000" "admin"

    # 2.2 修改完整配置
    run_test "test_iwan_proxy_complete_modify.json" "修改完整配置"
    verify_iwan_proxy "iwan2" "wan1" "1300" "server2.example.com" "8080" "admin2"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 设置初始配置（增量模式）
    run_test "test_iwan_proxy_full_sync_setup.json" "全量同步初始配置"
    verify_iwan_proxy "iwanS1" "wan1" "1420" "192.168.1.1" "8000" "user1"
    verify_iwan_proxy "iwanS2" "wan1" "1420" "192.168.1.2" "8000" "user2"
    verify_iwan_proxy "iwanS3" "wan1" "1420" "192.168.1.3" "8000" "user3"

    # 3.2 启动全量同步
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只保留iwanS2）
    run_test "test_iwan_proxy_full_sync_cleanup.json" "全量同步清理配置"
    verify_iwan_proxy "iwanS2" "wan1" "1500" "192.168.2.2" "9000" "newuser2"

    # 3.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 3.5 验证清理结果：iwanS1和iwanS3应该被删除
    sleep 2  # 等待清理完成
    verify_iwan_proxy_not_exists "iwanS1"
    verify_iwan_proxy_not_exists "iwanS3"
    verify_iwan_proxy "iwanS2" "wan1" "1500" "192.168.2.2" "9000" "newuser2"

    # 阶段4: 可选字段默认值恢复测试
    print_header "阶段4: 可选字段默认值恢复测试"

    # 重新创建WAN配置（因为全量同步可能清理了它们）
    setup_test_wan "wan1" "eth2" || exit 1
    setup_test_wan "wan2" "eth1" || exit 1

    # 4.1 创建包含所有可选字段的完整配置
    run_test "test_iwan_proxy_optional_fields_complete.json" "创建包含所有可选字段的完整配置"
    verify_iwan_proxy "iwan3" "wan1" "1420" "192.168.1.100" "8000" "testuser"

    # 4.2 修改配置，移除所有可选字段，验证默认值恢复
    run_test "test_iwan_proxy_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_iwan_proxy "iwan3" "wan1" "1420" "192.168.1.100" "8000" "testuser"
    verify_iwan_proxy_optional_defaults "iwan3"

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 确保WAN配置存在
    setup_test_wan "wan1" "eth2" || exit 1
    setup_test_wan "wan2" "eth1" || exit 1

    # 5.1 无效参数测试（这些应该失败）
    run_test "test_iwan_proxy_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_iwan_proxy_error_no_ifname.json" "缺少ifname字段错误测试" "false"
    run_test "test_iwan_proxy_error_invalid_mtu.json" "无效MTU错误测试" "false"
    run_test "test_iwan_proxy_error_invalid_port.json" "无效端口错误测试" "false"
    run_test "test_iwan_proxy_error_nonexistent_wan.json" "不存在WAN错误测试" "false"

    # 5.2 依赖关系测试
    run_test "test_iwan_proxy_dependency_test.json" "WAN依赖关系测试"
    verify_iwan_proxy "iwan4" "wan1" "1420" "192.168.1.100" "8000" "testuser"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_iwan_proxy_configs
    cleanup_test_wan "wan1"
    cleanup_test_wan "wan2"

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "iWAN Proxy模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
