[{"tx_id": "flow-control-policy-five-types-setup-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60001, "desc": "permit_policy_complete_config", "group_name": "flow_control_movement_group", "disable": false, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": true, "ip_rate": 1000, "tos": 8}, "in_ip": [{"ip": {"ipv4": 3232235776, "prefix_length": 24}}, {"ip_range": {"start_ip": {"ipv4": 167772161}, "end_ip": {"ipv4": 167772260}}}], "out_ip": [{"ip": {"ipv4": 3232235520, "prefix_length": 24}}], "in_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}]}, "out_port": {"ports": [{"start": 80, "end": 80}]}, "app": {"app_name": "any"}, "interface": {"bridge": "any", "dir": "FLOW_DIRECTION_BOTH", "ifname": "any", "in_if": "any", "vlan": {"start": 0, "end": 0}}}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60002, "desc": "permit_policy_default_config", "group_name": "flow_control_movement_group", "disable": false, "previous": 60001, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": false, "ip_rate": 0}, "in_ip": [{"ip": {"ipv4": 3232236032, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60003, "desc": "channel_policy_complete_config", "group_name": "flow_control_movement_group", "disable": false, "previous": 60002, "action": "FLOW_CONTROL_ACTION_CHANNEL", "action_channel": {"next": true, "channel": "testchannel1", "pri": 5, "ip_rate": 3000, "so_id": "test_stat_complete"}, "in_ip": [{"ip": {"ipv4": 3232236288, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 3232235264, "prefix_length": 24}}], "in_port": {"ports": [{"start": 8080, "end": 8090}]}, "app": {"app_name": "any"}}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60004, "desc": "channel_policy_default_config", "group_name": "flow_control_movement_group", "disable": false, "previous": 60003, "action": "FLOW_CONTROL_ACTION_CHANNEL", "action_channel": {"next": false, "channel": "testchannel2"}, "in_ip": [{"ip": {"ipv4": 3232236544, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60005, "desc": "deny_policy_config", "group_name": "flow_control_movement_group", "disable": false, "previous": 60004, "action": "FLOW_CONTROL_ACTION_DENY", "action_deny": {"next": false}, "in_ip": [{"ip": {"ipv4": 3232236800, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}], "in_port": {"ports": [{"start": 22, "end": 22}]}, "app": {"app_name": "any"}}}}]}]