[{"tx_id": "dhcp-modify-lease-001", "device_tasks": [{"task_type": "TASK_DHCP", "task_action": "EDIT_CONFIG", "dhcp_task": {"name": "testdhcplan", "dhcp_enable": true, "dhcp_pool": {"start_ip": {"ip_string": "**************"}, "end_ip": {"ip_string": "***************"}}, "lease_ttl": 3600, "dns0": {"ip_string": "*******"}, "dns1": {"ip_string": "*******"}, "dhcp_gateway": {"ip_string": "*************"}, "dhcp_mask": {"ip_string": "*************"}, "dhcp_domain": "test.local"}}]}]