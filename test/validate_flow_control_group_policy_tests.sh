#!/bin/bash

# Flow Control Group Policy 测试用例验证脚本
# 验证所有测试文件的语法和格式正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Flow Control Group Policy 测试用例验证 ===${NC}"

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 验证函数
check_file() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}✗ $description - 文件不存在: $file${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
    
    echo -e "${GREEN}✓ $description - 文件存在${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    return 0
}

check_json_syntax() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if ! python3 -m json.tool "$file" > /dev/null 2>&1; then
        echo -e "${RED}✗ $description - JSON格式错误: $file${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
    
    echo -e "${GREEN}✓ $description - JSON格式正确${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    return 0
}

check_bash_syntax() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if ! bash -n "$file" 2>/dev/null; then
        echo -e "${RED}✗ $description - Bash语法错误: $file${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
    
    echo -e "${GREEN}✓ $description - Bash语法正确${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    return 0
}

# 1. 验证主测试脚本
echo -e "\n${BLUE}1. 验证主测试脚本${NC}"
check_file "flow_control_group_policy_comprehensive_test.sh" "主测试脚本存在性"
check_bash_syntax "flow_control_group_policy_comprehensive_test.sh" "主测试脚本语法"

# 2. 验证基础 CRUD 操作测试用例
echo -e "\n${BLUE}2. 验证基础 CRUD 操作测试用例${NC}"
check_file "test_flow_control_group_policy_basic_new.json" "基础新增测试用例"
check_json_syntax "test_flow_control_group_policy_basic_new.json" "基础新增测试用例JSON格式"

check_file "test_flow_control_group_policy_idempotent.json" "幂等性测试用例"
check_json_syntax "test_flow_control_group_policy_idempotent.json" "幂等性测试用例JSON格式"

check_file "test_flow_control_group_policy_modify.json" "修改测试用例"
check_json_syntax "test_flow_control_group_policy_modify.json" "修改测试用例JSON格式"

check_file "test_flow_control_group_policy_enable.json" "启用测试用例"
check_json_syntax "test_flow_control_group_policy_enable.json" "启用测试用例JSON格式"

check_file "test_flow_control_group_policy_delete.json" "删除测试用例"
check_json_syntax "test_flow_control_group_policy_delete.json" "删除测试用例JSON格式"

check_file "test_flow_control_group_policy_delete_idempotent.json" "删除幂等性测试用例"
check_json_syntax "test_flow_control_group_policy_delete_idempotent.json" "删除幂等性测试用例JSON格式"

# 3. 验证排序功能测试用例
echo -e "\n${BLUE}3. 验证排序功能测试用例${NC}"
check_file "test_flow_control_group_policy_ordering_setup.json" "排序设置测试用例"
check_json_syntax "test_flow_control_group_policy_ordering_setup.json" "排序设置测试用例JSON格式"

check_file "test_flow_control_group_policy_move_to_first.json" "移动到首位测试用例"
check_json_syntax "test_flow_control_group_policy_move_to_first.json" "移动到首位测试用例JSON格式"

check_file "test_flow_control_group_policy_move_to_position.json" "移动到指定位置测试用例"
check_json_syntax "test_flow_control_group_policy_move_to_position.json" "移动到指定位置测试用例JSON格式"

check_file "test_flow_control_group_policy_move_to_last.json" "移动到末尾测试用例"
check_json_syntax "test_flow_control_group_policy_move_to_last.json" "移动到末尾测试用例JSON格式"

# 4. 验证全量同步测试用例
echo -e "\n${BLUE}4. 验证全量同步测试用例${NC}"
check_file "test_flow_control_group_policy_full_sync_setup.json" "全量同步设置测试用例"
check_json_syntax "test_flow_control_group_policy_full_sync_setup.json" "全量同步设置测试用例JSON格式"

check_file "test_flow_control_group_policy_full_sync_config.json" "全量同步配置测试用例"
check_json_syntax "test_flow_control_group_policy_full_sync_config.json" "全量同步配置测试用例JSON格式"

# 5. 验证边界条件和错误处理测试用例
echo -e "\n${BLUE}5. 验证边界条件和错误处理测试用例${NC}"
check_file "test_flow_control_group_policy_error_no_name.json" "缺少名称错误测试用例"
check_json_syntax "test_flow_control_group_policy_error_no_name.json" "缺少名称错误测试用例JSON格式"

check_file "test_flow_control_group_policy_error_invalid_time.json" "无效时间错误测试用例"
check_json_syntax "test_flow_control_group_policy_error_invalid_time.json" "无效时间错误测试用例JSON格式"

# 6. 验证可选字段默认值测试用例
echo -e "\n${BLUE}6. 验证可选字段默认值测试用例${NC}"
check_file "test_flow_control_group_policy_complete_config.json" "完整配置测试用例"
check_json_syntax "test_flow_control_group_policy_complete_config.json" "完整配置测试用例JSON格式"

check_file "test_flow_control_group_policy_default_values.json" "默认值验证测试用例"
check_json_syntax "test_flow_control_group_policy_default_values.json" "默认值验证测试用例JSON格式"

# 7. 验证 Makefile 集成
echo -e "\n${BLUE}7. 验证 Makefile 集成${NC}"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "test-flow-control-group-policy" ../Makefile; then
    echo -e "${GREEN}✓ Makefile 集成 - 测试目标已添加${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}✗ Makefile 集成 - 测试目标未找到${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

# 验证结果统计
echo -e "\n${BLUE}=== 验证结果统计 ===${NC}"
echo "总验证项: $TOTAL_CHECKS"
echo -e "${GREEN}通过验证: $PASSED_CHECKS${NC}"
echo -e "${RED}失败验证: $FAILED_CHECKS${NC}"
echo "成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有验证通过！Flow Control Group Policy 测试用例已准备就绪。${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 有 $FAILED_CHECKS 个验证失败，请检查相关文件。${NC}"
    exit 1
fi
