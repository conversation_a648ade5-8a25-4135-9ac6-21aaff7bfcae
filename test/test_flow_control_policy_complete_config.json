[{"tx_id": "flow-control-policy-complete-config-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 30001, "desc": "complete_test_policy_with_all_fields", "group_name": "flow_control_complete_group", "disable": false, "previous": 0, "action": "FLOW_CONTROL_ACTION_CHANNEL", "action_channel": {"next": true, "channel": "testchannel3", "pri": 5, "ip_rate": 5000, "so_id": "test_stat_complete"}, "in_ip": [{"ip": {"ipv4": 3232261120, "prefix_length": 24}}, {"ip_range": {"start_ip": {"ipv4": 167772161}, "end_ip": {"ipv4": 167772260}}}, {"ip_group_name": "test_ip_group"}, {"user_group_id": 100}, {"user_name": "test_user"}], "in_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}, {"start": 8000, "end": 8999}]}, "out_ip": [{"ip": {"ipv4": 134744072, "prefix_length": 32}}, {"ip_range": {"start_ip": {"ipv4": 1920103937}, "end_ip": {"ipv4": 1920104036}}}], "out_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}]}, "app": {"app_name": "httpgroup", "app_protocol": "tcp"}, "interface": {"bridge": "any", "dir": "FLOW_DIRECTION_BOTH", "ifname": "eth0", "inif": "eth0", "vlan": {"start": 0, "end": 0}}}}}]}]