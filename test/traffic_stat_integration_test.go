/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_stat_integration_test.go
 *
 * DESCRIPTION :    Integration tests for traffic statistics module
 *
 * AUTHOR :         wei
 *
 * HISTORY :        01/15/2025  create
 ******************************************************************************/

package test

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TrafficStatTestConfig represents a traffic statistics test configuration
type TrafficStatTestConfig struct {
	TxID        string        `json:"tx_id"`
	FullSync    bool          `json:"full_sync,omitempty"`
	DeviceTasks []DeviceTask  `json:"device_tasks"`
}

// DeviceTask represents a device task in the test configuration
type DeviceTask struct {
	TaskType         string            `json:"task_type"`
	TaskAction       string            `json:"task_action"`
	TrafficStatTask  *TrafficStatTask  `json:"traffic_stat_task,omitempty"`
}

// TrafficStatTask represents a traffic statistics task
type TrafficStatTask struct {
	Name    string `json:"name"`
	TrackIP bool   `json:"trackip"`
}

// TrafficStatInfo represents traffic statistics information from floweye
type TrafficStatInfo struct {
	ID      int    `json:"id"`
	Name    string `json:"name"`
	TrackIP int    `json:"trackip"`
	UpBps   int64  `json:"upbps"`
	DnBps   int64  `json:"dnbps"`
	UpBytes int64  `json:"upbytes"`
	DnBytes int64  `json:"dnbytes"`
}

// executeAgentDebugClient executes the agent-debug-client with the given config
func executeAgentDebugClient(t *testing.T, config TrafficStatTestConfig) (string, error) {
	// Create temporary config file
	configData, err := json.MarshalIndent([]TrafficStatTestConfig{config}, "", "  ")
	require.NoError(t, err)

	tmpFile, err := os.CreateTemp("", "traffic_stat_test_*.json")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())

	_, err = tmpFile.Write(configData)
	require.NoError(t, err)
	tmpFile.Close()

	// Execute agent-debug-client
	cmd := exec.Command("../agent-debug-client", "--config="+tmpFile.Name())
	output, err := cmd.CombinedOutput()
	return string(output), err
}

// getTrafficStatList gets the list of traffic statistics from floweye
func getTrafficStatList(t *testing.T) ([]TrafficStatInfo, error) {
	cmd := exec.Command("floweye", "ntmso", "list")
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" {
		return []TrafficStatInfo{}, nil
	}

	// Parse JSON objects separated by commas
	var stats []TrafficStatInfo
	jsonObjects := strings.Split(outputStr, "},{")
	
	for i, jsonStr := range jsonObjects {
		// Fix JSON format for split objects
		if i > 0 && !strings.HasPrefix(jsonStr, "{") {
			jsonStr = "{" + jsonStr
		}
		if i < len(jsonObjects)-1 && !strings.HasSuffix(jsonStr, "}") {
			jsonStr = jsonStr + "}"
		}

		var stat TrafficStatInfo
		if err := json.Unmarshal([]byte(jsonStr), &stat); err != nil {
			return nil, fmt.Errorf("failed to parse traffic stat JSON: %w", err)
		}
		stats = append(stats, stat)
	}

	return stats, nil
}

// findTrafficStatByName finds a traffic statistics by name
func findTrafficStatByName(stats []TrafficStatInfo, name string) *TrafficStatInfo {
	for _, stat := range stats {
		if stat.Name == name {
			return &stat
		}
	}
	return nil
}

// cleanupTrafficStat removes a traffic statistics by name
func cleanupTrafficStat(t *testing.T, name string) {
	stats, err := getTrafficStatList(t)
	if err != nil {
		t.Logf("Failed to get traffic stat list for cleanup: %v", err)
		return
	}

	stat := findTrafficStatByName(stats, name)
	if stat != nil {
		cmd := exec.Command("floweye", "ntmso", "remove", fmt.Sprintf("id=%d", stat.ID))
		if err := cmd.Run(); err != nil {
			t.Logf("Failed to cleanup traffic stat %s: %v", name, err)
		}
	}
}

// TestTrafficStatBasicOperations tests basic CRUD operations
func TestTrafficStatBasicOperations(t *testing.T) {
	testName := "integration_test_basic"
	
	// Cleanup before test
	cleanupTrafficStat(t, testName)
	defer cleanupTrafficStat(t, testName)

	// Test 1: Create traffic statistics
	createConfig := TrafficStatTestConfig{
		TxID: "test-create",
		DeviceTasks: []DeviceTask{
			{
				TaskType:   "TASK_TRAFFIC_STAT",
				TaskAction: "NEW_CONFIG",
				TrafficStatTask: &TrafficStatTask{
					Name:    testName,
					TrackIP: true,
				},
			},
		},
	}

	output, err := executeAgentDebugClient(t, createConfig)
	assert.NoError(t, err, "Create operation should succeed")
	assert.Contains(t, strings.ToLower(output), "success", "Create output should contain success")

	// Verify creation
	time.Sleep(1 * time.Second)
	stats, err := getTrafficStatList(t)
	require.NoError(t, err)
	
	stat := findTrafficStatByName(stats, testName)
	require.NotNil(t, stat, "Traffic statistics should be created")
	assert.Equal(t, testName, stat.Name)
	assert.Equal(t, 1, stat.TrackIP, "TrackIP should be true (1)")

	// Test 2: Update traffic statistics
	updateConfig := TrafficStatTestConfig{
		TxID: "test-update",
		DeviceTasks: []DeviceTask{
			{
				TaskType:   "TASK_TRAFFIC_STAT",
				TaskAction: "EDIT_CONFIG",
				TrafficStatTask: &TrafficStatTask{
					Name:    testName,
					TrackIP: false,
				},
			},
		},
	}

	output, err = executeAgentDebugClient(t, updateConfig)
	assert.NoError(t, err, "Update operation should succeed")
	assert.Contains(t, strings.ToLower(output), "success", "Update output should contain success")

	// Verify update
	time.Sleep(1 * time.Second)
	stats, err = getTrafficStatList(t)
	require.NoError(t, err)
	
	stat = findTrafficStatByName(stats, testName)
	require.NotNil(t, stat, "Traffic statistics should still exist")
	assert.Equal(t, 0, stat.TrackIP, "TrackIP should be false (0)")

	// Test 3: Delete traffic statistics
	deleteConfig := TrafficStatTestConfig{
		TxID: "test-delete",
		DeviceTasks: []DeviceTask{
			{
				TaskType:   "TASK_TRAFFIC_STAT",
				TaskAction: "DELETE_CONFIG",
				TrafficStatTask: &TrafficStatTask{
					Name: testName,
				},
			},
		},
	}

	output, err = executeAgentDebugClient(t, deleteConfig)
	assert.NoError(t, err, "Delete operation should succeed")
	assert.Contains(t, strings.ToLower(output), "success", "Delete output should contain success")

	// Verify deletion
	time.Sleep(1 * time.Second)
	stats, err = getTrafficStatList(t)
	require.NoError(t, err)
	
	stat = findTrafficStatByName(stats, testName)
	assert.Nil(t, stat, "Traffic statistics should be deleted")
}

// TestTrafficStatIdempotency tests idempotent operations
func TestTrafficStatIdempotency(t *testing.T) {
	testName := "integration_test_idempotent"
	
	// Cleanup before test
	cleanupTrafficStat(t, testName)
	defer cleanupTrafficStat(t, testName)

	config := TrafficStatTestConfig{
		TxID: "test-idempotent",
		DeviceTasks: []DeviceTask{
			{
				TaskType:   "TASK_TRAFFIC_STAT",
				TaskAction: "NEW_CONFIG",
				TrafficStatTask: &TrafficStatTask{
					Name:    testName,
					TrackIP: true,
				},
			},
		},
	}

	// First execution
	output1, err := executeAgentDebugClient(t, config)
	assert.NoError(t, err, "First execution should succeed")
	assert.Contains(t, strings.ToLower(output1), "success", "First execution should succeed")

	// Second execution (should be idempotent)
	time.Sleep(1 * time.Second)
	output2, err := executeAgentDebugClient(t, config)
	assert.NoError(t, err, "Second execution should succeed")
	assert.Contains(t, strings.ToLower(output2), "success", "Second execution should succeed")

	// Verify only one instance exists
	stats, err := getTrafficStatList(t)
	require.NoError(t, err)
	
	count := 0
	for _, stat := range stats {
		if stat.Name == testName {
			count++
		}
	}
	assert.Equal(t, 1, count, "Should have exactly one instance")
}

// TestTrafficStatBatchOperations tests batch operations
func TestTrafficStatBatchOperations(t *testing.T) {
	testNames := []string{"batch_test_1", "batch_test_2", "batch_test_3"}
	
	// Cleanup before test
	for _, name := range testNames {
		cleanupTrafficStat(t, name)
		defer cleanupTrafficStat(t, name)
	}

	// Create batch configuration
	var tasks []DeviceTask
	for i, name := range testNames {
		tasks = append(tasks, DeviceTask{
			TaskType:   "TASK_TRAFFIC_STAT",
			TaskAction: "NEW_CONFIG",
			TrafficStatTask: &TrafficStatTask{
				Name:    name,
				TrackIP: i%2 == 0, // Alternate trackip values
			},
		})
	}

	config := TrafficStatTestConfig{
		TxID:        "test-batch",
		DeviceTasks: tasks,
	}

	output, err := executeAgentDebugClient(t, config)
	assert.NoError(t, err, "Batch operation should succeed")
	assert.Contains(t, strings.ToLower(output), "success", "Batch output should contain success")

	// Verify all were created
	time.Sleep(2 * time.Second)
	stats, err := getTrafficStatList(t)
	require.NoError(t, err)
	
	for i, name := range testNames {
		stat := findTrafficStatByName(stats, name)
		require.NotNil(t, stat, "Traffic statistics %s should be created", name)
		expectedTrackIP := 0
		if i%2 == 0 {
			expectedTrackIP = 1
		}
		assert.Equal(t, expectedTrackIP, stat.TrackIP, "TrackIP should match for %s", name)
	}
}

// TestTrafficStatErrorHandling tests error handling
func TestTrafficStatErrorHandling(t *testing.T) {
	// Test missing name
	config := TrafficStatTestConfig{
		TxID: "test-error",
		DeviceTasks: []DeviceTask{
			{
				TaskType:   "TASK_TRAFFIC_STAT",
				TaskAction: "NEW_CONFIG",
				TrafficStatTask: &TrafficStatTask{
					TrackIP: true,
					// Name is missing
				},
			},
		},
	}

	output, err := executeAgentDebugClient(t, config)
	// Should fail due to missing name
	assert.Error(t, err, "Should fail with missing name")
	assert.Contains(t, strings.ToLower(output), "required", "Error message should mention required field")
}
