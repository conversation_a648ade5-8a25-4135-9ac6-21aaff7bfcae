#!/bin/bash

# SR Proxy模块综合测试脚本
# 测试所有SR Proxy模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="sr_proxy_test_results.log"
echo "SR Proxy模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}$1${NC}"
    echo "$1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证SR Proxy配置
verify_sr_proxy() {
    local name=$1
    local expected_mtu=$2
    local expected_links=$3
    local expected_fromin=$4
    local expected_keepalive=$5
    local expected_encrypt=${6:-""}
    local expected_password=${7:-""}

    echo "验证SR Proxy $name 配置..." >> $LOG_FILE
    local output=$(floweye nat getproxy $name 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取SR Proxy $name 配置"
        return 1
    fi

    # 验证MTU
    if echo "$output" | grep -q "mtu=$expected_mtu"; then
        print_success "SR Proxy $name mtu=$expected_mtu 验证通过"
    else
        print_error "SR Proxy $name mtu 验证失败，期望: $expected_mtu"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证links
    if echo "$output" | grep -q "sre_links=$expected_links"; then
        print_success "SR Proxy $name links=$expected_links 验证通过"
    else
        print_error "SR Proxy $name links 验证失败，期望: $expected_links"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证fromin
    if echo "$output" | grep -q "fromin=$expected_fromin"; then
        print_success "SR Proxy $name fromin=$expected_fromin 验证通过"
    else
        print_error "SR Proxy $name fromin 验证失败，期望: $expected_fromin"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证keepalive
    if echo "$output" | grep -q "sre_keepalive=$expected_keepalive"; then
        print_success "SR Proxy $name keepalive=$expected_keepalive 验证通过"
    else
        print_error "SR Proxy $name keepalive 验证失败，期望: $expected_keepalive"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证加密配置（如果指定）
    if [ -n "$expected_encrypt" ]; then
        if echo "$output" | grep -q "encrypt_algo=$expected_encrypt"; then
            print_success "SR Proxy $name encrypt=$expected_encrypt 验证通过"
        else
            print_error "SR Proxy $name encrypt 验证失败，期望: $expected_encrypt"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi

    # 验证密码（如果指定）
    if [ -n "$expected_password" ]; then
        if echo "$output" | grep -q "encrypt_password=$expected_password"; then
            print_success "SR Proxy $name password=$expected_password 验证通过"
        else
            print_error "SR Proxy $name password 验证失败，期望: $expected_password"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi

    return 0
}

# 验证SR Proxy不存在
verify_sr_proxy_not_exists() {
    local name=$1

    echo "验证SR Proxy $name 不存在..." >> $LOG_FILE
    local output=$(floweye nat getproxy $name 2>&1)

    if [ $? -ne 0 ]; then
        print_success "SR Proxy $name 确实不存在"
        return 0
    else
        print_error "SR Proxy $name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证SR Proxy可选字段默认值
verify_sr_proxy_optional_defaults() {
    local name=$1

    echo "验证SR Proxy $name 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye nat getproxy $name 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取SR Proxy $name 配置"
        return 1
    fi

    # 验证加密默认值为NULL
    if echo "$output" | grep -q "encrypt_algo=NULL" || ! echo "$output" | grep -q "encrypt_algo="; then
        print_success "SR Proxy $name 加密默认值验证通过 (NULL)"
    else
        print_error "SR Proxy $name 加密默认值验证失败，期望: NULL"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证密码默认值为空
    if echo "$output" | grep -q "encrypt_password=\"\"" || ! echo "$output" | grep -q "encrypt_password="; then
        print_success "SR Proxy $name 密码默认值验证通过 (空)"
    else
        print_error "SR Proxy $name 密码默认值验证失败，期望: 空"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}

# 清理所有SR Proxy配置
cleanup_all_sr_proxies() {
    echo "清理所有现有SR Proxy配置..." >> $LOG_FILE

    # 获取所有SR Proxy配置列表
    local sr_list=$(floweye nat listproxy type=srpxy json=1 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$sr_list" ] && [ "$sr_list" != "null" ]; then
        # 解析JSON并提取SR Proxy名称
        local sr_names=$(echo "$sr_list" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

        if [ -n "$sr_names" ]; then
            echo "发现现有SR Proxy配置: $sr_names" >> $LOG_FILE
            for sr_name in $sr_names; do
                echo "删除SR Proxy配置: $sr_name" >> $LOG_FILE
                floweye nat rmvproxy "$sr_name" > /dev/null 2>&1
            done
            print_success "清理了现有SR Proxy配置"
        fi
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "SR Proxy模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 清理现有配置
    print_header "清理测试环境"
    cleanup_all_sr_proxies

    # 获取初始状态
    print_header "获取初始SR Proxy状态"
    echo "初始SR Proxy状态:" >> $LOG_FILE
    floweye nat listproxy type=srpxy json=1 >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 基础SR Proxy配置
    run_test "test_sr_proxy_basic_new.json" "基础SR Proxy配置"
    verify_sr_proxy "srtest1" "1500" "10,20,30" "1" "1"

    # 1.2 幂等性测试 - 重复新增相同配置
    run_test "test_sr_proxy_idempotent.json" "幂等性测试-重复新增"
    verify_sr_proxy "srtest1" "1500" "10,20,30" "1" "1"

    # 1.3 字段修改测试
    run_test "test_sr_proxy_modify_mtu.json" "修改MTU字段"
    verify_sr_proxy "srtest1" "1400" "10,20,30" "1" "1"

    run_test "test_sr_proxy_modify_links.json" "修改Links字段"
    verify_sr_proxy "srtest1" "1400" "40,50,60" "1" "1"

    run_test "test_sr_proxy_modify_fromin.json" "修改FromIn字段"
    verify_sr_proxy "srtest1" "1400" "40,50,60" "0" "1"

    run_test "test_sr_proxy_modify_keepalive.json" "修改Keepalive字段"
    verify_sr_proxy "srtest1" "1400" "40,50,60" "0" "0"

    # 1.4 删除配置测试
    run_test "test_sr_proxy_delete.json" "删除SR Proxy配置"
    verify_sr_proxy_not_exists "srtest1"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_sr_proxy_delete_idempotent.json" "删除配置幂等性测试"
    verify_sr_proxy_not_exists "srtest1"

    # 阶段2: 加密配置测试
    print_header "阶段2: 加密配置测试"

    # 2.1 AES128加密配置
    run_test "test_sr_proxy_aes128.json" "AES128加密配置"
    verify_sr_proxy "srtest2" "1500" "100,200" "1" "1" "AES128" "password123"

    # 2.2 AES256加密配置
    run_test "test_sr_proxy_aes256.json" "AES256加密配置"
    verify_sr_proxy "srtest3" "1600" "300,400" "0" "1" "AES256" "strongpass456"

    # 2.3 加密配置修改
    run_test "test_sr_proxy_modify_encrypt.json" "修改加密配置"
    verify_sr_proxy "srtest2" "1500" "100,200" "1" "1" "AES256" "newpassword789"

    # 清理加密测试配置
    run_test "test_sr_proxy_delete_aes128.json" "删除AES128配置"
    run_test "test_sr_proxy_delete_aes256.json" "删除AES256配置"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 设置初始配置（增量模式）
    run_test "test_sr_proxy_full_sync_setup.json" "全量同步初始配置"
    verify_sr_proxy "srfull1" "1500" "10,20" "1" "1"
    verify_sr_proxy "srfull2" "1600" "30,40" "0" "0"
    verify_sr_proxy "srfull3" "1400" "50,60" "1" "0"

    # 3.2 启动全量同步
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只保留srfull2）
    run_test "test_sr_proxy_full_sync_cleanup.json" "全量同步清理配置"
    verify_sr_proxy "srfull2" "1700" "70,80" "1" "1"

    # 3.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 3.5 验证清理结果：srfull1和srfull3应该被删除
    sleep 2  # 等待清理完成
    verify_sr_proxy_not_exists "srfull1"
    verify_sr_proxy "srfull2" "1700" "70,80" "1" "1"
    verify_sr_proxy_not_exists "srfull3"

    # 清理全量同步测试配置
    run_test "test_sr_proxy_cleanup_full_sync.json" "清理全量同步配置"

    # 阶段4: 边界条件和错误处理测试
    print_header "阶段4: 边界条件和错误处理测试"

    # 4.1 无效参数测试（这些应该失败）
    run_test "test_sr_proxy_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_sr_proxy_error_invalid_mtu.json" "无效MTU错误测试" "false"
    run_test "test_sr_proxy_error_no_links.json" "缺少links字段错误测试" "false"
    run_test "test_sr_proxy_error_empty_links.json" "空links字段错误测试" "false"

    # 4.2 边界值测试
    run_test "test_sr_proxy_boundary_mtu_min.json" "MTU最小值测试"
    verify_sr_proxy "srbound1" "500" "1,2" "1" "1"

    run_test "test_sr_proxy_boundary_mtu_max.json" "MTU最大值测试"
    verify_sr_proxy "srbound2" "4700" "3,4" "0" "0"

    # 清理边界测试配置
    run_test "test_sr_proxy_cleanup_boundary.json" "清理边界测试配置"

    # 阶段5: 完整配置和默认值恢复测试
    print_header "阶段5: 完整配置和默认值恢复测试"

    # 5.1 创建包含所有字段的完整配置
    run_test "test_sr_proxy_complete_config.json" "创建完整配置"
    verify_sr_proxy "srcomplete" "1500" "100,200,300" "1" "1" "AES256" "completepass"

    # 5.2 修改配置，移除可选字段，验证默认值恢复
    run_test "test_sr_proxy_default_values.json" "验证默认值恢复"
    verify_sr_proxy "srcomplete" "1600" "400,500" "0" "0"
    verify_sr_proxy_optional_defaults "srcomplete"

    # 清理完整配置测试
    run_test "test_sr_proxy_cleanup_complete.json" "清理完整配置测试"

    # 最终清理
    print_header "最终清理测试环境"
    cleanup_all_sr_proxies

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "SR Proxy模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
