/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      dns_policy_test.go
 *
 * DESCRIPTION :    Unit tests for DNS policy module
 *
 * AUTHOR :         AI Assistant
 *
 * HISTORY :        01/20/2025  create
 ******************************************************************************/

package test

import (
	"context"
	"testing"

	"agent/internal/client/task"
	"agent/internal/logger"
	pb "agent/internal/pb"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDnsPolicyProcessor_GetTaskType(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)

	taskType := processor.GetTaskType()
	assert.Equal(t, pb.TaskType_TASK_DNS_POLICY, taskType)
}

func TestDnsPolicyProcessor_StartFullSync(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)

	err := processor.StartFullSync()
	// Should not fail even if floweye commands fail in test environment
	// In test environment without floweye, this will fail, which is expected
	if err != nil {
		assert.Contains(t, err.Error(), "floweye")
	}
}

func TestDnsPolicyProcessor_EndFullSync(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)

	// Should not panic
	processor.EndFullSync()
}

func TestParseDnsPolicyFromJSONOutput(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		wantErr  bool
		expected int
	}{
		{
			name:     "empty json",
			jsonData: "[]",
			wantErr:  false,
			expected: 0,
		},
		{
			name: "single policy",
			jsonData: `[{
				"polno": 1,
				"disabled": 0,
				"sch_id": 0,
				"srcip": "any",
				"dstip": "any",
				"ugroup_id": 0,
				"usrtype": "any",
				"dnsid": 0,
				"dnsname": "",
				"atype": "any",
				"inif": "any",
				"bridge": 0,
				"vlan": "0",
				"action": "pass",
				"actarg": "null",
				"ipqps": 0,
				"next": 1,
				"nosnat": 0,
				"dnslist": ""
			}]`,
			wantErr:  false,
			expected: 1,
		},
		{
			name:     "invalid json",
			jsonData: "invalid json",
			wantErr:  true,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configs, err := task.ParseDnsPolicyFromJSONOutput(tt.jsonData)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Len(t, configs, tt.expected)
				if tt.expected > 0 {
					assert.Equal(t, 1, configs[0].ID)
					assert.False(t, configs[0].Disable)
					assert.Equal(t, "any", configs[0].InIP)
					assert.Equal(t, "pass", configs[0].Action)
				}
			}
		})
	}
}

func TestParseDnsPolicyFromGetOutput(t *testing.T) {
	tests := []struct {
		name     string
		output   string
		wantErr  bool
		expected *task.DnsPolicyConfig
	}{
		{
			name: "valid output",
			output: `id=1
cookie=12345
disable=0
schtime=0
inip=any
outip=any
pool=0
usrtype=any
dns=0
atype=any
inif=any
bridge=0
vlan=0
action=pass
actarg=null
ipqps=0
next=1
nosnat=0
dnslist=`,
			wantErr: false,
			expected: &task.DnsPolicyConfig{
				ID:      1,
				Cookie:  12345,
				Disable: false,
				SchTime: 0,
				InIP:    "any",
				OutIP:   "any",
				Pool:    0,
				UsrType: "any",
				DNS:     0,
				AType:   "any",
				InIf:    "any",
				Bridge:  0,
				VLAN:    "0",
				Action:  "pass",
				ActArg:  "null",
				IPQps:   0,
				Next:    false, // next=1 means stop matching
				NoSnat:  false,
				DNSList: "",
			},
		},
		{
			name:     "missing cookie",
			output:   "id=1\ndisable=0",
			wantErr:  true,
			expected: nil,
		},
		{
			name:     "empty output",
			output:   "",
			wantErr:  true,
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := task.ParseDnsPolicyFromGetOutput(tt.output)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				require.NotNil(t, config)
				assert.Equal(t, tt.expected.ID, config.ID)
				assert.Equal(t, tt.expected.Cookie, config.Cookie)
				assert.Equal(t, tt.expected.Disable, config.Disable)
				assert.Equal(t, tt.expected.Action, config.Action)
				assert.Equal(t, tt.expected.Next, config.Next)
			}
		})
	}
}

func TestCompareDnsPolicyConfig(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)

	tests := []struct {
		name        string
		task        *pb.DnsPolicyTask
		localConfig *task.DnsPolicyConfig
		expected    bool
	}{
		{
			name: "matching configs",
			task: &pb.DnsPolicyTask{
				Cookie:      12345,
				Disable:     false,
				SchTime:     func() *int32 { v := int32(0); return &v }(),
				Pool:        func() *int32 { v := int32(0); return &v }(),
				UsrType:     func() *pb.UserType { v := pb.UserType_USER_TYPE_ANY; return &v }(),
				DomainGroup: []string{},
				AType:       func() *pb.DnsQueryType { v := pb.DnsQueryType_DNS_QUERY_TYPE_ANY; return &v }(),
				InIf:        func() *string { v := "any"; return &v }(),
				Bridge:      func() *int32 { v := int32(0); return &v }(),
				Action:      pb.DnsPolicyAction_DNS_ACTION_PASS,
			},
			localConfig: &task.DnsPolicyConfig{
				Cookie:  12345,
				Disable: false,
				SchTime: 0,
				Pool:    0,
				UsrType: "any",
				DNSName: "",
				AType:   "any",
				InIf:    "any",
				Bridge:  0,
				Action:  "pass",
			},
			expected: true,
		},
		{
			name: "different cookies",
			task: &pb.DnsPolicyTask{
				Cookie: 12345,
			},
			localConfig: &task.DnsPolicyConfig{
				Cookie: 54321,
			},
			expected: false,
		},
		{
			name:        "nil task",
			task:        nil,
			localConfig: &task.DnsPolicyConfig{},
			expected:    false,
		},
		{
			name: "nil local config",
			task: &pb.DnsPolicyTask{
				Cookie: 12345,
			},
			localConfig: nil,
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.task == nil {
				// Handle nil task case
				result := task.CompareDnsPolicyConfig(log, nil, tt.localConfig)
				assert.Equal(t, tt.expected, result)
			} else {
				// Convert protobuf to config first
				configData, err := task.ConvertDnsPolicyTaskToConfig(tt.task, log)
				if err != nil {
					t.Fatalf("Failed to convert task to config: %v", err)
				}
				result := task.CompareDnsPolicyConfig(log, configData, tt.localConfig)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDnsPolicyProcessor_ProcessTask_InvalidPayload(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)
	ctx := context.Background()

	// Test with nil DNS policy task
	deviceTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DNS_POLICY,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		// DnsPolicyTask is nil
	}

	desc, err := processor.ProcessTask(ctx, deviceTask)
	assert.Error(t, err)
	assert.Contains(t, desc, "DNS policy task payload is nil")
}

func TestDnsPolicyProcessor_ProcessTask_MissingCookie(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)
	ctx := context.Background()

	// Test with missing cookie
	deviceTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DNS_POLICY,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		Payload: &pb.DeviceTask_DnsPolicyTask{
			DnsPolicyTask: &pb.DnsPolicyTask{
				Cookie: 0, // Missing cookie
			},
		},
	}

	desc, err := processor.ProcessTask(ctx, deviceTask)
	assert.Error(t, err)
	assert.Contains(t, desc, "DNS policy cookie is required")
}

func TestDnsPolicyProcessor_ProcessTask_UnknownAction(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)
	ctx := context.Background()

	// Test with unknown task action
	deviceTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_DNS_POLICY,
		TaskAction: pb.TaskAction(999), // Unknown action
		Payload: &pb.DeviceTask_DnsPolicyTask{
			DnsPolicyTask: &pb.DnsPolicyTask{
				Cookie: 12345,
			},
		},
	}

	desc, err := processor.ProcessTask(ctx, deviceTask)
	assert.Error(t, err)
	assert.Contains(t, desc, "Unknown task action")
}

func TestDnsPolicyDependencyResolution(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)

	// Test IP group name resolution using common utility
	t.Run("IP group resolution", func(t *testing.T) {
		resolver := task.NewDefaultGroupResolver(log)
		_, err := resolver.ResolveIPGroupNameToID("test_ip_group")
		if err != nil {
			// Expected to fail in test environment
			assert.Contains(t, err.Error(), "floweye")
		}
	})

	// Test domain group name resolution using common utility
	t.Run("Domain group resolution", func(t *testing.T) {
		resolver := task.NewDefaultGroupResolver(log)
		_, err := resolver.ResolveDomainGroupNameToID("test_domain_group")
		if err != nil {
			// Expected to fail in test environment
			assert.Contains(t, err.Error(), "floweye")
		}
	})
}

func TestCompareDnsPolicyConfigWithIPAddresses(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)

	tests := []struct {
		name        string
		task        *pb.DnsPolicyTask
		localConfig *task.DnsPolicyConfig
		expected    bool
	}{
		{
			name: "matching IP addresses",
			task: &pb.DnsPolicyTask{
				Cookie:  12345,
				Disable: false,
				InIp: []*pb.AddressSelector{
					{
						Selector: &pb.AddressSelector_Ip{
							Ip: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{
									IpString: "***********/24",
								},
							},
						},
					},
				},
				OutIp: []*pb.AddressSelector{
					{
						Selector: &pb.AddressSelector_Ip{
							Ip: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{
									IpString: "********",
								},
							},
						},
					},
				},
				Pool:        func() *int32 { v := int32(0); return &v }(),
				UsrType:     func() *pb.UserType { v := pb.UserType_USER_TYPE_ANY; return &v }(),
				DomainGroup: []string{},
				AType:       func() *pb.DnsQueryType { v := pb.DnsQueryType_DNS_QUERY_TYPE_ANY; return &v }(),
				InIf:        func() *string { v := "any"; return &v }(),
				Bridge:      func() *int32 { v := int32(0); return &v }(),
				Action:      pb.DnsPolicyAction_DNS_ACTION_PASS,
			},
			localConfig: &task.DnsPolicyConfig{
				Cookie:  12345,
				Disable: false,
				InIP:    "***********/24",
				OutIP:   "********",
				Pool:    0,
				UsrType: "any",
				DNSName: "",
				AType:   "any",
				InIf:    "any",
				Bridge:  0,
				Action:  "pass",
			},
			expected: true,
		},
		{
			name: "mismatched source IP addresses",
			task: &pb.DnsPolicyTask{
				Cookie:  12345,
				Disable: false,
				InIp: []*pb.AddressSelector{
					{
						Selector: &pb.AddressSelector_Ip{
							Ip: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{
									IpString: "***********/24",
								},
							},
						},
					},
				},
				Pool:        func() *int32 { v := int32(0); return &v }(),
				UsrType:     func() *pb.UserType { v := pb.UserType_USER_TYPE_ANY; return &v }(),
				DomainGroup: []string{},
				AType:       func() *pb.DnsQueryType { v := pb.DnsQueryType_DNS_QUERY_TYPE_ANY; return &v }(),
				InIf:        func() *string { v := "any"; return &v }(),
				Bridge:      func() *int32 { v := int32(0); return &v }(),
				Action:      pb.DnsPolicyAction_DNS_ACTION_PASS,
			},
			localConfig: &task.DnsPolicyConfig{
				Cookie:  12345,
				Disable: false,
				InIP:    "***********/24",
				OutIP:   "any",
				Pool:    0,
				UsrType: "any",
				DNSName: "",
				AType:   "any",
				InIf:    "any",
				Bridge:  0,
				Action:  "pass",
			},
			expected: false,
		},
		{
			name: "empty IP addresses (any)",
			task: &pb.DnsPolicyTask{
				Cookie:      12345,
				Disable:     false,
				InIp:        []*pb.AddressSelector{}, // Empty means any
				OutIp:       []*pb.AddressSelector{}, // Empty means any
				Pool:        func() *int32 { v := int32(0); return &v }(),
				UsrType:     func() *pb.UserType { v := pb.UserType_USER_TYPE_ANY; return &v }(),
				DomainGroup: []string{},
				AType:       func() *pb.DnsQueryType { v := pb.DnsQueryType_DNS_QUERY_TYPE_ANY; return &v }(),
				InIf:        func() *string { v := "any"; return &v }(),
				Bridge:      func() *int32 { v := int32(0); return &v }(),
				Action:      pb.DnsPolicyAction_DNS_ACTION_PASS,
			},
			localConfig: &task.DnsPolicyConfig{
				Cookie:  12345,
				Disable: false,
				InIP:    "any",
				OutIP:   "any",
				Pool:    0,
				UsrType: "any",
				DNSName: "",
				AType:   "any",
				InIf:    "any",
				Bridge:  0,
				Action:  "pass",
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.task == nil {
				// Handle nil task case
				result := task.CompareDnsPolicyConfig(log, nil, tt.localConfig)
				assert.Equal(t, tt.expected, result)
			} else {
				// Convert protobuf to config first
				configData, err := task.ConvertDnsPolicyTaskToConfig(tt.task, log)
				if err != nil {
					t.Fatalf("Failed to convert task to config: %v", err)
				}
				result := task.CompareDnsPolicyConfig(log, configData, tt.localConfig)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDnsPolicyProcessor_GetCurrentPolicyIDByCookie(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)

	// Test that the function exists by testing the delete path that uses it
	// We can't directly test the private method, but we can test the code path
	t.Run("getCurrentPolicyIDByCookie is used in delete during full sync", func(t *testing.T) {
		// Start full sync to enable the getCurrentPolicyIDByCookie path
		processor.StartFullSync()

		// Create a delete task that will trigger the getCurrentPolicyIDByCookie path
		deleteTask := &pb.DeviceTask{
			TaskType:   pb.TaskType_TASK_DNS_POLICY,
			TaskAction: pb.TaskAction_DELETE_CONFIG,
			Payload: &pb.DeviceTask_DnsPolicyTask{
				DnsPolicyTask: &pb.DnsPolicyTask{
					Cookie: 12345,
				},
			},
		}

		// This will fail in test environment without floweye, but we can verify
		// that the code path is exercised
		_, err := processor.ProcessTask(context.Background(), deleteTask)
		if err != nil {
			// Expected to fail in test environment
			assert.Contains(t, err.Error(), "floweye")
		}

		processor.EndFullSync()
	})
}

func TestDnsPolicyProcessor_DeleteWithStaleCache(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)
	processor := task.NewDnsPolicyProcessor(log)
	ctx := context.Background()

	// Test deletion logic with potential stale cache scenario
	t.Run("delete handles stale cache during full sync", func(t *testing.T) {
		// Start full sync to enable the getCurrentPolicyIDByCookie path
		processor.StartFullSync()

		// Create a delete task
		deleteTask := &pb.DeviceTask{
			TaskType:   pb.TaskType_TASK_DNS_POLICY,
			TaskAction: pb.TaskAction_DELETE_CONFIG,
			Payload: &pb.DeviceTask_DnsPolicyTask{
				DnsPolicyTask: &pb.DnsPolicyTask{
					Cookie: 12345,
				},
			},
		}

		// This will fail in test environment without floweye, but we can verify
		// that the code path is exercised and error handling works
		desc, err := processor.ProcessTask(ctx, deleteTask)
		if err != nil {
			// Expected to fail in test environment
			assert.Contains(t, err.Error(), "floweye")
		} else {
			// If it somehow succeeds, check the description
			assert.Contains(t, desc, "deletion successful")
		}

		processor.EndFullSync()
	})

	t.Run("delete uses cached ID during incremental update", func(t *testing.T) {
		// Don't start full sync - this should use cached ID path
		deleteTask := &pb.DeviceTask{
			TaskType:   pb.TaskType_TASK_DNS_POLICY,
			TaskAction: pb.TaskAction_DELETE_CONFIG,
			Payload: &pb.DeviceTask_DnsPolicyTask{
				DnsPolicyTask: &pb.DnsPolicyTask{
					Cookie: 12345,
				},
			},
		}

		// This will fail in test environment without floweye
		desc, err := processor.ProcessTask(ctx, deleteTask)
		if err != nil {
			// Expected to fail in test environment
			assert.Contains(t, err.Error(), "floweye")
		} else {
			// If it somehow succeeds, check the description
			assert.Contains(t, desc, "deletion successful")
		}
	})
}
