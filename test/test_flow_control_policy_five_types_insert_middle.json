[{"tx_id": "flow-control-policy-five-types-insert-middle-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60006, "desc": "new_permit_policy_inserted_middle", "group_name": "flow_control_movement_group", "disable": false, "previous": 60001, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": true, "ip_rate": 500, "tos": 4}, "in_ip": [{"ip": {"ipv4": 3232237056, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}], "in_port": {"ports": [{"start": 3389, "end": 3389}]}, "app": {"app_name": "https", "app_protocol": "tcp"}}}}]}]