[{"tx_id": "test-iwan-proxy-complete-config-001", "device_tasks": [{"task_type": "TASK_IWAN_PROXY", "task_action": "NEW_CONFIG", "iwan_proxy_task": {"name": "iwan2", "ifname": "wan1", "mtu": 1420, "svr_addr": "server.example.com", "svr_port": 8000, "username": "admin", "password": "admin123", "encrypt": true, "link": 123, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "*******"}, "max_delay": 100}, "dns_pxy": true}}]}]