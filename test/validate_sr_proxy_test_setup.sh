#!/bin/bash

# SR Proxy测试设置验证脚本
# 验证所有必要的测试文件是否正确创建

echo "=== SR Proxy测试设置验证 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
TOTAL_FILES=0
FOUND_FILES=0
MISSING_FILES=0

# 检查文件函数
check_file() {
    local file=$1
    local description=$2
    
    TOTAL_FILES=$((TOTAL_FILES + 1))
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓${NC} $description: $file"
        FOUND_FILES=$((FOUND_FILES + 1))
    else
        echo -e "${RED}✗${NC} $description: $file (缺失)"
        MISSING_FILES=$((MISSING_FILES + 1))
    fi
}

echo "检查主要测试脚本..."
check_file "sr_proxy_comprehensive_test.sh" "主测试脚本"

echo ""
echo "检查基础CRUD测试用例..."
check_file "test_sr_proxy_basic_new.json" "基础SR Proxy配置"
check_file "test_sr_proxy_idempotent.json" "幂等性测试"
check_file "test_sr_proxy_modify_mtu.json" "MTU修改测试"
check_file "test_sr_proxy_modify_links.json" "Links修改测试"
check_file "test_sr_proxy_modify_fromin.json" "FromIn修改测试"
check_file "test_sr_proxy_modify_keepalive.json" "Keepalive修改测试"
check_file "test_sr_proxy_delete.json" "删除配置测试"
check_file "test_sr_proxy_delete_idempotent.json" "删除幂等性测试"

echo ""
echo "检查加密配置测试用例..."
check_file "test_sr_proxy_aes128.json" "AES128加密配置"
check_file "test_sr_proxy_aes256.json" "AES256加密配置"
check_file "test_sr_proxy_modify_encrypt.json" "加密配置修改"
check_file "test_sr_proxy_delete_aes128.json" "删除AES128配置"
check_file "test_sr_proxy_delete_aes256.json" "删除AES256配置"

echo ""
echo "检查全量同步测试用例..."
check_file "test_sr_proxy_full_sync_setup.json" "全量同步初始配置"
check_file "test_sr_proxy_full_sync_cleanup.json" "全量同步清理配置"
check_file "test_sr_proxy_cleanup_full_sync.json" "清理全量同步配置"

echo ""
echo "检查错误处理测试用例..."
check_file "test_sr_proxy_error_no_name.json" "缺少name字段错误测试"
check_file "test_sr_proxy_error_invalid_mtu.json" "无效MTU错误测试"
check_file "test_sr_proxy_error_no_links.json" "缺少links字段错误测试"
check_file "test_sr_proxy_error_empty_links.json" "空links字段错误测试"

echo ""
echo "检查边界值测试用例..."
check_file "test_sr_proxy_boundary_mtu_min.json" "MTU最小值测试"
check_file "test_sr_proxy_boundary_mtu_max.json" "MTU最大值测试"
check_file "test_sr_proxy_cleanup_boundary.json" "清理边界测试配置"

echo ""
echo "检查完整配置测试用例..."
check_file "test_sr_proxy_complete_config.json" "完整配置测试"
check_file "test_sr_proxy_default_values.json" "默认值恢复测试"
check_file "test_sr_proxy_cleanup_complete.json" "清理完整配置测试"

echo ""
echo "检查文档文件..."
check_file "SR_PROXY_TEST_README.md" "测试说明文档"
check_file "SR_PROXY_TEST_EXECUTION_REPORT.md" "测试执行报告"

echo ""
echo "检查测试脚本权限..."
if [ -x "sr_proxy_comprehensive_test.sh" ]; then
    echo -e "${GREEN}✓${NC} 测试脚本具有执行权限"
else
    echo -e "${RED}✗${NC} 测试脚本缺少执行权限"
    echo "请运行: chmod +x sr_proxy_comprehensive_test.sh"
fi

echo ""
echo "检查JSON文件格式..."
json_errors=0
for json_file in test_sr_proxy_*.json; do
    if [ -f "$json_file" ]; then
        if python3 -m json.tool "$json_file" > /dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $json_file JSON格式正确"
        else
            echo -e "${RED}✗${NC} $json_file JSON格式错误"
            json_errors=$((json_errors + 1))
        fi
    fi
done

echo ""
echo "=== 验证结果统计 ==="
echo "总文件数: $TOTAL_FILES"
echo "找到文件: $FOUND_FILES"
echo "缺失文件: $MISSING_FILES"
echo "JSON错误: $json_errors"

if [ $MISSING_FILES -eq 0 ] && [ $json_errors -eq 0 ]; then
    echo -e "${GREEN}✓ 所有测试文件验证通过！${NC}"
    echo ""
    echo "可以执行以下命令进行测试："
    echo "  make test-sr-proxy                    # 执行SR Proxy模块测试"
    echo "  VERBOSE=true make test-sr-proxy       # 详细模式执行"
    echo "  ./sr_proxy_comprehensive_test.sh     # 直接执行测试脚本"
    exit 0
else
    echo -e "${RED}✗ 发现 $MISSING_FILES 个缺失文件和 $json_errors 个JSON错误${NC}"
    echo "请检查并修复上述问题后重新验证"
    exit 1
fi
