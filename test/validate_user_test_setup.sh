#!/bin/bash

# User模块测试设置验证脚本
# 验证所有测试文件和配置是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

check_file() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        print_success "$description 存在"
        return 0
    else
        print_error "$description 不存在: $file"
        return 1
    fi
}

check_executable() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -x "$file" ]; then
        print_success "$description 可执行"
        return 0
    else
        print_error "$description 不可执行: $file"
        return 1
    fi
}

check_json_syntax() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if python3 -m json.tool "$file" > /dev/null 2>&1; then
        print_success "$description JSON格式正确"
        return 0
    else
        print_error "$description JSON格式错误: $file"
        return 1
    fi
}

check_bash_syntax() {
    local file=$1
    local description=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if bash -n "$file" 2>/dev/null; then
        print_success "$description Bash语法正确"
        return 0
    else
        print_error "$description Bash语法错误: $file"
        return 1
    fi
}

main() {
    print_header "User模块测试设置验证"
    
    # 检查主测试脚本
    print_header "检查主测试脚本"
    check_file "user_comprehensive_test.sh" "主测试脚本"
    check_executable "user_comprehensive_test.sh" "主测试脚本"
    check_bash_syntax "user_comprehensive_test.sh" "主测试脚本"
    
    # 检查基础CRUD测试用例
    print_header "检查基础CRUD测试用例"
    check_file "test_user_basic_new.json" "基础用户创建测试"
    check_json_syntax "test_user_basic_new.json" "基础用户创建测试"
    
    check_file "test_user_idempotent_new.json" "幂等性测试"
    check_json_syntax "test_user_idempotent_new.json" "幂等性测试"
    
    check_file "test_user_modify_password.json" "密码修改测试"
    check_json_syntax "test_user_modify_password.json" "密码修改测试"
    
    check_file "test_user_modify_max_online.json" "最大在线数修改测试"
    check_json_syntax "test_user_modify_max_online.json" "最大在线数修改测试"
    
    check_file "test_user_modify_bind_ip.json" "绑定IP修改测试"
    check_json_syntax "test_user_modify_bind_ip.json" "绑定IP修改测试"
    
    check_file "test_user_disable.json" "用户禁用测试"
    check_json_syntax "test_user_disable.json" "用户禁用测试"
    
    check_file "test_user_enable.json" "用户启用测试"
    check_json_syntax "test_user_enable.json" "用户启用测试"
    
    check_file "test_user_delete.json" "用户删除测试"
    check_json_syntax "test_user_delete.json" "用户删除测试"
    
    check_file "test_user_delete_idempotent.json" "删除幂等性测试"
    check_json_syntax "test_user_delete_idempotent.json" "删除幂等性测试"
    
    # 检查完整配置测试用例
    print_header "检查完整配置测试用例"
    check_file "test_user_complete_config.json" "完整配置测试"
    check_json_syntax "test_user_complete_config.json" "完整配置测试"
    
    check_file "test_user_modify_pool_id.json" "用户组修改测试"
    check_json_syntax "test_user_modify_pool_id.json" "用户组修改测试"
    
    check_file "test_user_optional_fields_default.json" "默认值恢复测试"
    check_json_syntax "test_user_optional_fields_default.json" "默认值恢复测试"
    
    # 检查全量同步测试用例
    print_header "检查全量同步测试用例"
    check_file "test_user_full_sync_setup.json" "全量同步初始配置"
    check_json_syntax "test_user_full_sync_setup.json" "全量同步初始配置"
    
    check_file "test_user_full_sync_cleanup.json" "全量同步清理配置"
    check_json_syntax "test_user_full_sync_cleanup.json" "全量同步清理配置"
    
    # 检查错误处理测试用例
    print_header "检查错误处理测试用例"
    check_file "test_user_error_no_name.json" "缺少name字段错误测试"
    check_json_syntax "test_user_error_no_name.json" "缺少name字段错误测试"
    
    check_file "test_user_error_no_pool_id.json" "缺少pool_id字段错误测试"
    check_json_syntax "test_user_error_no_pool_id.json" "缺少pool_id字段错误测试"
    
    check_file "test_user_error_no_password.json" "缺少password字段错误测试"
    check_json_syntax "test_user_error_no_password.json" "缺少password字段错误测试"
    
    check_file "test_user_error_invalid_pool_id.json" "无效pool_id错误测试"
    check_json_syntax "test_user_error_invalid_pool_id.json" "无效pool_id错误测试"
    
    check_file "test_user_error_invalid_date.json" "无效日期格式错误测试"
    check_json_syntax "test_user_error_invalid_date.json" "无效日期格式错误测试"
    
    check_file "test_user_error_long_name.json" "用户名过长错误测试"
    check_json_syntax "test_user_error_long_name.json" "用户名过长错误测试"
    
    # 检查依赖关系测试用例
    print_header "检查依赖关系测试用例"
    check_file "test_user_dependency_create.json" "依赖关系测试"
    check_json_syntax "test_user_dependency_create.json" "依赖关系测试"
    
    # 检查文档文件
    print_header "检查文档文件"
    check_file "USER_TEST_README.md" "测试说明文档"
    check_file "USER_TEST_SUMMARY.md" "测试总结文档"
    check_file "USER_TEST_EXECUTION_REPORT.md" "测试执行报告模板"
    
    # 检查Makefile配置
    print_header "检查Makefile配置"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if grep -q "test-user:" ../Makefile; then
        print_success "Makefile包含test-user目标"
    else
        print_error "Makefile缺少test-user目标"
    fi
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if grep -q "test-user-comprehensive:" ../Makefile; then
        print_success "Makefile包含test-user-comprehensive目标"
    else
        print_error "Makefile缺少test-user-comprehensive目标"
    fi
    
    # 检查自动化测试文档更新
    print_header "检查文档更新"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if grep -q "test-user" ../docs/AUTOMATED_TESTING.md; then
        print_success "自动化测试文档已更新"
    else
        print_error "自动化测试文档未更新"
    fi
    
    # 统计结果
    print_header "验证结果统计"
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过检查: $PASSED_CHECKS"
    echo "失败检查: $FAILED_CHECKS"
    echo "成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "所有检查通过！User模块测试设置完整。"
        echo ""
        echo "可以执行以下命令进行测试："
        echo "  make test-user                    # 执行User模块测试"
        echo "  VERBOSE=true make test-user       # 详细模式执行"
        echo "  make test-check-env               # 检查测试环境"
        exit 0
    else
        print_error "有 $FAILED_CHECKS 个检查失败，请修复后重新验证。"
        exit 1
    fi
}

# 执行主函数
main "$@"
