[{"tx_id": "flow-control-policy-five-types-move-to-last-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 60007, "desc": "new_permit_policy_appended_to_last", "group_name": "flow_control_movement_group", "disable": false, "previous": 4294967295, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": true, "ip_rate": 1000, "tos": 8}, "in_ip": [{"ip": {"ipv4": 3232235776, "prefix_length": 24}}, {"ip_range": {"start_ip": {"ipv4": 167772161}, "end_ip": {"ipv4": 167772260}}}], "out_ip": [{"ip": {"ipv4": 3232235520, "prefix_length": 24}}], "in_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}]}, "out_port": {"ports": [{"start": 80, "end": 80}]}, "app": {"app_name": "http", "app_protocol": "tcp"}, "interface": {"bridge": "any", "dir": "both", "if_name": "any", "in_if": "any", "vlan": {"start": 0, "end": 0}}}}}]}]