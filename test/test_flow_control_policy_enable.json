[{"tx_id": "flow-control-policy-enable-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "EDIT_CONFIG", "flow_control_task": {"policy": {"cookie": 12345, "desc": "flow_control_test_policy1_modified", "group_name": "flow_control_test_group1", "disable": false, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": true, "ip_rate": 2000, "tos": 10}, "in_ip": [{"ip": {"ipv4": 3232236032, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}], "in_port": {"ports": [{"start": 8080, "end": 8080}]}, "out_port": {"ports": [{"start": 80, "end": 80}]}, "app": {"app_name": "http", "app_protocol": "tcp"}, "interface": {"bridge": "any", "dir": "FLOW_DIRECTION_IN", "ifname": "eth0", "inif": "eth0", "vlan": {"start": 100, "end": 200}}}}}]}]