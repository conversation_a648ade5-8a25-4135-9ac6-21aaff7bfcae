[{"tx_id": "iwan-mapping-deps-setup-001", "device_tasks": [{"task_type": "TASK_INTERFACE", "task_action": "EDIT_CONFIG", "interface_task": {"name": "eth2", "mode": "INTERFACE_MODE_MONITOR", "zone": "INTERFACE_ZONE_OUTSIDE", "mix_mode": false}}]}, {"tx_id": "iwan-mapping-deps-setup-002", "device_tasks": [{"task_type": "TASK_WAN", "task_action": "NEW_CONFIG", "wan_task": {"name": "wantestmapping", "ifname": "eth2", "mtu": 1500, "static_ip": {"gw_pxy": "WAN_GATEWAY_TYPE_NORMAL", "addr": {"ip_string": "*************"}, "gateway": {"ip_string": "***************"}, "dns": {"ip_string": "*******"}, "nat_ip": {"ip_string": "0.0.0.0"}}, "heartbeat": {"ping_ip": {"ip_string": "*******"}, "ping_ip2": {"ip_string": "0.0.0.0"}, "max_delay": 1000}, "common": {"dns_pxy": false, "ping_disable": false, "clone_mac": "00-00-00-00-00-00"}}}]}, {"tx_id": "iwan-mapping-deps-setup-003", "device_tasks": [{"task_type": "TASK_IWAN_SERVICE", "task_action": "NEW_CONFIG", "iwan_service_task": {"name": "iwan-svc-test1", "addr": {"ip_string": "********"}, "mtu": 1400, "pool": 100}}]}, {"tx_id": "iwan-mapping-deps-setup-004", "device_tasks": [{"task_type": "TASK_IWAN_SERVICE", "task_action": "NEW_CONFIG", "iwan_service_task": {"name": "iwan-svc-test2", "addr": {"ip_string": "********"}, "mtu": 1400, "pool": 100}}]}]