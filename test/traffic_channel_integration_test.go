/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      traffic_channel_integration_test.go
 *
 * DESCRIPTION :    Integration tests for traffic channel module
 *
 * AUTHOR :         AI Assistant
 *
 * HISTORY :        01/03/2025  create
 ******************************************************************************/

package test

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
	"time"

	"agent/internal/client/task"
	"agent/internal/logger"
	pb "agent/internal/pb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TrafficChannelIntegrationTestSuite contains the test suite for traffic channel integration tests
type TrafficChannelIntegrationTestSuite struct {
	processor *task.TrafficChannelProcessor
	logger    *logger.Logger
	testData  []*pb.TaskTx
}

// setupTrafficChannelIntegrationTest sets up the integration test environment
func setupTrafficChannelIntegrationTest(t *testing.T) *TrafficChannelIntegrationTestSuite {
	// Skip if running in CI environment or if floweye command is not available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping integration test in CI environment")
	}

	// Create test logger
	config := &logger.Config{
		Level:  "debug",
		Format: "text",
	}
	log := logger.NewLogger(config)

	// Create traffic channel processor
	processor := task.NewTrafficChannelProcessor(log)

	// Load test data
	testData, err := loadTrafficChannelTestData()
	if err != nil {
		t.Fatalf("Failed to load test data: %v", err)
	}

	return &TrafficChannelIntegrationTestSuite{
		processor: processor,
		logger:    log,
		testData:  testData,
	}
}

// loadTrafficChannelTestData loads test configuration from JSON file
func loadTrafficChannelTestData() ([]*pb.TaskTx, error) {
	// Get the current working directory
	wd, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("failed to get working directory: %w", err)
	}

	// Try different possible paths for the test data file
	possiblePaths := []string{
		"traffic_channel_config.json",
		"test/traffic_channel_config.json",
		filepath.Join(wd, "traffic_channel_config.json"),
		filepath.Join(wd, "test/traffic_channel_config.json"),
		filepath.Join(filepath.Dir(wd), "test/traffic_channel_config.json"),
	}

	var data []byte
	var filePath string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			data, err = ioutil.ReadFile(path)
			if err == nil {
				filePath = path
				break
			}
		}
	}

	if data == nil {
		return nil, fmt.Errorf("test data file not found in any of the expected locations: %v", possiblePaths)
	}

	fmt.Printf("Loading test data from: %s\n", filePath)

	var batches []*pb.TaskTx
	if err := json.Unmarshal(data, &batches); err != nil {
		return nil, fmt.Errorf("failed to unmarshal test data: %w", err)
	}

	return batches, nil
}

// cleanupTrafficChannelTest cleans up test environment
func (suite *TrafficChannelIntegrationTestSuite) cleanupTrafficChannelTest(t *testing.T) {
	// Clean up any test channels that might have been created
	testChannelNames := []string{
		"channel_test_1",
		"channel_test_2",
		"test_channel_integration",
		"test_channel_performance",
	}

	for _, name := range testChannelNames {
		deleteTask := &pb.TrafficChannelTask{
			Name: name,
		}
		_, err := suite.processor.ProcessTask(context.Background(), &pb.DeviceTask{
			TaskType:           pb.TaskType_TASK_TRAFFIC_CHANNEL,
			TaskAction:         pb.TaskAction_DELETE_CONFIG,
			TrafficChannelTask: deleteTask,
		})
		if err != nil {
			t.Logf("Warning: Failed to cleanup test channel %s: %v", name, err)
		}
	}
}

// TestTrafficChannelIntegration_BasicWorkflow tests basic traffic channel operations
func TestTrafficChannelIntegration_BasicWorkflow(t *testing.T) {
	suite := setupTrafficChannelIntegrationTest(t)
	defer suite.cleanupTrafficChannelTest(t)

	ctx := context.Background()

	// Test 1: Create a new traffic channel
	createTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		TrafficChannelTask: &pb.TrafficChannelTask{
			Name:  "test_channel_integration",
			Rate:  20000,
			Quota: func() *int32 { v := int32(10000); return &v }(),
			Priorities: []*pb.TrafficChannelPriority{
				{
					Pri:     1,
					MaxRate: 15000,
					Gbw:     5000,
					Desc:    func() *string { s := "High priority traffic"; return &s }(),
				},
				{
					Pri:     2,
					MaxRate: 10000,
					Gbw:     3000,
					Desc:    func() *string { s := "Medium priority traffic"; return &s }(),
				},
			},
		},
	}

	result, err := suite.processor.ProcessTask(ctx, createTask)
	if err != nil && containsFlowyeError(err.Error()) {
		t.Skip("Skipping test because floweye command is not available or failed")
	}
	require.NoError(t, err)
	assert.Contains(t, result, "successfully")

	// Test 2: Modify the traffic channel
	editTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
		TaskAction: pb.TaskAction_EDIT_CONFIG,
		TrafficChannelTask: &pb.TrafficChannelTask{
			Name:  "test_channel_integration",
			Rate:  30000,
			Quota: func() *int32 { v := int32(15000); return &v }(),
			Priorities: []*pb.TrafficChannelPriority{
				{
					Pri:     1,
					MaxRate: 25000,
					Gbw:     8000,
					Desc:    func() *string { s := "Updated high priority"; return &s }(),
				},
				{
					Pri:     3,
					MaxRate: 15000,
					Gbw:     5000,
					Desc:    func() *string { s := "New priority level"; return &s }(),
				},
			},
		},
	}

	result, err = suite.processor.ProcessTask(ctx, editTask)
	require.NoError(t, err)
	assert.Contains(t, result, "successfully")

	// Test 3: Delete the traffic channel
	deleteTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		TrafficChannelTask: &pb.TrafficChannelTask{
			Name: "test_channel_integration",
		},
	}

	result, err = suite.processor.ProcessTask(ctx, deleteTask)
	require.NoError(t, err)
	assert.Contains(t, result, "successfully")
}

// TestTrafficChannelIntegration_FullSync tests full synchronization scenarios
func TestTrafficChannelIntegration_FullSync(t *testing.T) {
	suite := setupTrafficChannelIntegrationTest(t)
	defer suite.cleanupTrafficChannelTest(t)

	ctx := context.Background()

	// Test full sync workflow
	err := suite.processor.StartFullSync()
	if err != nil && containsFlowyeError(err.Error()) {
		t.Skip("Skipping test because floweye command is not available or failed")
	}
	require.NoError(t, err)

	// Process some tasks during full sync
	for i, batch := range suite.testData {
		if i >= 2 { // Limit to first 2 batches for testing
			break
		}

		for _, deviceTask := range batch.GetDeviceTasks() {
			if deviceTask.TaskType == pb.TaskType_TASK_TRAFFIC_CHANNEL {
				result, err := suite.processor.ProcessTask(ctx, deviceTask)
				if err != nil {
					t.Logf("Task processing failed (expected in test environment): %v", err)
					continue
				}
				t.Logf("Task processed successfully: %s", result)
			}
		}
	}

	// End full sync
	suite.processor.EndFullSync()
}

// TestTrafficChannelIntegration_ErrorHandling tests error handling scenarios
func TestTrafficChannelIntegration_ErrorHandling(t *testing.T) {
	suite := setupTrafficChannelIntegrationTest(t)
	defer suite.cleanupTrafficChannelTest(t)

	ctx := context.Background()

	// Test 1: Invalid channel name
	invalidTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		TrafficChannelTask: &pb.TrafficChannelTask{
			Name: "", // Empty name should fail
			Rate: 20000,
		},
	}

	result, err := suite.processor.ProcessTask(ctx, invalidTask)
	assert.Error(t, err)
	assert.Contains(t, result, "required")

	// Test 2: Invalid rate
	invalidRateTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
		TaskAction: pb.TaskAction_NEW_CONFIG,
		TrafficChannelTask: &pb.TrafficChannelTask{
			Name: "test_invalid_rate",
			Rate: -1000, // Negative rate should fail
		},
	}

	result, err = suite.processor.ProcessTask(ctx, invalidRateTask)
	assert.Error(t, err)
	assert.Contains(t, result, "rate")

	// Test 3: Delete non-existent channel
	deleteNonExistentTask := &pb.DeviceTask{
		TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
		TaskAction: pb.TaskAction_DELETE_CONFIG,
		TrafficChannelTask: &pb.TrafficChannelTask{
			Name: "non_existent_channel",
		},
	}

	result, err = suite.processor.ProcessTask(ctx, deleteNonExistentTask)
	// This should not error (idempotent operation)
	assert.NoError(t, err)
	assert.Contains(t, result, "not found")
}

// TestTrafficChannelIntegration_Performance tests performance with multiple operations
func TestTrafficChannelIntegration_Performance(t *testing.T) {
	suite := setupTrafficChannelIntegrationTest(t)
	defer suite.cleanupTrafficChannelTest(t)

	ctx := context.Background()

	// Create multiple channels concurrently
	numChannels := 5
	results := make(chan error, numChannels)

	start := time.Now()

	for i := 0; i < numChannels; i++ {
		go func(index int) {
			channelName := fmt.Sprintf("test_channel_performance_%d", index)
			createTask := &pb.DeviceTask{
				TaskType:   pb.TaskType_TASK_TRAFFIC_CHANNEL,
				TaskAction: pb.TaskAction_NEW_CONFIG,
				TrafficChannelTask: &pb.TrafficChannelTask{
					Name: channelName,
					Rate: int32(10000 + index*1000),
					Priorities: []*pb.TrafficChannelPriority{
						{
							Pri:     1,
							MaxRate: int32(8000 + index*800),
							Gbw:     int32(2000 + index*200),
							Desc:    func() *string { s := fmt.Sprintf("Priority for channel %d", index); return &s }(),
						},
					},
				},
			}

			_, err := suite.processor.ProcessTask(ctx, createTask)
			results <- err
		}(i)
	}

	// Wait for all operations to complete
	var errors []error
	for i := 0; i < numChannels; i++ {
		if err := <-results; err != nil {
			if !containsFlowyeError(err.Error()) {
				errors = append(errors, err)
			}
		}
	}

	duration := time.Since(start)
	t.Logf("Created %d channels in %v", numChannels, duration)

	// If floweye is available, we should have no errors
	if len(errors) > 0 && !containsFlowyeError(errors[0].Error()) {
		t.Errorf("Performance test failed with errors: %v", errors)
	}
}

// containsFlowyeError checks if the error is related to floweye command availability
func containsFlowyeError(errStr string) bool {
	flowyeErrors := []string{
		"executable file not found",
		"command not found",
		"no such file",
		"floweye",
		"failed to execute",
	}

	for _, errPattern := range flowyeErrors {
		if contains(errStr, errPattern) {
			return true
		}
	}
	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 || 
		(len(s) > len(substr) && containsHelper(s, substr)))
}

func containsHelper(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
