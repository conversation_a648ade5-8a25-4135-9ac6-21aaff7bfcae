#!/bin/bash

# Route Policy 测试文件验证脚本
# 验证所有测试文件的JSON格式和内容正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_FILES=0
VALID_FILES=0
INVALID_FILES=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 验证JSON格式
validate_json() {
    local file=$1
    if command -v jq &> /dev/null; then
        if jq empty "$file" 2>/dev/null; then
            return 0
        else
            return 1
        fi
    else
        # 如果没有jq，使用python验证
        if python3 -c "import json; json.load(open('$file'))" 2>/dev/null; then
            return 0
        else
            return 1
        fi
    fi
}

# 验证必需字段
validate_required_fields() {
    local file=$1
    local content=$(cat "$file")
    
    # 检查是否有tx_id
    if ! echo "$content" | grep -q '"tx_id"'; then
        echo "缺少tx_id字段"
        return 1
    fi
    
    # 检查是否有device_tasks
    if ! echo "$content" | grep -q '"device_tasks"'; then
        echo "缺少device_tasks字段"
        return 1
    fi
    
    # 检查是否有task_type
    if ! echo "$content" | grep -q '"task_type": "TASK_ROUTE_POLICY"'; then
        echo "缺少或错误的task_type字段"
        return 1
    fi
    
    # 检查是否有task_action
    if ! echo "$content" | grep -q '"task_action"'; then
        echo "缺少task_action字段"
        return 1
    fi
    
    # 检查是否有route_policy_task
    if ! echo "$content" | grep -q '"route_policy_task"'; then
        echo "缺少route_policy_task字段"
        return 1
    fi
    
    return 0
}

# 验证cookie字段
validate_cookie() {
    local file=$1
    local content=$(cat "$file")
    
    # 对于非DELETE操作，检查cookie字段
    if echo "$content" | grep -q '"task_action": "NEW_CONFIG"' || echo "$content" | grep -q '"task_action": "EDIT_CONFIG"'; then
        if ! echo "$content" | grep -q '"cookie"'; then
            echo "NEW_CONFIG或EDIT_CONFIG操作缺少cookie字段"
            return 1
        fi
    fi
    
    return 0
}

# 验证action和config一致性
validate_action_config_consistency() {
    local file=$1
    local content=$(cat "$file")
    
    # 检查ROUTE_ACTION_NAT是否有nat_config
    if echo "$content" | grep -q '"action": "ROUTE_ACTION_NAT"'; then
        if ! echo "$content" | grep -q '"nat_config"'; then
            echo "ROUTE_ACTION_NAT缺少nat_config"
            return 1
        fi
    fi
    
    # 检查ROUTE_ACTION_DNAT是否有nat_config
    if echo "$content" | grep -q '"action": "ROUTE_ACTION_DNAT"'; then
        if ! echo "$content" | grep -q '"nat_config"'; then
            echo "ROUTE_ACTION_DNAT缺少nat_config"
            return 1
        fi
    fi
    
    # 检查所有action是否都有route_config（除了错误测试文件）
    if echo "$content" | grep -q '"action": "ROUTE_ACTION_'; then
        if ! echo "$content" | grep -q '"route_config"' && ! echo "$file" | grep -q "error"; then
            echo "有action但缺少route_config（非错误测试文件）"
            return 1
        fi
    fi
    
    return 0
}

# 主验证函数
validate_file() {
    local file=$1
    local filename=$(basename "$file")
    
    TOTAL_FILES=$((TOTAL_FILES + 1))
    
    echo "验证文件: $filename"
    
    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        print_error "文件不存在: $file"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
    
    # 验证JSON格式
    if ! validate_json "$file"; then
        print_error "$filename: JSON格式无效"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
    
    # 验证必需字段
    local field_error=$(validate_required_fields "$file" 2>&1)
    if [ $? -ne 0 ]; then
        print_error "$filename: $field_error"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
    
    # 验证cookie字段
    local cookie_error=$(validate_cookie "$file" 2>&1)
    if [ $? -ne 0 ]; then
        print_error "$filename: $cookie_error"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
    
    # 验证action和config一致性
    local consistency_error=$(validate_action_config_consistency "$file" 2>&1)
    if [ $? -ne 0 ]; then
        print_error "$filename: $consistency_error"
        INVALID_FILES=$((INVALID_FILES + 1))
        return 1
    fi
    
    print_success "$filename: 验证通过"
    VALID_FILES=$((VALID_FILES + 1))
    return 0
}

# 主函数
main() {
    print_header "Route Policy 测试文件验证"
    
    # 获取所有route policy测试文件
    local test_files=(test_route_policy_*.json)
    
    if [ ${#test_files[@]} -eq 0 ] || [ ! -f "${test_files[0]}" ]; then
        print_error "未找到route policy测试文件"
        exit 1
    fi
    
    # 验证每个文件
    for file in "${test_files[@]}"; do
        validate_file "$file"
        echo ""
    done
    
    # 统计结果
    print_header "验证结果统计"
    echo "总文件数: $TOTAL_FILES"
    echo "有效文件: $VALID_FILES"
    echo "无效文件: $INVALID_FILES"
    
    if [ $INVALID_FILES -eq 0 ]; then
        print_success "所有测试文件验证通过！"
        exit 0
    else
        print_error "有 $INVALID_FILES 个文件验证失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
