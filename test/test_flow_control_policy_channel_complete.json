[{"tx_id": "flow-control-policy-channel-complete-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 40001, "desc": "channel_policy_complete_config", "group_name": "flow_control_test_group1", "disable": false, "action": "FLOW_CONTROL_ACTION_CHANNEL", "action_channel": {"next": true, "channel": "testchannel1", "pri": 3, "ip_rate": 2000, "so_id": "test_stat_channel"}, "in_ip": [{"ip": {"ipv4": 3232261120, "prefix_length": 24}}, {"ip_range": {"start_ip": {"ipv4": 167772161}, "end_ip": {"ipv4": 167772260}}}], "out_ip": [{"ip": {"ipv4": 3232235520, "prefix_length": 24}}], "in_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}, {"start": 8080, "end": 8090}]}, "out_port": {"ports": [{"start": 80, "end": 80}, {"start": 443, "end": 443}]}, "app": {"app_name": "http", "app_protocol": "tcp"}, "interface": {"bridge": "any", "dir": "both", "if_name": "any", "in_if": "any", "vlan": {"start": 0, "end": 0}}}}}]}]