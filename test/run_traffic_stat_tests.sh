#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 测试目录
TEST_DIR=$(dirname "$0")
cd "$TEST_DIR" || exit 1

# 日志文件
LOG_FILE="traffic_stat_test_results.log"
echo "流量统计模块测试开始时间: $(date)" > $LOG_FILE

# 函数: 打印带颜色的消息并记录到日志
log_message() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
  echo "$message" >> $LOG_FILE
}

# 函数: 执行测试并验证结果
run_test() {
  local test_name=$1
  local test_file=$2
  local verify_cmd=$3
  local expected_result=$4
  local success_pattern=${5:-"success"}

  log_message "$BLUE" "===== 执行测试: $test_name ====="
  log_message "$YELLOW" "使用配置文件: $test_file"

  # 执行agent-debug-client
  log_message "$YELLOW" "执行agent-debug-client..."
  result=$(../agent-debug-client --config=$test_file)
  echo "$result" >> $LOG_FILE

  # 检查执行结果是否成功（不区分大小写）
  if echo "$result" | grep -qi "$success_pattern"; then
    log_message "$GREEN" "agent-debug-client执行成功"

    # 执行验证命令
    log_message "$YELLOW" "验证配置: $verify_cmd"
    verify_output=$(eval $verify_cmd)
    echo "验证命令输出:" >> $LOG_FILE
    echo "$verify_output" >> $LOG_FILE

    # 检查验证结果
    if [ -z "$expected_result" ] || echo "$verify_output" | grep -q "$expected_result"; then
      log_message "$GREEN" "✅ 验证成功: 找到预期结果 '$expected_result'"
      PASSED_TESTS=$((PASSED_TESTS+1))
    else
      log_message "$RED" "❌ 验证失败: 未找到预期结果 '$expected_result'"
      FAILED_TESTS=$((FAILED_TESTS+1))
    fi
  else
    if [[ "$test_name" == *"错误处理"* ]]; then
      # 对于错误处理测试，预期会失败
      log_message "$GREEN" "✅ 错误处理测试成功捕获错误"
      PASSED_TESTS=$((PASSED_TESTS+1))
    else
      log_message "$RED" "❌ agent-debug-client执行失败"
      FAILED_TESTS=$((FAILED_TESTS+1))
    fi
  fi

  # 给系统一些时间处理配置
  sleep 1

  echo "" >> $LOG_FILE
  TOTAL_TESTS=$((TOTAL_TESTS+1))
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 主测试流程
log_message "$CYAN" "=========================================="
log_message "$CYAN" "     流量统计模块集成测试脚本           "
log_message "$CYAN" "=========================================="

# 启动调试服务器
log_message "$YELLOW" "启动调试服务器..."
../agent debug start
sleep 2

# ===== 流量统计模块测试 =====
log_message "$BLUE" "===== 流量统计模块测试 ====="

# 测试1: 新建流量统计配置 - trackip=true
run_test "流量统计 - 新建配置(trackip=true)" "traffic_stat_new_config.json" "floweye ntmso list | grep wan_stats" "wan_stats"

# 测试2: 编辑流量统计配置 - 修改trackip设置
run_test "流量统计 - 编辑配置(修改trackip)" "traffic_stat_edit_config.json" "floweye ntmso get id=\$(floweye ntmso list | grep wan_stats | grep -o '\"id\":[0-9]*' | cut -d: -f2) | grep trackip" "trackip=0"

# 测试3: 新建流量统计配置 - trackip=false
run_test "流量统计 - 新建配置(trackip=false)" "traffic_stat_trackip_false_config.json" "floweye ntmso list | grep no_track_stats" "no_track_stats"

# 测试4: 批量创建流量统计配置
run_test "流量统计 - 批量创建配置" "traffic_stat_batch_config.json" "floweye ntmso list | grep -E 'lan_stats|user_stats|guest_stats'" "lan_stats"

# 测试5: 流量统计配置一致性测试
run_test "流量统计 - 配置一致性" "traffic_stat_consistency_test.json" "floweye ntmso list | grep consistency_test" "consistency_test" "unchanged"

# 测试6: 流量统计幂等性测试 - 重复应用相同配置
run_test "流量统计 - 幂等性测试" "traffic_stat_idempotent_test.json" "floweye ntmso list | grep idempotent_test" "idempotent_test"

# 测试7: 流量统计幂等性验证 - 再次应用相同配置
run_test "流量统计 - 幂等性验证" "traffic_stat_idempotent_test.json" "floweye ntmso list | grep idempotent_test" "idempotent_test" "unchanged"

# 测试8: 流量统计全量同步测试
run_test "流量统计 - 全量同步" "traffic_stat_full_sync_test.json" "floweye ntmso list | grep -E 'sync_wan_stats|sync_lan_stats'" "sync_wan_stats"

# 测试9: 流量统计错误处理 - 缺少名称
run_test "流量统计 - 错误处理 - 缺少名称" "traffic_stat_error_test.json" "echo 'Expected to fail'" "" "name is required"

# 测试10: 删除流量统计配置
run_test "流量统计 - 删除配置" "traffic_stat_delete_config.json" "floweye ntmso list | grep wan_stats || echo '流量统计不存在'" "流量统计不存在"

# 清理测试环境
log_message "$YELLOW" "清理流量统计测试环境..."
../agent-debug-client --config=traffic_stat_cleanup_config.json 2>/dev/null || true

# 手动清理可能遗留的流量统计配置
for stat_name in wan_stats no_track_stats lan_stats user_stats guest_stats consistency_test idempotent_test sync_wan_stats sync_lan_stats; do
  stat_id=$(floweye ntmso list 2>/dev/null | grep "$stat_name" | grep -o '"id":[0-9]*' | cut -d: -f2 2>/dev/null || echo "")
  if [ -n "$stat_id" ]; then
    log_message "$YELLOW" "清理流量统计: $stat_name (ID: $stat_id)"
    floweye ntmso remove id=$stat_id 2>/dev/null || true
  fi
done

# 停止调试服务器
log_message "$YELLOW" "停止调试服务器..."
../agent debug stop

# 打印测试结果摘要
log_message "$BLUE" "===== 流量统计模块测试结果摘要 ====="
log_message "$YELLOW" "总测试数: $TOTAL_TESTS"
log_message "$GREEN" "通过测试数: $PASSED_TESTS"
log_message "$RED" "失败测试数: $FAILED_TESTS"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
  log_message "$GREEN" "所有流量统计测试通过! 🎉"
  exit 0
else
  log_message "$RED" "有测试失败，请查看日志文件 $LOG_FILE 获取详细信息"
  exit 1
fi

echo "流量统计模块测试结束时间: $(date)" >> $LOG_FILE
