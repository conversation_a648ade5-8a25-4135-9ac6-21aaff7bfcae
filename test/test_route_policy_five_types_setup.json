[{"tx_id": "route-policy-five-types-setup-001", "device_tasks": [{"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10051, "desc": "完整配置策略1-路由动作", "previous": 0, "disable": false, "zone": "CUST_TIER_T2", "sch_time": 1, "src": [{"ip_addr": {"ip_string": "***********", "prefix_len": 24}}], "src_port": {"port_list": [80, 443]}, "usr_type": "USER_TYPE_IPPXY", "pool": 100, "dst": [{"ip_addr": {"ip_string": "*******", "prefix_len": 32}}], "dst_port": {"port_list": [53, 80]}, "proto": {"app_name": "any", "app_protocol": "tcp"}, "app": "http", "in_if": "eth1", "wan_bw": 1000, "wan_bw_out": 2000, "vlan": {"start": 100, "end": 200}, "ttl": {"start": 64, "end": 128}, "dscp": {"start": 0, "end": 63}, "action": "ROUTE_ACTION_ROUTE", "route_config": {"proxy": "wan", "next_hop": {"ip_string": "***********"}}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10052, "desc": "基础配置策略1-NAT动作", "previous": 10051, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_NAT", "route_config": {"proxy": "wan"}, "nat_config": {"nat_ip": {"ip_ranges": [{"start_ip": {"ip_string": "**************"}, "end_ip": {"ip_string": "**************"}}]}}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10053, "desc": "完整配置策略2-DNAT动作", "previous": 10052, "disable": false, "zone": "CUST_TIER_T2", "sch_time": 2, "src": [{"ip_addr": {"ip_string": "10.0.0.0", "prefix_len": 8}}], "usr_type": "USER_TYPE_NONIPPXY", "dst": [{"ip_addr": {"ip_string": "*******", "prefix_len": 32}}], "proto": {"app_name": "any", "app_protocol": "udp"}, "app": "dns", "action": "ROUTE_ACTION_DNAT", "route_config": {"proxy": "wan2"}, "nat_config": {"new_dst_ip": {"ip_string": "**************"}, "no_snat": true}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10054, "desc": "基础配置策略2-代播动作", "previous": 10053, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_PROXY", "route_config": {"proxy": "wan3"}}}, {"task_type": "TASK_ROUTE_POLICY", "task_action": "NEW_CONFIG", "route_policy_task": {"cookie": 10055, "desc": "禁用策略-完整配置", "previous": 10054, "disable": true, "zone": "CUST_TIER_T2", "sch_time": 3, "src": [{"ip_range": {"start_ip": {"ip_string": "**********"}, "end_ip": {"ip_string": "**************"}}}], "dst": [{"ip_addr": {"ip_string": "0.0.0.0", "prefix_len": 0}}], "proto": {"app_name": "any", "app_protocol": "any"}, "app": "any", "action": "ROUTE_ACTION_ROUTE", "route_config": {"proxy": "wan4"}}}]}]