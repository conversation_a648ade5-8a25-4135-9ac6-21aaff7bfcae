[{"tx_id": "flow-control-policy-order-setup-001", "device_tasks": [{"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 10001, "desc": "order_test_policy_1", "group_name": "flow_control_test_group1", "disable": false, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": false, "ip_rate": 0}, "in_ip": [{"ip": {"ipv4": 3232235776, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 10002, "desc": "order_test_policy_2", "group_name": "flow_control_test_group1", "disable": false, "previous": 10001, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": false, "ip_rate": 0}, "in_ip": [{"ip": {"ipv4": 3232236032, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}, {"task_type": "TASK_FLOW_CONTROL", "task_action": "NEW_CONFIG", "flow_control_task": {"policy": {"cookie": 10003, "desc": "order_test_policy_3", "group_name": "flow_control_test_group1", "disable": false, "previous": 10002, "action": "FLOW_CONTROL_ACTION_PERMIT", "action_accept": {"next": false, "ip_rate": 0}, "in_ip": [{"ip": {"ipv4": 3232236288, "prefix_length": 24}}], "out_ip": [{"ip": {"ipv4": 0, "prefix_length": 0}}]}}}]}]