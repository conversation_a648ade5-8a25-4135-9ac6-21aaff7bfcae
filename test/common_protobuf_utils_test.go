/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      common_protobuf_utils_test.go
 *
 * DESCRIPTION :    Unit tests for common protobuf utilities
 *
 * AUTHOR :         AI Assistant
 *
 * HISTORY :        01/20/2025  create
 ******************************************************************************/

package test

import (
	"testing"

	"agent/internal/client/task"
	"agent/internal/logger"
	pb "agent/internal/pb"
	"github.com/stretchr/testify/assert"
)

func TestConvertIpAddressToString(t *testing.T) {
	tests := []struct {
		name     string
		ip       *pb.IpAddress
		expected string
		wantErr  bool
	}{
		{
			name: "IPv4 address",
			ip: &pb.IpAddress{
				Ip: &pb.IpAddress_Ipv4{
					Ipv4: 0xC0A80101, // ***********
				},
			},
			expected: "***********",
			wantErr:  false,
		},
		{
			name: "IP string",
			ip: &pb.IpAddress{
				Ip: &pb.IpAddress_IpString{
					IpString: "********",
				},
			},
			expected: "********",
			wantErr:  false,
		},
		{
			name: "IPv4 CIDR",
			ip: &pb.IpAddress{
				Ip: &pb.IpAddress_V4Cidr{
					V4Cidr: &pb.V4Cidr{
						Ip:           0xC0A80100, // ***********
						PrefixLength: 24,
					},
				},
			},
			expected: "***********/24",
			wantErr:  false,
		},
		{
			name: "IPv6 address",
			ip: &pb.IpAddress{
				Ip: &pb.IpAddress_Ipv6{
					Ipv6: []byte{0x20, 0x01, 0x0d, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01},
				},
			},
			expected: "2001:0db8:0000:0000:0000:0000:0000:0001",
			wantErr:  false,
		},
		{
			name:     "nil IP address",
			ip:       nil,
			expected: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := task.ConvertIpAddressToString(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestBuildAddressSelectorsString(t *testing.T) {
	config := logger.LogConfig{
		Level:  logger.LevelDebug,
		Format: logger.FormatText,
		Outputs: []logger.Output{
			{Type: logger.TypeConsole},
		},
	}
	log, _ := logger.NewLogger(config)

	// Mock resolver for testing
	mockResolver := &MockGroupResolver{}

	tests := []struct {
		name      string
		selectors []*pb.AddressSelector
		expected  string
		wantErr   bool
	}{
		{
			name:      "empty selectors",
			selectors: []*pb.AddressSelector{},
			expected:  "any",
			wantErr:   false,
		},
		{
			name: "single IP address",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_Ip{
						Ip: &pb.IpAddress{
							Ip: &pb.IpAddress_IpString{
								IpString: "***********",
							},
						},
					},
				},
			},
			expected: ",***********,",
			wantErr:  false,
		},
		{
			name: "IP range",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_IpRange{
						IpRange: &pb.IpRange{
							StartIp: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{
									IpString: "***********",
								},
							},
							EndIp: &pb.IpAddress{
								Ip: &pb.IpAddress_IpString{
									IpString: "***********00",
								},
							},
						},
					},
				},
			},
			expected: ",***********-***********00,",
			wantErr:  false,
		},
		{
			name: "IP group name",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_IpGroupName{
						IpGroupName: "test_group",
					},
				},
			},
			expected: ",123,", // Mock resolver returns 123
			wantErr:  false,
		},
		{
			name: "MAC group ID",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_MacGroupId{
						MacGroupId: 456,
					},
				},
			},
			expected: ",mac.456,",
			wantErr:  false,
		},
		{
			name: "username",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_Username{
						Username: "testuser",
					},
				},
			},
			expected: ",acct.testuser,",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := task.BuildAddressSelectorsString(tt.selectors, mockResolver, log)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestBuildIntRangeString(t *testing.T) {
	tests := []struct {
		name      string
		rangeSpec *pb.IntRange
		expected  string
	}{
		{
			name:      "nil range",
			rangeSpec: nil,
			expected:  "0",
		},
		{
			name: "single value",
			rangeSpec: &pb.IntRange{
				Start: 100,
				End:   100,
			},
			expected: "100",
		},
		{
			name: "range",
			rangeSpec: &pb.IntRange{
				Start: 100,
				End:   200,
			},
			expected: "100-200",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := task.BuildIntRangeString(tt.rangeSpec)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidateAddressSelectors(t *testing.T) {
	tests := []struct {
		name      string
		selectors []*pb.AddressSelector
		wantErr   bool
	}{
		{
			name:      "empty selectors",
			selectors: []*pb.AddressSelector{},
			wantErr:   false,
		},
		{
			name: "valid IP address",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_Ip{
						Ip: &pb.IpAddress{
							Ip: &pb.IpAddress_IpString{
								IpString: "***********",
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "nil selector",
			selectors: []*pb.AddressSelector{
				nil,
			},
			wantErr: true,
		},
		{
			name: "empty IP group name",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_IpGroupName{
						IpGroupName: "",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "invalid MAC group ID",
			selectors: []*pb.AddressSelector{
				{
					Selector: &pb.AddressSelector_MacGroupId{
						MacGroupId: -1,
					},
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := task.ValidateAddressSelectors(tt.selectors)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// MockGroupResolver for testing
type MockGroupResolver struct{}

func (m *MockGroupResolver) ResolveIPGroupNameToID(groupName string) (int, error) {
	// Return a fixed ID for testing
	return 123, nil
}

func (m *MockGroupResolver) ResolveDomainGroupNameToID(groupName string) (int, error) {
	// Return a fixed ID for testing
	return 456, nil
}
