#!/bin/bash

# iWAN Proxy测试用例验证脚本
# 验证测试文件的完整性和正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

check_file_exists() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        print_success "$description 存在"
        return 0
    else
        print_error "$description 不存在: $file"
        return 1
    fi
}

check_json_validity() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if python3 -m json.tool "$file" > /dev/null 2>&1; then
        print_success "$description JSON格式正确"
        return 0
    else
        print_error "$description JSON格式错误: $file"
        return 1
    fi
}

check_required_fields() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # 检查必需字段
    if grep -q '"task_type": "TASK_IWAN_PROXY"' "$file" && \
       grep -q '"iwan_proxy_task"' "$file"; then
        print_success "$description 包含必需字段"
        return 0
    else
        print_error "$description 缺少必需字段: $file"
        return 1
    fi
}

main() {
    print_header "iWAN Proxy测试用例验证开始"
    
    # 检查主测试脚本
    print_header "检查主测试脚本"
    check_file_exists "iwan_proxy_comprehensive_test.sh" "主测试脚本"
    
    if [ -f "iwan_proxy_comprehensive_test.sh" ]; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if [ -x "iwan_proxy_comprehensive_test.sh" ]; then
            print_success "主测试脚本具有执行权限"
        else
            print_error "主测试脚本缺少执行权限"
        fi
        
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if bash -n "iwan_proxy_comprehensive_test.sh"; then
            print_success "主测试脚本语法正确"
        else
            print_error "主测试脚本语法错误"
        fi
    fi
    
    # 检查基础CRUD操作测试用例
    print_header "检查基础CRUD操作测试用例"
    local crud_tests=(
        "test_iwan_proxy_basic_new.json:基础创建测试"
        "test_iwan_proxy_idempotent.json:幂等性测试"
        "test_iwan_proxy_modify_mtu.json:MTU修改测试"
        "test_iwan_proxy_modify_server.json:服务器修改测试"
        "test_iwan_proxy_modify_credentials.json:凭据修改测试"
        "test_iwan_proxy_delete.json:删除测试"
        "test_iwan_proxy_delete_idempotent.json:删除幂等性测试"
    )
    
    for test_info in "${crud_tests[@]}"; do
        IFS=':' read -r file desc <<< "$test_info"
        check_file_exists "$file" "$desc"
        if [ -f "$file" ]; then
            check_json_validity "$file" "$desc"
            check_required_fields "$file" "$desc"
        fi
    done
    
    # 检查完整配置测试用例
    print_header "检查完整配置测试用例"
    local complete_tests=(
        "test_iwan_proxy_complete_config.json:完整配置创建测试"
        "test_iwan_proxy_complete_modify.json:完整配置修改测试"
    )
    
    for test_info in "${complete_tests[@]}"; do
        IFS=':' read -r file desc <<< "$test_info"
        check_file_exists "$file" "$desc"
        if [ -f "$file" ]; then
            check_json_validity "$file" "$desc"
            check_required_fields "$file" "$desc"
        fi
    done
    
    # 检查全量同步测试用例
    print_header "检查全量同步测试用例"
    local fullsync_tests=(
        "test_iwan_proxy_full_sync_setup.json:全量同步设置测试"
        "test_iwan_proxy_full_sync_cleanup.json:全量同步清理测试"
    )
    
    for test_info in "${fullsync_tests[@]}"; do
        IFS=':' read -r file desc <<< "$test_info"
        check_file_exists "$file" "$desc"
        if [ -f "$file" ]; then
            check_json_validity "$file" "$desc"
            check_required_fields "$file" "$desc"
        fi
    done
    
    # 检查可选字段默认值恢复测试用例
    print_header "检查可选字段默认值恢复测试用例"
    local optional_tests=(
        "test_iwan_proxy_optional_fields_complete.json:可选字段完整配置测试"
        "test_iwan_proxy_optional_fields_default.json:可选字段默认值测试"
    )
    
    for test_info in "${optional_tests[@]}"; do
        IFS=':' read -r file desc <<< "$test_info"
        check_file_exists "$file" "$desc"
        if [ -f "$file" ]; then
            check_json_validity "$file" "$desc"
            check_required_fields "$file" "$desc"
        fi
    done
    
    # 检查错误处理测试用例
    print_header "检查错误处理测试用例"
    local error_tests=(
        "test_iwan_proxy_error_no_name.json:缺少name字段错误测试"
        "test_iwan_proxy_error_no_ifname.json:缺少ifname字段错误测试"
        "test_iwan_proxy_error_invalid_mtu.json:无效MTU错误测试"
        "test_iwan_proxy_error_invalid_port.json:无效端口错误测试"
        "test_iwan_proxy_error_nonexistent_wan.json:不存在WAN错误测试"
    )
    
    for test_info in "${error_tests[@]}"; do
        IFS=':' read -r file desc <<< "$test_info"
        check_file_exists "$file" "$desc"
        if [ -f "$file" ]; then
            check_json_validity "$file" "$desc"
            check_required_fields "$file" "$desc"
        fi
    done
    
    # 检查依赖关系测试用例
    print_header "检查依赖关系测试用例"
    check_file_exists "test_iwan_proxy_dependency_test.json" "依赖关系测试"
    if [ -f "test_iwan_proxy_dependency_test.json" ]; then
        check_json_validity "test_iwan_proxy_dependency_test.json" "依赖关系测试"
        check_required_fields "test_iwan_proxy_dependency_test.json" "依赖关系测试"
    fi
    
    # 检查文档
    print_header "检查文档"
    check_file_exists "IWAN_PROXY_TEST_SUMMARY.md" "测试总结文档"
    
    # 统计结果
    print_header "验证结果统计"
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过检查: $PASSED_CHECKS"
    echo "失败检查: $FAILED_CHECKS"
    echo "成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "所有验证通过！iWAN Proxy测试用例设置完整"
        echo ""
        echo "可以使用以下命令执行测试："
        echo "  make test-iwan-proxy"
        echo "  VERBOSE=true make test-iwan-proxy"
        exit 0
    else
        print_error "有 $FAILED_CHECKS 个验证失败，请检查并修复"
        exit 1
    fi
}

# 执行主函数
main "$@"
