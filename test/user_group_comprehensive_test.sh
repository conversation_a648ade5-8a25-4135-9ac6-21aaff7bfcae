#!/bin/bash

# User Group模块综合测试脚本
# 测试所有user group模块的核心功能和边界条件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="user_group_test_results.log"
echo "User Group模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    if ../agent-debug-client --config=$test_file >> $LOG_FILE 2>&1; then
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证用户组配置
verify_user_group() {
    local group_id=$1
    local expected_name=$2
    local expected_pid=$3
    local expected_start_ip=${4:-""}
    local expected_end_ip=${5:-""}
    
    echo "验证用户组 ID=$group_id 配置..." >> $LOG_FILE
    local output=$(floweye pppoeippool get id=$group_id 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取用户组 ID=$group_id 配置"
        return 1
    fi
    
    # 验证name
    if echo "$output" | grep -q "name=$expected_name"; then
        print_success "用户组 ID=$group_id name=$expected_name 验证通过"
    else
        print_error "用户组 ID=$group_id name 验证失败，期望: $expected_name"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证pid
    if echo "$output" | grep -q "pid=$expected_pid"; then
        print_success "用户组 ID=$group_id pid=$expected_pid 验证通过"
    else
        print_error "用户组 ID=$group_id pid 验证失败，期望: $expected_pid"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证start IP（如果指定）
    if [ -n "$expected_start_ip" ]; then
        if echo "$output" | grep -q "start=$expected_start_ip"; then
            print_success "用户组 ID=$group_id start=$expected_start_ip 验证通过"
        else
            print_error "用户组 ID=$group_id start 验证失败，期望: $expected_start_ip"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
    
    # 验证end IP（如果指定）
    if [ -n "$expected_end_ip" ]; then
        if echo "$output" | grep -q "end=$expected_end_ip"; then
            print_success "用户组 ID=$group_id end=$expected_end_ip 验证通过"
        else
            print_error "用户组 ID=$group_id end 验证失败，期望: $expected_end_ip"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
    
    return 0
}

# 验证用户组不存在
verify_user_group_not_exists() {
    local group_id=$1
    
    echo "验证用户组 ID=$group_id 不存在..." >> $LOG_FILE
    local output=$(floweye pppoeippool get id=$group_id 2>&1)
    
    if [ $? -ne 0 ] || echo "$output" | grep -q "NEXIST"; then
        print_success "用户组 ID=$group_id 确认不存在"
        return 0
    else
        print_error "用户组 ID=$group_id 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证用户组可选字段默认值
verify_user_group_optional_defaults() {
    local group_id=$1
    
    echo "验证用户组 ID=$group_id 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye pppoeippool get id=$group_id 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取用户组 ID=$group_id 配置"
        return 1
    fi
    
    # 验证start IP默认值为0.0.0.0
    if echo "$output" | grep -q "start=0.0.0.0"; then
        print_success "用户组 ID=$group_id start IP默认值验证通过 (0.0.0.0)"
    else
        print_error "用户组 ID=$group_id start IP默认值验证失败，期望: 0.0.0.0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证end IP默认值为0.0.0.0
    if echo "$output" | grep -q "end=0.0.0.0"; then
        print_success "用户组 ID=$group_id end IP默认值验证通过 (0.0.0.0)"
    else
        print_error "用户组 ID=$group_id end IP默认值验证失败，期望: 0.0.0.0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证带宽限制默认值为0
    if echo "$output" | grep -q "ratein=0" && echo "$output" | grep -q "rateout=0"; then
        print_success "用户组 ID=$group_id 带宽限制默认值验证通过 (0)"
    else
        print_error "用户组 ID=$group_id 带宽限制默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证过期账号处理默认值为reject
    if echo "$output" | grep -q "clntepa=reject"; then
        print_success "用户组 ID=$group_id 过期账号处理默认值验证通过 (reject)"
    else
        print_error "用户组 ID=$group_id 过期账号处理默认值验证失败，期望: reject"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 清理用户组配置
cleanup_user_group() {
    local group_id=$1
    echo "清理用户组 ID=$group_id 配置..." >> $LOG_FILE
    floweye pppoeippool remove id=$group_id >> $LOG_FILE 2>&1 || true
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有测试用户组
cleanup_all_test_user_groups() {
    echo "清理所有测试用户组..." >> $LOG_FILE

    # 清理测试用的用户组（ID 100-200范围）
    for id in {100..200}; do
        cleanup_user_group $id
    done

    print_success "测试用户组清理完成"
}

# 主测试流程
main() {
    print_header "User Group模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 获取初始用户组状态
    print_header "获取初始用户组状态"
    echo "初始用户组状态:" >> $LOG_FILE
    floweye pppoeippool list >> $LOG_FILE 2>&1

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_test_user_groups

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础用户组
    run_test "test_user_group_basic_new.json" "创建基础用户组"
    verify_user_group "100" "test_group_basic" "1"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_user_group_idempotent.json" "幂等性测试-重复创建"
    verify_user_group "100" "test_group_basic" "1"

    # 1.3 字段修改测试
    run_test "test_user_group_modify_name.json" "修改用户组名称"
    verify_user_group "100" "test_group_modified" "1"

    run_test "test_user_group_modify_ip_range.json" "修改IP地址范围"
    verify_user_group "100" "test_group_modified" "1" "*************" "*************00"

    # 1.4 删除配置测试
    run_test "test_user_group_delete.json" "删除用户组配置"
    verify_user_group_not_exists "100"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_user_group_delete_idempotent.json" "删除配置幂等性测试"
    verify_user_group_not_exists "100"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建包含所有字段的完整配置
    run_test "test_user_group_complete_config.json" "创建完整配置用户组"
    verify_user_group "101" "test_group_complete" "1" "*************" "***************"

    # 2.2 修改带宽限制
    run_test "test_user_group_modify_bandwidth.json" "修改带宽限制"
    verify_user_group "101" "test_group_complete" "1"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 设置初始配置（增量模式）
    run_test "test_user_group_full_sync_setup.json" "全量同步初始配置"
    verify_user_group "102" "test_group_sync1" "1"
    verify_user_group "103" "test_group_sync2" "1"
    verify_user_group "104" "test_group_sync3" "1"

    # 3.2 启动全量同步
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只包含需要保留的配置）
    run_test "test_user_group_full_sync_cleanup.json" "全量同步清理配置"
    verify_user_group "104" "test_group_sync3_updated" "1"

    # 3.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 3.5 验证清理结果：未在全量同步中的配置应被删除
    sleep 2  # 等待清理完成
    verify_user_group_not_exists "102"
    verify_user_group_not_exists "103"
    verify_user_group "104" "test_group_sync3_updated" "1"

    # 阶段4: 可选字段默认值恢复测试
    print_header "阶段4: 可选字段默认值恢复测试"

    # 4.1 创建包含所有可选字段的完整配置
    run_test "test_user_group_optional_fields_complete.json" "创建包含所有可选字段的完整配置"
    verify_user_group "105" "test_group_optional" "1" "192.168.105.1" "192.168.105.100"

    # 4.2 修改配置，移除所有可选字段，验证默认值恢复
    run_test "test_user_group_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_user_group "105" "test_group_optional_default" "1"
    verify_user_group_optional_defaults "105"

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 5.1 无效参数测试（这些应该失败）
    run_test "test_user_group_error_no_id.json" "缺少ID字段错误测试" "false"
    run_test "test_user_group_error_invalid_id.json" "无效ID范围错误测试" "false"
    run_test "test_user_group_error_no_name.json" "缺少name字段错误测试" "false"

    # 5.2 ID冲突测试
    run_test "test_user_group_id_conflict.json" "ID冲突错误测试" "false"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_test_user_groups

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "User Group模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
