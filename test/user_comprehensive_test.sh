#!/bin/bash

# User模块综合测试脚本
# 测试所有user模块的核心功能和边界条件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="user_test_results.log"
echo "User模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    local test_output=$(../agent-debug-client --config=$test_file 2>&1)
    local test_exit_code=$?
    echo "测试输出: $test_output" >> $LOG_FILE

    # 检查响应内容来判断是否真正成功
    # agent-debug-client 总是返回退出码0，需要检查响应中的 err_code
    local has_error=false
    if echo "$test_output" | grep -q '"err_code":[[:space:]]*[1-9]'; then
        has_error=true
    elif echo "$test_output" | grep -q 'Task failed:'; then
        has_error=true
    fi

    # 根据响应内容判断实际结果
    if [ "$has_error" = "false" ]; then
        # 实际成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            print_error "测试输出: $test_output"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 实际失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            print_error "退出码: $test_exit_code"
            print_error "测试输出: $test_output"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证用户配置
verify_user() {
    local username=$1
    local expected_pool_id=$2
    local expected_enabled=$3
    local expected_max_online=${4:-""}
    local expected_bind_ip=${5:-""}
    
    echo "验证用户 $username 配置..." >> $LOG_FILE
    local output=$(floweye pppoeacct get name=$username 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取用户 $username 配置"
        return 1
    fi
    
    # 验证pool_id
    if echo "$output" | grep -q "poolid=$expected_pool_id"; then
        print_success "用户 $username poolid=$expected_pool_id 验证通过"
    else
        print_error "用户 $username poolid 验证失败，期望: $expected_pool_id"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证启用状态（通过list命令检查）
    local list_output=$(floweye pppoeacct list 2>/dev/null | grep " $username ")
    if [ $? -eq 0 ]; then
        # 检查禁用标志（第11个字段，0为启用，1为禁用）
        local disable_flag=$(echo "$list_output" | awk '{print $11}')
        if [ "$expected_enabled" = "true" ]; then
            if [ "$disable_flag" = "0" ]; then
                print_success "用户 $username 启用状态验证通过"
            else
                print_error "用户 $username 启用状态验证失败，期望启用但实际禁用"
                return 1
            fi
        else
            if [ "$disable_flag" = "1" ]; then
                print_success "用户 $username 禁用状态验证通过"
            else
                print_error "用户 $username 禁用状态验证失败，期望禁用但实际启用"
                return 1
            fi
        fi
    else
        print_error "无法在用户列表中找到用户 $username"
        return 1
    fi
    
    # 验证max_online（如果指定）
    if [ -n "$expected_max_online" ]; then
        if echo "$output" | grep -q "maxonline=$expected_max_online"; then
            print_success "用户 $username maxonline=$expected_max_online 验证通过"
        else
            print_error "用户 $username maxonline 验证失败，期望: $expected_max_online"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
    
    # 验证bind_ip（如果指定）
    if [ -n "$expected_bind_ip" ]; then
        if echo "$output" | grep -q "bindip=$expected_bind_ip"; then
            print_success "用户 $username bindip=$expected_bind_ip 验证通过"
        else
            print_error "用户 $username bindip 验证失败，期望: $expected_bind_ip"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
    
    return 0
}

# 验证用户不存在
verify_user_not_exists() {
    local username=$1
    
    echo "验证用户 $username 不存在..." >> $LOG_FILE
    local output=$(floweye pppoeacct get name=$username 2>&1)
    
    if [ $? -ne 0 ] || echo "$output" | grep -q "not found\|NEXIST"; then
        print_success "用户 $username 不存在验证通过"
        return 0
    else
        print_error "用户 $username 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证用户可选字段默认值
verify_user_optional_defaults() {
    local username=$1
    
    echo "验证用户 $username 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye pppoeacct get name=$username 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取用户 $username 配置"
        return 1
    fi
    
    # 验证maxonline默认值
    if echo "$output" | grep -q "maxonline=0"; then
        print_success "用户 $username maxonline默认值验证通过 (0)"
    else
        print_error "用户 $username maxonline默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证bindip默认值
    if echo "$output" | grep -q "bindip=0.0.0.0"; then
        print_success "用户 $username bindip默认值验证通过 (0.0.0.0)"
    else
        print_error "用户 $username bindip默认值验证失败，期望: 0.0.0.0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证bindmac默认值
    if echo "$output" | grep -q "bindmac=" || echo "$output" | grep -q "bindmac=00-00-00-00-00-00"; then
        print_success "用户 $username bindmac默认值验证通过 (空或00-00-00-00-00-00)"
    else
        print_error "用户 $username bindmac默认值验证失败"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证outvlan默认值
    if echo "$output" | grep -q "outvlan=0"; then
        print_success "用户 $username outvlan默认值验证通过 (0)"
    else
        print_error "用户 $username outvlan默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 清理用户配置
cleanup_user() {
    local username=$1
    echo "清理用户 $username 配置..." >> $LOG_FILE
    floweye pppoeacct remove name=$username >> $LOG_FILE 2>&1 || true
}

# 诊断用户组状态
diagnose_user_groups() {
    print_info "诊断用户组状态"
    echo "=== 用户组诊断信息 ===" >> $LOG_FILE

    # 检查用户组100
    local group100_status=$(floweye pppoeippool get id=100 2>&1)
    echo "用户组100状态: $group100_status" >> $LOG_FILE
    if echo "$group100_status" | grep -q "NEXIST"; then
        print_error "用户组100不存在"
    else
        print_success "用户组100存在"
    fi

    # 检查用户组101
    local group101_status=$(floweye pppoeippool get id=101 2>&1)
    echo "用户组101状态: $group101_status" >> $LOG_FILE
    if echo "$group101_status" | grep -q "NEXIST"; then
        print_error "用户组101不存在"
    else
        print_success "用户组101存在"
    fi

    # 列出所有用户组
    local all_groups=$(floweye pppoeippool list 2>&1)
    echo "所有用户组列表: $all_groups" >> $LOG_FILE
    print_info "用户组列表已记录到日志文件"
}

# 设置测试用户组
setup_test_user_group() {
    local group_id=$1
    local group_name=$2

    print_info "设置测试用户组 $group_name (ID: $group_id)"
    echo "设置测试用户组 $group_name (ID: $group_id)..." >> $LOG_FILE

    # 检查用户组是否已存在
    local output=$(floweye pppoeippool get id=$group_id 2>/dev/null)
    if [ $? -eq 0 ] && ! echo "$output" | grep -q "NEXIST"; then
        print_info "用户组 $group_id 已存在，跳过创建"
        return 0
    fi

    # 使用agent-debug-client创建测试用户组，确保与测试环境一致
    local temp_config="/tmp/setup_user_group_${group_id}.json"
    cat > "$temp_config" << EOF
[
  {
    "tx_id": "setup-user-group-${group_id}",
    "device_tasks": [
      {
        "task_type": "TASK_USER_GROUP",
        "task_action": "NEW_CONFIG",
        "user_group_task": {
          "id": ${group_id},
          "pid": 1,
          "name": "${group_name}"
        }
      }
    ]
  }
]
EOF

    # 执行用户组创建
    local create_output=$(../agent-debug-client --config="$temp_config" 2>&1)
    local exit_code=$?
    rm -f "$temp_config"

    if [ $exit_code -eq 0 ]; then
        print_success "测试用户组 $group_name 创建成功"
        echo "$create_output" >> $LOG_FILE
        return 0
    else
        print_error "测试用户组 $group_name 创建失败: $create_output"
        echo "$create_output" >> $LOG_FILE
        return 1
    fi
}

# 清理测试用户组
cleanup_test_user_group() {
    local group_id=$1
    echo "清理测试用户组 $group_id..." >> $LOG_FILE

    # 使用agent-debug-client删除用户组，确保与测试环境一致
    local temp_config="/tmp/cleanup_user_group_${group_id}.json"
    cat > "$temp_config" << EOF
[
  {
    "tx_id": "cleanup-user-group-${group_id}",
    "device_tasks": [
      {
        "task_type": "TASK_USER_GROUP",
        "task_action": "DELETE_CONFIG",
        "user_group_task": {
          "id": ${group_id}
        }
      }
    ]
  }
]
EOF

    # 执行用户组删除
    local delete_output=$(../agent-debug-client --config="$temp_config" 2>&1)
    rm -f "$temp_config"
    echo "$delete_output" >> $LOG_FILE
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "User模块综合测试开始"

    # 检查agent debug服务器是否运行
    print_header "检查Agent Debug服务器状态"
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        print_info "启动agent debug服务器..."
        local start_output=$(../agent debug start 2>&1)
        local start_exit_code=$?
        echo "Debug服务器启动输出: $start_output" >> $LOG_FILE

        if [ $start_exit_code -ne 0 ]; then
            print_warning "Debug服务器启动命令返回错误，检查是否已在运行"
        fi

        # 等待服务器启动
        sleep 3

        # 再次检查端口
        if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
            print_error "Debug服务器无法启动且端口8080未被监听"
            print_error "启动输出: $start_output"
            exit 1
        fi
        print_success "Debug服务器启动成功"
    else
        print_success "Debug服务器已经在运行中"
    fi

    # 测试debug服务器连接
    print_info "测试debug服务器连接..."
    local test_response=$(curl -s -m 5 http://localhost:8080/health 2>&1 || echo "connection_failed")
    if echo "$test_response" | grep -q "connection_failed\|Connection refused\|timeout"; then
        print_warning "Debug服务器健康检查失败，但端口已监听，继续测试"
        echo "健康检查响应: $test_response" >> $LOG_FILE
    else
        print_success "Debug服务器连接正常"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 设置测试用户组
    print_header "设置测试依赖 - 用户组"
    setup_test_user_group 100 "test_user_group_100" || {
        print_error "无法创建测试用户组100，测试无法继续"
        exit 1
    }
    setup_test_user_group 101 "test_user_group_101" || {
        print_error "无法创建测试用户组101，测试无法继续"
        exit 1
    }

    # 验证用户组创建成功
    print_info "验证用户组创建结果"
    local group100_check=$(floweye pppoeippool get id=100 2>/dev/null)
    local group100_exit_code=$?
    local group101_check=$(floweye pppoeippool get id=101 2>/dev/null)
    local group101_exit_code=$?

    if [ $group100_exit_code -eq 0 ] && ! echo "$group100_check" | grep -q "NEXIST" && echo "$group100_check" | grep -q "name=test_user_group_100"; then
        print_success "用户组100验证通过"
    else
        print_error "用户组100验证失败"
        echo "用户组100状态: $group100_check" >> $LOG_FILE
        exit 1
    fi

    if [ $group101_exit_code -eq 0 ] && ! echo "$group101_check" | grep -q "NEXIST" && echo "$group101_check" | grep -q "name=test_user_group_101"; then
        print_success "用户组101验证通过"
    else
        print_error "用户组101验证失败"
        echo "用户组101状态: $group101_check" >> $LOG_FILE
        exit 1
    fi

    # 获取初始用户状态
    print_header "获取初始用户状态"
    echo "初始用户状态:" >> $LOG_FILE
    floweye pppoeacct list >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础用户
    if ! run_test "test_user_basic_new.json" "创建基础用户"; then
        print_error "第一个测试失败，进行诊断"
        diagnose_user_groups
        exit 1
    fi
    verify_user "test_user_basic" "100" "true" "1"

    # 1.2 幂等性测试 - 重复创建相同用户
    run_test "test_user_idempotent_new.json" "幂等性测试-重复创建"
    verify_user "test_user_basic" "100" "true" "1"

    # 1.3 字段修改测试
    run_test "test_user_modify_password.json" "修改密码字段"
    verify_user "test_user_basic" "100" "true" "1"

    run_test "test_user_modify_max_online.json" "修改最大在线数字段"
    verify_user "test_user_basic" "100" "true" "5"

    run_test "test_user_modify_bind_ip.json" "修改绑定IP字段"
    verify_user "test_user_basic" "100" "true" "5" "*************"

    # 1.4 用户启用/禁用测试
    run_test "test_user_disable.json" "禁用用户测试"
    verify_user "test_user_basic" "100" "false" "5" "*************"

    run_test "test_user_enable.json" "启用用户测试"
    verify_user "test_user_basic" "100" "true" "5" "*************"

    # 1.5 删除用户测试
    run_test "test_user_delete.json" "删除用户测试"
    verify_user_not_exists "test_user_basic"

    # 1.6 删除不存在用户的幂等性测试
    run_test "test_user_delete_idempotent.json" "删除用户幂等性测试"
    verify_user_not_exists "test_user_basic"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建包含所有字段的完整用户配置
    run_test "test_user_complete_config.json" "创建完整用户配置"
    verify_user "test_user_complete" "101" "true" "10" "*************"

    # 2.2 修改用户组
    run_test "test_user_modify_pool_id.json" "修改用户组"
    verify_user "test_user_complete" "100" "true" "10" "*************"

    # 阶段3: 可选字段默认值恢复测试
    print_header "阶段3: 可选字段默认值恢复测试"

    # 3.1 修改配置，移除所有可选字段，验证默认值恢复
    run_test "test_user_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_user "test_user_complete" "100" "true"
    verify_user_optional_defaults "test_user_complete"

    # 清理当前用户
    cleanup_user "test_user_complete"

    # 阶段4: 全量同步测试
    print_header "阶段4: 全量同步测试"

    # 4.1 设置初始配置（增量模式）
    run_test "test_user_full_sync_setup.json" "全量同步初始配置"
    verify_user "test_user_sync1" "100" "true" "1"
    verify_user "test_user_sync2" "101" "true" "2"
    verify_user "test_user_sync3" "100" "false" "3"

    # 4.2 启动全量同步
    print_header "全量同步测试 - 启动全量同步"
    start_full_sync || exit 1

    # 4.3 发送全量同步配置（只包含需要保留的用户）
    run_test "test_user_full_sync_cleanup.json" "全量同步清理配置"
    verify_user "test_user_sync2" "101" "true" "5"

    # 4.4 结束全量同步，触发清理逻辑
    print_header "全量同步测试 - 结束全量同步"
    end_full_sync || exit 1

    # 4.5 验证清理结果：未在全量同步中的用户应被删除
    sleep 2  # 等待清理完成
    verify_user_not_exists "test_user_sync1"
    verify_user_not_exists "test_user_sync3"

    # 注意：由于全量同步中没有包含用户组配置，用户组101会被删除，
    # 导致test_user_sync2也被级联删除。这是正确的行为。
    print_info "注意：用户组101被删除，test_user_sync2被级联删除（这是正确的行为）"
    verify_user_not_exists "test_user_sync2"

    # 清理剩余用户
    cleanup_user "test_user_sync2"

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 5.1 无效参数测试（这些应该失败）
    run_test "test_user_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_user_error_no_pool_id.json" "缺少pool_id字段错误测试" "false"
    run_test "test_user_error_no_password.json" "缺少password字段错误测试" "false"
    run_test "test_user_error_invalid_pool_id.json" "无效pool_id错误测试" "false"

    # 5.2 日期格式测试
    run_test "test_user_error_invalid_date.json" "无效日期格式错误测试" "false"

    # 5.3 用户名长度测试
    run_test "test_user_error_long_name.json" "用户名过长错误测试" "false"

    # 阶段6: 用户组依赖关系测试
    print_header "阶段6: 用户组依赖关系测试"

    # 重新创建用户组101用于依赖测试（因为在全量同步测试中被删除了）
    print_info "重新创建用户组101用于依赖测试"
    setup_test_user_group 101 "test_user_group_101" || exit 1

    # 6.1 创建用户后删除用户组（应该失败或有特殊处理）
    run_test "test_user_dependency_create.json" "创建依赖用户"
    verify_user "test_user_dependency" "101" "true" "1"

    # 注意：根据文档，删除用户组时其下所有用户都会被删除
    print_info "测试用户组删除对用户的影响"
    cleanup_test_user_group 101

    # 验证用户是否被自动删除
    sleep 1
    verify_user_not_exists "test_user_dependency"

    # 重新创建用户组101用于后续清理
    setup_test_user_group 101 "test_user_group_101" || exit 1

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_test_user_group 100
    cleanup_test_user_group 101

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "User模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
