#!/bin/bash

# WAN模块综合测试脚本
# 执行完整的功能验证，包括全量同步测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="wan_comprehensive_test_results.log"

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    echo "执行命令: ../agent-debug-client --config=\"$test_file\"" >> $LOG_FILE
    if ../agent-debug-client --config="$test_file" >> $LOG_FILE 2>&1; then
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证WAN配置
verify_wan() {
    local wan_name=$1
    local expected_type=$2
    local expected_ifname=$3
    local expected_mtu=$4
    
    echo "验证WAN $wan_name 配置..." >> $LOG_FILE
    local output=$(floweye nat getproxy $wan_name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取WAN $wan_name 配置"
        return 1
    fi
    
    # 验证type
    if echo "$output" | grep -q "type=$expected_type"; then
        print_success "WAN $wan_name type=$expected_type 验证通过"
    else
        print_error "WAN $wan_name type 验证失败，期望: $expected_type"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证ifname
    if echo "$output" | grep -q "ifname=$expected_ifname"; then
        print_success "WAN $wan_name ifname=$expected_ifname 验证通过"
    else
        print_error "WAN $wan_name ifname 验证失败，期望: $expected_ifname"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证mtu
    if echo "$output" | grep -q "mtu=$expected_mtu"; then
        print_success "WAN $wan_name mtu=$expected_mtu 验证通过"
    else
        print_error "WAN $wan_name mtu 验证失败，期望: $expected_mtu"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 验证WAN不存在
verify_wan_not_exists() {
    local wan_name=$1
    
    echo "验证WAN $wan_name 不存在..." >> $LOG_FILE
    local output=$(floweye nat getproxy $wan_name 2>&1)
    
    if [ $? -ne 0 ] || echo "$output" | grep -q "NEXIST"; then
        print_success "WAN $wan_name 确认不存在"
        return 0
    else
        print_error "WAN $wan_name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证WAN可选字段默认值
verify_wan_optional_defaults() {
    local wan_name=$1

    echo "验证WAN $wan_name 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye nat getproxy $wan_name 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取WAN $wan_name 配置"
        return 1
    fi

    # 验证clone_mac默认值为00-00-00-00-00-00
    if echo "$output" | grep -q "clone_mac=00-00-00-00-00-00" || ! echo "$output" | grep -q "clone_mac="; then
        print_success "WAN $wan_name clone_mac默认值验证通过 (00-00-00-00-00-00或未设置)"
    else
        print_error "WAN $wan_name clone_mac默认值验证失败，期望: 00-00-00-00-00-00"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证dns_pxy默认值为0（关闭）
    if echo "$output" | grep -q "dns_pxy=0" || ! echo "$output" | grep -q "dns_pxy="; then
        print_success "WAN $wan_name dns_pxy默认值验证通过 (0或未设置)"
    else
        print_error "WAN $wan_name dns_pxy默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证ping_disable默认值为0（允许ping）
    if echo "$output" | grep -q "ping_disable=0" || ! echo "$output" | grep -q "ping_disable="; then
        print_success "WAN $wan_name ping_disable默认值验证通过 (0或未设置)"
    else
        print_error "WAN $wan_name ping_disable默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证heartbeat相关字段默认值
    if echo "$output" | grep -q "ping_ip=0.0.0.0" || ! echo "$output" | grep -q "ping_ip="; then
        print_success "WAN $wan_name ping_ip默认值验证通过 (0.0.0.0或未设置)"
    else
        print_error "WAN $wan_name ping_ip默认值验证失败，期望: 0.0.0.0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}

# 设置interface为接外模式（WAN配置的前置条件）
setup_interface_for_wan() {
    local interface=$1
    
    print_info "设置接口 $interface 为接外模式..."
    echo "设置接口 $interface 为接外模式..." >> $LOG_FILE
    
    local output=$(floweye if set name=$interface mode=0 zone=outside mixmode=0 2>&1)
    if [ $? -eq 0 ]; then
        print_success "接口 $interface 设置为接外模式成功"
        return 0
    else
        print_error "接口 $interface 设置为接外模式失败: $output"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 清理interface配置
cleanup_interface() {
    local interface=$1
    echo "清理接口 $interface 配置..." >> $LOG_FILE
    floweye if set name=$interface mode=0 zone=inside mixmode=0 > /dev/null 2>&1 || true
}

# 清理WAN配置
cleanup_wan() {
    local wan_name=$1
    echo "清理WAN $wan_name 配置..." >> $LOG_FILE
    floweye nat rmvproxy $wan_name > /dev/null 2>&1 || true
}

# 清理所有现有的WAN配置
cleanup_all_wan_configs() {
    echo "清理所有现有WAN配置..." >> $LOG_FILE

    # 获取所有WAN配置列表
    local wan_list=$(floweye nat listproxy json=1 type=wan 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$wan_list" ] && [ "$wan_list" != "null" ]; then
        # 解析JSON并提取WAN名称
        local wan_names=$(echo "$wan_list" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)

        if [ -n "$wan_names" ]; then
            echo "发现现有WAN配置: $wan_names" >> $LOG_FILE
            for wan_name in $wan_names; do
                echo "删除WAN配置: $wan_name" >> $LOG_FILE
                floweye nat rmvproxy "$wan_name" > /dev/null 2>&1 || true
            done
            print_success "清理了现有WAN配置"
        else
            print_info "没有发现现有WAN配置"
        fi
    else
        print_info "没有发现现有WAN配置"
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "WAN模块综合测试"
    
    # 初始化日志文件
    echo "WAN模块综合测试 - $(date)" > $LOG_FILE
    
    # 检查agent debug服务器是否运行
    if ! pgrep -f "agent.*debug" > /dev/null; then
        print_info "启动agent debug服务器..."
        if ../agent debug start 2>/dev/null; then
            print_success "debug服务器启动成功"
            sleep 2
        else
            print_warning "debug服务器启动失败，可能已经在运行"
        fi
    else
        print_info "debug服务器已在运行"
    fi
    
    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi
    
    # 设置测试环境 - 配置interface为接外模式
    print_info "设置测试环境..."
    setup_interface_for_wan "eth2"

    # 清理所有现有的WAN配置
    print_info "清理现有WAN配置..."
    cleanup_all_wan_configs
    
    print_info "开始执行综合功能测试..."
    
    # 1. 基础CRUD操作测试
    print_header "基础CRUD操作测试"
    
    # 1.1 创建-创建-无变化测试
    print_info "1.1 创建-创建-无变化测试"
    run_test "test_wan_static_new.json" "创建静态IP WAN"
    verify_wan "wan-test-static" "proxy" "eth2" "1500"
    
    run_test "test_wan_static_idempotent.json" "重复创建相同配置（幂等性）"
    verify_wan "wan-test-static" "proxy" "eth2" "1500"
    
    # 1.2 创建-修改测试（单字段修改）
    print_info "1.2 创建-修改测试"
    run_test "test_wan_static_modify_mtu.json" "修改MTU字段"
    verify_wan "wan-test-static" "proxy" "eth2" "1400"
    
    run_test "test_wan_static_modify_ip.json" "修改IP地址字段"
    verify_wan "wan-test-static" "proxy" "eth2" "1400"
    
    # 1.3 创建-删除测试
    print_info "1.3 创建-删除测试"
    run_test "test_wan_static_delete.json" "删除WAN配置"
    verify_wan_not_exists "wan-test-static"
    
    run_test "test_wan_static_delete_idempotent.json" "重复删除（幂等性）"
    verify_wan_not_exists "wan-test-static"
    
    # 2. 全量同步测试
    print_header "全量同步测试"
    
    # 2.1 设置初始配置（增量模式）
    print_info "2.1 设置初始配置"
    run_test "test_wan_full_sync_setup.json" "全量同步初始配置"
    verify_wan "wan-sync-1" "proxy" "eth2" "1500"
    verify_wan "wan-sync-2" "dhcpwan" "eth2" "1500"
    
    # 2.2 启动全量同步
    print_info "2.2 启动全量同步"
    start_full_sync || exit 1
    
    # 2.3 发送全量同步配置（只包含需要保留的配置）
    print_info "2.3 发送全量同步配置"
    run_test "test_wan_full_sync_cleanup.json" "全量同步配置"
    verify_wan "wan-sync-1" "proxy" "eth2" "1400"  # 修改后的配置
    
    # 2.4 结束全量同步，触发清理逻辑
    print_info "2.4 结束全量同步"
    end_full_sync || exit 1
    
    # 2.5 验证清理结果：wan-sync-2应该被删除
    print_info "2.5 验证清理结果"
    sleep 2  # 等待清理完成
    verify_wan "wan-sync-1" "proxy" "eth2" "1400"  # 保留的配置
    verify_wan_not_exists "wan-sync-2"  # 应该被清理的配置
    
    # 3. 不同WAN类型测试
    print_header "不同WAN类型测试"
    
    # 3.1 DHCP WAN测试
    print_info "3.1 DHCP WAN测试"
    run_test "test_wan_dhcp_new.json" "创建DHCP WAN"
    verify_wan "wan-test-dhcp" "dhcpwan" "eth2" "1500"
    
    run_test "test_wan_dhcp_modify.json" "修改DHCP WAN"
    verify_wan "wan-test-dhcp" "dhcpwan" "eth2" "1400"
    
    run_test "test_wan_dhcp_delete.json" "删除DHCP WAN"
    verify_wan_not_exists "wan-test-dhcp"
    
    # 3.2 PPPoE WAN测试
    print_info "3.2 PPPoE WAN测试"
    run_test "test_wan_pppoe_new.json" "创建PPPoE WAN"
    verify_wan "wan-test-pppoe" "pppoe" "eth2" "1460"
    
    run_test "test_wan_pppoe_modify.json" "修改PPPoE WAN"
    verify_wan "wan-test-pppoe" "pppoe" "eth2" "1400"
    
    run_test "test_wan_pppoe_delete.json" "删除PPPoE WAN"
    verify_wan_not_exists "wan-test-pppoe"
    
    # 4. 边界条件和错误处理测试
    print_header "边界条件和错误处理测试"
    
    run_test "test_wan_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_wan_error_no_ifname.json" "缺少ifname字段错误测试" "false"
    run_test "test_wan_error_invalid_mtu.json" "无效MTU错误测试" "false"
    run_test "test_wan_error_no_type.json" "缺少WAN类型错误测试" "false"

    # 5. 可选字段默认值恢复测试
    print_header "可选字段默认值恢复测试"

    # 5.1 创建包含所有可选字段的完整配置
    print_info "5.1 创建包含所有可选字段的完整配置"
    run_test "test_wan_optional_fields_complete.json" "创建包含所有可选字段的完整配置"
    verify_wan "wan-opt" "proxy" "eth2" "1500"

    # 验证可选字段已设置
    local output=$(floweye nat getproxy wan-opt 2>/dev/null)
    if echo "$output" | grep -iq "clonemac=aa-bb-cc-dd-ee-ff"; then
        print_success "可选字段clone_mac已正确设置"
    else
        print_error "可选字段clone_mac设置失败"
        echo "$output" >> $LOG_FILE
    fi

    if echo "$output" | grep -q "dnspxy=1"; then
        print_success "可选字段dns_pxy已正确设置"
    else
        print_error "可选字段dns_pxy设置失败"
        echo "$output" >> $LOG_FILE
    fi

    # 5.2 修改配置，移除所有可选字段，验证默认值恢复
    print_info "5.2 移除可选字段验证默认值恢复"
    run_test "test_wan_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_wan "wan-opt" "proxy" "eth2" "1500"
    verify_wan_optional_defaults "wan-opt"

    # 清理测试环境
    print_info "清理测试环境..."
    cleanup_wan "wan-test-static"
    cleanup_wan "wan-test-dhcp"
    cleanup_wan "wan-test-pppoe"
    cleanup_wan "wan-sync-1"
    cleanup_wan "wan-sync-2"
    cleanup_wan "wan-opt"
    cleanup_interface "eth2"
    
    # 测试结果统计
    print_header "测试结果"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        echo "成功率: 100%"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败"
        echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
        exit 1
    fi
}

# 执行主函数
main "$@"
