#!/bin/bash

# Traffic Stat模块综合测试脚本
# 测试所有traffic stat模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="traffic_stat_test_results.log"
echo "Traffic Stat模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE
    
    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi
    
    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi
    
    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证traffic stat配置
verify_traffic_stat() {
    local name=$1
    local expected_trackip=$2
    
    echo "验证流量统计 $name 配置..." >> $LOG_FILE
    local output=$(floweye ntmso list 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取流量统计列表"
        return 1
    fi
    
    # 检查是否存在指定名称的流量统计
    if echo "$output" | grep -q "\"name\":\"$name\""; then
        print_success "流量统计 $name 存在"
        
        # 验证trackip字段
        if echo "$output" | grep -q "\"name\":\"$name\".*\"trackip\":$expected_trackip"; then
            print_success "流量统计 $name trackip=$expected_trackip 验证通过"
            return 0
        else
            print_error "流量统计 $name trackip 验证失败，期望: $expected_trackip"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    else
        print_error "流量统计 $name 不存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 验证traffic stat不存在
verify_traffic_stat_not_exists() {
    local name=$1
    
    echo "验证流量统计 $name 不存在..." >> $LOG_FILE
    local output=$(floweye ntmso list 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取流量统计列表"
        return 1
    fi
    
    # 检查是否不存在指定名称的流量统计
    if echo "$output" | grep -q "\"name\":\"$name\""; then
        print_error "流量统计 $name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    else
        print_success "流量统计 $name 已删除"
        return 0
    fi
}

# 清理所有测试用的traffic stat配置
cleanup_test_traffic_stats() {
    echo "清理所有测试用的流量统计配置..." >> $LOG_FILE
    
    # 获取所有流量统计列表
    local output=$(floweye ntmso list 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$output" ] && [ "$output" != "null" ]; then
        # 提取测试用的流量统计名称（以test_开头）
        local test_stats=$(echo "$output" | grep -o '"name":"test_[^"]*"' | cut -d'"' -f4)
        
        if [ -n "$test_stats" ]; then
            echo "发现测试用流量统计: $test_stats" >> $LOG_FILE
            for stat_name in $test_stats; do
                # 获取ID
                local stat_id=$(echo "$output" | grep -o "\"id\":[0-9]*,\"name\":\"$stat_name\"" | cut -d':' -f2 | cut -d',' -f1)
                if [ -n "$stat_id" ]; then
                    echo "删除流量统计: $stat_name (ID: $stat_id)" >> $LOG_FILE
                    floweye ntmso remove id=$stat_id > /dev/null 2>&1
                fi
            done
            print_success "清理了测试用流量统计配置"
        fi
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "Traffic Stat模块综合测试开始"
    
    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi
    
    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi
    
    # 清理测试环境
    print_header "清理测试环境"
    cleanup_test_traffic_stats
    
    # 获取初始状态
    print_header "获取初始流量统计状态"
    echo "初始流量统计状态:" >> $LOG_FILE
    floweye ntmso list >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础流量统计配置
    run_test "test_traffic_stat_basic_new.json" "创建基础流量统计配置"
    verify_traffic_stat "test_stat_basic" "1"

    # 1.2 幂等性测试 - 重复创建相同配置
    run_test "test_traffic_stat_idempotent_new.json" "幂等性测试-重复创建"
    verify_traffic_stat "test_stat_basic" "1"

    # 1.3 修改trackip字段
    run_test "test_traffic_stat_modify_trackip.json" "修改trackip字段"
    verify_traffic_stat "test_stat_basic" "0"

    # 1.4 删除配置测试
    run_test "test_traffic_stat_delete.json" "删除配置测试"
    verify_traffic_stat_not_exists "test_stat_basic"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_traffic_stat_delete_idempotent.json" "删除配置幂等性测试"
    verify_traffic_stat_not_exists "test_stat_basic"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建包含所有字段的完整配置
    run_test "test_traffic_stat_complete_config.json" "创建完整配置"
    verify_traffic_stat "test_stat_complete" "1"

    # 2.2 修改完整配置
    run_test "test_traffic_stat_complete_modify.json" "修改完整配置"
    verify_traffic_stat "test_stat_complete" "0"

    # 阶段3: 默认值验证测试
    print_header "阶段3: 默认值验证测试"

    # 3.1 创建包含所有字段的配置
    run_test "test_traffic_stat_optional_fields_complete.json" "创建包含所有字段的配置"
    verify_traffic_stat "test_stat_optional" "1"

    # 3.2 移除可选字段，验证默认值恢复
    run_test "test_traffic_stat_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_traffic_stat "test_stat_optional" "0"

    # 阶段4: 全量同步测试
    print_header "阶段4: 全量同步测试"

    # 4.1 设置初始配置（增量模式）
    run_test "test_traffic_stat_full_sync_setup.json" "全量同步初始配置"
    verify_traffic_stat "test_stat_sync1" "1"
    verify_traffic_stat "test_stat_sync2" "0"
    verify_traffic_stat "test_stat_sync3" "1"

    # 4.2 启动全量同步
    print_header "全量同步测试 - 启动全量同步"
    start_full_sync || exit 1

    # 4.3 发送全量同步配置（只包含需要保留的配置）
    run_test "test_traffic_stat_full_sync_cleanup.json" "全量同步清理配置"
    verify_traffic_stat "test_stat_sync2" "1"

    # 4.4 结束全量同步，触发清理逻辑
    print_header "全量同步测试 - 结束全量同步"
    end_full_sync || exit 1

    # 4.5 验证清理结果：未在全量同步中的配置应被删除
    sleep 2  # 等待清理完成
    verify_traffic_stat_not_exists "test_stat_sync1"
    verify_traffic_stat "test_stat_sync2" "1"
    verify_traffic_stat_not_exists "test_stat_sync3"

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 5.1 无效参数测试（这些应该失败）
    run_test "test_traffic_stat_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_traffic_stat_error_empty_name.json" "空name字段错误测试" "false"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_test_traffic_stats

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Traffic Stat模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
