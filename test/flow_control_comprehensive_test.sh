#!/bin/bash
set -e
# Flow Control Policy模块综合测试脚本
# 测试所有Flow Control Policy模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="flow_control_test_results.log"
echo "Flow Control Policy模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))

        # 输出当前统计信息供结果收集脚本使用
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"

        return 1
    fi

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 等待配置生效
    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 检查是否有解析错误
    if echo "$output" | grep -q "Failed to parse"; then
        has_task_failed=true
    fi

    # 检查是否有语法错误
    if echo "$output" | grep -q "syntax error"; then
        has_task_failed=true
    fi

    # 检查是否有服务器错误状态
    if echo "$output" | grep -q "server returned status [4-5][0-9][0-9]"; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))

            # 输出当前统计信息供结果收集脚本使用
            echo "TOTAL_TESTS=$TOTAL_TESTS"
            echo "PASSED_TESTS=$PASSED_TESTS"
            echo "FAILED_TESTS=$FAILED_TESTS"

            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))

            # 输出当前统计信息供结果收集脚本使用
            echo "TOTAL_TESTS=$TOTAL_TESTS"
            echo "PASSED_TESTS=$PASSED_TESTS"
            echo "FAILED_TESTS=$FAILED_TESTS"

            return 1
        fi
    fi
}

# 验证策略组配置
verify_policy_group() {
    local group_name=$1
    local expected_disable=$2
    local expected_stop=$3

    echo "验证策略组 $group_name 配置..." >> $LOG_FILE

    # 等待一下确保策略组已经创建

    local output=$(floweye policygroup2 list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取策略组列表"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"
        return 1
    fi

    # 查找指定策略组
    local group_line=$(echo "$output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_error "策略组 $group_name 不存在"
        echo "当前策略组列表:" >> $LOG_FILE
        echo "$output" >> $LOG_FILE
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"
        return 1
    fi

    print_success "策略组 $group_name 存在"
    echo "策略组信息: $group_line" >> $LOG_FILE

    # 验证disable状态（第5列）
    local actual_disable=$(echo "$group_line" | awk '{print $5}')
    if [ "$actual_disable" = "$expected_disable" ]; then
        print_success "策略组 $group_name disable=$expected_disable 验证通过"
    else
        print_error "策略组 $group_name disable 验证失败，期望: $expected_disable，实际: $actual_disable"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"
        return 1
    fi

    # 验证stop状态（第6列）
    local actual_stop=$(echo "$group_line" | awk '{print $6}')
    if [ "$actual_stop" = "$expected_stop" ]; then
        print_success "策略组 $group_name stop=$expected_stop 验证通过"
    else
        print_error "策略组 $group_name stop 验证失败，期望: $expected_stop，实际: $actual_stop"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"
        return 1
    fi

    return 0
}

# 验证策略组不存在
verify_policy_group_not_exists() {
    local group_name=$1

    echo "验证策略组 $group_name 不存在..." >> $LOG_FILE
    local output=$(floweye policygroup2 list 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取策略组列表"
        return 1
    fi

    # 查找指定策略组
    local group_line=$(echo "$output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_success "策略组 $group_name 确实不存在"
        return 0
    else
        print_error "策略组 $group_name 仍然存在"
        echo "策略组信息: $group_line" >> $LOG_FILE
        return 1
    fi
}

# 验证策略配置
verify_policy() {
    local group_name=$1
    local cookie=$2
    local expected_disable=$3

    echo "验证策略 cookie=$cookie 在组 $group_name 中的配置..." >> $LOG_FILE

    # 首先获取策略组ID
    local group_output=$(floweye policygroup2 list 2>/dev/null)
    local group_line=$(echo "$group_output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_error "策略组 $group_name 不存在"
        return 1
    fi

    local group_id=$(echo "$group_line" | awk '{print $1}')
    echo "策略组 $group_name ID: $group_id" >> $LOG_FILE

    # 等待一下确保策略已经创建

    # 使用详细配置查询而不是list - 通过cookie获取策略详细配置
    local policy_output=$(floweye newpolicy get group=$group_id cookie=$cookie 2>/dev/null)
    local get_exit_code=$?

    echo "策略详细配置输出: $policy_output" >> $LOG_FILE

    # 检查策略是否存在
    if [ $get_exit_code -eq 0 ] && [ -n "$policy_output" ] && echo "$policy_output" | grep -q "cookie=$cookie"; then
        print_success "策略 cookie=$cookie 存在于组 $group_name"

        # 验证disable状态（如果提供了期望值）
        if [ -n "$expected_disable" ]; then
            if echo "$policy_output" | grep -q "disable=$expected_disable"; then
                print_success "策略 cookie=$cookie 的disable状态正确: $expected_disable"
            else
                print_error "策略 cookie=$cookie 的disable状态不正确，期望: $expected_disable"
                echo "实际配置: $policy_output" >> $LOG_FILE
                return 1
            fi
        fi

        echo "策略详细信息: $policy_output" >> $LOG_FILE
        return 0
    else
        print_error "策略 cookie=$cookie 不存在于组 $group_name"
        echo "获取策略配置失败，退出码: $get_exit_code" >> $LOG_FILE
        return 1
    fi
}

# 验证策略不存在
verify_policy_not_exists() {
    local group_name=$1
    local cookie=$2

    echo "验证策略 cookie=$cookie 在组 $group_name 中不存在..." >> $LOG_FILE

    # 首先获取策略组ID
    local group_output=$(floweye policygroup2 list 2>/dev/null)
    local group_line=$(echo "$group_output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_success "策略组 $group_name 不存在，策略自然不存在"
        return 0
    fi

    local group_id=$(echo "$group_line" | awk '{print $1}')

    # 使用详细配置查询而不是list - 尝试通过cookie获取策略详细配置
    local policy_output=$(floweye newpolicy get group=$group_id cookie=$cookie 2>/dev/null)
    local get_exit_code=$?

    echo "策略查询结果，退出码: $get_exit_code" >> $LOG_FILE
    echo "策略查询输出: $policy_output" >> $LOG_FILE

    # 检查策略是否不存在
    if [ $get_exit_code -ne 0 ] || [ -z "$policy_output" ] || ! echo "$policy_output" | grep -q "cookie=$cookie"; then
        print_success "策略 cookie=$cookie 确实不存在于组 $group_name"
        return 0
    else
        print_error "策略 cookie=$cookie 仍然存在于组 $group_name"
        echo "策略详细信息: $policy_output" >> $LOG_FILE
        return 1
    fi
}

# 验证策略字段完整性（除ID外其他字段不应改变）
verify_policy_fields_integrity() {
    local group_name=$1
    local cookie=$2
    local expected_desc=$3
    local expected_action=$4
    local expected_inip=$5
    local expected_outip=$6
    local expected_app_name=$7
    local expected_app_protocol=$8

    echo "验证策略 cookie=$cookie 的字段完整性..." >> $LOG_FILE

    # 获取策略组ID
    local group_output=$(floweye policygroup2 list 2>/dev/null)
    local group_line=$(echo "$group_output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_error "策略组 $group_name 不存在"
        return 1
    fi

    local group_id=$(echo "$group_line" | awk '{print $1}')

    # 获取策略详细配置
    local policy_output=$(floweye newpolicy get group=$group_id cookie=$cookie 2>/dev/null)
    if [ $? -ne 0 ]; then
        print_error "无法获取策略 cookie=$cookie 的详细配置"
        return 1
    fi

    echo "策略详细配置: $policy_output" >> $LOG_FILE

    # 验证各个字段
    local desc=$(echo "$policy_output" | grep "desc=" | cut -d'=' -f2)
    local action=$(echo "$policy_output" | grep "action=" | cut -d'=' -f2)
    local inip=$(echo "$policy_output" | grep "inip=" | cut -d'=' -f2)
    local outip=$(echo "$policy_output" | grep "outip=" | cut -d'=' -f2)
    local appid=$(echo "$policy_output" | grep "appid=" | cut -d'=' -f2)
    local proto=$(echo "$policy_output" | grep "proto=" | cut -d'=' -f2)

    # 标准化IP地址格式进行比较
    normalize_ip() {
        local ip="$1"
        if [ -z "$ip" ]; then
            echo ""
            return
        fi

        # 处理复合IP地址格式: "ip,32,***********;rng,********,********00"
        local result=""

        # 按分号分割处理多个IP规范
        IFS=';' read -ra IP_SPECS <<< "$ip"
        for spec in "${IP_SPECS[@]}"; do
            if echo "$spec" | grep -q "^ip,[0-9]*,"; then
                # 处理单个IP: "ip,32,***********" -> "***********"
                local single_ip=$(echo "$spec" | cut -d',' -f3)
                if [ -n "$result" ]; then
                    result="$result,$single_ip"
                else
                    result="$single_ip"
                fi
            elif echo "$spec" | grep -q "^rng,"; then
                # 处理IP范围: "rng,********,********00" -> "********-********00"
                local start_ip=$(echo "$spec" | cut -d',' -f2)
                local end_ip=$(echo "$spec" | cut -d',' -f3)
                local range_ip="$start_ip-$end_ip"
                if [ -n "$result" ]; then
                    result="$result,$range_ip"
                else
                    result="$range_ip"
                fi
            else
                # 其他格式直接使用
                if [ -n "$result" ]; then
                    result="$result,$spec"
                else
                    result="$spec"
                fi
            fi
        done

        echo "$result"
    }

    local normalized_inip=$(normalize_ip "$inip")
    local normalized_outip=$(normalize_ip "$outip")

    local field_errors=0

    if [ "$desc" != "$expected_desc" ]; then
        print_error "策略 $cookie 描述字段不匹配: 期望=$expected_desc, 实际=$desc"
        field_errors=$((field_errors + 1))
    fi

    if [ "$action" != "$expected_action" ]; then
        print_error "策略 $cookie 动作字段不匹配: 期望=$expected_action, 实际=$action"
        field_errors=$((field_errors + 1))
    fi

    if [ "$normalized_inip" != "$expected_inip" ]; then
        print_error "策略 $cookie 内部IP字段不匹配: 期望=$expected_inip, 实际=$normalized_inip (原始=$inip)"
        field_errors=$((field_errors + 1))
    fi

    if [ "$normalized_outip" != "$expected_outip" ]; then
        print_error "策略 $cookie 外部IP字段不匹配: 期望=$expected_outip, 实际=$normalized_outip (原始=$outip)"
        field_errors=$((field_errors + 1))
    fi

    if [ "$appid" != "$expected_app_name" ]; then
        print_error "策略 $cookie 应用ID字段不匹配: 期望=$expected_app_name, 实际=$appid"
        field_errors=$((field_errors + 1))
    fi

    if [ "$proto" != "$expected_app_protocol" ]; then
        print_error "策略 $cookie 协议字段不匹配: 期望=$expected_app_protocol, 实际=$proto"
        field_errors=$((field_errors + 1))
    fi

    if [ $field_errors -eq 0 ]; then
        print_success "策略 $cookie 字段完整性验证通过"
        return 0
    else
        print_error "策略 $cookie 字段完整性验证失败，共 $field_errors 个字段不匹配"
        return 1
    fi
}

# 验证策略natip字段（专门用于NAT策略）
verify_policy_natip() {
    local group_name=$1
    local cookie=$2
    local expected_natip=$3

    echo "验证策略 cookie=$cookie 的natip字段..." >> $LOG_FILE

    # 获取策略组ID
    local group_output=$(floweye policygroup2 list 2>/dev/null)
    local group_line=$(echo "$group_output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_error "策略组 $group_name 不存在"
        return 1
    fi

    local group_id=$(echo "$group_line" | awk '{print $1}')

    # 获取策略详细配置
    local policy_output=$(floweye newpolicy get group=$group_id cookie=$cookie 2>/dev/null)
    if [ $? -ne 0 ]; then
        print_error "无法获取策略 cookie=$cookie 的详细配置"
        return 1
    fi

    echo "策略详细配置: $policy_output" >> $LOG_FILE

    # 提取natip字段
    local natip=$(echo "$policy_output" | grep "natip=" | cut -d'=' -f2)

    echo "期望natip: $expected_natip" >> $LOG_FILE
    echo "实际natip: $natip" >> $LOG_FILE

    if [ "$natip" = "$expected_natip" ]; then
        print_success "策略 $cookie natip字段验证通过: $natip"
        return 0
    else
        print_error "策略 $cookie natip字段验证失败，期望: $expected_natip，实际: $natip"
        return 1
    fi
}

# 验证策略排序
verify_policy_order() {
    local group_name=$1
    shift
    local expected_order=("$@")

    echo "验证策略组 $group_name 中的策略排序..." >> $LOG_FILE

    # 获取策略组ID
    local group_output=$(floweye policygroup2 list 2>/dev/null)
    local group_line=$(echo "$group_output" | grep " $group_name ")
    if [ -z "$group_line" ]; then
        print_error "策略组 $group_name 不存在"
        return 1
    fi

    local group_id=$(echo "$group_line" | awk '{print $1}')

    # 等待一下确保策略已经创建
    sleep 1

    # 获取策略列表，使用JSON格式并解析
    local policy_output=$(floweye newpolicy list group=$group_id json=1 2>/dev/null)
    if [ $? -ne 0 ]; then
        print_error "无法获取策略组 $group_id 的策略列表"
        return 1
    fi

    echo "策略列表输出: $policy_output" >> $LOG_FILE

    # 解析JSON格式的策略列表并提取cookie顺序（按ID顺序）
    local actual_order=()
    if [ -n "$policy_output" ]; then
        # 使用详细的get命令获取每个策略的cookie
        local policy_ids=$(echo "$policy_output" | grep -o '"id":[0-9]*' | cut -d':' -f2 | sort -n)
        for id in $policy_ids; do
            local policy_detail=$(floweye newpolicy get group=$group_id id=$id 2>/dev/null)
            if [[ $policy_detail =~ cookie=([0-9]+) ]]; then
                actual_order+=(${BASH_REMATCH[1]})
            fi
        done
    fi

    echo "期望顺序: ${expected_order[*]}" >> $LOG_FILE
    echo "实际顺序: ${actual_order[*]}" >> $LOG_FILE

    # 比较顺序
    if [ ${#actual_order[@]} -eq ${#expected_order[@]} ]; then
        local order_match=true
        for i in "${!expected_order[@]}"; do
            if [ "${actual_order[i]}" != "${expected_order[i]}" ]; then
                order_match=false
                break
            fi
        done

        if [ "$order_match" = true ]; then
            print_success "策略排序验证通过"
            return 0
        else
            print_error "策略排序验证失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))

            # 输出当前统计信息供结果收集脚本使用
            echo "TOTAL_TESTS=$TOTAL_TESTS"
            echo "PASSED_TESTS=$PASSED_TESTS"
            echo "FAILED_TESTS=$FAILED_TESTS"

            return 1
        fi
    else
        print_error "策略数量不匹配，期望: ${#expected_order[@]}，实际: ${#actual_order[@]}"
        FAILED_TESTS=$((FAILED_TESTS + 1))

        # 输出当前统计信息供结果收集脚本使用
        echo "TOTAL_TESTS=$TOTAL_TESTS"
        echo "PASSED_TESTS=$PASSED_TESTS"
        echo "FAILED_TESTS=$FAILED_TESTS"

        return 1
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 清理所有测试策略组和策略
cleanup_all_flow_control() {
    echo "清理所有测试策略组和策略..." >> $LOG_FILE

    # 获取所有测试相关的策略组（包括新增的测试组）
    local groups=$(floweye policygroup2 list 2>/dev/null | grep -E "(test_|flow_control_test|flow_control_complete|flow_control_mixed|flow_control_movement)" | awk '{print $1}')

    for group_id in $groups; do
        echo "删除策略组 ID: $group_id" >> $LOG_FILE
        floweye policygroup2 remove id=$group_id >> $LOG_FILE 2>&1
    done

    print_success "清理完成"
}

# 主测试流程
main() {
    print_header "Flow Control Policy模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null && ! [ -x "/usr/bin/floweye" ]; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        print_info "PATH: $PATH"
        print_info "检查结果: $(which floweye 2>&1 || echo 'which命令失败')"
        exit 1
    else
        print_success "floweye命令可用: $(which floweye || echo '/usr/bin/floweye')"
    fi

    # 清理环境
    print_header "清理测试环境"
    cleanup_all_flow_control

    # 获取初始状态
    print_header "获取初始状态"
    echo "初始策略组状态:" >> $LOG_FILE
    floweye policygroup2 list >> $LOG_FILE 2>&1

    # 阶段1: 策略组基础CRUD操作测试
    print_header "阶段1: 策略组基础CRUD操作测试"

    # 1.1 创建策略组
    run_test "test_flow_control_policy_group_basic_new.json" "创建基础策略组"
    verify_policy_group "flow_control_test_group1" "0" "0"

    # 1.2 幂等性测试 - 重复创建相同策略组
    run_test "test_flow_control_policy_group_idempotent.json" "策略组幂等性测试"
    verify_policy_group "flow_control_test_group1" "0" "0"

    # 1.3 修改策略组
    run_test "test_flow_control_policy_group_modify.json" "修改策略组配置"
    verify_policy_group "flow_control_test_group1" "1" "1"

    # 1.4 启用策略组
    run_test "test_flow_control_policy_group_enable.json" "启用策略组"
    verify_policy_group "flow_control_test_group1" "0" "1"

    # 1.5 删除策略组
    run_test "test_flow_control_policy_group_delete.json" "删除策略组"
    verify_policy_group_not_exists "flow_control_test_group1"

    # 1.6 删除不存在策略组的幂等性测试
    run_test "test_flow_control_policy_group_delete_idempotent.json" "策略组删除幂等性测试"
    verify_policy_group_not_exists "flow_control_test_group1"

    # 阶段2: 策略基础CRUD操作测试
    print_header "阶段2: 策略基础CRUD操作测试"

    # 2.1 先创建策略组作为前置条件
    run_test "test_flow_control_policy_group_basic_new.json" "创建策略组（策略测试前置条件）"
    verify_policy_group "flow_control_test_group1" "0" "0"

    # 2.2 创建策略
    run_test "test_flow_control_policy_basic_new.json" "创建基础策略"
    verify_policy "flow_control_test_group1" "12345" "0"

    # 2.3 幂等性测试 - 重复创建相同策略
    run_test "test_flow_control_policy_idempotent.json" "策略幂等性测试"
    verify_policy "flow_control_test_group1" "12345" "0"

    # 2.4 修改策略
    run_test "test_flow_control_policy_modify.json" "修改策略配置"
    verify_policy "flow_control_test_group1" "12345" "0"

    # 2.5 禁用策略
    run_test "test_flow_control_policy_disable.json" "禁用策略"
    verify_policy "flow_control_test_group1" "12345" "1"

    # 2.6 启用策略
    run_test "test_flow_control_policy_enable.json" "启用策略"
    verify_policy "flow_control_test_group1" "12345" "0"

    # 2.7 删除策略
    run_test "test_flow_control_policy_delete.json" "删除策略"
    verify_policy_not_exists "flow_control_test_group1" "12345"

    # 2.8 删除不存在策略的幂等性测试
    run_test "test_flow_control_policy_delete_idempotent.json" "策略删除幂等性测试"
    verify_policy_not_exists "flow_control_test_group1" "12345"

    # 阶段3: 策略排序测试
    print_header "阶段3: 策略排序测试"

    # 3.1 创建多个策略测试排序
    run_test "test_flow_control_policy_order_setup.json" "创建多个策略用于排序测试"
    verify_policy "flow_control_test_group1" "10001" "0"
    verify_policy "flow_control_test_group1" "10002" "0"
    verify_policy "flow_control_test_group1" "10003" "0"

    # 3.2 验证初始排序
    verify_policy_order "flow_control_test_group1" "10001" "10002" "10003"

    # 3.3 测试向前移动策略
    run_test "test_flow_control_policy_order_forward.json" "策略向前移动"
    verify_policy_order "flow_control_test_group1" "10002" "10001" "10003"

    # 3.4 测试向后移动策略
    run_test "test_flow_control_policy_order_backward.json" "策略向后移动"
    verify_policy_order "flow_control_test_group1" "10002" "10001" "10003"

    # 3.5 测试移动到首位
    run_test "test_flow_control_policy_move_to_first.json" "策略移动到首位"
    verify_policy_order "flow_control_test_group1" "10003" "10002" "10001"

    # 3.6 测试移动到末尾（使用previous = 4294967295，即^uint32(0)）
    run_test "test_flow_control_policy_move_to_last.json" "策略移动到末尾"
    verify_policy_order "flow_control_test_group1" "10003" "10002" "10001"

    # 阶段4: 5种策略类型移动测试
    print_header "阶段4: 5种策略类型移动测试"

    # 4.0 设置测试依赖项（流量通道和流量统计）
    run_test "test_flow_control_dependencies_setup.json" "设置5种策略类型测试依赖项"

    # 4.1 创建移动测试专用策略组
    run_test "test_flow_control_policy_group_movement_new.json" "创建移动测试策略组"
    verify_policy_group "flow_control_movement_group" "0" "0"

    # 4.2 创建5种不同类型的策略
    run_test "test_flow_control_policy_five_types_setup.json" "创建5种策略类型用于移动测试"
    verify_policy "flow_control_movement_group" "60001" "0"  # permit完整配置
    verify_policy "flow_control_movement_group" "60002" "0"  # permit默认配置
    verify_policy "flow_control_movement_group" "60003" "0"  # channel完整配置
    verify_policy "flow_control_movement_group" "60004" "0"  # channel默认配置
    verify_policy "flow_control_movement_group" "60005" "0"  # deny配置

    # 4.3 验证初始排序
    verify_policy_order "flow_control_movement_group" "60001" "60002" "60003" "60004" "60005"

    # 4.4 测试向前移动（将第4个策略移动到第2位）
    run_test "test_flow_control_policy_five_types_forward_move.json" "5种策略类型向前移动测试"
    verify_policy_order "flow_control_movement_group" "60001" "60004" "60002" "60003" "60005"

    # 4.5 测试向后移动（将第2个策略移动到第4位）
    run_test "test_flow_control_policy_five_types_backward_move.json" "5种策略类型向后移动测试"
    verify_policy_order "flow_control_movement_group" "60001" "60004" "60003" "60002" "60005"

    # 4.6 测试移动到首位（将第5个策略移动到首位）
    run_test "test_flow_control_policy_five_types_move_to_first.json" "5种策略类型移动到首位测试"
    verify_policy_order "flow_control_movement_group" "60005" "60001" "60004" "60003" "60002"

    # 4.7 测试追加到末尾（创建新策略追加到末尾，使用previous = 4294967295）
    run_test "test_flow_control_policy_five_types_move_to_last.json" "5种策略类型追加到末尾测试"
    verify_policy "flow_control_movement_group" "60007" "0"
    verify_policy_order "flow_control_movement_group" "60005" "60001" "60004" "60003" "60002" "60007"

    # 4.8 测试删除策略导致的ID调整（删除中间的策略60004）
    run_test "test_flow_control_policy_five_types_delete_middle.json" "5种策略类型删除中间策略ID调整测试"
    verify_policy_not_exists "flow_control_movement_group" "60004"
    verify_policy_order "flow_control_movement_group" "60005" "60001" "60003" "60002" "60007"

    # 4.8.1 验证删除后其他策略字段完整性（除ID外不应改变）
    verify_policy_fields_integrity "flow_control_movement_group" "60005" "deny_policy_config_moved_to_first" "deny" "192.168.5.0" "0.0.0.0" "ssh" "tcp"
    verify_policy_fields_integrity "flow_control_movement_group" "60001" "permit_policy_complete_config" "permit" "***********,********-********00" "***********" "any" "any"
    verify_policy_fields_integrity "flow_control_movement_group" "60003" "channel_policy_complete_config" "testchannel1" "192.168.3.0" "192.167.255.0" "any" "any"
    verify_policy_fields_integrity "flow_control_movement_group" "60002" "permit_policy_default_config_moved_backward" "permit" "192.168.2.0" "0.0.0.0" "any" "any"
    verify_policy_fields_integrity "flow_control_movement_group" "60007" "new_permit_policy_appended_to_last" "permit" "***********,********-********00" "***********" "http" "tcp"

    # 4.9 测试插入新策略导致的ID调整（在60001后插入60006）
    run_test "test_flow_control_policy_five_types_insert_middle.json" "5种策略类型插入中间策略ID调整测试"
    verify_policy "flow_control_movement_group" "60006" "0"
    verify_policy_order "flow_control_movement_group" "60005" "60001" "60006" "60003" "60002" "60007"

    # 4.9.1 验证插入后其他策略字段完整性（除ID外不应改变）
    verify_policy_fields_integrity "flow_control_movement_group" "60005" "deny_policy_config_moved_to_first" "deny" "192.168.5.0" "0.0.0.0" "ssh" "tcp"
    verify_policy_fields_integrity "flow_control_movement_group" "60001" "permit_policy_complete_config" "permit" "***********,********-********00" "***********" "any" "any"
    verify_policy_fields_integrity "flow_control_movement_group" "60006" "new_permit_policy_inserted_middle" "permit" "192.168.6.0" "0.0.0.0" "https" "tcp"
    verify_policy_fields_integrity "flow_control_movement_group" "60003" "channel_policy_complete_config" "testchannel1" "192.168.3.0" "192.167.255.0" "any" "any"
    verify_policy_fields_integrity "flow_control_movement_group" "60002" "permit_policy_default_config_moved_backward" "permit" "192.168.2.0" "0.0.0.0" "any" "any"
    verify_policy_fields_integrity "flow_control_movement_group" "60007" "new_permit_policy_appended_to_last" "permit" "***********,********-********00" "***********" "http" "tcp"

    # 阶段5: 全量同步测试
    print_header "阶段4: 全量同步测试"

    # 5.1 设置初始配置（增量模式）
    run_test "test_flow_control_full_sync_setup.json" "全量同步初始配置"
    verify_policy_group "flow_control_test_group1" "0" "0"
    verify_policy_group "flow_control_test_group2" "0" "0"
    verify_policy "flow_control_test_group1" "20001" "0"
    verify_policy "flow_control_test_group2" "20002" "0"

    # 5.2 启动全量同步
    start_full_sync || exit 1

    # 5.3 发送全量同步配置（只包含需要保留的配置）
    run_test "test_flow_control_full_sync_config.json" "全量同步配置"
    verify_policy_group "flow_control_test_group1" "0" "0"
    verify_policy "flow_control_test_group1" "20001" "0"

    # 5.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 5.5 验证清理结果：未在全量同步中的配置应被删除
    verify_policy_group "flow_control_test_group1" "0" "0"
    verify_policy_group_not_exists "flow_control_test_group2"
    verify_policy "flow_control_test_group1" "20001" "0"
    verify_policy_not_exists "flow_control_test_group1" "20002"

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 6.1 策略组错误测试
    run_test "test_flow_control_policy_group_error_no_name.json" "策略组缺少名称错误测试" "false"
    run_test "test_flow_control_policy_group_error_invalid_time.json" "策略组无效时间错误测试" "false"

    # 6.2 策略错误测试
    run_test "test_flow_control_policy_error_no_cookie.json" "策略缺少cookie错误测试" "false"
    run_test "test_flow_control_policy_error_no_group.json" "策略缺少策略组错误测试" "false"

    # 阶段7: 多种策略类型测试
    print_header "阶段7: 多种策略类型测试"

    # 7.1 创建混合策略组
    run_test "test_flow_control_policy_group_mixed_new.json" "创建混合策略组"
    verify_policy_group "flow_control_mixed_group" "0" "0"

    # 7.2 设置通道限速策略测试依赖项
    run_test "test_flow_control_policy_channel_dependencies.json" "设置通道限速策略测试依赖项"

    # 7.3 创建通道限速策略（完整配置）
    run_test "test_flow_control_policy_channel_complete.json" "通道限速策略完整配置测试"
    verify_policy "flow_control_test_group1" "40001" "0"

    # 7.4 创建通道限速策略（默认值配置）
    run_test "test_flow_control_policy_channel_default.json" "通道限速策略默认值配置测试"
    verify_policy "flow_control_test_group1" "40002" "0"

    # 7.5 创建拒绝策略
    run_test "test_flow_control_policy_deny.json" "拒绝策略测试"
    verify_policy "flow_control_test_group1" "40003" "0"

    # 7.5 创建混合类型策略设置
    run_test "test_flow_control_policy_mixed_types_setup.json" "混合类型策略设置"
    verify_policy "flow_control_mixed_group" "50001" "0"
    verify_policy "flow_control_mixed_group" "50002" "0"
    verify_policy "flow_control_mixed_group" "50003" "0"

    # 7.6 验证混合策略初始排序
    verify_policy_order "flow_control_mixed_group" "50001" "50002" "50003"

    # 7.7 测试删除策略导致的ID调整
    run_test "test_flow_control_policy_delete_id_adjustment.json" "删除策略ID调整测试"
    verify_policy_not_exists "flow_control_mixed_group" "50002"
    verify_policy_order "flow_control_mixed_group" "50001" "50003"

    # 7.8 测试插入策略导致的ID调整
    run_test "test_flow_control_policy_insert_id_adjustment.json" "插入策略ID调整测试"
    verify_policy "flow_control_mixed_group" "50004" "0"
    verify_policy_order "flow_control_mixed_group" "50001" "50004" "50003"

    # 7.9 测试混合类型策略排序
    run_test "test_flow_control_policy_mixed_types_ordering.json" "混合类型策略排序测试"
    verify_policy_order "flow_control_mixed_group" "50003" "50001" "50004"

    # 阶段8: 完整配置测试
    print_header "阶段8: 完整配置测试"

    # 8.0 设置完整配置测试的依赖项（流量通道和流量统计）
    run_test "test_flow_control_dependencies_setup.json" "设置完整配置测试依赖项"

    # 8.1 创建包含所有字段的完整策略组配置
    run_test "test_flow_control_policy_group_complete_config.json" "完整策略组配置测试"
    verify_policy_group "flow_control_complete_group" "0" "0"

    # 8.2 创建包含所有字段的完整策略配置
    run_test "test_flow_control_policy_complete_config.json" "完整策略配置测试"
    verify_policy "flow_control_complete_group" "30001" "0"

    # 阶段9: CIDR格式地址测试
    print_header "阶段9: CIDR格式地址测试"

    # 9.1 测试CIDR格式natip字段处理
    run_test "test_flow_control_policy_cidr_natip.json" "CIDR格式natip字段测试"
    verify_policy "flow_control_test_group1" "50001" "0"
    verify_policy "flow_control_test_group1" "50002" "0"
    verify_policy "flow_control_test_group1" "50003" "0"

    # 9.2 验证CIDR格式地址的字段完整性
    verify_policy_fields_integrity "flow_control_test_group1" "50001" "flow_control_cidr_natip_test_policy_24" "nat" "***********" "*******" "any" "tcp"
    verify_policy_fields_integrity "flow_control_test_group1" "50002" "flow_control_cidr_natip_test_policy_32" "nat" "***********" "*******" "httpgroup" "tcp"
    verify_policy_fields_integrity "flow_control_test_group1" "50003" "flow_control_cidr_natip_test_policy_16" "nat" "***********" "" "any" "any"

    # 9.3 验证CIDR格式natip字段的正确处理
    verify_policy_natip "flow_control_test_group1" "50001" "*************/24"
    verify_policy_natip "flow_control_test_group1" "50002" "*************,*************-*************0"
    verify_policy_natip "flow_control_test_group1" "50003" "10.0.0.0/16"

    # 阶段10: 默认值验证测试
    print_header "阶段10: 默认值验证测试"

    # 10.1 移除策略组可选字段，验证默认值恢复
    run_test "test_flow_control_policy_group_default_values.json" "策略组默认值验证测试"
    verify_policy_group "flow_control_complete_group" "0" "0"

    # 10.2 移除策略可选字段，验证默认值恢复
    run_test "test_flow_control_policy_default_values.json" "策略默认值验证测试"
    verify_policy "flow_control_complete_group" "30001" "0"

    # 清理测试环境
    print_header "清理测试环境"

    # 清理完整配置测试的依赖项
    run_test "test_flow_control_dependencies_cleanup.json" "清理完整配置测试依赖项"

    cleanup_all_flow_control

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    # 输出变量值供结果收集脚本使用
    echo "TOTAL_TESTS=$TOTAL_TESTS"
    echo "PASSED_TESTS=$PASSED_TESTS"
    echo "FAILED_TESTS=$FAILED_TESTS"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "TOTAL_TESTS=$TOTAL_TESTS" >> $LOG_FILE
    echo "PASSED_TESTS=$PASSED_TESTS" >> $LOG_FILE
    echo "FAILED_TESTS=$FAILED_TESTS" >> $LOG_FILE
    echo "Flow Control Policy模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
