[{"tx_id": "dns-policy-cidr-addresses-001", "device_tasks": [{"task_type": "TASK_DNS_POLICY", "task_action": "NEW_CONFIG", "dns_policy_task": {"cookie": 30201, "previous": 0, "disable": false, "sch_time": 0, "in_ip": [{"ip": {"v4_cidr": {"ip": 3232235776, "prefix_length": 24}}}, {"ip": {"v4_cidr": {"ip": 3232235975, "prefix_length": 32}}}, {"ip": {"ip_string": "10.0.0.0/8"}}], "out_ip": [{"ip": {"v4_cidr": {"ip": 134744072, "prefix_length": 24}}}, {"ip": {"ip_string": "***************/32"}}], "pool": 0, "usr_type": "USER_TYPE_ANY", "domain_group": ["test_domain_group"], "app": {"app_name": "any"}, "query_type": "DNS_QUERY_TYPE_ANY", "action": "DNS_ACTION_RDR", "action_rdr": {"act_arg": "wan1", "no_snat": false, "dns_list": [{"v4_cidr": {"ip": 134744072, "prefix_length": 32}}, {"v4_cidr": {"ip": 134744068, "prefix_length": 32}}, {"ip_string": "*******/32"}, {"ip_string": "************/24"}]}}}]}, {"tx_id": "dns-policy-cidr-addresses-002", "device_tasks": [{"task_type": "TASK_DNS_POLICY", "task_action": "NEW_CONFIG", "dns_policy_task": {"cookie": 30202, "previous": 30201, "disable": false, "in_ip": [{"ip": {"v4_cidr": {"ip": 2886729728, "prefix_length": 16}}}], "domain_group": ["malware_domains"], "action": "DNS_ACTION_REPLY", "action_reply": {"act_arg": [{"v4_cidr": {"ip": 0, "prefix_length": 32}}, {"ip_string": "127.0.0.1/32"}]}}}]}]