#!/bin/bash

# 设置UTF-8编码
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# Route Policy模块综合测试脚本
# 测试所有route policy模块的核心功能和边界条件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="route_policy_test_results.log"
echo "Route Policy模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-"true"}

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_info "执行测试: $test_name"
    echo "执行测试: $test_name" >> $LOG_FILE

    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi

    # 执行测试
    echo "执行命令: ../agent-debug-client --config=$test_file" >> $LOG_FILE
    local output=$(../agent-debug-client --config=$test_file 2>&1)
    local exit_code=$?
    echo "命令输出: $output" >> $LOG_FILE
    echo "退出码: $exit_code" >> $LOG_FILE

    # 解析agent-debug-client的输出来判断任务是否成功
    # 检查是否有任务失败的信息
    local has_task_failed=false
    if echo "$output" | grep -q "Task failed:"; then
        has_task_failed=true
    fi

    # 检查是否有错误码不为0的任务
    if echo "$output" | grep -q '"err_code": [1-9]'; then
        has_task_failed=true
    fi

    # 检查是否有HTTP错误状态码
    if echo "$output" | grep -q "Error: server returned status [4-5][0-9][0-9]"; then
        has_task_failed=true
    fi

    # 检查是否有protobuf解析错误
    if echo "$output" | grep -q "Failed to parse task"; then
        has_task_failed=true
    fi

    # 检查是否有proto语法错误
    if echo "$output" | grep -q "proto: syntax error"; then
        has_task_failed=true
    fi

    # 根据任务执行结果和期望结果判断测试是否通过
    if [ "$has_task_failed" = "false" ]; then
        # 任务成功
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        # 任务失败
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            echo "错误输出: $output" >> $LOG_FILE
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证路由策略配置
verify_route_policy() {
    local cookie=$1
    local expected_desc=$2
    local expected_action=$3
    local expected_disable=${4:-""}

    echo "验证路由策略 cookie=$cookie 配置..." >> $LOG_FILE
    local output=$(./floweye_tool rtpolicy get cookie=$cookie 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取路由策略 cookie=$cookie 配置"
        return 1
    fi

    # 验证描述 - 使用更宽松的匹配方式处理编码问题
    if echo "$output" | grep -q "desc=$expected_desc"; then
        print_success "路由策略 $cookie desc=$expected_desc 验证通过"
    else
        # 尝试提取实际的描述进行比较
        local actual_desc=$(echo "$output" | grep -o "desc=[^[:space:]]*" | cut -d'=' -f2-)

        # 如果描述包含中文字符，可能存在编码问题，使用长度比较作为备选方案
        local expected_length=${#expected_desc}
        local actual_length=${#actual_desc}

        if [ "$actual_desc" = "$expected_desc" ]; then
            print_success "路由策略 $cookie desc=$expected_desc 验证通过"
        elif [ "$expected_length" -eq "$actual_length" ] && [[ "$expected_desc" =~ [^[:ascii:]] ]]; then
            # 如果期望描述包含非ASCII字符且长度匹配，认为是编码问题但内容正确
            print_success "路由策略 $cookie desc 验证通过（检测到编码问题但长度匹配）"
            echo "注意：检测到描述编码问题，期望: $expected_desc，实际: $actual_desc" >> $LOG_FILE
        else
            print_error "路由策略 $cookie desc 验证失败，期望: $expected_desc，实际: $actual_desc"
            echo "完整输出: $output" >> $LOG_FILE
            return 1
        fi
    fi

    # 验证动作
    if echo "$output" | grep -q "action=$expected_action"; then
        print_success "路由策略 $cookie action=$expected_action 验证通过"
    else
        print_error "路由策略 $cookie action 验证失败，期望: $expected_action"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证disable状态（如果指定）
    if [ -n "$expected_disable" ]; then
        if echo "$output" | grep -q "disable=$expected_disable"; then
            print_success "路由策略 $cookie disable=$expected_disable 验证通过"
        else
            print_error "路由策略 $cookie disable 验证失败，期望: $expected_disable"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi

    return 0
}

# 验证路由策略不存在
verify_route_policy_not_exists() {
    local cookie=$1

    echo "验证路由策略 cookie=$cookie 不存在..." >> $LOG_FILE
    local output=$(./floweye_tool rtpolicy get cookie=$cookie 2>/dev/null)

    # 检查命令失败、输出为空或输出包含NEXIST
    if [ $? -ne 0 ] || [ -z "$output" ] || [[ "$output" == "NEXIST" ]]; then
        print_success "路由策略 cookie=$cookie 确实不存在"
        return 0
    else
        print_error "路由策略 cookie=$cookie 仍然存在"
        echo "策略详细信息: $output" >> $LOG_FILE
        return 1
    fi
}

# 验证路由策略排序
verify_route_policy_order() {
    local zone=$1
    shift
    local expected_order=("$@")

    echo "验证zone=$zone中的路由策略排序..." >> $LOG_FILE

    # 获取指定zone的策略列表
    local output=$(./floweye_tool route list json=1 2>/dev/null)
    if [ $? -ne 0 ]; then
        print_error "无法获取路由策略列表"
        return 1
    fi

    # 解析JSON并提取指定zone的策略cookie顺序（按polno排序）
    local actual_order=()

    # 根据zone确定优先级范围
    local min_priority max_priority
    case "$zone" in
        "CTRL_TIER_T1")
            min_priority=1
            max_priority=5000
            ;;
        "CUST_TIER_T2")
            min_priority=5001
            max_priority=50000
            ;;
        "LPM_TIER_T3")
            min_priority=50001
            max_priority=60000
            ;;
        "DEF_WAN_TIER_T4")
            min_priority=60001
            max_priority=65535
            ;;
        *)
            echo "未知的zone: $zone" >> $LOG_FILE
            return 1
            ;;
    esac

    # 使用全局匹配提取所有polno值
    local polnos=($(echo "$output" | grep -oE '"polno":[0-9]+' | grep -oE '[0-9]+'))

    for polno in "${polnos[@]}"; do
        # 根据zone过滤策略：只处理在指定zone范围内的策略
        if [ "$polno" -ge "$min_priority" ] && [ "$polno" -le "$max_priority" ]; then
            # 获取该策略的详细信息以确定cookie
            local policy_detail=$(./floweye_tool rtpolicy get id=$polno 2>/dev/null)
            if [[ $policy_detail =~ cookie=([0-9]+) ]]; then
                local cookie=${BASH_REMATCH[1]}
                # 跳过系统默认策略（cookie=0）
                if [ "$cookie" -ne 0 ]; then
                    actual_order+=($cookie)
                fi
            fi
        fi
    done

    # 只验证期望的策略在实际顺序中的相对位置
    local filtered_actual_order=()

    # 从实际顺序中提取期望的策略
    for cookie in "${actual_order[@]}"; do
        for expected_cookie in "${expected_order[@]}"; do
            if [ "$cookie" = "$expected_cookie" ]; then
                filtered_actual_order+=($cookie)
                break
            fi
        done
    done

    echo "期望顺序: ${expected_order[*]}" >> $LOG_FILE
    echo "实际顺序（全部）: ${actual_order[*]}" >> $LOG_FILE
    echo "实际顺序（过滤后）: ${filtered_actual_order[*]}" >> $LOG_FILE

    # 比较过滤后的顺序
    for i in "${!expected_order[@]}"; do
        if [ $i -ge ${#filtered_actual_order[@]} ]; then
            print_error "策略排序不匹配，位置$i期望: ${expected_order[i]}，但实际顺序已结束"
            return 1
        fi
        if [ "${filtered_actual_order[i]}" != "${expected_order[i]}" ]; then
            print_error "策略排序不匹配，位置$i期望: ${expected_order[i]}，实际: ${filtered_actual_order[i]}"
            return 1
        fi
    done

    print_success "路由策略排序验证通过: ${expected_order[*]}"
    return 0
}

# 清理路由策略
cleanup_route_policy() {
    local cookie=$1
    echo "清理路由策略 cookie=$cookie..." >> $LOG_FILE
    
    # 获取策略ID
    local output=$(./floweye_tool rtpolicy get cookie=$cookie 2>/dev/null)
    if [ $? -eq 0 ] && [[ $output =~ id=([0-9]+) ]]; then
        local policy_id=${BASH_REMATCH[1]}
        ./floweye_tool route remove id=$policy_id >> $LOG_FILE 2>&1
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 创建测试依赖（IP群组、用户组、WAN线路等）
setup_test_dependencies() {
    print_header "设置测试依赖"

    # 使用JSON配置文件设置依赖
    echo "使用JSON配置设置测试依赖..." >> $LOG_FILE
    if ! run_test "test_route_policy_dependencies_setup.json" "设置Route Policy测试依赖"; then
        print_error "测试依赖设置失败，无法继续执行测试"
        exit 1
    fi

    print_success "测试依赖设置完成"
}

# 清理测试依赖
cleanup_test_dependencies() {
    print_header "清理测试依赖"

    # 使用JSON配置文件清理依赖
    echo "使用JSON配置清理测试依赖..." >> $LOG_FILE
    run_test "test_route_policy_dependencies_cleanup.json" "清理Route Policy测试依赖"

    print_success "测试依赖清理完成"
}

# 主测试流程
main() {
    print_header "Route Policy模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye_tool是否可用
    if [ ! -f "./floweye_tool" ]; then
        print_error "floweye_tool不可用，请确保在正确的目录中运行"
        exit 1
    fi

    # 设置测试依赖
    setup_test_dependencies

    # 获取初始路由策略状态
    print_header "获取初始路由策略状态"
    echo "初始路由策略状态:" >> $LOG_FILE
    ./floweye_tool route list >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 创建基础路由策略
    if run_test "test_route_policy_basic_new.json" "创建基础路由策略"; then
        verify_route_policy "10001" "test_route_basic" "route" "0" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 1.2 幂等性测试 - 重复创建相同配置
    if run_test "test_route_policy_idempotent.json" "幂等性测试-重复创建"; then
        verify_route_policy "10001" "test_route_basic" "route" "0" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 1.3 修改路由策略
    if run_test "test_route_policy_modify_desc.json" "修改策略描述"; then
        verify_route_policy "10001" "test_route_modified" "route" "0" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    if run_test "test_route_policy_modify_action.json" "修改策略动作"; then
        verify_route_policy "10001" "test_route_modified" "nat" "0" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 1.4 启用/禁用策略
    if run_test "test_route_policy_disable.json" "禁用路由策略"; then
        verify_route_policy "10001" "test_route_modified" "nat" "1" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    if run_test "test_route_policy_enable.json" "启用路由策略"; then
        verify_route_policy "10001" "test_route_modified" "nat" "0" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 1.5 删除策略
    if run_test "test_route_policy_delete.json" "删除路由策略"; then
        verify_route_policy_not_exists "10001" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 1.6 删除不存在策略的幂等性测试
    if run_test "test_route_policy_delete_idempotent.json" "删除策略幂等性测试"; then
        verify_route_policy_not_exists "10001" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 创建包含所有字段的完整配置
    run_test "test_route_policy_complete_config.json" "创建完整配置路由策略"
    verify_route_policy "10002" "complete_route_policy" "dnat" "0"

    # 2.2 修改完整配置
    run_test "test_route_policy_complete_modify.json" "修改完整配置路由策略"
    verify_route_policy "10002" "complete_route_modified" "nat" "0"

    # 阶段3: 默认值验证测试
    print_header "阶段3: 默认值验证测试"

    # 3.1 创建包含所有可选字段的配置
    run_test "test_route_policy_optional_fields_complete.json" "创建包含所有可选字段的配置"
    verify_route_policy "10003" "optional_fields_complete" "route" "0"

    # 3.2 移除可选字段，验证默认值恢复
    run_test "test_route_policy_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_route_policy "10003" "optional_fields_default" "route" "0"

    # 阶段4: 策略排序测试
    print_header "阶段4: 策略排序测试"

    # 4.1 创建多个策略用于排序测试
    run_test "test_route_policy_ordering_setup.json" "创建多个策略用于排序测试"
    verify_route_policy "10011" "policy_first" "route" "0"
    verify_route_policy "10012" "policy_second" "nat" "0"
    verify_route_policy "10013" "policy_third" "dnat" "0"

    # 4.2 测试策略移动到首位
    if run_test "test_route_policy_move_to_first.json" "移动策略到首位"; then
        verify_route_policy_order "CUST_TIER_T2" "10013" "10011" "10012" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 4.3 测试策略移动到末尾
    if run_test "test_route_policy_move_to_last.json" "移动策略到末尾"; then
        verify_route_policy_order "CUST_TIER_T2" "10011" "10012" "10013" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 4.4 测试中间插入
    run_test "test_route_policy_insert_middle.json" "在中间插入新策略"
    verify_route_policy "10014" "policy_middle" "route" "0"

    # 4.5 五种策略类型排序测试（参考flow control policy最佳实践）
    print_header "五种策略类型排序测试"

    # 清理之前的测试策略
    cleanup_route_policy "10011"
    cleanup_route_policy "10012"
    cleanup_route_policy "10013"
    cleanup_route_policy "10014"

    # 创建五种不同类型的策略
    run_test "test_route_policy_five_types_setup.json" "创建五种类型策略用于排序测试"
    verify_route_policy "10051" "完整配置策略1-路由动作" "route" "0"
    verify_route_policy "10052" "基础配置策略1-NAT动作" "nat" "0"
    verify_route_policy "10053" "完整配置策略2-DNAT动作" "dnat" "0"
    verify_route_policy "10054" "基础配置策略2-代播动作" "route" "0"
    verify_route_policy "10055" "禁用策略-完整配置" "route" "1"

    # 测试向前移动策略
    if run_test "test_route_policy_five_types_forward_move.json" "向前移动策略"; then
        verify_route_policy_order "CUST_TIER_T2" "10051" "10054" "10052" "10053" "10055" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 测试向后移动策略
    if run_test "test_route_policy_five_types_backward_move.json" "向后移动策略"; then
        verify_route_policy_order "CUST_TIER_T2" "10051" "10054" "10053" "10052" "10055" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 测试移动到首位
    if run_test "test_route_policy_five_types_move_to_first.json" "移动策略到首位"; then
        verify_route_policy_order "CUST_TIER_T2" "10055" "10051" "10054" "10053" "10052" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 测试移动到末尾
    if run_test "test_route_policy_five_types_move_to_last.json" "移动策略到末尾"; then
        verify_route_policy_order "CUST_TIER_T2" "10055" "10054" "10053" "10052" "10051" || FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # 测试中间插入新策略
    run_test "test_route_policy_five_types_insert_middle.json" "在中间插入新策略"
    verify_route_policy "10056" "中间插入策略-混合配置" "route" "0"

    # 测试删除中间策略并验证ID调整
    run_test "test_route_policy_five_types_delete_middle.json" "删除中间策略验证ID调整"
    verify_route_policy_not_exists "10053"

    # 阶段5: LPM自动排序测试
    print_header "阶段5: LPM自动排序测试"

    # 5.1 创建LPM zone策略
    run_test "test_route_policy_lmp_basic.json" "创建LPM zone基础策略"
    verify_route_policy "10061" "lmp_policy_basic" "route" "0"

    # 5.2 创建多个LPM策略测试自动排序
    run_test "test_route_policy_lmp_multiple.json" "创建多个LPM策略测试自动排序"
    verify_route_policy "10022" "lmp_policy_specific" "route" "0"
    verify_route_policy "10023" "lmp_policy_general" "route" "0"

    # 阶段6: 全量同步测试
    print_header "阶段6: 全量同步测试"

    # 6.1 设置初始配置（增量模式）
    run_test "test_route_policy_full_sync_setup.json" "全量同步初始配置"
    verify_route_policy "10031" "full_sync_policy1" "route" "0"
    verify_route_policy "10032" "full_sync_policy2" "nat" "0"
    verify_route_policy "10033" "full_sync_policy3" "dnat" "0"

    # 6.2 启动全量同步
    start_full_sync || exit 1

    # 6.3 发送全量同步配置（只保留部分策略）
    run_test "test_route_policy_full_sync_cleanup.json" "全量同步清理配置"
    verify_route_policy "10031" "full_sync_policy1_updated" "route" "0"

    # 6.4 结束全量同步，触发清理逻辑
    end_full_sync || exit 1

    # 6.5 验证清理结果：未在全量同步中的策略应被删除
    sleep 2  # 等待清理完成
    verify_route_policy "10031" "full_sync_policy1_updated" "route" "0"
    verify_route_policy_not_exists "10032"
    verify_route_policy_not_exists "10033"

    # 阶段7: 边界条件和错误处理测试
    print_header "阶段7: 边界条件和错误处理测试"

    # 7.1 无效参数测试（这些应该失败）
    run_test "test_route_policy_error_no_cookie.json" "缺少cookie字段错误测试" "false"
    run_test "test_route_policy_error_no_desc.json" "缺少desc字段测试(现在应该成功)" "true"
    run_test "test_route_policy_error_invalid_action.json" "无效action错误测试" "false"
    run_test "test_route_policy_error_invalid_zone.json" "无效zone错误测试" "false"
    run_test "test_route_policy_error_missing_action.json" "缺少action字段错误测试" "false"
    run_test "test_route_policy_error_missing_route_config.json" "缺少route_config错误测试" "false"

    # 清理所有测试策略
    print_header "清理测试环境"
    cleanup_route_policy "10001"
    cleanup_route_policy "10002"
    cleanup_route_policy "10003"
    cleanup_route_policy "10011"
    cleanup_route_policy "10012"
    cleanup_route_policy "10013"
    cleanup_route_policy "10014"
    cleanup_route_policy "10061"
    cleanup_route_policy "10022"
    cleanup_route_policy "10023"
    cleanup_route_policy "10031"
    cleanup_route_policy "10032"
    cleanup_route_policy "10033"
    cleanup_route_policy "10051"
    cleanup_route_policy "10052"
    cleanup_route_policy "10053"
    cleanup_route_policy "10054"
    cleanup_route_policy "10055"
    cleanup_route_policy "10056"

    # 清理测试依赖
    cleanup_test_dependencies

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Route Policy模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
