#!/bin/bash

# iWAN Service模块综合测试脚本
# 测试所有iWAN Service模块的核心功能和边界条件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="iwan_service_test_results.log"
echo "iWAN Service模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
    echo "ℹ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    if ../agent-debug-client --config=$test_file >> $LOG_FILE 2>&1; then
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证iWAN Service配置
verify_iwan_service() {
    local service_name=$1
    local expected_addr=$2
    local expected_mtu=$3
    local expected_pool=$4
    
    echo "验证iWAN Service $service_name 配置..." >> $LOG_FILE
    local output=$(floweye nat getproxy name=$service_name 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取iWAN Service $service_name 配置"
        return 1
    fi
    
    # 检查是否返回NEXIST
    if echo "$output" | grep -q "NEXIST"; then
        print_error "iWAN Service $service_name 不存在"
        return 1
    fi
    
    # 验证type为iwansvc
    if echo "$output" | grep -q "type=iwansvc"; then
        print_success "iWAN Service $service_name type=iwansvc 验证通过"
    else
        print_error "iWAN Service $service_name type 验证失败，期望: iwansvc"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证addr
    if echo "$output" | grep -q "addr=$expected_addr"; then
        print_success "iWAN Service $service_name addr=$expected_addr 验证通过"
    else
        print_error "iWAN Service $service_name addr 验证失败，期望: $expected_addr"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证mtu
    if echo "$output" | grep -q "mtu=$expected_mtu"; then
        print_success "iWAN Service $service_name mtu=$expected_mtu 验证通过"
    else
        print_error "iWAN Service $service_name mtu 验证失败，期望: $expected_mtu"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证pool
    if echo "$output" | grep -q "pool=$expected_pool"; then
        print_success "iWAN Service $service_name pool=$expected_pool 验证通过"
    else
        print_error "iWAN Service $service_name pool 验证失败，期望: $expected_pool"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    return 0
}

# 验证iWAN Service不存在
verify_iwan_service_not_exists() {
    local service_name=$1
    
    echo "验证iWAN Service $service_name 不存在..." >> $LOG_FILE
    local output=$(floweye nat getproxy name=$service_name 2>/dev/null)
    
    if echo "$output" | grep -q "NEXIST" || [ $? -ne 0 ]; then
        print_success "iWAN Service $service_name 确实不存在"
        return 0
    else
        print_error "iWAN Service $service_name 仍然存在"
        echo "$output" >> $LOG_FILE
        return 1
    fi
}

# 创建测试用户组
setup_test_user_groups() {
    print_info "创建测试用户组..."
    
    # 创建用户组 ID=100
    floweye pppoeippool add id=100 name=test-pool-100 pid=100 >> $LOG_FILE 2>&1
    if [ $? -eq 0 ]; then
        print_success "创建测试用户组 100 成功"
    else
        print_warning "创建测试用户组 100 失败或已存在"
    fi
    
    # 创建用户组 ID=101  
    floweye pppoeippool add id=101 name=test-pool-101 pid=101 >> $LOG_FILE 2>&1
    if [ $? -eq 0 ]; then
        print_success "创建测试用户组 101 成功"
    else
        print_warning "创建测试用户组 101 失败或已存在"
    fi
}

# 清理测试用户组
cleanup_test_user_groups() {
    print_info "清理测试用户组..."
    
    # 删除用户组 ID=100
    floweye pppoeippool del id=100 >> $LOG_FILE 2>&1
    if [ $? -eq 0 ]; then
        print_success "删除测试用户组 100 成功"
    else
        print_warning "删除测试用户组 100 失败或不存在"
    fi
    
    # 删除用户组 ID=101
    floweye pppoeippool del id=101 >> $LOG_FILE 2>&1
    if [ $? -eq 0 ]; then
        print_success "删除测试用户组 101 成功"
    else
        print_warning "删除测试用户组 101 失败或不存在"
    fi
}

# 清理所有现有的iWAN Service配置
cleanup_all_iwan_service_configs() {
    echo "清理所有现有iWAN Service配置..." >> $LOG_FILE
    
    # 获取所有iWAN Service配置列表
    local service_list=$(floweye nat listproxy json=1 type=iwansvc 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$service_list" ] && [ "$service_list" != "null" ]; then
        # 解析JSON并提取iWAN Service名称
        local service_names=$(echo "$service_list" | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
        
        if [ -n "$service_names" ]; then
            echo "发现现有iWAN Service配置: $service_names" >> $LOG_FILE
            for service_name in $service_names; do
                echo "删除iWAN Service配置: $service_name" >> $LOG_FILE
                floweye nat rmvproxy "$service_name" > /dev/null 2>&1
            done
            print_success "清理了现有iWAN Service配置"
        fi
    fi
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "iWAN Service模块综合测试开始"

    # 检查agent debug服务器是否运行
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi

    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi

    # 清理现有配置
    print_header "清理现有配置"
    cleanup_all_iwan_service_configs

    # 设置测试依赖
    print_header "设置测试依赖"
    setup_test_user_groups

    # 获取初始状态
    print_header "获取初始iWAN Service状态"
    echo "初始iWAN Service状态:" >> $LOG_FILE
    floweye nat listproxy type=iwansvc >> $LOG_FILE 2>&1

    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"

    # 1.1 基础iWAN Service配置
    run_test "test_iwan_service_basic_new.json" "基础iWAN Service配置"
    verify_iwan_service "iwantest1" "192.168.17.1" "1436" "100"

    # 1.2 幂等性测试 - 重复新增相同配置
    run_test "test_iwan_service_idempotent.json" "幂等性测试-重复新增"
    verify_iwan_service "iwantest1" "192.168.17.1" "1436" "100"

    # 1.3 字段修改测试
    run_test "test_iwan_service_modify_addr.json" "修改地址字段"
    verify_iwan_service "iwantest1" "192.168.18.1" "1436" "100"

    run_test "test_iwan_service_modify_mtu.json" "修改MTU字段"
    verify_iwan_service "iwantest1" "192.168.18.1" "1500" "100"

    run_test "test_iwan_service_modify_pool.json" "修改用户组字段"
    verify_iwan_service "iwantest1" "192.168.18.1" "1500" "101"

    # 1.4 删除配置测试
    run_test "test_iwan_service_delete.json" "删除配置测试"
    verify_iwan_service_not_exists "iwantest1"

    # 1.5 删除不存在配置的幂等性测试
    run_test "test_iwan_service_delete_idempotent.json" "删除配置幂等性测试"
    verify_iwan_service_not_exists "iwantest1"

    # 阶段2: 完整配置测试
    print_header "阶段2: 完整配置测试"

    # 2.1 完整配置创建
    run_test "test_iwan_service_complete_config.json" "完整配置创建"
    verify_iwan_service "iwantest2" "192.168.100.1" "1400" "100"

    # 2.2 完整配置修改
    run_test "test_iwan_service_complete_modify.json" "完整配置修改"
    verify_iwan_service "iwantest2" "192.168.101.1" "1450" "101"

    # 阶段3: 全量同步测试
    print_header "阶段3: 全量同步测试"

    # 3.1 全量同步清理测试 - 设置初始配置
    print_header "全量同步清理测试 - 设置初始配置"
    run_test "test_iwan_service_full_sync_setup.json" "全量同步清理初始配置"
    verify_iwan_service "iwansvc1" "192.168.10.1" "1436" "100"
    verify_iwan_service "iwansvc2" "192.168.20.1" "1500" "101"

    # 3.2 启动全量同步
    print_header "全量同步清理测试 - 启动全量同步"
    start_full_sync || exit 1

    # 3.3 发送全量同步配置（只配置iwansvc1）
    run_test "test_iwan_service_full_sync_cleanup.json" "全量同步清理配置"
    verify_iwan_service "iwansvc1" "192.168.11.1" "1400" "100"

    # 3.4 结束全量同步，触发清理逻辑
    print_header "全量同步清理测试 - 结束全量同步"
    end_full_sync || exit 1

    # 3.5 验证清理结果：iwansvc2应该被删除
    sleep 2  # 等待清理完成
    verify_iwan_service "iwansvc1" "192.168.11.1" "1400" "100"
    verify_iwan_service_not_exists "iwansvc2"

    # 阶段4: 可选字段默认值恢复测试
    print_header "阶段4: 可选字段默认值恢复测试"

    # 4.1 创建包含所有字段的完整配置
    run_test "test_iwan_service_optional_fields_complete.json" "创建包含所有字段的完整配置"
    verify_iwan_service "iwantest3" "192.168.50.1" "1300" "100"

    # 4.2 修改配置，移除可选字段，验证默认值恢复
    run_test "test_iwan_service_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_iwan_service "iwantest3" "192.168.51.1" "1436" "101"

    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"

    # 5.1 无效参数测试（这些应该失败）
    run_test "test_iwan_service_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_iwan_service_error_no_addr.json" "缺少addr字段错误测试" "false"
    run_test "test_iwan_service_error_no_mtu.json" "缺少mtu字段错误测试" "false"
    run_test "test_iwan_service_error_no_pool.json" "缺少pool字段错误测试" "false"

    # 5.2 无效值测试
    run_test "test_iwan_service_error_invalid_mtu.json" "无效MTU错误测试" "false"
    run_test "test_iwan_service_error_invalid_pool.json" "无效用户组错误测试" "false"

    # 清理测试环境
    print_header "清理测试环境"
    cleanup_all_iwan_service_configs
    cleanup_test_user_groups

    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "iWAN Service模块测试结束 - $(date)" >> $LOG_FILE

    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
