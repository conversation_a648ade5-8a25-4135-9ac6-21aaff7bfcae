#!/bin/bash

# iWAN Proxy模块测试用例干运行验证脚本
# 验证测试脚本逻辑和JSON文件格式，不执行实际的floweye命令

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 验证JSON文件格式和内容
validate_json_file() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo "验证 $file..."
    
    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        print_error "$description - 文件不存在"
        return 1
    fi
    
    # 检查JSON格式
    if ! python3 -m json.tool "$file" > /dev/null 2>&1; then
        print_error "$description - JSON格式错误"
        return 1
    fi
    
    # 检查必需字段
    if ! grep -q '"task_type": "TASK_IWAN_PROXY"' "$file"; then
        print_error "$description - 缺少task_type字段"
        return 1
    fi
    
    if ! grep -q '"iwan_proxy_task"' "$file"; then
        print_error "$description - 缺少iwan_proxy_task字段"
        return 1
    fi
    
    # 检查task_action字段
    if grep -q '"task_action": "NEW_CONFIG"' "$file" || 
       grep -q '"task_action": "EDIT_CONFIG"' "$file" || 
       grep -q '"task_action": "DELETE_CONFIG"' "$file"; then
        print_success "$description - JSON格式和字段验证通过"
        return 0
    else
        print_error "$description - task_action字段值无效"
        return 1
    fi
}

# 验证protobuf字段映射
validate_protobuf_fields() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo "验证 $file 的protobuf字段映射..."
    
    # 检查是否包含必需的protobuf字段
    local has_required_fields=true
    
    # 对于NEW_CONFIG和EDIT_CONFIG，检查必需字段
    if grep -q '"task_action": "NEW_CONFIG"' "$file" || grep -q '"task_action": "EDIT_CONFIG"' "$file"; then
        if ! grep -q '"name"' "$file"; then
            print_error "$description - 缺少name字段"
            has_required_fields=false
        fi
        
        # 对于非错误测试用例，检查其他必需字段
        if [[ ! "$file" =~ error ]]; then
            if ! grep -q '"ifname"' "$file"; then
                print_error "$description - 缺少ifname字段"
                has_required_fields=false
            fi
            
            if ! grep -q '"mtu"' "$file"; then
                print_error "$description - 缺少mtu字段"
                has_required_fields=false
            fi
            
            if ! grep -q '"svr_addr"' "$file"; then
                print_error "$description - 缺少svr_addr字段"
                has_required_fields=false
            fi
            
            if ! grep -q '"svr_port"' "$file"; then
                print_error "$description - 缺少svr_port字段"
                has_required_fields=false
            fi
            
            if ! grep -q '"username"' "$file"; then
                print_error "$description - 缺少username字段"
                has_required_fields=false
            fi
            
            if ! grep -q '"password"' "$file"; then
                print_error "$description - 缺少password字段"
                has_required_fields=false
            fi
        fi
    fi
    
    if $has_required_fields; then
        print_success "$description - protobuf字段映射验证通过"
        return 0
    else
        return 1
    fi
}

# 验证测试脚本语法
validate_test_script() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo "验证测试脚本语法..."
    
    if bash -n "iwan_proxy_comprehensive_test.sh"; then
        print_success "测试脚本语法验证通过"
        return 0
    else
        print_error "测试脚本语法错误"
        return 1
    fi
}

# 验证测试脚本中的函数定义
validate_script_functions() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo "验证测试脚本函数定义..."
    
    local required_functions=(
        "verify_iwan_proxy"
        "verify_iwan_proxy_not_exists"
        "verify_iwan_proxy_optional_defaults"
        "setup_test_wan"
        "cleanup_test_wan"
        "cleanup_all_iwan_proxy_configs"
        "start_full_sync"
        "end_full_sync"
        "run_test"
    )
    
    local missing_functions=()
    
    for func in "${required_functions[@]}"; do
        if ! grep -q "^$func()" "iwan_proxy_comprehensive_test.sh"; then
            missing_functions+=("$func")
        fi
    done
    
    if [ ${#missing_functions[@]} -eq 0 ]; then
        print_success "测试脚本函数定义验证通过"
        return 0
    else
        print_error "测试脚本缺少函数: ${missing_functions[*]}"
        return 1
    fi
}

# 验证floweye命令使用
validate_floweye_commands() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    echo "验证floweye命令使用..."

    # 检查是否使用了正确的验证和查询命令
    if grep -q "floweye nat rmvproxy" "iwan_proxy_comprehensive_test.sh" &&
       grep -q "floweye nat getproxy" "iwan_proxy_comprehensive_test.sh" &&
       grep -q "floweye nat listproxy" "iwan_proxy_comprehensive_test.sh"; then
        print_success "floweye命令使用验证通过"
        return 0
    else
        print_error "floweye命令使用不正确"
        return 1
    fi
}

main() {
    print_header "iWAN Proxy测试用例干运行验证开始"
    
    # 验证测试脚本
    print_header "验证测试脚本"
    validate_test_script
    validate_script_functions
    validate_floweye_commands
    
    # 验证基础CRUD操作测试用例
    print_header "验证基础CRUD操作测试用例"
    validate_json_file "test_iwan_proxy_basic_new.json" "基础创建测试"
    validate_protobuf_fields "test_iwan_proxy_basic_new.json" "基础创建测试"
    
    validate_json_file "test_iwan_proxy_idempotent.json" "幂等性测试"
    validate_protobuf_fields "test_iwan_proxy_idempotent.json" "幂等性测试"
    
    validate_json_file "test_iwan_proxy_modify_mtu.json" "MTU修改测试"
    validate_protobuf_fields "test_iwan_proxy_modify_mtu.json" "MTU修改测试"
    
    validate_json_file "test_iwan_proxy_modify_server.json" "服务器修改测试"
    validate_protobuf_fields "test_iwan_proxy_modify_server.json" "服务器修改测试"
    
    validate_json_file "test_iwan_proxy_modify_credentials.json" "凭据修改测试"
    validate_protobuf_fields "test_iwan_proxy_modify_credentials.json" "凭据修改测试"
    
    validate_json_file "test_iwan_proxy_delete.json" "删除测试"
    validate_protobuf_fields "test_iwan_proxy_delete.json" "删除测试"
    
    validate_json_file "test_iwan_proxy_delete_idempotent.json" "删除幂等性测试"
    validate_protobuf_fields "test_iwan_proxy_delete_idempotent.json" "删除幂等性测试"
    
    # 验证完整配置测试用例
    print_header "验证完整配置测试用例"
    validate_json_file "test_iwan_proxy_complete_config.json" "完整配置创建测试"
    validate_protobuf_fields "test_iwan_proxy_complete_config.json" "完整配置创建测试"
    
    validate_json_file "test_iwan_proxy_complete_modify.json" "完整配置修改测试"
    validate_protobuf_fields "test_iwan_proxy_complete_modify.json" "完整配置修改测试"
    
    # 验证全量同步测试用例
    print_header "验证全量同步测试用例"
    validate_json_file "test_iwan_proxy_full_sync_setup.json" "全量同步设置测试"
    validate_protobuf_fields "test_iwan_proxy_full_sync_setup.json" "全量同步设置测试"
    
    validate_json_file "test_iwan_proxy_full_sync_cleanup.json" "全量同步清理测试"
    validate_protobuf_fields "test_iwan_proxy_full_sync_cleanup.json" "全量同步清理测试"
    
    # 验证可选字段默认值恢复测试用例
    print_header "验证可选字段默认值恢复测试用例"
    validate_json_file "test_iwan_proxy_optional_fields_complete.json" "可选字段完整配置测试"
    validate_protobuf_fields "test_iwan_proxy_optional_fields_complete.json" "可选字段完整配置测试"
    
    validate_json_file "test_iwan_proxy_optional_fields_default.json" "可选字段默认值测试"
    validate_protobuf_fields "test_iwan_proxy_optional_fields_default.json" "可选字段默认值测试"
    
    # 验证错误处理测试用例
    print_header "验证错误处理测试用例"
    validate_json_file "test_iwan_proxy_error_no_name.json" "缺少name字段错误测试"
    validate_json_file "test_iwan_proxy_error_no_ifname.json" "缺少ifname字段错误测试"
    validate_json_file "test_iwan_proxy_error_invalid_mtu.json" "无效MTU错误测试"
    validate_json_file "test_iwan_proxy_error_invalid_port.json" "无效端口错误测试"
    validate_json_file "test_iwan_proxy_error_nonexistent_wan.json" "不存在WAN错误测试"
    
    # 验证依赖关系测试用例
    print_header "验证依赖关系测试用例"
    validate_json_file "test_iwan_proxy_dependency_test.json" "依赖关系测试"
    validate_protobuf_fields "test_iwan_proxy_dependency_test.json" "依赖关系测试"
    
    # 统计结果
    print_header "验证结果统计"
    echo "总验证项: $TOTAL_CHECKS"
    echo "通过验证: $PASSED_CHECKS"
    echo "失败验证: $FAILED_CHECKS"
    echo "成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "所有验证通过！测试用例准备就绪"
        echo ""
        echo "测试用例验证完成，可以在PA环境中执行："
        echo "  make test-iwan-proxy"
        echo "  VERBOSE=true make test-iwan-proxy"
        exit 0
    else
        print_error "有 $FAILED_CHECKS 个验证失败，请检查并修复"
        exit 1
    fi
}

# 执行主函数
main "$@"
