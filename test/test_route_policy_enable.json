[{"tx_id": "route-policy-enable-001", "device_tasks": [{"task_type": "TASK_ROUTE_POLICY", "task_action": "EDIT_CONFIG", "route_policy_task": {"cookie": 10001, "desc": "test_route_modified", "previous": 0, "disable": false, "zone": "CUST_TIER_T2", "action": "ROUTE_ACTION_NAT", "route_config": {"proxy": "wan"}, "nat_config": {"nat_ip": {"ip_ranges": [{"start_ip": {"ip_string": "**************"}, "end_ip": {"ip_string": "**************"}}]}}}}]}]