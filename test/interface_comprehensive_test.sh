#!/bin/bash

# Interface模块综合测试脚本
# 测试所有interface模块的核心功能和边界条件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="interface_test_results.log"
echo "Interface模块测试开始 - $(date)" > $LOG_FILE

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    echo "✓ $1" >> $LOG_FILE
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    echo "✗ $1" >> $LOG_FILE
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    echo "⚠ $1" >> $LOG_FILE
}

# 测试执行函数
run_test() {
    local test_file=$1
    local test_name=$2
    local expected_success=${3:-true}
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}执行测试: $test_name${NC}"
    echo "执行测试: $test_name" >> $LOG_FILE
    
    if [ ! -f "$test_file" ]; then
        print_error "测试文件不存在: $test_file"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    # 执行测试
    if ../agent-debug-client --config=$test_file >> $LOG_FILE 2>&1; then
        if [ "$expected_success" = "true" ]; then
            print_success "$test_name 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 应该失败但成功了"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    else
        if [ "$expected_success" = "false" ]; then
            print_success "$test_name 正确失败"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        else
            print_error "$test_name 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            return 1
        fi
    fi
}

# 验证接口配置
verify_interface() {
    local interface=$1
    local expected_mode=$2
    local expected_zone=$3
    local expected_mixmode=${4:-""}
    local expected_lagroup=${5:-""}
    
    echo "验证接口 $interface 配置..." >> $LOG_FILE
    local output=$(floweye if get $interface 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取接口 $interface 配置"
        return 1
    fi
    
    # 验证mode
    if echo "$output" | grep -q "mode=$expected_mode"; then
        print_success "接口 $interface mode=$expected_mode 验证通过"
    else
        print_error "接口 $interface mode 验证失败，期望: $expected_mode"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证zone
    if echo "$output" | grep -q "zone=$expected_zone"; then
        print_success "接口 $interface zone=$expected_zone 验证通过"
    else
        print_error "接口 $interface zone 验证失败，期望: $expected_zone"
        echo "$output" >> $LOG_FILE
        return 1
    fi
    
    # 验证mixmode（如果指定）
    if [ -n "$expected_mixmode" ]; then
        if echo "$output" | grep -q "mixmode=$expected_mixmode"; then
            print_success "接口 $interface mixmode=$expected_mixmode 验证通过"
        else
            print_error "接口 $interface mixmode 验证失败，期望: $expected_mixmode"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
    
    # 验证lagroup（如果指定）
    if [ -n "$expected_lagroup" ]; then
        if echo "$output" | grep -q "lagroup=$expected_lagroup"; then
            print_success "接口 $interface lagroup=$expected_lagroup 验证通过"
        else
            print_error "接口 $interface lagroup 验证失败，期望: $expected_lagroup"
            echo "$output" >> $LOG_FILE
            return 1
        fi
    fi
    
    return 0
}

# 验证LACP配置
verify_lacp() {
    local lag_id=$1
    local expected_enable=$2
    local expected_timeout=$3
    local expected_passive=$4

    echo "验证LACP组 $lag_id 配置..." >> $LOG_FILE
    local output=$(floweye lacp get lag=$lag_id 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取LACP组 $lag_id 配置"
        return 1
    fi

    # 验证enable
    if echo "$output" | grep -q "enable=$expected_enable"; then
        print_success "LACP组 $lag_id enable=$expected_enable 验证通过"
    else
        print_error "LACP组 $lag_id enable 验证失败，期望: $expected_enable"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证timeout
    if echo "$output" | grep -q "timeout=$expected_timeout"; then
        print_success "LACP组 $lag_id timeout=$expected_timeout 验证通过"
    else
        print_error "LACP组 $lag_id timeout 验证失败，期望: $expected_timeout"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证passive
    if echo "$output" | grep -q "passive=$expected_passive"; then
        print_success "LACP组 $lag_id passive=$expected_passive 验证通过"
    else
        print_error "LACP组 $lag_id passive 验证失败，期望: $expected_passive"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}

# 验证接口可选字段默认值
verify_interface_optional_defaults() {
    local interface=$1

    echo "验证接口 $interface 可选字段默认值..." >> $LOG_FILE
    local output=$(floweye if get $interface 2>/dev/null)

    if [ $? -ne 0 ]; then
        print_error "无法获取接口 $interface 配置"
        return 1
    fi

    # 验证mixmode默认值为0
    if echo "$output" | grep -q "mixmode=0"; then
        print_success "接口 $interface mixmode默认值验证通过 (0)"
    else
        print_error "接口 $interface mixmode默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证lagroup默认值为0
    if echo "$output" | grep -q "lagroup=0"; then
        print_success "接口 $interface lagroup默认值验证通过 (0)"
    else
        print_error "接口 $interface lagroup默认值验证失败，期望: 0"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    # 验证peer默认值为空或none
    if echo "$output" | grep -q "peer=none" || echo "$output" | grep -q "peer=\"\"" || ! echo "$output" | grep -q "peer="; then
        print_success "接口 $interface peer默认值验证通过 (none或空)"
    else
        print_error "接口 $interface peer默认值验证失败，期望: none或空"
        echo "$output" >> $LOG_FILE
        return 1
    fi

    return 0
}

# 清理函数
cleanup_interface() {
    local interface=$1
    echo "清理接口 $interface 配置..." >> $LOG_FILE
    floweye if set name=$interface mode=0 zone=inside lagroup=0 mixmode=0 >> $LOG_FILE 2>&1
}

# 启动全量同步
start_full_sync() {
    echo "启动全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync start 2>&1)
    local exit_code=$?
    echo "StartFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步启动成功"
        return 0
    else
        print_error "全量同步启动失败: $response"
        return 1
    fi
}

# 结束全量同步
end_full_sync() {
    echo "结束全量同步..." >> $LOG_FILE
    local response=$(../agent debug fullsync end 2>&1)
    local exit_code=$?
    echo "EndFullSync响应: $response" >> $LOG_FILE

    if [ $exit_code -eq 0 ] && echo "$response" | grep -q "successfully"; then
        print_success "全量同步结束成功"
        return 0
    else
        print_error "全量同步结束失败: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "Interface模块综合测试开始"
    
    # 检查agent debug服务器是否运行
    # 通过检测端口8080是否被监听来判断debug服务器状态
    if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
        echo "启动agent debug服务器..."
        if ! ../agent debug start 2>/dev/null; then
            echo "Debug服务器启动失败，可能已经在运行中"
            # 验证服务器是否真的在运行
            if ! netstat -tln 2>/dev/null | grep -q ":8080 "; then
                print_error "Debug服务器无法启动且端口8080未被监听"
                exit 1
            fi
        fi
        sleep 2
    else
        echo "Debug服务器已经在运行中"
    fi
    
    # 检查floweye命令是否可用
    if ! command -v floweye &> /dev/null; then
        print_error "floweye命令不可用，请确保在PA环境中运行"
        exit 1
    fi
    
    # 获取初始接口状态
    print_header "获取初始接口状态"
    echo "初始接口状态:" >> $LOG_FILE
    floweye if list >> $LOG_FILE 2>&1
    
    # 阶段1: 基础CRUD操作测试
    print_header "阶段1: 基础CRUD操作测试"
    
    # 1.1 监控模式基础配置
    run_test "test_interface_monitor_basic.json" "监控模式基础配置"
    verify_interface "eth2" "0" "inside" "0"
    
    # 1.2 幂等性测试 - 重复新增相同配置
    run_test "test_interface_idempotent_new.json" "幂等性测试-重复新增"
    verify_interface "eth2" "0" "inside" "0"
    
    # 1.3 字段修改测试
    run_test "test_interface_modify_zone.json" "修改zone字段"
    verify_interface "eth2" "0" "outside" "0"
    
    run_test "test_interface_modify_mixmode.json" "修改mixmode字段"
    verify_interface "eth2" "0" "outside" "1"
    
    # 1.4 删除配置测试
    run_test "test_interface_delete.json" "删除配置测试"
    verify_interface "eth2" "0" "inside" "0"
    
    # 1.5 删除不存在配置的幂等性测试
    run_test "test_interface_delete_idempotent.json" "删除配置幂等性测试"
    verify_interface "eth2" "0" "inside" "0"
    
    # 阶段2: 网桥模式测试
    print_header "阶段2: 网桥模式测试"
    
    # 2.1 网桥模式1配置
    run_test "test_interface_bridge1.json" "网桥模式1配置"
    verify_interface "eth2" "1" "inside" "1"
    
    # 2.2 网桥模式变更
    run_test "test_interface_bridge4.json" "网桥模式变更"
    verify_interface "eth2" "2" "inside" "1"

    # 2.3 网桥对端变更
    run_test "test_interface_bridge_peer_change.json" "网桥对端变更"
    verify_interface "eth2" "2" "inside" "1"
    
    # 阶段3: 链路聚合测试
    print_header "阶段3: 链路聚合测试"
    
    # 3.1 基础链路聚合配置
    run_test "test_interface_lacp_basic.json" "基础链路聚合配置"
    verify_interface "eth2" "1" "inside" "1" "3"
    verify_lacp "3" "1" "1" "1"
    
    # 3.2 LACP协议变更
    run_test "test_interface_lacp_protocol_change.json" "LACP协议变更"
    verify_interface "eth2" "1" "inside" "1" "3"
    verify_lacp "3" "0" "0" "0"
    
    # 3.3 多链路聚合组
    run_test "test_interface_multiple_lacp.json" "多链路聚合组"
    verify_interface "eth0" "1" "outside" "1" "1"
    verify_interface "eth2" "2" "inside" "1" "2"
    verify_lacp "1" "1" "1" "0"
    verify_lacp "2" "0" "0" "1"
    
    # 阶段4: 全量同步测试
    print_header "阶段4: 全量同步测试"

    # 4.1 全量同步基础流程
    run_test "test_interface_full_sync_basic.json" "全量同步基础流程"
    verify_interface "eth0" "1" "outside" "1"
    verify_interface "eth2" "0" "inside" "0"

    # 4.2 全量同步清理测试（真正的全量同步流程）
    print_header "全量同步清理测试 - 设置初始配置"
    # 先在增量模式下设置初始配置
    run_test "test_interface_full_sync_setup.json" "全量同步清理初始配置"
    verify_interface "eth0" "2" "outside" "1"
    verify_interface "eth1" "2" "outside" "1"
    verify_interface "eth2" "1" "inside" "1"

    print_header "全量同步清理测试 - 启动全量同步"
    start_full_sync || exit 1

    # 发送全量同步配置（只配置eth2）
    run_test "test_interface_full_sync_cleanup.json" "全量同步清理配置"
    verify_interface "eth2" "0" "inside" "0"

    # 结束全量同步，触发清理逻辑
    print_header "全量同步清理测试 - 结束全量同步"
    end_full_sync || exit 1

    # 验证清理结果：eth0和eth1应该被重置为默认配置
    sleep 2  # 等待清理完成
    verify_interface "eth0" "0" "inside" "0"
    verify_interface "eth1" "0" "inside" "0"
    verify_interface "eth2" "0" "inside" "0"
    
    # 阶段5: 边界条件和错误处理测试
    print_header "阶段5: 边界条件和错误处理测试"
    
    # 5.1 无效参数测试（这些应该失败）
    run_test "test_interface_error_no_name.json" "缺少name字段错误测试" "false"
    run_test "test_interface_error_bridge_no_peer.json" "网桥模式缺少peer错误测试" "false"
    run_test "test_interface_error_nonexistent.json" "不存在接口错误测试" "false"
    
    # 5.2 链路聚合边界测试
    run_test "test_interface_error_invalid_lagroup.json" "无效lagroup错误测试" "false"
    
    # 阶段6: 可选字段默认值恢复测试
    print_header "阶段6: 可选字段默认值恢复测试"

    # 6.1 创建包含所有可选字段的完整配置
    run_test "test_interface_optional_fields_complete.json" "创建包含所有可选字段的完整配置"
    verify_interface "eth2" "1" "outside" "1" "3"
    verify_lacp "3" "1" "1" "1"

    # 6.2 修改配置，移除所有可选字段，验证默认值恢复
    run_test "test_interface_optional_fields_default.json" "移除可选字段验证默认值恢复"
    verify_interface "eth2" "0" "inside" "0"
    verify_interface_optional_defaults "eth2"

    # 阶段7: 物理接口限制测试
    print_header "阶段7: 物理接口限制测试"

    # 7.1 所有可用接口配置
    run_test "test_interface_all_interfaces.json" "所有接口配置测试"
    verify_interface "eth0" "0" "outside" "1"
    verify_interface "eth1" "0" "outside" "1"
    verify_interface "eth2" "0" "inside" "0"
    
    # 清理所有配置
    print_header "清理测试环境"
    cleanup_interface "eth0"
    cleanup_interface "eth1"
    cleanup_interface "eth2"
    
    # 测试结果统计
    print_header "测试结果统计"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过测试: $PASSED_TESTS"
    echo "失败测试: $FAILED_TESTS"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    
    echo "" >> $LOG_FILE
    echo "测试结果统计:" >> $LOG_FILE
    echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
    echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
    echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%" >> $LOG_FILE
    echo "Interface模块测试结束 - $(date)" >> $LOG_FILE
    
    if [ $FAILED_TESTS -eq 0 ]; then
        print_success "所有测试通过！"
        exit 0
    else
        print_error "有 $FAILED_TESTS 个测试失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
